package knowledge

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// KnowledgeLevel 知识库层级
type KnowledgeLevel string

const (
	// KnowledgeLevelBase 基础知识库（所有角色可访问）
	KnowledgeLevelBase KnowledgeLevel = "base"
	// KnowledgeLevelRole 角色专用知识库
	KnowledgeLevelRole KnowledgeLevel = "role"
	// KnowledgeLevelDynamic 动态知识库
	KnowledgeLevelDynamic KnowledgeLevel = "dynamic"
)

// AccessLevel 访问权限级别
type AccessLevel string

const (
	// AccessLevelPublic 公开访问
	AccessLevelPublic AccessLevel = "public"
	// AccessLevelRole 角色限制
	AccessLevelRole AccessLevel = "role"
	// AccessLevelPrivate 私有访问
	AccessLevelPrivate AccessLevel = "private"
)

// FAQItem FAQ条目
type FAQItem struct {
	ID          string                 `json:"id" yaml:"id"`
	Question    string                 `json:"question" yaml:"question"`
	Answer      string                 `json:"answer" yaml:"answer"`
	Keywords    []string               `json:"keywords" yaml:"keywords"`
	Tags        []string               `json:"tags,omitempty" yaml:"tags,omitempty"`
	Category    string                 `json:"category,omitempty" yaml:"category,omitempty"`
	Priority    int                    `json:"priority" yaml:"priority"`               // 1-10，数字越大优先级越高
	Roles       []string               `json:"roles,omitempty" yaml:"roles,omitempty"` // 适用角色，为空表示所有角色
	Level       KnowledgeLevel         `json:"level" yaml:"level"`                     // 知识库层级
	AccessLevel AccessLevel            `json:"access_level" yaml:"access_level"`       // 访问权限
	Version     string                 `json:"version" yaml:"version"`
	CreatedAt   time.Time              `json:"created_at" yaml:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at" yaml:"updated_at"`
	Metadata    map[string]interface{} `json:"metadata,omitempty" yaml:"metadata,omitempty"`
}

// FAQKnowledgeBase FAQ知识库
type FAQKnowledgeBase struct {
	ID          string                 `json:"id" yaml:"id"`
	Name        string                 `json:"name" yaml:"name"`
	Description string                 `json:"description" yaml:"description"`
	Level       KnowledgeLevel         `json:"level" yaml:"level"`
	AccessLevel AccessLevel            `json:"access_level" yaml:"access_level"`
	Roles       []string               `json:"roles,omitempty" yaml:"roles,omitempty"`
	Items       []*FAQItem             `json:"items" yaml:"items"`
	Version     string                 `json:"version" yaml:"version"`
	CreatedAt   time.Time              `json:"created_at" yaml:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at" yaml:"updated_at"`
	Metadata    map[string]interface{} `json:"metadata,omitempty" yaml:"metadata,omitempty"`
}

// FAQSearchQuery FAQ搜索查询
type FAQSearchQuery struct {
	Query       string         `json:"query"`                  // 查询文本
	Role        string         `json:"role,omitempty"`         // 角色过滤
	Category    string         `json:"category,omitempty"`     // 分类过滤
	Tags        []string       `json:"tags,omitempty"`         // 标签过滤
	Level       KnowledgeLevel `json:"level,omitempty"`        // 知识库层级过滤
	AccessLevel AccessLevel    `json:"access_level,omitempty"` // 访问权限过滤
	Limit       int            `json:"limit,omitempty"`        // 返回结果数量限制
	MinScore    float64        `json:"min_score,omitempty"`    // 最小相似度分数
}

// FAQSearchResult FAQ搜索结果
type FAQSearchResult struct {
	Item            *FAQItem `json:"item"`             // FAQ条目
	Score           float64  `json:"score"`            // 相似度分数（0-1）
	MatchedKeywords []string `json:"matched_keywords"` // 匹配的关键词
	MatchType       string   `json:"match_type"`       // 匹配类型：exact, keyword, semantic
	Reason          string   `json:"reason"`           // 匹配原因
}

// FAQManager FAQ知识库管理器
type FAQManager struct {
	knowledgeBases map[string]*FAQKnowledgeBase // 知识库映射
	itemIndex      map[string]*FAQItem          // 条目索引
	keywordIndex   map[string][]*FAQItem        // 关键词索引
	categoryIndex  map[string][]*FAQItem        // 分类索引
	roleIndex      map[string][]*FAQItem        // 角色索引
	mu             sync.RWMutex                 // 读写锁
	logger         logger.Logger                // 日志器
}

// NewFAQManager 创建FAQ管理器
func NewFAQManager() *FAQManager {
	return &FAQManager{
		knowledgeBases: make(map[string]*FAQKnowledgeBase),
		itemIndex:      make(map[string]*FAQItem),
		keywordIndex:   make(map[string][]*FAQItem),
		categoryIndex:  make(map[string][]*FAQItem),
		roleIndex:      make(map[string][]*FAQItem),
		logger:         logger.GetGlobalLogger(),
	}
}

// LoadKnowledgeBase 加载FAQ知识库
func (fm *FAQManager) LoadKnowledgeBase(ctx context.Context, kb *FAQKnowledgeBase) error {
	if kb == nil {
		return fmt.Errorf("知识库不能为空")
	}

	fm.mu.Lock()
	defer fm.mu.Unlock()

	// 存储知识库
	fm.knowledgeBases[kb.ID] = kb

	// 构建索引
	for _, item := range kb.Items {
		if item.ID == "" {
			item.ID = fmt.Sprintf("faq_%d", time.Now().UnixNano())
		}

		// 条目索引
		fm.itemIndex[item.ID] = item

		// 关键词索引
		for _, keyword := range item.Keywords {
			keyword = strings.ToLower(strings.TrimSpace(keyword))
			if keyword != "" {
				fm.keywordIndex[keyword] = append(fm.keywordIndex[keyword], item)
			}
		}

		// 分类索引
		if item.Category != "" {
			category := strings.ToLower(strings.TrimSpace(item.Category))
			fm.categoryIndex[category] = append(fm.categoryIndex[category], item)
		}

		// 角色索引
		if len(item.Roles) == 0 {
			// 如果没有指定角色，则所有角色都可以访问
			fm.roleIndex["*"] = append(fm.roleIndex["*"], item)
		} else {
			for _, role := range item.Roles {
				role = strings.ToLower(strings.TrimSpace(role))
				if role != "" {
					fm.roleIndex[role] = append(fm.roleIndex[role], item)
				}
			}
		}
	}

	fm.logger.Info("FAQ知识库加载完成", "kb_id", kb.ID, "items_count", len(kb.Items))
	return nil
}

// Search 搜索FAQ
func (fm *FAQManager) Search(ctx context.Context, query *FAQSearchQuery) ([]*FAQSearchResult, error) {
	if query == nil || query.Query == "" {
		return nil, fmt.Errorf("查询不能为空")
	}

	fm.mu.RLock()
	defer fm.mu.RUnlock()

	var results []*FAQSearchResult
	queryLower := strings.ToLower(strings.TrimSpace(query.Query))
	queryWords := strings.Fields(queryLower)

	// 收集候选FAQ条目
	candidates := make(map[string]*FAQItem)

	// 1. 精确匹配问题
	for _, item := range fm.itemIndex {
		if fm.matchesFilters(item, query) {
			questionLower := strings.ToLower(item.Question)
			if strings.Contains(questionLower, queryLower) {
				candidates[item.ID] = item
			}
		}
	}

	// 2. 关键词匹配
	for _, word := range queryWords {
		if items, exists := fm.keywordIndex[word]; exists {
			for _, item := range items {
				if fm.matchesFilters(item, query) {
					candidates[item.ID] = item
				}
			}
		}
	}

	// 3. 模糊匹配关键词
	for keyword, items := range fm.keywordIndex {
		for _, word := range queryWords {
			if strings.Contains(keyword, word) || strings.Contains(word, keyword) {
				for _, item := range items {
					if fm.matchesFilters(item, query) {
						candidates[item.ID] = item
					}
				}
			}
		}
	}

	// 计算相似度分数并生成结果
	for _, item := range candidates {
		score, matchedKeywords, matchType := fm.calculateScore(item, queryLower, queryWords)

		if score >= query.MinScore {
			result := &FAQSearchResult{
				Item:            item,
				Score:           score,
				MatchedKeywords: matchedKeywords,
				MatchType:       matchType,
				Reason:          fm.generateMatchReason(item, queryLower, matchedKeywords, matchType),
			}
			results = append(results, result)
		}
	}

	// 按分数和优先级排序
	sort.Slice(results, func(i, j int) bool {
		if results[i].Score != results[j].Score {
			return results[i].Score > results[j].Score
		}
		return results[i].Item.Priority > results[j].Item.Priority
	})

	// 限制返回结果数量
	if query.Limit > 0 && len(results) > query.Limit {
		results = results[:query.Limit]
	}

	return results, nil
}

// GetByID 根据ID获取FAQ条目
func (fm *FAQManager) GetByID(ctx context.Context, id string) (*FAQItem, error) {
	fm.mu.RLock()
	defer fm.mu.RUnlock()

	if item, exists := fm.itemIndex[id]; exists {
		return item, nil
	}
	return nil, fmt.Errorf("FAQ条目不存在: %s", id)
}

// GetByCategory 根据分类获取FAQ条目
func (fm *FAQManager) GetByCategory(ctx context.Context, category string, role string) ([]*FAQItem, error) {
	fm.mu.RLock()
	defer fm.mu.RUnlock()

	categoryLower := strings.ToLower(strings.TrimSpace(category))
	items, exists := fm.categoryIndex[categoryLower]
	if !exists {
		return nil, nil
	}

	var result []*FAQItem
	for _, item := range items {
		if fm.hasRoleAccess(item, role) {
			result = append(result, item)
		}
	}

	return result, nil
}

// GetByRole 根据角色获取FAQ条目
func (fm *FAQManager) GetByRole(ctx context.Context, role string) ([]*FAQItem, error) {
	fm.mu.RLock()
	defer fm.mu.RUnlock()

	roleLower := strings.ToLower(strings.TrimSpace(role))
	var result []*FAQItem

	// 获取通用FAQ（所有角色可访问）
	if items, exists := fm.roleIndex["*"]; exists {
		result = append(result, items...)
	}

	// 获取角色专用FAQ
	if items, exists := fm.roleIndex[roleLower]; exists {
		result = append(result, items...)
	}

	return result, nil
}

// matchesFilters 检查FAQ条目是否匹配过滤条件
func (fm *FAQManager) matchesFilters(item *FAQItem, query *FAQSearchQuery) bool {
	// 角色过滤
	if query.Role != "" && !fm.hasRoleAccess(item, query.Role) {
		return false
	}

	// 分类过滤
	if query.Category != "" {
		categoryLower := strings.ToLower(strings.TrimSpace(query.Category))
		itemCategoryLower := strings.ToLower(strings.TrimSpace(item.Category))
		if itemCategoryLower != categoryLower {
			return false
		}
	}

	// 标签过滤
	if len(query.Tags) > 0 {
		hasTag := false
		for _, queryTag := range query.Tags {
			for _, itemTag := range item.Tags {
				if strings.EqualFold(queryTag, itemTag) {
					hasTag = true
					break
				}
			}
			if hasTag {
				break
			}
		}
		if !hasTag {
			return false
		}
	}

	// 知识库层级过滤
	if query.Level != "" && item.Level != query.Level {
		return false
	}

	// 访问权限过滤
	if query.AccessLevel != "" && item.AccessLevel != query.AccessLevel {
		return false
	}

	return true
}

// hasRoleAccess 检查角色是否有访问权限
func (fm *FAQManager) hasRoleAccess(item *FAQItem, role string) bool {
	if len(item.Roles) == 0 {
		return true // 没有角色限制，所有角色都可以访问
	}

	roleLower := strings.ToLower(strings.TrimSpace(role))
	for _, itemRole := range item.Roles {
		if strings.EqualFold(itemRole, roleLower) {
			return true
		}
	}
	return false
}

// calculateScore 计算相似度分数
func (fm *FAQManager) calculateScore(item *FAQItem, queryLower string, queryWords []string) (float64, []string, string) {
	var score float64
	var matchedKeywords []string
	var matchType string

	questionLower := strings.ToLower(item.Question)
	answerLower := strings.ToLower(item.Answer)

	// 1. 精确匹配问题（最高分）
	if strings.Contains(questionLower, queryLower) {
		score += 1.0
		matchType = "exact"
	}

	// 2. 关键词匹配
	keywordScore := 0.0
	for _, keyword := range item.Keywords {
		keywordLower := strings.ToLower(keyword)
		for _, word := range queryWords {
			if strings.Contains(keywordLower, word) || strings.Contains(word, keywordLower) {
				keywordScore += 0.8
				matchedKeywords = append(matchedKeywords, keyword)
				if matchType == "" {
					matchType = "keyword"
				}
			}
		}
	}
	score += keywordScore / float64(len(item.Keywords))

	// 3. 问题文本相似度
	questionScore := 0.0
	for _, word := range queryWords {
		if strings.Contains(questionLower, word) {
			questionScore += 0.6
		}
	}
	score += questionScore / float64(len(queryWords))

	// 4. 答案文本相似度（权重较低）
	answerScore := 0.0
	for _, word := range queryWords {
		if strings.Contains(answerLower, word) {
			answerScore += 0.3
		}
	}
	score += answerScore / float64(len(queryWords))

	// 5. 优先级加权
	priorityWeight := float64(item.Priority) / 10.0
	score = score * (0.8 + 0.2*priorityWeight)

	// 确保分数在0-1范围内
	if score > 1.0 {
		score = 1.0
	}

	if matchType == "" {
		matchType = "semantic"
	}

	return score, matchedKeywords, matchType
}

// generateMatchReason 生成匹配原因
func (fm *FAQManager) generateMatchReason(item *FAQItem, query string, matchedKeywords []string, matchType string) string {
	switch matchType {
	case "exact":
		return "问题精确匹配"
	case "keyword":
		if len(matchedKeywords) > 0 {
			return fmt.Sprintf("关键词匹配: %s", strings.Join(matchedKeywords, ", "))
		}
		return "关键词匹配"
	case "semantic":
		return "语义相似度匹配"
	default:
		return "文本相似度匹配"
	}
}

// ListKnowledgeBases 列出所有知识库
func (fm *FAQManager) ListKnowledgeBases(ctx context.Context) ([]*FAQKnowledgeBase, error) {
	fm.mu.RLock()
	defer fm.mu.RUnlock()

	var result []*FAQKnowledgeBase
	for _, kb := range fm.knowledgeBases {
		result = append(result, kb)
	}
	return result, nil
}

// GetKnowledgeBase 获取指定知识库
func (fm *FAQManager) GetKnowledgeBase(ctx context.Context, id string) (*FAQKnowledgeBase, error) {
	fm.mu.RLock()
	defer fm.mu.RUnlock()

	if kb, exists := fm.knowledgeBases[id]; exists {
		return kb, nil
	}
	return nil, fmt.Errorf("知识库不存在: %s", id)
}

// Close 关闭FAQ管理器
func (fm *FAQManager) Close() error {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	// 清理资源
	fm.knowledgeBases = make(map[string]*FAQKnowledgeBase)
	fm.itemIndex = make(map[string]*FAQItem)
	fm.keywordIndex = make(map[string][]*FAQItem)
	fm.categoryIndex = make(map[string][]*FAQItem)
	fm.roleIndex = make(map[string][]*FAQItem)

	fm.logger.Info("FAQ管理器已关闭")
	return nil
}
