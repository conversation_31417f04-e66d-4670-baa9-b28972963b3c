package tool

import (
	"time"
)

// ToolResult 工具执行结果（保留兼容性）
type ToolResult struct {
	Success   bool                   `json:"success"`
	Data      interface{}            `json:"data,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Message   string                 `json:"message,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Duration  time.Duration          `json:"duration"`
	Timestamp time.Time              `json:"timestamp"`
}

// ToolCall 工具调用（保留兼容性）
type ToolCall struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Parameters map[string]interface{} `json:"parameters"`
	Timestamp  time.Time              `json:"timestamp"`
}

// ToolConfig 工具配置
type ToolConfig struct {
	Name        string                 `yaml:"name" json:"name"`
	Type        string                 `yaml:"type" json:"type"`
	Description string                 `yaml:"description,omitempty" json:"description,omitempty"`
	Enabled     bool                   `yaml:"enabled" json:"enabled"`
	Parameters  map[string]interface{} `yaml:"parameters,omitempty" json:"parameters,omitempty"`
	Timeout     time.Duration          `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	MaxRetries  int                    `yaml:"max_retries,omitempty" json:"max_retries,omitempty"`
}

// Validate 验证工具配置
func (tc *ToolConfig) Validate() error {
	if tc.Name == "" {
		return &ValidationError{Field: "name", Message: "tool name cannot be empty"}
	}
	if tc.Type == "" {
		return &ValidationError{Field: "type", Message: "tool type cannot be empty"}
	}
	if tc.Timeout <= 0 {
		tc.Timeout = 30 * time.Second
	}
	if tc.MaxRetries < 0 {
		tc.MaxRetries = 3
	}
	return nil
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}

// ToolExecutionContext 工具执行上下文
type ToolExecutionContext struct {
	AgentID   string                 `json:"agent_id"`
	SessionID string                 `json:"session_id"`
	CallID    string                 `json:"call_id"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Timeout   time.Duration          `json:"timeout"`
}

// ToolFactory 工具工厂接口
type ToolFactory interface {
	// CreateTool 创建工具实例
	CreateTool(config *ToolConfig) (Tool, error)

	// SupportedTypes 返回支持的工具类型
	SupportedTypes() []string
}
