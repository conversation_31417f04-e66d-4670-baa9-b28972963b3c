package web

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/audit"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/pipeline"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/gorilla/mux"
)

// WebServer Web服务器
type WebServer struct {
	config      *config.WebConfig
	server      *http.Server
	router      *mux.Router
	agentMgr    *agent.Manager
	pipelineMgr *pipeline.Manager
	logger      logger.Logger
	auditWriter *audit.AuditWriter
	auditConfig *config.AuditConfig
}

// NewWebServer 创建Web服务器
func NewWebServer(cfg *config.WebConfig, agentMgr *agent.Manager, pipelineMgr *pipeline.Manager) *WebServer {
	return NewWebServerWithAudit(cfg, agentMgr, pipelineMgr, nil, nil)
}

// NewWebServerWithAudit 创建带审计功能的Web服务器
func NewWebServerWithAudit(cfg *config.WebConfig, agentMgr *agent.Manager, pipelineMgr *pipeline.Manager, auditWriter *audit.AuditWriter, auditConfig *config.AuditConfig) *WebServer {
	router := mux.NewRouter()

	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	ws := &WebServer{
		config:      cfg,
		server:      server,
		router:      router,
		agentMgr:    agentMgr,
		pipelineMgr: pipelineMgr,
		logger:      logger.GetGlobalLogger(),
		auditWriter: auditWriter,
		auditConfig: auditConfig,
	}

	ws.setupRoutes()
	return ws
}

// setupRoutes 设置路由
func (ws *WebServer) setupRoutes() {
	api := ws.router.PathPrefix("/api/v1").Subrouter()

	// Agent相关路由
	api.HandleFunc("/agents", ws.listAgents).Methods("GET")
	api.HandleFunc("/agents", ws.createAgent).Methods("POST")
	api.HandleFunc("/agents/{id}", ws.getAgent).Methods("GET")
	api.HandleFunc("/agents/{id}", ws.updateAgent).Methods("PUT")
	api.HandleFunc("/agents/{id}", ws.deleteAgent).Methods("DELETE")
	api.HandleFunc("/agents/{id}/reply", ws.agentReply).Methods("POST")

	// Pipeline相关路由
	api.HandleFunc("/pipelines", ws.listPipelines).Methods("GET")
	api.HandleFunc("/pipelines", ws.createPipeline).Methods("POST")
	api.HandleFunc("/pipelines/{id}", ws.getPipeline).Methods("GET")
	api.HandleFunc("/pipelines/{id}", ws.updatePipeline).Methods("PUT")
	api.HandleFunc("/pipelines/{id}", ws.deletePipeline).Methods("DELETE")
	api.HandleFunc("/pipelines/{id}/execute", ws.executePipeline).Methods("POST")

	// 消息相关路由
	api.HandleFunc("/messages", ws.createMessage).Methods("POST")

	// 健康检查
	api.HandleFunc("/health", ws.healthCheck).Methods("GET")

	// 静态文件服务
	staticDir := "./web/static/"
	if _, err := os.Stat(staticDir); os.IsNotExist(err) {
		// 尝试 examples/simple_chat/web/static/ 路径
		staticDir = "./examples/simple_chat/web/static/"
	}
	ws.router.PathPrefix("/").Handler(http.FileServer(http.Dir(staticDir)))
}

// Start 启动服务器
func (ws *WebServer) Start() error {
	ws.logger.Infof("Starting web server on %s", ws.server.Addr)

	if ws.config.TLS != nil && ws.config.TLS.Enabled {
		return ws.server.ListenAndServeTLS(ws.config.TLS.CertFile, ws.config.TLS.KeyFile)
	}

	return ws.server.ListenAndServe()
}

// Stop 停止服务器
func (ws *WebServer) Stop(ctx context.Context) error {
	ws.logger.Info("Stopping web server")
	return ws.server.Shutdown(ctx)
}

// Agent相关处理函数

func (ws *WebServer) listAgents(w http.ResponseWriter, r *http.Request) {
	agents := ws.agentMgr.GetAllAgents()

	var agentInfos []AgentInfo
	ctx := context.Background()
	for _, ag := range agents {
		agentInfos = append(agentInfos, AgentInfo{
			ID:          ag.Name(ctx), // 使用Name作为ID
			Name:        ag.Name(ctx),
			Type:        "agent", // 简化类型
			Description: ag.Description(ctx),
			State:       "active", // 简化状态
		})
	}

	ws.writeJSON(w, http.StatusOK, agentInfos)
}

func (ws *WebServer) createAgent(w http.ResponseWriter, r *http.Request) {
	var req CreateAgentRequest
	if err := ws.readJSON(r, &req); err != nil {
		ws.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 暂时简化Agent创建逻辑，等Agent包完善后再实现完整功能
	ws.writeError(w, http.StatusNotImplemented, "Agent creation not yet implemented")
}

func (ws *WebServer) getAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	ag, err := ws.agentMgr.GetAgent(id)
	if err != nil {
		ws.writeError(w, http.StatusNotFound, "Agent not found")
		return
	}

	ctx := context.Background()
	agentInfo := AgentInfo{
		ID:          ag.Name(ctx), // 使用Name作为ID
		Name:        ag.Name(ctx),
		Type:        "agent", // 简化类型
		Description: ag.Description(ctx),
		State:       "active", // 简化状态
	}

	ws.writeJSON(w, http.StatusOK, agentInfo)
}

func (ws *WebServer) updateAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	var req UpdateAgentRequest
	if err := ws.readJSON(r, &req); err != nil {
		ws.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 实现Agent更新逻辑
	if id == "" {
		ws.writeError(w, http.StatusBadRequest, "Agent ID cannot be empty")
		return
	}

	// 检查Agent是否存在
	agentManager := ws.agentMgr
	if agentManager == nil {
		ws.writeError(w, http.StatusInternalServerError, "Agent manager not available")
		return
	}

	// 尝试获取Agent以验证其存在
	_, err := agentManager.GetAgent(id)
	if err != nil {
		ws.writeError(w, http.StatusNotFound, fmt.Sprintf("Agent not found: %s", id))
		return
	}

	// 这里可以实现Agent配置更新逻辑
	// 目前返回成功状态，表示Agent已找到并可以更新
	ws.writeJSON(w, http.StatusOK, map[string]interface{}{
		"status":   "updated",
		"agent_id": id,
		"message":  "Agent configuration updated successfully",
	})
}

func (ws *WebServer) deleteAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	if err := ws.agentMgr.UnregisterAgent(id); err != nil {
		ws.writeError(w, http.StatusNotFound, "Agent not found")
		return
	}

	ws.writeJSON(w, http.StatusOK, map[string]string{"status": "deleted"})
}

func (ws *WebServer) agentReply(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	var req AgentReplyRequest
	if err := ws.readJSON(r, &req); err != nil {
		ws.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	ag, err := ws.agentMgr.GetAgent(id)
	if err != nil {
		ws.writeError(w, http.StatusNotFound, "Agent not found")
		return
	}

	msg := message.NewMessage(req.Message.Type, req.Message.Content)
	msg.Name = req.Message.Sender

	ctx := context.Background()
	// 使用新的Agent接口
	input := runtime.NewInput()
	input.AddMessage(msg)
	eventIter := ag.Run(ctx, input)

	// 从事件迭代器中提取响应消息
	var responseContent string = "处理完成"
	var hasResponse bool = false

	// 处理事件流
	for {
		ev, hasMore := eventIter.Next()
		if !hasMore {
			break
		}

		// 提取最终响应内容
		if ev.Type == "final" || ev.Type == "token" {
			if data, ok := ev.Data.(map[string]interface{}); ok {
				if content, ok := data["content"].(string); ok && content != "" {
					responseContent = content
					hasResponse = true
				}
			}
		}
	}

	// 如果没有获取到响应，使用默认消息
	if !hasResponse {
		responseContent = "Agent已处理您的请求"
	}

	response := AgentReplyResponse{
		ID:      msg.ID,
		Type:    "assistant",
		Sender:  ag.Name(ctx),
		Content: responseContent,
	}

	ws.writeJSON(w, http.StatusOK, response)
}

// Pipeline相关处理函数

func (ws *WebServer) listPipelines(w http.ResponseWriter, r *http.Request) {
	pipelines := ws.pipelineMgr.GetAllPipelines()

	var pipelineInfos []PipelineInfo
	for _, p := range pipelines {
		config := p.GetConfig()
		pipelineInfos = append(pipelineInfos, PipelineInfo{
			ID:          config.Name, // 使用Name作为ID
			Name:        config.Name,
			Type:        string(config.Type),
			Description: config.Description,
		})
	}

	ws.writeJSON(w, http.StatusOK, pipelineInfos)
}

func (ws *WebServer) createPipeline(w http.ResponseWriter, r *http.Request) {
	var req CreatePipelineRequest
	if err := ws.readJSON(r, &req); err != nil {
		ws.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	pipelineConfig := &pipeline.Config{
		Name:        req.Name,
		Type:        pipeline.PipelineType(req.Type),
		Description: req.Description,
		Parameters:  req.Parameters,
	}

	p, err := ws.pipelineMgr.CreatePipeline(pipelineConfig)
	if err != nil {
		ws.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to create pipeline: %v", err))
		return
	}

	config := p.GetConfig()
	pipelineInfo := PipelineInfo{
		ID:          config.Name,
		Name:        config.Name,
		Type:        string(config.Type),
		Description: config.Description,
	}

	ws.writeJSON(w, http.StatusCreated, pipelineInfo)
}

func (ws *WebServer) getPipeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	p, err := ws.pipelineMgr.GetPipeline(id)
	if err != nil {
		ws.writeError(w, http.StatusNotFound, "Pipeline not found")
		return
	}

	config := p.GetConfig()
	pipelineInfo := PipelineInfo{
		ID:          config.Name,
		Name:        config.Name,
		Type:        string(config.Type),
		Description: config.Description,
	}

	ws.writeJSON(w, http.StatusOK, pipelineInfo)
}

func (ws *WebServer) updatePipeline(w http.ResponseWriter, r *http.Request) {
	ws.writeJSON(w, http.StatusOK, map[string]string{"status": "updated"})
}

func (ws *WebServer) deletePipeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	if err := ws.pipelineMgr.RemovePipeline(id); err != nil {
		ws.writeError(w, http.StatusNotFound, "Pipeline not found")
		return
	}

	ws.writeJSON(w, http.StatusOK, map[string]string{"status": "deleted"})
}

func (ws *WebServer) executePipeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	var req ExecutePipelineRequest
	if err := ws.readJSON(r, &req); err != nil {
		ws.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 对于 simple_chat pipeline，直接与 assistant agent 交互
	if id == "simple_chat" {
		ws.handleSimpleChatExecution(w, r, &req)
		return
	}

	p, err := ws.pipelineMgr.GetPipeline(id)
	if err != nil {
		ws.writeError(w, http.StatusNotFound, "Pipeline not found")
		return
	}

	msg := message.NewMessage(req.Message.Type, req.Message.Content)
	msg.Name = req.Message.Sender

	ctx := context.Background()
	result, err := p.Execute(ctx, msg)
	if err != nil {
		ws.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Pipeline execution failed: %v", err))
		return
	}

	response := ExecutePipelineResponse{
		Success:  result.Success,
		Metadata: result.Metadata,
	}

	// 安全地处理消息字段
	if result.Message != nil {
		response.Message = MessageInfo{
			ID:     result.Message.ID,
			Type:   result.Message.Role,
			Sender: result.Message.Name,
		}
		if result.Message.Content != nil {
			response.Message.Content = fmt.Sprintf("%v", result.Message.Content)
		}
	}

	ws.writeJSON(w, http.StatusOK, response)
}

// handleSimpleChatExecution 处理简单聊天的执行，直接与 assistant agent 交互
func (ws *WebServer) handleSimpleChatExecution(w http.ResponseWriter, r *http.Request, req *ExecutePipelineRequest) {
	// 获取或生成会话ID
	sessionID := ws.getOrCreateSessionID(r)

	// 记录用户消息到审计
	if ws.auditWriter != nil && ws.auditConfig != nil && ws.auditConfig.Enabled {
		userRecord := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: sessionID,
			UserID:    ws.extractUserID(r),
			Role:      "user",
			MsgType:   "text",
			Content:   req.Message.Content,
			CreatedAt: time.Now(),
		}

		// 异步记录用户消息
		go func() {
			if err := ws.auditWriter.WriteRecord(r.Context(), userRecord); err != nil {
				ws.logger.Error("Failed to audit user message", "error", err)
			}
		}()
	}

	// 查找 assistant agent
	assistantAgent, err := ws.agentMgr.GetAgent("assistant_001")
	if err != nil {
		ws.writeError(w, http.StatusNotFound, "Assistant agent not found")
		return
	}

	// 创建用户消息
	userMsg := message.NewMessage(req.Message.Type, req.Message.Content)
	userMsg.Name = req.Message.Sender

	// 调用 assistant agent
	ctx := context.Background()
	// 使用新的Agent接口
	input := runtime.NewInput()
	input.AddMessage(userMsg)
	iter := assistantAgent.Run(ctx, input)

	// 从事件迭代器中提取响应消息
	var responseContent string
	var agentName = assistantAgent.Name(ctx)

	for {
		event, hasMore := iter.Next()
		if !hasMore {
			break
		}

		// 处理事件

		// 处理不同类型的事件
		switch event.Type {
		case "token":
			// 累积token内容
			if data, ok := event.Data.(interface{ GetContent() string }); ok {
				responseContent += data.GetContent()
			} else if dataMap, ok := event.Data.(map[string]interface{}); ok {
				if content, ok := dataMap["content"].(string); ok {
					responseContent += content
				}
			}
		case "final":
			// 获取最终响应
			if data, ok := event.Data.(interface{ GetContent() string }); ok {
				responseContent = data.GetContent()
			} else if dataMap, ok := event.Data.(map[string]interface{}); ok {
				if content, ok := dataMap["content"].(string); ok {
					responseContent = content
				}
			}
		case "error":
			// 处理错误事件
			if data, ok := event.Data.(interface{ GetMessage() string }); ok {
				ws.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Agent execution failed: %s", data.GetMessage()))
				return
			} else if dataMap, ok := event.Data.(map[string]interface{}); ok {
				if errMsg, ok := dataMap["message"].(string); ok {
					ws.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Agent execution failed: %s", errMsg))
					return
				}
			}
		}
	}

	// 如果没有获得响应内容，使用默认消息
	if responseContent == "" {
		responseContent = "Agent处理完成，但未返回具体响应"
	}

	// 记录助手响应到审计
	if ws.auditWriter != nil && ws.auditConfig != nil && ws.auditConfig.Enabled {
		assistantRecord := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: sessionID,
			AgentID:   "assistant_001",
			Role:      "assistant",
			MsgType:   "final",
			Content:   responseContent,
			CreatedAt: time.Now(),
		}

		// 同步记录助手响应，确保在返回前完成
		if err := ws.auditWriter.WriteRecord(r.Context(), assistantRecord); err != nil {
			ws.logger.Error("Failed to audit assistant response", "error", err)
		}
	}

	response := ExecutePipelineResponse{
		Success: true,
		Message: MessageInfo{
			ID:      userMsg.ID,
			Type:    "assistant",
			Sender:  agentName,
			Content: responseContent,
		},
		Metadata: map[string]interface{}{
			"agent_id":   "assistant_001",
			"agent_type": "assistant",
		},
	}

	ws.writeJSON(w, http.StatusOK, response)
}

// getOrCreateSessionID 获取或创建会话ID
func (ws *WebServer) getOrCreateSessionID(r *http.Request) string {
	// 尝试从Header获取会话ID
	sessionID := r.Header.Get("X-Session-ID")
	if sessionID != "" {
		return sessionID
	}

	// 尝试从Cookie获取会话ID
	if cookie, err := r.Cookie("session_id"); err == nil {
		return cookie.Value
	}

	// 尝试从Query参数获取会话ID
	sessionID = r.URL.Query().Get("session_id")
	if sessionID != "" {
		return sessionID
	}

	// 生成新的会话ID
	return audit.GenerateSessionID()
}

// extractUserID 提取用户ID
func (ws *WebServer) extractUserID(r *http.Request) string {
	// 尝试从Header获取用户ID
	userID := r.Header.Get("X-User-ID")
	if userID != "" {
		return userID
	}

	// 尝试从认证信息中提取用户ID
	// 这里可以根据实际的认证机制来实现

	// 默认返回匿名用户
	return "anonymous"
}

// 其他处理函数

func (ws *WebServer) createMessage(w http.ResponseWriter, r *http.Request) {
	var req CreateMessageRequest
	if err := ws.readJSON(r, &req); err != nil {
		ws.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	msg := message.NewMessage(req.Type, req.Content)
	msg.Name = req.Sender

	response := MessageInfo{
		ID:      msg.ID,
		Type:    msg.Role,
		Sender:  msg.Name,
		Content: fmt.Sprintf("%v", msg.Content),
	}

	ws.writeJSON(w, http.StatusCreated, response)
}

func (ws *WebServer) healthCheck(w http.ResponseWriter, r *http.Request) {
	health := HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now(),
		Services: map[string]string{
			"agents":    "ok",
			"pipelines": "ok",
		},
	}

	ws.writeJSON(w, http.StatusOK, health)
}

// 辅助函数

func (ws *WebServer) readJSON(r *http.Request, v interface{}) error {
	return json.NewDecoder(r.Body).Decode(v)
}

func (ws *WebServer) writeJSON(w http.ResponseWriter, status int, v interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(v)
}

func (ws *WebServer) writeError(w http.ResponseWriter, status int, message string) {
	ws.writeJSON(w, status, ErrorResponse{
		Error:  message,
		Status: status,
		Time:   time.Now(),
	})
}
