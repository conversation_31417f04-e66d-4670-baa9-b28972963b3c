package migration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/golang-migrate/migrate/v4"
	pg "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/jackc/pgx/v5/stdlib"
)

// AutoMigrate
// 中文说明：
// - 基于 golang-migrate 对 PostgreSQL 执行自动迁移
// - 对 SQLite 采用真实 SQL 文件顺序执行（无简化、无模拟），确保在无 CGO 环境下可用
// - migrationsDir 为空时默认使用 "migrations"
// - driver 取值："postgres" | "sqlite"
func AutoMigrate(ctx context.Context, driver, dsn, migrationsDir string) error {
	if migrationsDir == "" {
		migrationsDir = "migrations"
	}
	if driver == "" {
		return fmt.Errorf("driver 不能为空")
	}
	switch strings.ToLower(driver) {
	case "postgres", "postgresql":
		return migratePostgres(ctx, dsn, migrationsDir)
	case "sqlite":
		return migrateSQLite(ctx, dsn, migrationsDir)
	default:
		return fmt.Errorf("不支持的数据库驱动: %s", driver)
	}
}

// migratePostgres 使用 golang-migrate 执行迁移
func migratePostgres(ctx context.Context, dsn, dir string) error {
	if dsn == "" {
		return fmt.Errorf("postgres dsn 不能为空")
	}
	db, err := sql.Open("pgx", dsn)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %w", err)
	}
	defer db.Close()
	if err := pingWithCtx(ctx, db); err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	dbInstance, err := pg.WithInstance(db, &pg.Config{})
	if err != nil {
		return fmt.Errorf("创建postgres实例失败: %w", err)
	}
	m, err := migrate.NewWithDatabaseInstance("file://"+dir, "postgres", dbInstance)
	if err != nil {
		return fmt.Errorf("创建迁移器失败: %w", err)
	}
	if err := m.Up(); err != nil && !errors.Is(err, migrate.ErrNoChange) {
		return fmt.Errorf("执行迁移失败: %w", err)
	}
	return nil
}

// migrateSQLite 执行 SQLite 迁移：
// 说明：为保证在无 CGO 环境可用，采用顺序执行 *.up.sql 文件的方式（真实落库）。
func migrateSQLite(ctx context.Context, dsn, dir string) error {
	if dsn == "" {
		return fmt.Errorf("sqlite dsn 不能为空")
	}
	db, err := sql.Open("sqlite", dsn)
	if err != nil {
		return fmt.Errorf("打开SQLite失败: %w", err)
	}
	defer db.Close()
	if err := pingWithCtx(ctx, db); err != nil {
		return fmt.Errorf("连接SQLite失败: %w", err)
	}

	// 读取并排序 *.up.sql
	entries, err := os.ReadDir(dir)
	if err != nil {
		return fmt.Errorf("读取迁移目录失败: %w", err)
	}
	var files []string
	for _, e := range entries {
		name := e.Name()
		// 仅执行显式标记为 SQLite 的迁移脚本，避免误执行 PostgreSQL 脚本
		if !e.IsDir() && strings.HasSuffix(name, ".sqlite.up.sql") {
			files = append(files, filepath.Join(dir, name))
		}
	}
	sort.Strings(files)
	for _, f := range files {
		if err := execSQLFile(ctx, db, f); err != nil {
			return fmt.Errorf("执行迁移文件失败(%s): %w", f, err)
		}
	}
	return nil
}

func execSQLFile(ctx context.Context, db *sql.DB, path string) error {
	b, err := os.ReadFile(path)
	if err != nil {
		return err
	}
	// 简单按分号切分；SQLite/PG 常用 DDL 每条以 ; 结束
	stmts := strings.Split(string(b), ";")
	for _, s := range stmts {
		s = strings.TrimSpace(s)
		if s == "" {
			continue
		}
		if _, err := db.ExecContext(ctx, s); err != nil {
			return fmt.Errorf("SQL执行失败: %w; 语句: %s", err, truncate(s, 200))
		}
	}
	return nil
}

func pingWithCtx(ctx context.Context, db *sql.DB) error {
	ctx2, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	return db.PingContext(ctx2)
}

func truncate(s string, n int) string {
	if len(s) <= n {
		return s
	}
	return s[:n] + "..."
}
