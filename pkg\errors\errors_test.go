package errors

import (
	"errors"
	"testing"
	"time"
)

func TestAgentScopeError(t *testing.T) {
	// Test basic error creation
	err := &AgentScopeError{
		Type:      ErrorTypeValidation,
		Code:      "VALIDATION_001",
		Message:   "test error",
		Details:   "field validation failed",
		Timestamp: time.Now(),
	}

	expectedMsg := "[validation:VALIDATION_001] test error: field validation failed"
	if err.Error() != expectedMsg {
		t.<PERSON><PERSON>("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}

	if err.Type != ErrorTypeValidation {
		t.<PERSON><PERSON><PERSON>("Expected error type %s, got %s", ErrorTypeValidation, err.Type)
	}

	if err.Code != "VALIDATION_001" {
		t.<PERSON><PERSON>("Expected error code 'VALIDATION_001', got '%s'", err.Code)
	}
}

func TestAgentScopeErrorWrapping(t *testing.T) {
	originalErr := errors.New("original error")
	wrappedErr := &AgentScopeError{
		Type:    ErrorTypeInternal,
		Code:    "INTERNAL_001",
		Message: "wrapped error",
		Cause:   originalErr,
	}

	if wrappedErr.Unwrap() != originalErr {
		t.Error("Expected wrapped error to unwrap to original error")
	}

	if !errors.Is(wrappedErr, originalErr) {
		t.Error("Expected wrapped error to be identified as original error")
	}
}

func TestAgentScopeErrorContext(t *testing.T) {
	err := &AgentScopeError{
		Type:    ErrorTypeValidation,
		Code:    "VALIDATION_001",
		Message: "test error",
	}

	// Test adding context
	err.WithContext("field", "username")
	err.WithContext("value", "invalid@user")

	if err.Context["field"] != "username" {
		t.Errorf("Expected context field 'username', got '%v'", err.Context["field"])
	}
	if err.Context["value"] != "invalid@user" {
		t.Errorf("Expected context value 'invalid@user', got '%v'", err.Context["value"])
	}
}

func TestAgentScopeErrorType(t *testing.T) {
	err := &AgentScopeError{
		Type: ErrorTypeNetwork,
		Code: "NETWORK_001",
	}

	if !err.IsType(ErrorTypeNetwork) {
		t.Error("Expected error to be of type network")
	}

	if err.IsType(ErrorTypeValidation) {
		t.Error("Expected error to not be of type validation")
	}
}

func TestAgentScopeErrorCode(t *testing.T) {
	err := &AgentScopeError{
		Type: ErrorTypeValidation,
		Code: "VALIDATION_001",
	}

	if !err.IsCode("VALIDATION_001") {
		t.Error("Expected error to have code VALIDATION_001")
	}

	if err.IsCode("VALIDATION_002") {
		t.Error("Expected error to not have code VALIDATION_002")
	}
}

func TestErrorTypes(t *testing.T) {
	expectedTypes := []ErrorType{
		ErrorTypeValidation,
		ErrorTypeNetwork,
		ErrorTypeTimeout,
		ErrorTypeAuth,
		ErrorTypeInternal,
		ErrorTypeLLM,
		ErrorTypeTool,
		ErrorTypeAgent,
		ErrorTypePipeline,
	}

	// Test that all error types are defined and non-empty
	for _, errorType := range expectedTypes {
		if string(errorType) == "" {
			t.Errorf("Error type should not be empty: %v", errorType)
		}
	}

	// Test error type uniqueness
	typeSet := make(map[ErrorType]bool)
	for _, errorType := range expectedTypes {
		if typeSet[errorType] {
			t.Errorf("Duplicate error type found: %s", errorType)
		}
		typeSet[errorType] = true
	}
}

// TestConstructors 测试构造函数
func TestConstructors(t *testing.T) {
	// 测试 New 函数
	err1 := New(ErrorTypeValidation, "VAL_001", "validation failed")
	if err1.Type != ErrorTypeValidation {
		t.Errorf("Expected type %s, got %s", ErrorTypeValidation, err1.Type)
	}
	if err1.Code != "VAL_001" {
		t.Errorf("Expected code VAL_001, got %s", err1.Code)
	}
	if err1.Message != "validation failed" {
		t.Errorf("Expected message 'validation failed', got %s", err1.Message)
	}

	// 测试 Newf 函数
	err2 := Newf(ErrorTypeNetwork, "NET_001", "connection failed to %s:%d", "localhost", 8080)
	expectedMsg := "connection failed to localhost:8080"
	if err2.Message != expectedMsg {
		t.Errorf("Expected message '%s', got '%s'", expectedMsg, err2.Message)
	}

	// 测试 Wrap 函数
	originalErr := errors.New("original error")
	err3 := Wrap(originalErr, ErrorTypeInternal, "INT_001", "internal error")
	if err3.Cause != originalErr {
		t.Error("Expected wrapped error to contain original error")
	}
	if err3.Unwrap() != originalErr {
		t.Error("Expected wrapped error to unwrap to original error")
	}

	// 测试 Wrapf 函数
	err4 := Wrapf(originalErr, ErrorTypeTimeout, "TMO_001", "timeout after %d seconds", 30)
	expectedMsg2 := "timeout after 30 seconds"
	if err4.Message != expectedMsg2 {
		t.Errorf("Expected message '%s', got '%s'", expectedMsg2, err4.Message)
	}
}

// TestWithMethods 测试 With 方法
func TestWithMethods(t *testing.T) {
	err := New(ErrorTypeAgent, "AGT_001", "agent failed")

	// 手动设置 Details
	err.Details = "detailed error information"
	if err.Details != "detailed error information" {
		t.Errorf("Expected details 'detailed error information', got '%s'", err.Details)
	}

	// 测试 WithContext
	err = err.WithContext("agent_id", "agent_123")
	err = err.WithContext("task_id", 456)

	if err.Context["agent_id"] != "agent_123" {
		t.Errorf("Expected context agent_id 'agent_123', got %v", err.Context["agent_id"])
	}
	if err.Context["task_id"] != 456 {
		t.Errorf("Expected context task_id 456, got %v", err.Context["task_id"])
	}
}

// TestUtilityFunctions 测试工具函数
func TestUtilityFunctions(t *testing.T) {
	agentErr := New(ErrorTypeAgent, "AGT_001", "agent error")
	regularErr := errors.New("regular error")

	// 测试 IsAgentScopeError
	if !IsAgentScopeError(agentErr) {
		t.Error("Expected IsAgentScopeError to return true for AgentScopeError")
	}
	if IsAgentScopeError(regularErr) {
		t.Error("Expected IsAgentScopeError to return false for regular error")
	}

	// 测试 GetErrorType
	errType, ok := GetErrorType(agentErr)
	if !ok || errType != ErrorTypeAgent {
		t.Errorf("Expected error type %s, got %s", ErrorTypeAgent, errType)
	}
	_, ok = GetErrorType(regularErr)
	if ok {
		t.Error("Expected GetErrorType to return false for regular error")
	}

	// 测试 GetErrorCode
	errCode, ok := GetErrorCode(agentErr)
	if !ok || errCode != "AGT_001" {
		t.Errorf("Expected error code AGT_001, got %s", errCode)
	}
	_, ok = GetErrorCode(regularErr)
	if ok {
		t.Error("Expected GetErrorCode to return false for regular error")
	}

	// 测试 IsType
	if !IsType(agentErr, ErrorTypeAgent) {
		t.Error("Expected IsType to return true for matching type")
	}
	if IsType(agentErr, ErrorTypeNetwork) {
		t.Error("Expected IsType to return false for non-matching type")
	}

	// 测试 IsCode
	if !IsCode(agentErr, "AGT_001") {
		t.Error("Expected IsCode to return true for matching code")
	}
	if IsCode(agentErr, "NET_001") {
		t.Error("Expected IsCode to return false for non-matching code")
	}
}

// TestErrorCodes 测试错误代码常量
func TestErrorCodes(t *testing.T) {
	// 测试验证错误代码
	validationCodes := []string{
		CodeInvalidInput,
		CodeMissingRequired,
		CodeInvalidFormat,
		CodeOutOfRange,
		CodeInvalidType,
	}

	for _, code := range validationCodes {
		if code == "" {
			t.Error("Validation error code should not be empty")
		}
	}

	// 测试网络错误代码
	networkCodes := []string{
		CodeConnectionFailed,
		CodeRequestFailed,
		CodeResponseInvalid,
		CodeServiceUnavailable,
	}

	for _, code := range networkCodes {
		if code == "" {
			t.Error("Network error code should not be empty")
		}
	}

	// 测试超时错误代码
	timeoutCodes := []string{
		CodeRequestTimeout,
		CodeOperationTimeout,
		CodeContextCanceled,
	}

	for _, code := range timeoutCodes {
		if code == "" {
			t.Error("Timeout error code should not be empty")
		}
	}
}

// TestAsAgentScopeError 测试 AsAgentScopeError 函数
func TestAsAgentScopeError(t *testing.T) {
	agentErr := New(ErrorTypeAgent, "AGT_001", "agent error")
	regularErr := errors.New("regular error")

	// 测试 AgentScopeError
	asErr, ok := AsAgentScopeError(agentErr)
	if !ok {
		t.Error("Expected AsAgentScopeError to return true for AgentScopeError")
	}
	if asErr != agentErr {
		t.Error("Expected AsAgentScopeError to return the same error")
	}

	// 测试普通错误
	_, ok = AsAgentScopeError(regularErr)
	if ok {
		t.Error("Expected AsAgentScopeError to return false for regular error")
	}
}

// TestErrorMethods 测试错误方法
func TestErrorMethods(t *testing.T) {
	err := New(ErrorTypeAgent, "AGT_001", "agent error")

	// 测试 IsType
	if !err.IsType(ErrorTypeAgent) {
		t.Error("Expected IsType to return true for matching type")
	}
	if err.IsType(ErrorTypeNetwork) {
		t.Error("Expected IsType to return false for non-matching type")
	}

	// 测试 IsCode
	if !err.IsCode("AGT_001") {
		t.Error("Expected IsCode to return true for matching code")
	}
	if err.IsCode("NET_001") {
		t.Error("Expected IsCode to return false for non-matching code")
	}

	// 测试 WithCause
	originalErr := errors.New("original error")
	err = err.WithCause(originalErr)
	if err.Cause != originalErr {
		t.Error("Expected WithCause to set the cause error")
	}
	if err.Unwrap() != originalErr {
		t.Error("Expected Unwrap to return the cause error")
	}
}

// TestChainFunction 测试 Chain 函数
func TestChainFunction(t *testing.T) {
	// 测试空错误链
	result := Chain()
	if result != nil {
		t.Error("Expected Chain() to return nil for empty input")
	}

	// 测试单个错误
	err1 := New(ErrorTypeValidation, "VAL_001", "validation error")
	result = Chain(err1)
	if result != err1 {
		t.Error("Expected Chain with single error to return that error")
	}

	// 测试多个错误链
	err2 := New(ErrorTypeNetwork, "NET_001", "network error")
	err3 := errors.New("regular error")
	result = Chain(err1, err2, err3)

	if result == nil {
		t.Fatal("Expected Chain to return non-nil result")
	}

	// 验证链式错误包含原始错误
	asErr, ok := AsAgentScopeError(result)
	if !ok {
		t.Fatal("Expected chained error to be AgentScopeError")
	}

	if asErr.Type != ErrorTypeValidation {
		t.Errorf("Expected chained error type %s, got %s", ErrorTypeValidation, asErr.Type)
	}

	// 测试包含nil错误的链
	result = Chain(err1, nil, err2)
	if result == nil {
		t.Error("Expected Chain to handle nil errors gracefully")
	}
}

// TestIsNotFoundError 测试 IsNotFoundError 函数
func TestIsNotFoundError(t *testing.T) {
	// 测试 not_found 错误
	notFoundErr := New(ErrorTypeValidation, "not_found", "resource not found")
	if !IsNotFoundError(notFoundErr) {
		t.Error("Expected IsNotFoundError to return true for not_found code")
	}

	// 测试 entry_not_found 错误
	entryNotFoundErr := New(ErrorTypeInternal, "entry_not_found", "entry not found")
	if !IsNotFoundError(entryNotFoundErr) {
		t.Error("Expected IsNotFoundError to return true for entry_not_found code")
	}

	// 测试 memory_not_found 错误
	memoryNotFoundErr := New(ErrorTypeAgent, "memory_not_found", "memory not found")
	if !IsNotFoundError(memoryNotFoundErr) {
		t.Error("Expected IsNotFoundError to return true for memory_not_found code")
	}

	// 测试其他错误
	otherErr := New(ErrorTypeNetwork, "NET_001", "network error")
	if IsNotFoundError(otherErr) {
		t.Error("Expected IsNotFoundError to return false for non-not-found error")
	}

	// 测试普通错误
	regularErr := errors.New("regular error")
	if IsNotFoundError(regularErr) {
		t.Error("Expected IsNotFoundError to return false for regular error")
	}
}

// TestGetErrorTypeAndCode 测试 GetErrorType 和 GetErrorCode 函数
func TestGetErrorTypeAndCode(t *testing.T) {
	agentErr := New(ErrorTypeAgent, "AGT_001", "agent error")
	regularErr := errors.New("regular error")

	// 测试 GetErrorType
	errType, ok := GetErrorType(agentErr)
	if !ok {
		t.Error("Expected GetErrorType to return true for AgentScopeError")
	}
	if errType != ErrorTypeAgent {
		t.Errorf("Expected error type %s, got %s", ErrorTypeAgent, errType)
	}

	_, ok = GetErrorType(regularErr)
	if ok {
		t.Error("Expected GetErrorType to return false for regular error")
	}

	// 测试 GetErrorCode
	errCode, ok := GetErrorCode(agentErr)
	if !ok {
		t.Error("Expected GetErrorCode to return true for AgentScopeError")
	}
	if errCode != "AGT_001" {
		t.Errorf("Expected error code AGT_001, got %s", errCode)
	}

	_, ok = GetErrorCode(regularErr)
	if ok {
		t.Error("Expected GetErrorCode to return false for regular error")
	}
}
