---
type: "always_apply"
---

# AgentScope-Golang 开发规范和纪律要求

## 核心开发纪律

### 1. 禁止简化实现
- **严格禁止**使用简化实现、模拟实现和硬编码实现
- 所有功能必须使用真实的API和服务
- LLM集成必须使用真实的API调用，不允许返回固定字符串或模拟响应
- 数据库操作必须连接真实的数据库，不允许使用内存模拟

### 2. API Key 和认证
- 使用环境变量 `DEEPSEEK_API_KEY` 进行LLM API认证
- 所有API调用都必须使用真实的认证凭据
- 不允许跳过认证或使用假的API Key

### 3. 错误处理要求
- 必须实现完整的错误处理机制
- 不允许忽略错误或使用空的错误处理
- 所有外部API调用都必须有适当的超时和重试机制


### 4. 文档即契约
- 文档与实现必须严格一致；出现偏差时，以经过评审的文档为准，代码需尽快对齐
- 对外接口（API、CLI、配置项）变更必须同时更新文档与示例，作为合并前置条件
- 合并请求需包含文档更新记录（变更说明、示例更新、兼容性说明若适用）
- 所有文档与代码注释必须使用中文，术语需在术语表中统一（若存在）

## 开发和调试规范

### 1. 程序运行方式
- **开发阶段不需要编译**，统一使用 `go run` 命令运行程序
- 示例运行命令：`go run cmd/main.go` 或 `go run examples/simple_chat/main.go`
- 调试时使用 `go run` 而不是先编译再运行
- 测试时使用 `go test` 命令

### 2. Web功能测试规范
当涉及到Web界面或API测试时，必须遵循以下流程：

#### 开发完成后的验证流程
1. **完成开发或问题修复**后，首先使用MCP Playwright工具进行自动化测试
2. **可视化验证**：确认界面显示正常，布局正确
3. **功能验证**：确认所有UI功能正常工作
4. **错误检查**：确认没有JavaScript报错或控制台错误
5. **只有在通过所有自动化验证后**，才交付给用户进行人工验收

#### 具体测试要求
- 使用MCP Playwright进行页面截图和功能测试
- 检查页面加载是否正常
- 验证所有按钮、表单、链接等交互元素
- 确认API端点响应正确
- 检查浏览器控制台无错误信息

### 3. 测试覆盖要求
- 单元测试覆盖率必须达到80%以上
- 集成测试必须覆盖所有主要功能模块
- 所有API端点都必须有对应的测试用例
- 使用真实的外部服务进行集成测试

## 代码质量标准

### 1. Go语言规范
- 严格遵循Go语言官方代码规范
- 使用 `go fmt` 格式化代码
- 使用 `go vet` 进行静态检查
- 使用 `golint` 进行代码风格检查

### 2. 文档要求
- 所有公开的函数、结构体、接口都必须有完整的注释
- 注释必须使用中文编写
- 复杂的业务逻辑必须有详细的实现说明
- 配置参数必须有清晰的说明和示例

### 3. 错误信息规范
- 错误信息必须清晰、具体、可操作
- 包含足够的上下文信息用于调试
- 使用结构化的错误类型
- 错误信息使用中文编写

## 性能和安全要求

### 1. 并发安全
- 所有共享数据结构必须是并发安全的
- 正确使用mutex、channel等同步原语
- 避免数据竞争和死锁

### 2. 资源管理
- 及时释放资源（文件句柄、网络连接等）
- 使用context进行超时控制
- 避免内存泄漏

### 3. 安全实践
- 输入验证和参数校验
- SQL注入防护
- XSS防护（Web界面）
- 敏感信息不能硬编码

## 配置和部署

### 1. 配置管理
- 使用YAML格式的配置文件
- 支持环境变量覆盖配置
- 配置项必须有合理的默认值
- 敏感配置通过环境变量传递

### 2. 日志规范
- 使用结构化日志格式
- 日志级别使用标准的DEBUG、INFO、WARN、ERROR
- 重要操作必须记录日志
- 不在日志中输出敏感信息

## 交付标准

### 1. 功能完整性
- 所有需求文档中的功能都必须实现
- 功能实现必须符合设计文档的规范
- 边界条件和异常情况都必须处理

### 2. 测试完备性
- 通过所有单元测试
- 通过所有集成测试
- Web功能通过MCP Playwright自动化测试
- 性能测试达到预期指标

### 3. 文档完整性
- API文档完整准确
- 使用示例清晰可运行
- 部署文档详细可操作
- 故障排查指南完善

## 开发工具和环境

### 1. 必需的开发工具
- Go 1.21+ 版本
- Git版本控制
- MCP Playwright（用于Web测试）
- 代码编辑器（推荐VS Code with Go扩展）

### 2. 环境变量设置
```bash
# LLM API认证
export DEEPSEEK_API_KEY="your_api_key_here"

# 其他可能需要的环境变量
export LOG_LEVEL="INFO"
export CONFIG_PATH="./config.yaml"
```

### 3. 项目结构规范
```
hzAgent/
├── cmd/                 # 主程序入口
├── pkg/                 # 公开的包
├── internal/            # 内部包
├── examples/            # 示例程序
├── configs/             # 配置文件
├── docs/                # 文档
├── tests/               # 测试文件
└── scripts/             # 构建和部署脚本
```

## 违规处理

违反以上任何规范和纪律要求的代码将被拒绝，必须重新开发直到符合标准。特别是使用简化实现、模拟实现或硬编码实现的代码将被严格拒绝。