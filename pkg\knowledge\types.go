package knowledge

import (
	"context"
	"time"
)

// DocumentType 定义文档类型
type DocumentType string

const (
	DocumentTypeText     DocumentType = "text"
	DocumentTypeMarkdown DocumentType = "markdown"
	DocumentTypeHTML     DocumentType = "html"
	DocumentTypeJSON     DocumentType = "json"
	DocumentTypePDF      DocumentType = "pdf"
	DocumentTypeCode     DocumentType = "code"
)

// Document 表示知识库中的文档
type Document struct {
	ID        string                 `json:"id"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	Type      DocumentType           `json:"type"`
	Source    string                 `json:"source,omitempty"`    // 文档来源
	Language  string                 `json:"language,omitempty"`  // 文档语言
	Tags      []string               `json:"tags,omitempty"`      // 标签
	Metadata  map[string]interface{} `json:"metadata,omitempty"`  // 元数据
	Chunks    []*DocumentChunk       `json:"chunks,omitempty"`    // 文档分块
	Entities  []*Entity              `json:"entities,omitempty"`  // 实体
	Relations []*Relation            `json:"relations,omitempty"` // 关系
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	Version   int                    `json:"version"`
	Hash      string                 `json:"hash"` // 内容哈希
}

// DocumentChunk 表示文档分块
type DocumentChunk struct {
	ID         string                 `json:"id"`
	DocumentID string                 `json:"document_id"`
	Content    string                 `json:"content"`
	StartPos   int                    `json:"start_pos"`   // 在原文档中的起始位置
	EndPos     int                    `json:"end_pos"`     // 在原文档中的结束位置
	ChunkIndex int                    `json:"chunk_index"` // 分块索引
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	Vector     []float64              `json:"vector,omitempty"` // 向量表示
	CreatedAt  time.Time              `json:"created_at"`
}

// Entity 表示实体
type Entity struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"` // 实体类型，如 "person", "organization", "location"
	Description string                 `json:"description,omitempty"`
	Properties  map[string]interface{} `json:"properties,omitempty"`
	Aliases     []string               `json:"aliases,omitempty"`      // 别名
	DocumentIDs []string               `json:"document_ids,omitempty"` // 关联的文档ID
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// Relation 表示实体间的关系
type Relation struct {
	ID          string                 `json:"id"`
	FromEntity  string                 `json:"from_entity"` // 源实体ID
	ToEntity    string                 `json:"to_entity"`   // 目标实体ID
	Type        string                 `json:"type"`        // 关系类型
	Description string                 `json:"description,omitempty"`
	Properties  map[string]interface{} `json:"properties,omitempty"`
	Confidence  float64                `json:"confidence"`             // 置信度
	DocumentIDs []string               `json:"document_ids,omitempty"` // 关联的文档ID
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// SearchQuery 搜索查询
type SearchQuery struct {
	Query         string                 `json:"query"`
	Type          *DocumentType          `json:"type,omitempty"`
	Tags          []string               `json:"tags,omitempty"`
	Language      string                 `json:"language,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	TimeRange     *TimeRange             `json:"time_range,omitempty"`
	Limit         int                    `json:"limit,omitempty"`
	Offset        int                    `json:"offset,omitempty"`
	MinScore      float64                `json:"min_score,omitempty"`
	IncludeChunks bool                   `json:"include_chunks,omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start *time.Time `json:"start,omitempty"`
	End   *time.Time `json:"end,omitempty"`
}

// SearchResult 搜索结果
type SearchResult struct {
	Document *Document `json:"document"`
	Score    float64   `json:"score"`
	Matches  []*Match  `json:"matches,omitempty"` // 匹配的片段
}

// Match 匹配片段
type Match struct {
	ChunkID   string  `json:"chunk_id,omitempty"`
	Content   string  `json:"content"`
	StartPos  int     `json:"start_pos"`
	EndPos    int     `json:"end_pos"`
	Score     float64 `json:"score"`
	Highlight string  `json:"highlight,omitempty"` // 高亮显示的内容
}

// EntityQuery 实体查询
type EntityQuery struct {
	Name        string                 `json:"name,omitempty"`
	Type        string                 `json:"type,omitempty"`
	Properties  map[string]interface{} `json:"properties,omitempty"`
	DocumentIDs []string               `json:"document_ids,omitempty"`
	Limit       int                    `json:"limit,omitempty"`
	Offset      int                    `json:"offset,omitempty"`
}

// RelationQuery 关系查询
type RelationQuery struct {
	FromEntity    string                 `json:"from_entity,omitempty"`
	ToEntity      string                 `json:"to_entity,omitempty"`
	Type          string                 `json:"type,omitempty"`
	Properties    map[string]interface{} `json:"properties,omitempty"`
	MinConfidence float64                `json:"min_confidence,omitempty"`
	DocumentIDs   []string               `json:"document_ids,omitempty"`
	Limit         int                    `json:"limit,omitempty"`
	Offset        int                    `json:"offset,omitempty"`
}

// KnowledgeBase 知识库接口
type KnowledgeBase interface {
	// 文档管理
	AddDocument(ctx context.Context, doc *Document) error
	GetDocument(ctx context.Context, id string) (*Document, error)
	UpdateDocument(ctx context.Context, doc *Document) error
	DeleteDocument(ctx context.Context, id string) error
	ListDocuments(ctx context.Context, limit, offset int) ([]*Document, error)

	// 文档搜索
	SearchDocuments(ctx context.Context, query *SearchQuery) ([]*SearchResult, error)
	SearchSimilar(ctx context.Context, docID string, limit int) ([]*SearchResult, error)

	// 实体管理
	AddEntity(ctx context.Context, entity *Entity) error
	GetEntity(ctx context.Context, id string) (*Entity, error)
	UpdateEntity(ctx context.Context, entity *Entity) error
	DeleteEntity(ctx context.Context, id string) error
	SearchEntities(ctx context.Context, query *EntityQuery) ([]*Entity, error)

	// 关系管理
	AddRelation(ctx context.Context, relation *Relation) error
	GetRelation(ctx context.Context, id string) (*Relation, error)
	UpdateRelation(ctx context.Context, relation *Relation) error
	DeleteRelation(ctx context.Context, id string) error
	SearchRelations(ctx context.Context, query *RelationQuery) ([]*Relation, error)

	// 图查询
	GetEntityRelations(ctx context.Context, entityID string) ([]*Relation, error)
	GetRelatedEntities(ctx context.Context, entityID string, relationTypes []string) ([]*Entity, error)
	FindPath(ctx context.Context, fromEntityID, toEntityID string, maxDepth int) ([][]*Relation, error)

	// 统计信息
	GetStats(ctx context.Context) (*KnowledgeBaseStats, error)

	// 索引管理
	RebuildIndex(ctx context.Context) error
	OptimizeIndex(ctx context.Context) error
}

// KnowledgeBaseStats 知识库统计信息
type KnowledgeBaseStats struct {
	DocumentCount int                  `json:"document_count"`
	ChunkCount    int                  `json:"chunk_count"`
	EntityCount   int                  `json:"entity_count"`
	RelationCount int                  `json:"relation_count"`
	TypeCounts    map[DocumentType]int `json:"type_counts"`
	EntityTypes   map[string]int       `json:"entity_types"`
	RelationTypes map[string]int       `json:"relation_types"`
	IndexSize     int64                `json:"index_size"`
	LastUpdated   time.Time            `json:"last_updated"`
}

// DocumentProcessor 文档处理器接口
type DocumentProcessor interface {
	// ProcessDocument 处理文档，提取实体和关系
	ProcessDocument(ctx context.Context, doc *Document) error
	// ChunkDocument 将文档分块
	ChunkDocument(ctx context.Context, doc *Document, chunkSize int) ([]*DocumentChunk, error)
	// ExtractEntities 提取实体
	ExtractEntities(ctx context.Context, content string) ([]*Entity, error)
	// ExtractRelations 提取关系
	ExtractRelations(ctx context.Context, content string, entities []*Entity) ([]*Relation, error)
}

// VectorStore 向量存储接口
type VectorStore interface {
	// AddVector 添加向量
	AddVector(ctx context.Context, id string, vector []float64, metadata map[string]interface{}) error
	// GetVector 获取向量
	GetVector(ctx context.Context, id string) ([]float64, error)
	// SearchSimilar 搜索相似向量
	SearchSimilar(ctx context.Context, vector []float64, limit int, threshold float64) ([]VectorSearchResult, error)
	// DeleteVector 删除向量
	DeleteVector(ctx context.Context, id string) error
	// GetStats 获取统计信息
	GetStats(ctx context.Context) (*VectorStoreStats, error)
}

// VectorSearchResult 向量搜索结果
type VectorSearchResult struct {
	ID       string                 `json:"id"`
	Score    float64                `json:"score"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// VectorStoreStats 向量存储统计信息
type VectorStoreStats struct {
	VectorCount int   `json:"vector_count"`
	Dimension   int   `json:"dimension"`
	IndexSize   int64 `json:"index_size"`
}

// Indexer 索引器接口
type Indexer interface {
	// IndexDocument 索引文档
	IndexDocument(ctx context.Context, doc *Document) error
	// RemoveDocument 从索引中移除文档
	RemoveDocument(ctx context.Context, docID string) error
	// Search 搜索
	Search(ctx context.Context, query string, limit int) ([]*SearchResult, error)
	// Rebuild 重建索引
	Rebuild(ctx context.Context, documents []*Document) error
	// Optimize 优化索引
	Optimize(ctx context.Context) error
}
