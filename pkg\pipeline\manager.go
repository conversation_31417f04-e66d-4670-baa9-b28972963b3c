package pipeline

import (
	"context"
	"sync"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
)

// Manager 管道管理器
type Manager struct {
	pipelines map[string]Pipeline
	mu        sync.RWMutex
	logger    logger.Logger
}

// NewManager 创建新的管道管理器
func NewManager() *Manager {
	return &Manager{
		pipelines: make(map[string]Pipeline),
		logger:    logger.GetGlobalLogger(),
	}
}

// AddPipeline 添加管道
func (m *Manager) AddPipeline(name string, pipeline Pipeline) error {
	if pipeline == nil {
		return errors.NewValidationError("invalid_pipeline", "pipeline cannot be nil")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.pipelines[name]; exists {
		return errors.NewValidationError("pipeline_exists", "pipeline already exists")
	}

	m.pipelines[name] = pipeline
	m.logger.Info("Pipeline added: %s", name)
	return nil
}

// RemovePipeline 移除管道
func (m *Manager) RemovePipeline(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.pipelines[name]; !exists {
		return errors.NewNotFoundError("pipeline_not_found", "pipeline not found")
	}

	delete(m.pipelines, name)
	m.logger.Info("Pipeline removed: %s", name)
	return nil
}

// GetPipeline 获取管道
func (m *Manager) GetPipeline(name string) (Pipeline, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	pipeline, exists := m.pipelines[name]
	if !exists {
		return nil, errors.NewNotFoundError("pipeline_not_found", "pipeline not found")
	}

	return pipeline, nil
}

// ListPipelines 列出所有管道名称
func (m *Manager) ListPipelines() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	names := make([]string, 0, len(m.pipelines))
	for name := range m.pipelines {
		names = append(names, name)
	}

	return names
}

// GetAllPipelines 获取所有管道对象
func (m *Manager) GetAllPipelines() []Pipeline {
	m.mu.RLock()
	defer m.mu.RUnlock()

	pipelines := make([]Pipeline, 0, len(m.pipelines))
	for _, pipeline := range m.pipelines {
		pipelines = append(pipelines, pipeline)
	}

	return pipelines
}

// ExecutePipeline 执行管道
func (m *Manager) ExecutePipeline(ctx context.Context, name string, input *message.Message) (*PipelineResult, error) {
	pipeline, err := m.GetPipeline(name)
	if err != nil {
		return nil, err
	}

	return pipeline.Execute(ctx, input)
}

// HasPipeline 检查管道是否存在
func (m *Manager) HasPipeline(name string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	_, exists := m.pipelines[name]
	return exists
}

// Count 获取管道数量
func (m *Manager) Count() int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return len(m.pipelines)
}

// CreatePipeline 创建新管道
func (m *Manager) CreatePipeline(config *Config) (Pipeline, error) {
	if config == nil {
		return nil, errors.NewValidationError("invalid_config", "config cannot be nil")
	}

	// 创建管道实例（这里使用SequentialPipeline作为默认实现）
	pipeline, err := NewSequentialPipeline(config)
	if err != nil {
		return nil, err
	}

	// 添加到管理器
	err = m.AddPipeline(config.Name, pipeline)
	if err != nil {
		return nil, err
	}

	return pipeline, nil
}
