package tests

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/audit"
)

// TestAuditConcurrency 并发安全测试：验证100并发读写场景
func TestAuditConcurrency(t *testing.T) {
	// 跳过并发测试，除非设置了环境变量
	if os.Getenv("RUN_CONCURRENT_TESTS") == "" {
		t.Skip("跳过并发测试，设置 RUN_CONCURRENT_TESTS=1 来运行")
	}

	// 创建临时数据库文件
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_concurrent_audit.db")

	// 创建测试配置
	auditConfig := &audit.Config{
		Enabled: true,
		Driver:  "sqlite",
		DSN:     dbPath,
		Privacy: audit.PrivacyConfig{
			RedactPII:     false, // 并发测试中禁用脱敏以简化
			HashContent:   false,
			EncryptAtRest: false,
		},
		Batch: audit.BatchConfig{
			Async:         true, // 使用异步模式测试并发
			ChanBuffer:    1024,
			FlushInterval: time.Millisecond * 50,
		},
		Retention: audit.RetentionConfig{
			Enabled: false,
		},
		Web: audit.WebConfig{
			AllowReadAPI:   true,
			AllowDeleteAPI: false,
		},
	}

	// 创建审计存储
	store, err := audit.NewSQLiteStore(dbPath, auditConfig)
	if err != nil {
		t.Fatalf("创建审计存储失败: %v", err)
	}
	defer store.Close()

	// 创建数据脱敏器
	sanitizer, err := audit.NewSanitizer(&auditConfig.Privacy)
	if err != nil {
		t.Fatalf("创建数据脱敏器失败: %v", err)
	}

	// 创建审计写入器
	writer := audit.NewAuditWriter(store, sanitizer, &auditConfig.Batch)
	err = writer.Start(context.Background())
	if err != nil {
		t.Fatalf("启动审计写入器失败: %v", err)
	}
	defer writer.Close()

	// 创建审计查询器
	reader := audit.NewAuditReader(store, sanitizer)

	// 测试1：并发写入测试
	t.Run("并发写入测试", func(t *testing.T) {
		testConcurrentWrites(t, writer)
	})

	// 测试2：并发读写混合测试
	t.Run("并发读写混合测试", func(t *testing.T) {
		testConcurrentReadWrites(t, writer, reader)
	})

	// 测试3：高并发压力测试
	t.Run("高并发压力测试", func(t *testing.T) {
		testHighConcurrencyStress(t, writer, reader)
	})

	// 测试4：资源泄漏检测
	t.Run("资源泄漏检测", func(t *testing.T) {
		testResourceLeaks(t, store, writer)
	})

	// 测试5：数据一致性验证
	t.Run("数据一致性验证", func(t *testing.T) {
		testDataConsistency(t, writer, reader)
	})
}

// testConcurrentWrites 测试并发写入
func testConcurrentWrites(t *testing.T, writer *audit.AuditWriter) {
	const numGoroutines = 50
	const recordsPerGoroutine = 20

	ctx := context.Background()
	var wg sync.WaitGroup
	var writeErrors sync.Map

	// 启动多个goroutine并发写入
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < recordsPerGoroutine; j++ {
				record := &audit.Record{
					ID:        audit.GenerateID(),
					SessionID: fmt.Sprintf("session-%d", goroutineID),
					UserID:    fmt.Sprintf("user-%d", goroutineID),
					Role:      "user",
					MsgType:   "text",
					Content:   fmt.Sprintf("并发消息 G%d-R%d", goroutineID, j),
					CreatedAt: time.Now(),
				}

				if err := writer.WriteRecord(ctx, record); err != nil {
					writeErrors.Store(fmt.Sprintf("G%d-R%d", goroutineID, j), err)
				}
			}
		}(i)
	}

	// 等待所有写入完成
	wg.Wait()

	// 检查是否有写入错误
	errorCount := 0
	writeErrors.Range(func(key, value interface{}) bool {
		t.Errorf("写入错误 %s: %v", key, value)
		errorCount++
		return true
	})

	if errorCount > 0 {
		t.Fatalf("并发写入测试失败，共有 %d 个错误", errorCount)
	}

	// 等待异步写入完成
	time.Sleep(time.Second)

	// 检查写入器统计信息
	stats := writer.GetStats()
	if stats.IsClosed {
		t.Error("写入器不应该已关闭")
	}

	if !stats.IsRunning {
		t.Error("写入器应该正在运行")
	}
}

// testConcurrentReadWrites 测试并发读写混合
func testConcurrentReadWrites(t *testing.T, writer *audit.AuditWriter, reader *audit.AuditReader) {
	const numWriters = 25
	const numReaders = 25
	const recordsPerWriter = 10
	const readsPerReader = 20

	ctx := context.Background()
	var wg sync.WaitGroup
	var errors sync.Map

	// 先写入一些基础数据
	baseSessionID := "base-session"
	for i := 0; i < 10; i++ {
		record := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: baseSessionID,
			UserID:    "base-user",
			Role:      "user",
			MsgType:   "text",
			Content:   fmt.Sprintf("基础消息 %d", i),
			CreatedAt: time.Now(),
		}
		writer.WriteRecord(ctx, record)
	}

	// 等待基础数据写入
	time.Sleep(time.Millisecond * 100)

	// 启动写入器
	for i := 0; i < numWriters; i++ {
		wg.Add(1)
		go func(writerID int) {
			defer wg.Done()

			for j := 0; j < recordsPerWriter; j++ {
				record := &audit.Record{
					ID:        audit.GenerateID(),
					SessionID: fmt.Sprintf("rw-session-%d", writerID),
					UserID:    fmt.Sprintf("rw-user-%d", writerID),
					Role:      "user",
					MsgType:   "text",
					Content:   fmt.Sprintf("读写混合消息 W%d-R%d", writerID, j),
					CreatedAt: time.Now(),
				}

				if err := writer.WriteRecord(ctx, record); err != nil {
					errors.Store(fmt.Sprintf("write-W%d-R%d", writerID, j), err)
				}

				// 添加小延迟模拟真实场景
				time.Sleep(time.Microsecond * 100)
			}
		}(i)
	}

	// 启动读取器
	for i := 0; i < numReaders; i++ {
		wg.Add(1)
		go func(readerID int) {
			defer wg.Done()

			for j := 0; j < readsPerReader; j++ {
				// 随机查询不同的会话
				sessionID := baseSessionID
				if j%3 == 0 {
					sessionID = fmt.Sprintf("rw-session-%d", readerID%numWriters)
				}

				query := audit.Query{
					SessionID: &sessionID,
					Limit:     5,
					Offset:    0,
				}

				_, err := reader.QueryMessages(ctx, query)
				if err != nil {
					errors.Store(fmt.Sprintf("read-R%d-Q%d", readerID, j), err)
				}

				// 添加小延迟
				time.Sleep(time.Microsecond * 50)
			}
		}(i)
	}

	// 等待所有操作完成
	wg.Wait()

	// 检查错误
	errorCount := 0
	errors.Range(func(key, value interface{}) bool {
		t.Errorf("读写混合错误 %s: %v", key, value)
		errorCount++
		return true
	})

	if errorCount > 0 {
		t.Fatalf("并发读写混合测试失败，共有 %d 个错误", errorCount)
	}
}

// testHighConcurrencyStress 高并发压力测试
func testHighConcurrencyStress(t *testing.T, writer *audit.AuditWriter, reader *audit.AuditReader) {
	const numGoroutines = 100
	const operationsPerGoroutine = 50

	ctx := context.Background()
	var wg sync.WaitGroup
	var errors sync.Map

	startTime := time.Now()

	// 启动高并发操作
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < operationsPerGoroutine; j++ {
				// 70%写入，30%读取
				if j%10 < 7 {
					// 写入操作
					record := &audit.Record{
						ID:        audit.GenerateID(),
						SessionID: fmt.Sprintf("stress-session-%d", goroutineID%10),
						UserID:    fmt.Sprintf("stress-user-%d", goroutineID),
						Role:      "user",
						MsgType:   "text",
						Content:   fmt.Sprintf("压力测试消息 G%d-O%d", goroutineID, j),
						CreatedAt: time.Now(),
					}

					if err := writer.WriteRecord(ctx, record); err != nil {
						errors.Store(fmt.Sprintf("stress-write-G%d-O%d", goroutineID, j), err)
					}
				} else {
					// 读取操作
					sessionID := fmt.Sprintf("stress-session-%d", goroutineID%10)
					query := audit.Query{
						SessionID: &sessionID,
						Limit:     10,
						Offset:    0,
					}

					_, err := reader.QueryMessages(ctx, query)
					if err != nil {
						errors.Store(fmt.Sprintf("stress-read-G%d-O%d", goroutineID, j), err)
					}
				}
			}
		}(i)
	}

	// 等待所有操作完成
	wg.Wait()

	duration := time.Since(startTime)
	totalOperations := numGoroutines * operationsPerGoroutine

	t.Logf("高并发压力测试完成：%d个操作，耗时 %v，平均 %.2f ops/sec",
		totalOperations, duration, float64(totalOperations)/duration.Seconds())

	// 检查错误
	errorCount := 0
	errors.Range(func(key, value interface{}) bool {
		t.Errorf("压力测试错误 %s: %v", key, value)
		errorCount++
		return true
	})

	if errorCount > 0 {
		t.Fatalf("高并发压力测试失败，共有 %d 个错误", errorCount)
	}
}

// testResourceLeaks 测试资源泄漏
func testResourceLeaks(t *testing.T, store audit.Store, writer *audit.AuditWriter) {
	// 记录初始goroutine数量
	initialGoroutines := runtime.NumGoroutine()

	// 执行一些操作
	ctx := context.Background()
	for i := 0; i < 100; i++ {
		record := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: "leak-test-session",
			UserID:    "leak-test-user",
			Role:      "user",
			MsgType:   "text",
			Content:   fmt.Sprintf("资源泄漏测试消息 %d", i),
			CreatedAt: time.Now(),
		}

		writer.WriteRecord(ctx, record)
	}

	// 等待操作完成
	time.Sleep(time.Second)

	// 强制垃圾回收
	runtime.GC()
	runtime.GC()

	// 检查goroutine数量
	finalGoroutines := runtime.NumGoroutine()
	goroutineDiff := finalGoroutines - initialGoroutines

	// 允许少量goroutine增长（审计写入器的后台goroutine）
	if goroutineDiff > 5 {
		t.Errorf("可能存在goroutine泄漏：初始 %d，最终 %d，增长 %d",
			initialGoroutines, finalGoroutines, goroutineDiff)
	}

	t.Logf("Goroutine数量：初始 %d，最终 %d，增长 %d",
		initialGoroutines, finalGoroutines, goroutineDiff)
}

// testDataConsistency 测试数据一致性
func testDataConsistency(t *testing.T, writer *audit.AuditWriter, reader *audit.AuditReader) {
	const numSessions = 10
	const messagesPerSession = 20

	ctx := context.Background()
	var wg sync.WaitGroup

	// 并发写入多个会话的消息
	for sessionID := 0; sessionID < numSessions; sessionID++ {
		wg.Add(1)
		go func(sid int) {
			defer wg.Done()

			sessionIDStr := fmt.Sprintf("consistency-session-%d", sid)
			for msgID := 0; msgID < messagesPerSession; msgID++ {
				record := &audit.Record{
					ID:        audit.GenerateID(),
					SessionID: sessionIDStr,
					UserID:    fmt.Sprintf("consistency-user-%d", sid),
					Role:      "user",
					MsgType:   "text",
					Content:   fmt.Sprintf("一致性测试消息 S%d-M%d", sid, msgID),
					CreatedAt: time.Now().Add(time.Duration(msgID) * time.Millisecond),
				}

				writer.WriteRecord(ctx, record)
			}
		}(sessionID)
	}

	// 等待所有写入完成
	wg.Wait()
	time.Sleep(time.Second) // 等待异步写入完成

	// 验证每个会话的消息数量
	for sessionID := 0; sessionID < numSessions; sessionID++ {
		sessionIDStr := fmt.Sprintf("consistency-session-%d", sessionID)

		query := audit.Query{
			SessionID: &sessionIDStr,
			Limit:     100,
			Offset:    0,
		}

		result, err := reader.QueryMessages(ctx, query)
		if err != nil {
			t.Errorf("查询会话 %s 失败: %v", sessionIDStr, err)
			continue
		}

		if len(result.Records) != messagesPerSession {
			t.Errorf("会话 %s 消息数量不一致：期望 %d，实际 %d",
				sessionIDStr, messagesPerSession, len(result.Records))
		}

		// 验证消息顺序（按时间排序）
		for i := 1; i < len(result.Records); i++ {
			if result.Records[i].CreatedAt.Before(result.Records[i-1].CreatedAt) {
				t.Errorf("会话 %s 消息顺序错误：消息 %d 的时间早于消息 %d",
					sessionIDStr, i, i-1)
			}
		}
	}
}
