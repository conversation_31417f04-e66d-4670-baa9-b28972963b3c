package llm

import (
	"context"
	"testing"
	"time"
)

// mockChatModel 模拟聊天模型实现
type mockChatModel struct {
	chatResponse   *ChatResponse
	chatError      error
	deltaResponses []*ChatDelta
	streamError    error
}

func (m *mockChatModel) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	if m.chatError != nil {
		return nil, m.chatError
	}
	return m.chatResponse, nil
}

func (m *mockChatModel) ChatStream(ctx context.Context, req *ChatRequest) (<-chan *ChatDelta, error) {
	if m.streamError != nil {
		return nil, m.streamError
	}

	deltaChan := make(chan *Chat<PERSON><PERSON><PERSON>, len(m.deltaResponses))

	go func() {
		defer close(deltaChan)
		for _, delta := range m.deltaResponses {
			select {
			case deltaChan <- delta:
			case <-ctx.Done():
				return
			}
		}
	}()

	return deltaChan, nil
}

func TestNewChatRequest(t *testing.T) {
	model := "test-model"
	messages := []*ChatMessage{
		{Role: "user", Content: "Hello"},
	}

	req := NewChatRequest(model, messages)

	if req.Model != model {
		t.Errorf("模型不匹配: got %s, want %s", req.Model, model)
	}

	if len(req.Messages) != 1 {
		t.Errorf("消息数量不匹配: got %d, want 1", len(req.Messages))
	}

	if req.Messages[0].Role != "user" {
		t.Errorf("消息角色不匹配: got %s, want user", req.Messages[0].Role)
	}

	if req.Messages[0].Content != "Hello" {
		t.Errorf("消息内容不匹配: got %s, want Hello", req.Messages[0].Content)
	}
}

func TestChatRequestAddMessage(t *testing.T) {
	req := NewChatRequest("test-model", nil)

	// 测试 AddMessage
	req.AddMessage("user", "Hello")
	if len(req.Messages) != 1 {
		t.Errorf("添加消息后数量不匹配: got %d, want 1", len(req.Messages))
	}

	// 测试 AddUserMessage
	req.AddUserMessage("User message")
	if len(req.Messages) != 2 {
		t.Errorf("添加用户消息后数量不匹配: got %d, want 2", len(req.Messages))
	}

	if req.Messages[1].Role != "user" {
		t.Errorf("用户消息角色不匹配: got %s, want user", req.Messages[1].Role)
	}

	// 测试 AddAssistantMessage
	req.AddAssistantMessage("Assistant message")
	if len(req.Messages) != 3 {
		t.Errorf("添加助手消息后数量不匹配: got %d, want 3", len(req.Messages))
	}

	if req.Messages[2].Role != "assistant" {
		t.Errorf("助手消息角色不匹配: got %s, want assistant", req.Messages[2].Role)
	}

	// 测试 AddSystemMessage
	req.AddSystemMessage("System message")
	if len(req.Messages) != 4 {
		t.Errorf("添加系统消息后数量不匹配: got %d, want 4", len(req.Messages))
	}

	if req.Messages[3].Role != "system" {
		t.Errorf("系统消息角色不匹配: got %s, want system", req.Messages[3].Role)
	}

	// 测试 AddToolMessage
	req.AddToolMessage("Tool result", "call-123")
	if len(req.Messages) != 5 {
		t.Errorf("添加工具消息后数量不匹配: got %d, want 5", len(req.Messages))
	}

	if req.Messages[4].Role != "tool" {
		t.Errorf("工具消息角色不匹配: got %s, want tool", req.Messages[4].Role)
	}

	if req.Messages[4].ToolCallID != "call-123" {
		t.Errorf("工具调用ID不匹配: got %s, want call-123", req.Messages[4].ToolCallID)
	}
}

func TestChatRequestWithMethods(t *testing.T) {
	req := NewChatRequest("test-model", nil)

	// 测试 WithTemperature
	temp := 0.7
	req.WithTemperature(temp)
	if req.Temperature == nil || *req.Temperature != temp {
		t.Errorf("温度设置不匹配: got %v, want %f", req.Temperature, temp)
	}

	// 测试 WithTopP
	topP := 0.9
	req.WithTopP(topP)
	if req.TopP == nil || *req.TopP != topP {
		t.Errorf("TopP设置不匹配: got %v, want %f", req.TopP, topP)
	}

	// 测试 WithMaxTokens
	maxTokens := 100
	req.WithMaxTokens(maxTokens)
	if req.MaxTokens == nil || *req.MaxTokens != maxTokens {
		t.Errorf("MaxTokens设置不匹配: got %v, want %d", req.MaxTokens, maxTokens)
	}

	// 测试 WithTools
	tools := []ToolDefinition{
		{Type: "function", Function: &ToolFunctionDefinition{Name: "test_tool"}},
	}
	req.WithTools(tools)
	if len(req.Tools) != 1 {
		t.Errorf("工具数量不匹配: got %d, want 1", len(req.Tools))
	}

	// 测试 WithToolChoice
	toolChoice := "auto"
	req.WithToolChoice(toolChoice)
	if req.ToolChoice != toolChoice {
		t.Errorf("工具选择不匹配: got %v, want %s", req.ToolChoice, toolChoice)
	}

	// 测试 WithStream
	req.WithStream(true)
	if !req.Stream {
		t.Error("流式设置应该为 true")
	}
}

func TestChatResponseMethods(t *testing.T) {
	// 创建测试响应
	response := &ChatResponse{
		ID:      "test-id",
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   "test-model",
		Choices: []ChatChoice{
			{
				Index: 0,
				Message: ChatMessage{
					Role:    "assistant",
					Content: "Hello, world!",
					ToolCalls: []*ToolCall{
						{
							ID:   "call-123",
							Type: "function",
							Function: &ToolCallFunction{
								Name:      "test_function",
								Arguments: `{"arg": "value"}`,
							},
						},
					},
				},
				FinishReason: "tool_calls",
			},
		},
		Usage: &ChatUsage{
			PromptTokens:     10,
			CompletionTokens: 5,
			TotalTokens:      15,
		},
	}

	// 测试 GetFirstChoice
	firstChoice := response.GetFirstChoice()
	if firstChoice == nil {
		t.Fatal("GetFirstChoice 应该返回非空值")
	}

	if firstChoice.Role != "assistant" {
		t.Errorf("第一个选择角色不匹配: got %s, want assistant", firstChoice.Role)
	}

	// 测试 GetContent
	content := response.GetContent()
	if content != "Hello, world!" {
		t.Errorf("内容不匹配: got %s, want Hello, world!", content)
	}

	// 测试 HasToolCalls
	if !response.HasToolCalls() {
		t.Error("应该包含工具调用")
	}

	// 测试 GetToolCalls
	toolCalls := response.GetToolCalls()
	if len(toolCalls) != 1 {
		t.Errorf("工具调用数量不匹配: got %d, want 1", len(toolCalls))
	}

	if toolCalls[0].ID != "call-123" {
		t.Errorf("工具调用ID不匹配: got %s, want call-123", toolCalls[0].ID)
	}

	// 测试 IsFinished
	if !response.IsFinished() {
		t.Error("响应应该已完成")
	}

	// 测试空响应
	emptyResponse := &ChatResponse{}
	if emptyResponse.GetFirstChoice() != nil {
		t.Error("空响应的 GetFirstChoice 应该返回 nil")
	}

	if emptyResponse.GetContent() != "" {
		t.Error("空响应的 GetContent 应该返回空字符串")
	}

	if emptyResponse.HasToolCalls() {
		t.Error("空响应不应该包含工具调用")
	}

	if emptyResponse.IsFinished() {
		t.Error("空响应不应该标记为已完成")
	}
}

func TestChatDeltaMethods(t *testing.T) {
	// 创建测试增量
	delta := &ChatDelta{
		ID:      "test-id",
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   "test-model",
		Choices: []ChatDeltaChoice{
			{
				Index: 0,
				Delta: ChatDeltaContent{
					Role:    "assistant",
					Content: "Hello",
					ToolCalls: []*ToolCall{
						{
							ID:   "call-456",
							Type: "function",
							Function: &ToolCallFunction{
								Name:      "test_function",
								Arguments: `{"arg": "value"}`,
							},
						},
					},
				},
				FinishReason: func() *string { s := "stop"; return &s }(),
			},
		},
	}

	// 测试 GetContent
	content := delta.GetContent()
	if content != "Hello" {
		t.Errorf("增量内容不匹配: got %s, want Hello", content)
	}

	// 测试 HasToolCalls
	if !delta.HasToolCalls() {
		t.Error("增量应该包含工具调用")
	}

	// 测试 GetToolCalls
	toolCalls := delta.GetToolCalls()
	if len(toolCalls) != 1 {
		t.Errorf("增量工具调用数量不匹配: got %d, want 1", len(toolCalls))
	}

	if toolCalls[0].ID != "call-456" {
		t.Errorf("增量工具调用ID不匹配: got %s, want call-456", toolCalls[0].ID)
	}

	// 测试 IsFinished
	if !delta.IsFinished() {
		t.Error("增量应该已完成")
	}

	// 测试空增量
	emptyDelta := &ChatDelta{}
	if emptyDelta.GetContent() != "" {
		t.Error("空增量的 GetContent 应该返回空字符串")
	}

	if emptyDelta.HasToolCalls() {
		t.Error("空增量不应该包含工具调用")
	}

	if emptyDelta.IsFinished() {
		t.Error("空增量不应该标记为已完成")
	}
}

func TestMockChatModel(t *testing.T) {
	// 测试成功的聊天响应
	mockResponse := &ChatResponse{
		ID:    "test-id",
		Model: "test-model",
		Choices: []ChatChoice{
			{
				Message: ChatMessage{
					Role:    "assistant",
					Content: "Test response",
				},
			},
		},
	}

	mock := &mockChatModel{
		chatResponse: mockResponse,
	}

	req := NewChatRequest("test-model", []*ChatMessage{
		{Role: "user", Content: "Hello"},
	})

	resp, err := mock.Chat(context.Background(), req)
	if err != nil {
		t.Fatalf("聊天请求失败: %v", err)
	}

	if resp.ID != "test-id" {
		t.Errorf("响应ID不匹配: got %s, want test-id", resp.ID)
	}

	// 测试流式响应
	deltaResponses := []*ChatDelta{
		{
			ID:    "stream-id",
			Model: "test-model",
			Choices: []ChatDeltaChoice{
				{
					Delta: ChatDeltaContent{
						Content: "Hello",
					},
				},
			},
		},
		{
			ID:    "stream-id",
			Model: "test-model",
			Choices: []ChatDeltaChoice{
				{
					Delta: ChatDeltaContent{
						Content: " world!",
					},
				},
			},
		},
	}

	mock.deltaResponses = deltaResponses

	deltaChan, err := mock.ChatStream(context.Background(), req)
	if err != nil {
		t.Fatalf("流式聊天请求失败: %v", err)
	}

	var receivedDeltas []*ChatDelta
	for delta := range deltaChan {
		receivedDeltas = append(receivedDeltas, delta)
	}

	if len(receivedDeltas) != 2 {
		t.Errorf("接收到的增量数量不匹配: got %d, want 2", len(receivedDeltas))
	}

	if receivedDeltas[0].GetContent() != "Hello" {
		t.Errorf("第一个增量内容不匹配: got %s, want Hello", receivedDeltas[0].GetContent())
	}

	if receivedDeltas[1].GetContent() != " world!" {
		t.Errorf("第二个增量内容不匹配: got %s, want  world!", receivedDeltas[1].GetContent())
	}
}

// TestGenerateRequestFields 测试GenerateRequest字段
func TestGenerateRequestFields(t *testing.T) {
	// 测试有效请求
	validRequest := &GenerateRequest{
		Messages: []*ChatMessage{
			{Role: "user", Content: "Hello"},
		},
		Model:       "test-model",
		Temperature: func() *float64 { v := 0.7; return &v }(),
		MaxTokens:   func() *int { v := 100; return &v }(),
	}

	if validRequest.Model != "test-model" {
		t.Errorf("模型名不匹配: got %s, want test-model", validRequest.Model)
	}

	if len(validRequest.Messages) != 1 {
		t.Errorf("消息数量不匹配: got %d, want 1", len(validRequest.Messages))
	}

	if *validRequest.Temperature != 0.7 {
		t.Errorf("温度不匹配: got %f, want 0.7", *validRequest.Temperature)
	}

	if *validRequest.MaxTokens != 100 {
		t.Errorf("最大token数不匹配: got %d, want 100", *validRequest.MaxTokens)
	}

	// 测试空消息
	emptyMsgRequest := &GenerateRequest{
		Messages: []*ChatMessage{},
	}
	if len(emptyMsgRequest.Messages) != 0 {
		t.Error("空消息请求应该有0个消息")
	}
}

// TestUsageCalculation 测试Usage计算
func TestUsageCalculation(t *testing.T) {
	usage := &Usage{
		PromptTokens:     100,
		CompletionTokens: 50,
	}

	// 测试总token计算
	usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
	if usage.TotalTokens != 150 {
		t.Errorf("总token计算错误: got %d, want 150", usage.TotalTokens)
	}

	// 测试零值
	zeroUsage := &Usage{}
	if zeroUsage.TotalTokens != 0 {
		t.Errorf("零值Usage总token应该为0: got %d", zeroUsage.TotalTokens)
	}
}

// TestToolDefinition 测试工具定义
func TestToolDefinition(t *testing.T) {
	tool := &ToolDefinition{
		Type: "function",
		Function: &ToolFunctionDefinition{
			Name:        "get_weather",
			Description: "Get current weather information",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"location": map[string]interface{}{
						"type":        "string",
						"description": "The city and state, e.g. San Francisco, CA",
					},
				},
				"required": []string{"location"},
			},
		},
	}

	if tool.Type != "function" {
		t.Errorf("工具类型不匹配: got %s, want function", tool.Type)
	}

	if tool.Function.Name != "get_weather" {
		t.Errorf("函数名不匹配: got %s, want get_weather", tool.Function.Name)
	}

	if tool.Function.Description != "Get current weather information" {
		t.Errorf("函数描述不匹配: got %s", tool.Function.Description)
	}

	// 测试参数结构
	params := tool.Function.Parameters
	if params["type"] != "object" {
		t.Errorf("参数类型不匹配: got %v, want object", params["type"])
	}
}

// TestMultiModalGenerateRequest 测试多模态请求
func TestMultiModalGenerateRequest(t *testing.T) {
	request := &MultiModalGenerateRequest{
		Messages: []*MultiModalMessage{
			{
				Role: "user",
				Content: []MultiModalContent{
					{
						Type: "text",
						Text: "What's in this image?",
					},
					{
						Type: "image_url",
						ImageURL: &ImageURL{
							URL: "https://example.com/image.jpg",
						},
					},
				},
			},
		},
		Model:       "vision-model",
		MaxTokens:   func() *int { v := 300; return &v }(),
		Temperature: func() *float64 { v := 0.7; return &v }(),
	}

	if request.Model != "vision-model" {
		t.Errorf("模型名不匹配: got %s, want vision-model", request.Model)
	}

	if len(request.Messages) != 1 {
		t.Errorf("消息数量不匹配: got %d, want 1", len(request.Messages))
	}

	msg := request.Messages[0]
	if msg.Role != "user" {
		t.Errorf("消息角色不匹配: got %s, want user", msg.Role)
	}

	if len(msg.Content) != 2 {
		t.Errorf("消息内容数量不匹配: got %d, want 2", len(msg.Content))
	}

	// 测试文本内容
	textContent := msg.Content[0]
	if textContent.Type != "text" {
		t.Errorf("文本内容类型不匹配: got %s, want text", textContent.Type)
	}
	if textContent.Text != "What's in this image?" {
		t.Errorf("文本内容不匹配: got %s", textContent.Text)
	}

	// 测试图片内容
	imageContent := msg.Content[1]
	if imageContent.Type != "image_url" {
		t.Errorf("图片内容类型不匹配: got %s, want image_url", imageContent.Type)
	}
	if imageContent.ImageURL.URL != "https://example.com/image.jpg" {
		t.Errorf("图片URL不匹配: got %s", imageContent.ImageURL.URL)
	}
}
