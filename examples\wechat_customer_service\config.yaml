# 企业微信客服Agent配置 - 知识库架构版本
name: "企业微信客服Agent"
description: "基于分层知识库架构的智能客服Agent"

# LLM配置
llm:
  default_provider: "deepseek"
  providers:
    deepseek:
      type: "deepseek"
      api_key: "${DEEPSEEK_API_KEY}"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
      timeout: 30s
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048

# Agent配置
agent:
  type: "prompt_driven"
  name: "企业微信客服助手"
  description: "专业的企业微信客服Agent，提供7x24小时智能客服服务"
  
  # 基础角色定义
  base_role: "wechat_customer_service"
  
  # AI能力配置
  ai_capabilities:
    - "emotion_analyst"      # 情绪分析师
    - "problem_classifier"   # 问题分类专家
    - "empathetic_responder" # 共情回应者
    - "professional_service" # 专业服务者
  
  # 工具配置
  tools:
    - "ticket_manager"       # 工单管理工具
  
  # 个性特征
  personality_traits:
    - "耐心细致"
    - "专业可靠"
    - "温暖友善"
    - "积极主动"
  
  # 行为约束
  behavioral_constraints:
    - "始终保持礼貌和专业"
    - "不得泄露客户隐私信息"
    - "遇到无法解决的问题及时转人工"
    - "严格遵循公司政策和流程"
  
  # 业务配置
  business_config:
    # 问题分类配置
    classification:
      categories:
        - name: "账户问题"
          keywords: ["登录", "密码", "账户", "注册"]
          priority: 9
        - name: "订单问题"
          keywords: ["订单", "支付", "退款", "发货"]
          priority: 8
        - name: "技术支持"
          keywords: ["故障", "bug", "错误", "无法使用"]
          priority: 7
        - name: "产品咨询"
          keywords: ["功能", "价格", "试用", "购买"]
          priority: 6
        - name: "投诉建议"
          keywords: ["投诉", "建议", "不满", "改进"]
          priority: 10
    
    # 知识库配置
    knowledge_bases:
      # 存储配置
      repository:
        type: "file"
        base_path: "./examples/wechat_customer_service/knowledge"
      
      # 检索配置
      retriever:
        algorithm: "keyword"  # 检索算法：keyword, semantic, hybrid
        min_score: 0.3        # 最小相似度分数
        max_results: 3        # 最大返回结果数
        cache:
          enabled: true
          ttl: 300           # 缓存过期时间（秒）
          max_size: 1000     # 最大缓存大小
      
      # 知识库列表
      bases:
        # 基础知识库（所有角色可访问）
        - id: "company_basic"
          name: "公司基础信息知识库"
          enabled: true
          priority: 8
        
        # 角色专用知识库
        - id: "customer_service"
          name: "客服专用知识库"
          enabled: true
          priority: 9
          roles: ["customer_service", "wechat_customer_service"]
        
        - id: "sales"
          name: "销售专用知识库"
          enabled: false  # 客服Agent不启用销售知识库
          priority: 7
          roles: ["sales", "sales_agent"]
        
        # 动态知识库
        - id: "recent_updates"
          name: "最新动态知识库"
          enabled: true
          priority: 10
    
    # 情绪处理配置
    emotion_handling:
      escalation_threshold: 0.7  # 负面情绪升级阈值
      negative_keywords: ["生气", "愤怒", "不满", "投诉", "差评"]
      calming_phrases: ["我理解您的感受", "非常抱歉", "让我来帮您解决"]
    
    # 质量标准
    quality_standards:
      response_time: "30秒内响应"
      resolution_rate: "一次解决率85%以上"
      satisfaction_score: "客户满意度4.5分以上"
      key_metrics: ["响应速度", "解决质量", "服务态度", "专业程度"]

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Web配置（可选）
web:
  enabled: false
  host: "localhost"
  port: 8080
