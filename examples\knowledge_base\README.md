# Knowledge Base Example

This example demonstrates the comprehensive knowledge base capabilities of AgentScope-Golang.

## Features Demonstrated

1. **Document Management**: Adding, retrieving, and searching documents
2. **Entity Management**: Creating and managing entities with properties
3. **Relationship Mapping**: Defining relationships between entities
4. **Full-Text Search**: Searching documents by content and tags
5. **Agent Integration**: Using the knowledge base with agents
6. **Statistics**: Getting insights about the knowledge base

## Running the Example

```bash
# From the project root
go run examples/knowledge_base/main.go

# Or using make (if added to Makefile)
make run-knowledge-example
```

## What You'll See

The example will:

1. Create an in-memory knowledge base
2. Add sample documents about AI and machine learning
3. Perform various search operations
4. Create entities and relationships
5. Show knowledge base statistics
6. Demonstrate agent integration features

## Sample Output

```
=== AgentScope-Golang Knowledge Base Example ===

1. Adding documents...
  Added: Machine Learning Fundamentals
  Added: Deep Learning Networks
  Added: Natural Language Processing

2. Searching documents...
  Found 2 documents for 'machine learning':
    - Machine Learning Fundamentals (Score: 0.60)
    - Deep Learning Networks (Score: 0.20)

3. Tag-based search...
  Found 2 documents with 'ai' tag:
    - Machine Learning Fundamentals
    - Deep Learning Networks

4. Managing entities...
  Added entity: Neural Networks (concept)
  Added entity: Artificial Intelligence (field)

5. Creating relations...
  Created relation: Neural Networks -> Artificial Intelligence (is_part_of)
  Related entities to Neural Networks:
    - Artificial Intelligence

6. Knowledge base statistics...
  Documents: 4
  Entities: 2
  Relations: 1
  Document types:
    - text: 4
  Entity types:
    - concept: 1
    - field: 1

7. Agent knowledge integration...
  Added factual knowledge about Go programming
  Found 1 relevant documents for 'programming language':
    - Go Programming Language
  Built knowledge context (truncated):
  相关知识:

  **Machine Learning Fundamentals**
  Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience...

=== Knowledge Base Example Completed ===
```

## Key Concepts

### Document Types
- Text documents for general content
- Markdown for formatted documentation
- JSON for structured data
- Code for programming examples

### Entity Types
- Concepts: Abstract ideas or technologies
- Fields: Areas of study or application
- People: Researchers, developers, etc.
- Organizations: Companies, institutions

### Relation Types
- `is_part_of`: Hierarchical relationships
- `related_to`: General associations
- `implements`: Implementation relationships
- `uses`: Usage relationships

### Search Strategies
- **Keyword Search**: Text-based matching
- **Tag Filtering**: Category-based filtering
- **Metadata Filtering**: Property-based filtering
- **Similarity Search**: Find similar documents

## Extending the Example

You can extend this example by:

1. Adding more document types (PDF, HTML, etc.)
2. Implementing custom entity extractors
3. Adding vector-based similarity search
4. Integrating with external databases
5. Adding real-time indexing capabilities

## Integration with Agents

The knowledge base integrates seamlessly with agents:

```go
// Create agent with knowledge
akm := knowledge.NewAgentKnowledgeManager(kb, "my-agent")

// Add conversation messages as knowledge
akm.AddMessageAsKnowledge(ctx, message)

// Search for relevant context
docs := akm.SearchRelevantKnowledge(ctx, "query", 5)

// Build context for LLM
context := akm.BuildContextFromKnowledge(ctx, "topic", 2000)
```

This enables agents to:
- Learn from conversations
- Access relevant knowledge
- Provide informed responses
- Build contextual understanding