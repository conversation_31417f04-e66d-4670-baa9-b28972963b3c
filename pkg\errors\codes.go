package errors

// Common error codes used throughout the system

// Validation error codes
const (
	CodeInvalidInput    = "INVALID_INPUT"
	CodeMissingRequired = "MISSING_REQUIRED"
	CodeInvalidFormat   = "INVALID_FORMAT"
	CodeOutOfRange      = "OUT_OF_RANGE"
	CodeInvalidType     = "INVALID_TYPE"
)

// Network error codes
const (
	CodeConnectionFailed   = "CONNECTION_FAILED"
	CodeRequestFailed      = "REQUEST_FAILED"
	CodeResponseInvalid    = "RESPONSE_INVALID"
	CodeServiceUnavailable = "SERVICE_UNAVAILABLE"
)

// Timeout error codes
const (
	CodeRequestTimeout   = "REQUEST_TIMEOUT"
	CodeOperationTimeout = "OPERATION_TIMEOUT"
	CodeContextCanceled  = "CONTEXT_CANCELED"
)

// Authentication error codes
const (
	CodeInvalidCredentials = "INVALID_CREDENTIALS"
	CodeTokenExpired       = "TOKEN_EXPIRED"
	CodeUnauthorized       = "UNAUTHORIZED"
	CodeForbidden          = "FORBIDDEN"
	CodeAPIKeyMissing      = "API_KEY_MISSING"
	CodeAccessDenied       = "ACCESS_DENIED"
)

// Internal error codes
const (
	CodeInternalError       = "INTERNAL_ERROR"
	CodeConfigError         = "CONFIG_ERROR"
	CodeInitializationError = "INITIALIZATION_ERROR"
	CodeResourceExhausted   = "RESOURCE_EXHAUSTED"
)

// LLM error codes
const (
	CodeLLMAPIError           = "LLM_API_ERROR"
	CodeLLMRateLimited        = "LLM_RATE_LIMITED"
	CodeLLMInvalidModel       = "LLM_INVALID_MODEL"
	CodeLLMTokenLimitExceeded = "LLM_TOKEN_LIMIT_EXCEEDED"
	CodeLLMRequestFailed      = "LLM_REQUEST_FAILED"
	CodeLLMResponseInvalid    = "LLM_RESPONSE_INVALID"
	CodeLLMQuotaExceeded      = "LLM_QUOTA_EXCEEDED"
	CodeLLMModelNotFound      = "LLM_MODEL_NOT_FOUND"
)

// Tool error codes
const (
	CodeToolNotFound        = "TOOL_NOT_FOUND"
	CodeToolExecutionFailed = "TOOL_EXECUTION_FAILED"
	CodeToolInvalidParams   = "TOOL_INVALID_PARAMS"
	CodeToolTimeout         = "TOOL_TIMEOUT"
)

// Agent error codes
const (
	CodeAgentNotFound      = "AGENT_NOT_FOUND"
	CodeAgentInitFailed    = "AGENT_INIT_FAILED"
	CodeAgentStateMismatch = "AGENT_STATE_MISMATCH"
	CodeAgentReplyFailed   = "AGENT_REPLY_FAILED"
)

// Pipeline error codes
const (
	CodePipelineEmpty       = "PIPELINE_EMPTY"
	CodePipelineExecFailed  = "PIPELINE_EXEC_FAILED"
	CodePipelineAgentFailed = "PIPELINE_AGENT_FAILED"
	CodePipelineTimeout     = "PIPELINE_TIMEOUT"
)
