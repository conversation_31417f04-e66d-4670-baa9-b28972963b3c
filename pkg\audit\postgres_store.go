package audit

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	_ "github.com/lib/pq"
)

// PostgresStore PostgreSQL存储实现
type PostgresStore struct {
	db     *sql.DB
	config *Config
	mu     sync.RWMutex
	closed bool
}

// NewPostgresStore 创建PostgreSQL存储
func NewPostgresStore(dsn string, config *Config) (*PostgresStore, error) {
	if config == nil {
		config = DefaultConfig()
	}

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, NewStoreInitError("打开PostgreSQL数据库失败", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(20)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := db.<PERSON>(); err != nil {
		db.Close()
		return nil, NewStoreInitError("连接PostgreSQL数据库失败", err)
	}

	store := &PostgresStore{
		db:     db,
		config: config,
	}

	// 执行数据库迁移
	if err := store.migrate(context.Background()); err != nil {
		db.Close()
		return nil, err
	}

	return store, nil
}

// migrate 执行数据库迁移
func (s *PostgresStore) migrate(ctx context.Context) error {
	migrator := NewMigrator(s.db, GetPostgreSQLMigrations())
	return migrator.MigrateToLatest(ctx)
}

// SaveMessage 保存消息记录
func (s *PostgresStore) SaveMessage(ctx context.Context, r *Record) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if err := ValidateRecord(r); err != nil {
		return err
	}

	query := `
		INSERT INTO audit_messages (
			id, session_id, user_id, agent_id, role, msg_type, 
			content, content_hash, event_type, tool_name, error_code, 
			trace_id, span_id, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`

	_, err := s.db.ExecContext(ctx, query,
		r.ID, r.SessionID, r.UserID, r.AgentID, r.Role, r.MsgType,
		r.Content, r.ContentHash, r.EventType, r.ToolName, r.ErrorCode,
		r.TraceID, r.SpanID, r.CreatedAt,
	)

	if err != nil {
		return NewStoreOperationError("保存消息记录失败", err)
	}

	return nil
}

// SaveSession 保存会话信息
func (s *PostgresStore) SaveSession(ctx context.Context, sessionID, userID string, meta map[string]any) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if sessionID == "" || userID == "" {
		return NewInvalidRecordError("会话ID和用户ID不能为空", nil)
	}

	var metaJSON []byte
	if meta != nil {
		var err error
		metaJSON, err = json.Marshal(meta)
		if err != nil {
			return NewStoreOperationError("序列化会话元数据失败", err)
		}
	}

	now := time.Now()
	query := `
		INSERT INTO audit_sessions (session_id, user_id, created_at, last_active_at, metadata)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (session_id) 
		DO UPDATE SET last_active_at = $4, metadata = $5`

	_, err := s.db.ExecContext(ctx, query, sessionID, userID, now, now, metaJSON)
	if err != nil {
		return NewStoreOperationError("保存会话信息失败", err)
	}

	return nil
}

// TouchSession 更新会话最后活跃时间
func (s *PostgresStore) TouchSession(ctx context.Context, sessionID string, at time.Time) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if sessionID == "" {
		return NewInvalidRecordError("会话ID不能为空", nil)
	}

	query := `UPDATE audit_sessions SET last_active_at = $1 WHERE session_id = $2`
	_, err := s.db.ExecContext(ctx, query, at, sessionID)
	if err != nil {
		return NewStoreOperationError("更新会话活跃时间失败", err)
	}

	return nil
}

// QueryMessages 查询消息记录
func (s *PostgresStore) QueryMessages(ctx context.Context, q Query) (*QueryResult, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return nil, NewStoreOperationError("存储已关闭", nil)
	}

	if err := ValidateQuery(q); err != nil {
		return nil, err
	}

	NormalizeQuery(&q)

	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if q.SessionID != nil {
		conditions = append(conditions, fmt.Sprintf("session_id = $%d", argIndex))
		args = append(args, *q.SessionID)
		argIndex++
	}

	if q.UserID != nil {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, *q.UserID)
		argIndex++
	}

	if q.AgentID != nil {
		conditions = append(conditions, fmt.Sprintf("agent_id = $%d", argIndex))
		args = append(args, *q.AgentID)
		argIndex++
	}

	if len(q.Types) > 0 {
		placeholders := make([]string, len(q.Types))
		for i, t := range q.Types {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, t)
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("msg_type = ANY(ARRAY[%s])", strings.Join(placeholders, ",")))
	}

	if q.Since != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *q.Since)
		argIndex++
	}

	if q.Until != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *q.Until)
		argIndex++
	}

	if q.Keyword != "" {
		conditions = append(conditions, fmt.Sprintf("content ILIKE $%d", argIndex))
		args = append(args, "%"+q.Keyword+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM audit_messages %s", whereClause)
	var total int
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, NewStoreOperationError("查询消息总数失败", err)
	}

	// 查询记录
	dataQuery := fmt.Sprintf(`
		SELECT id, session_id, user_id, agent_id, role, msg_type, 
			   content, content_hash, event_type, tool_name, error_code, 
			   trace_id, span_id, created_at
		FROM audit_messages %s 
		ORDER BY created_at DESC 
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, q.Limit, q.Offset)

	rows, err := s.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, NewStoreOperationError("查询消息记录失败", err)
	}
	defer rows.Close()

	var records []*Record
	for rows.Next() {
		r := &Record{}
		err := rows.Scan(
			&r.ID, &r.SessionID, &r.UserID, &r.AgentID, &r.Role, &r.MsgType,
			&r.Content, &r.ContentHash, &r.EventType, &r.ToolName, &r.ErrorCode,
			&r.TraceID, &r.SpanID, &r.CreatedAt,
		)
		if err != nil {
			return nil, NewStoreOperationError("扫描消息记录失败", err)
		}
		records = append(records, r)
	}

	if err := rows.Err(); err != nil {
		return nil, NewStoreOperationError("遍历消息记录失败", err)
	}

	hasMore := q.Offset+len(records) < total

	return &QueryResult{
		Records: records,
		Total:   total,
		HasMore: hasMore,
	}, nil
}

// QuerySessions 查询会话列表
func (s *PostgresStore) QuerySessions(ctx context.Context, userID string, limit, offset int) ([]*Session, int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return nil, 0, NewStoreOperationError("存储已关闭", nil)
	}

	if limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000
	}
	if offset < 0 {
		offset = 0
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM audit_sessions"
	var args []interface{}
	argIndex := 1
	if userID != "" {
		countQuery += " WHERE user_id = $1"
		args = append(args, userID)
		argIndex++
	}

	var total int
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, NewStoreOperationError("查询会话总数失败", err)
	}

	// 查询会话列表
	dataQuery := "SELECT session_id, user_id, created_at, last_active_at, metadata FROM audit_sessions"
	if userID != "" {
		dataQuery += " WHERE user_id = $1"
	}
	dataQuery += fmt.Sprintf(" ORDER BY last_active_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := s.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, 0, NewStoreOperationError("查询会话列表失败", err)
	}
	defer rows.Close()

	var sessions []*Session
	for rows.Next() {
		s := &Session{}
		var metaJSON []byte
		err := rows.Scan(&s.SessionID, &s.UserID, &s.CreatedAt, &s.LastActiveAt, &metaJSON)
		if err != nil {
			return nil, 0, NewStoreOperationError("扫描会话记录失败", err)
		}

		if len(metaJSON) > 0 {
			var meta map[string]any
			if err := json.Unmarshal(metaJSON, &meta); err == nil {
				s.Metadata = meta
			}
		}

		sessions = append(sessions, s)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, NewStoreOperationError("遍历会话记录失败", err)
	}

	return sessions, total, nil
}

// RunRetention 执行数据保留策略清理
func (s *PostgresStore) RunRetention(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if !s.config.Retention.Enabled || s.config.Retention.MaxDays <= 0 {
		return nil // 未启用保留策略
	}

	cutoffTime := time.Now().AddDate(0, 0, -s.config.Retention.MaxDays)

	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return NewRetentionError("开始保留策略事务失败", err)
	}
	defer tx.Rollback()

	// 删除过期消息
	result, err := tx.ExecContext(ctx, "DELETE FROM audit_messages WHERE created_at < $1", cutoffTime)
	if err != nil {
		return NewRetentionError("删除过期消息失败", err)
	}

	deletedMessages, _ := result.RowsAffected()

	// 删除没有消息的会话
	result, err = tx.ExecContext(ctx, `
		DELETE FROM audit_sessions
		WHERE session_id NOT IN (SELECT DISTINCT session_id FROM audit_messages)`)
	if err != nil {
		return NewRetentionError("删除空会话失败", err)
	}

	deletedSessions, _ := result.RowsAffected()

	if err := tx.Commit(); err != nil {
		return NewRetentionError("提交保留策略事务失败", err)
	}

	// 记录清理结果（可选添加日志）
	_ = deletedMessages
	_ = deletedSessions

	return nil
}

// Close 关闭存储连接
func (s *PostgresStore) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.closed {
		return nil
	}

	s.closed = true
	return s.db.Close()
}
