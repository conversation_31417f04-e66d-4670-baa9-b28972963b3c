package knowledge

import (
	"fmt"

	"github.com/agentscope/agentscope-golang/pkg/errors"
)

// KnowledgeBaseType 知识库类型
type KnowledgeBaseType string

const (
	KnowledgeBaseTypeInMemory   KnowledgeBaseType = "inmemory"
	KnowledgeBaseTypePostgreSQL KnowledgeBaseType = "postgresql"
)

// Config 知识库配置
type Config struct {
	Type   KnowledgeBaseType      `yaml:"type" json:"type"`
	Config map[string]interface{} `yaml:"config" json:"config"`
}

// KnowledgeBaseFactory 知识库工厂
type KnowledgeBaseFactory struct {
	creators map[KnowledgeBaseType]KnowledgeBaseCreator
}

// KnowledgeBaseCreator 知识库创建函数
type KnowledgeBaseCreator func(config map[string]interface{}) (KnowledgeBase, error)

// NewKnowledgeBaseFactory 创建知识库工厂
func NewKnowledgeBaseFactory() *KnowledgeBaseFactory {
	factory := &KnowledgeBaseFactory{
		creators: make(map[KnowledgeBaseType]KnowledgeBaseCreator),
	}

	// 注册默认的知识库类型
	factory.RegisterKnowledgeBaseType(KnowledgeBaseTypeInMemory, createInMemoryKnowledgeBase)
	factory.RegisterKnowledgeBaseType(KnowledgeBaseTypePostgreSQL, createPostgresKnowledgeBase)

	return factory
}

// RegisterKnowledgeBaseType 注册知识库类型
func (f *KnowledgeBaseFactory) RegisterKnowledgeBaseType(kbType KnowledgeBaseType, creator KnowledgeBaseCreator) error {
	if creator == nil {
		return errors.NewValidationError("invalid_input", "knowledge base creator cannot be nil")
	}

	f.creators[kbType] = creator
	return nil
}

// CreateKnowledgeBase 创建知识库
func (f *KnowledgeBaseFactory) CreateKnowledgeBase(config *Config) (KnowledgeBase, error) {
	if config == nil {
		return nil, errors.NewValidationError("invalid_input", "knowledge base config cannot be nil")
	}

	creator, exists := f.creators[config.Type]
	if !exists {
		return nil, errors.NewValidationError("unsupported_knowledge_base_type", fmt.Sprintf("unsupported knowledge base type: %s", string(config.Type)))
	}

	kb, err := creator(config.Config)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "creation_failed", "failed to create knowledge base")
	}

	return kb, nil
}

// GetSupportedTypes 获取支持的知识库类型
func (f *KnowledgeBaseFactory) GetSupportedTypes() []KnowledgeBaseType {
	types := make([]KnowledgeBaseType, 0, len(f.creators))
	for kbType := range f.creators {
		types = append(types, kbType)
	}
	return types
}

// createInMemoryKnowledgeBase 创建内存知识库
func createInMemoryKnowledgeBase(config map[string]interface{}) (KnowledgeBase, error) {
	// 内存知识库不需要特殊配置
	return NewInMemoryKnowledgeBase(), nil
}

// createPostgresKnowledgeBase 创建PostgreSQL知识库
func createPostgresKnowledgeBase(cfg map[string]interface{}) (KnowledgeBase, error) {
	dsnRaw, ok := cfg["dsn"]
	if !ok {
		return nil, errors.NewValidationError("missing_dsn", "postgresql knowledge base requires dsn in config")
	}
	dsn, _ := dsnRaw.(string)
	if dsn == "" {
		return nil, errors.NewValidationError("invalid_dsn", "dsn cannot be empty")
	}
	kb, err := NewPostgresKnowledgeBase(dsn)
	if err != nil {
		return nil, err
	}
	return kb, nil
}

// 全局工厂实例
var defaultFactory = NewKnowledgeBaseFactory()

// NewKnowledgeBase 使用默认工厂创建知识库
func NewKnowledgeBase(config *Config) (KnowledgeBase, error) {
	return defaultFactory.CreateKnowledgeBase(config)
}

// RegisterKnowledgeBaseType 在默认工厂中注册知识库类型
func RegisterKnowledgeBaseType(kbType KnowledgeBaseType, creator KnowledgeBaseCreator) error {
	return defaultFactory.RegisterKnowledgeBaseType(kbType, creator)
}

// GetSupportedKnowledgeBaseTypes 获取默认工厂支持的知识库类型
func GetSupportedKnowledgeBaseTypes() []KnowledgeBaseType {
	return defaultFactory.GetSupportedTypes()
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Type == "" {
		return errors.NewValidationError("invalid_input", "knowledge base type cannot be empty")
	}

	// 检查类型是否支持
	supportedTypes := GetSupportedKnowledgeBaseTypes()
	supported := false
	for _, supportedType := range supportedTypes {
		if c.Type == supportedType {
			supported = true
			break
		}
	}

	if !supported {
		return errors.NewValidationError("unsupported knowledge base type: %s", string(c.Type))
	}

	return nil
}

// String 返回配置的字符串表示
func (c *Config) String() string {
	return fmt.Sprintf("KnowledgeBaseConfig{Type: %s}", c.Type)
}

// NewSimpleKnowledgeBase 创建简单的内存知识库（用于测试和简单场景）
func NewSimpleKnowledgeBase() KnowledgeBase {
	return NewInMemoryKnowledgeBase()
}
