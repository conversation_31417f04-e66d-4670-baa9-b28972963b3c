package errors

import (
	"errors"
)

// IsAgentScopeError checks if an error is an AgentScopeError
func IsAgentScopeError(err error) bool {
	var asErr *AgentScopeError
	return errors.As(err, &asErr)
}

// AsAgentScopeError converts an error to AgentScopeError if possible
func AsAgentScopeError(err error) (*AgentScopeError, bool) {
	var asErr *AgentScopeError
	if errors.As(err, &asErr) {
		return asErr, true
	}
	return nil, false
}

// IsType checks if an error is of a specific AgentScope error type
func IsType(err error, errorType ErrorType) bool {
	if asErr, ok := AsAgentScopeError(err); ok {
		return asErr.IsType(errorType)
	}
	return false
}

// IsCode checks if an error has a specific AgentScope error code
func IsCode(err error, code string) bool {
	if asErr, ok := AsAgentScopeError(err); ok {
		return asErr.IsCode(code)
	}
	return false
}

// GetErrorType returns the error type if it's an AgentScopeError
func GetErrorType(err error) (ErrorType, bool) {
	if asErr, ok := AsAgentScopeError(err); ok {
		return asErr.Type, true
	}
	return "", false
}

// GetErrorCode returns the error code if it's an AgentScopeError
func GetErrorCode(err error) (string, bool) {
	if asErr, ok := AsAgentScopeError(err); ok {
		return asErr.Code, true
	}
	return "", false
}

// Chain creates a chain of errors for better error context
func Chain(errs ...error) error {
	var result error
	for _, err := range errs {
		if err != nil {
			if result == nil {
				result = err
			} else {
				if asErr, ok := AsAgentScopeError(result); ok {
					result = asErr.WithCause(err)
				} else {
					result = Wrap(result, ErrorTypeInternal, CodeInternalError, "error chain").WithCause(err)
				}
			}
		}
	}
	return result
}

// IsNotFoundError checks if an error is a not found error
func IsNotFoundError(err error) bool {
	if asErr, ok := AsAgentScopeError(err); ok {
		return asErr.Code == "not_found" || asErr.Code == "entry_not_found" || asErr.Code == "memory_not_found"
	}
	return false
}
