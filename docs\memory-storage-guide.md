# 内存存储使用指南

## 概述

hzAgent 提供了统一的内存存储接口，支持会话消息存储和向量语义搜索功能。本指南详细介绍各种存储后端的特性、使用方法和最佳实践。

## 存储接口

### Store 接口（基础消息存储）
```go
type Store interface {
    Save(ctx context.Context, sessionID string, msg *message.Message) error
    Load(ctx context.Context, sessionID string, limit int) ([]*message.Message, error)
    LoadAll(ctx context.Context, sessionID string) ([]*message.Message, error)
    Delete(ctx context.Context, sessionID string) error
    DeleteMessage(ctx context.Context, sessionID, messageID string) error
    Search(ctx context.Context, sessionID, query string, limit int) ([]*message.Message, error)
    GetSessions(ctx context.Context) ([]string, error)
    Close() error
}
```

### VectorStore 接口（向量语义搜索）
```go
type VectorStore interface {
    SaveVector(ctx context.Context, sessionID, messageID string, vector []float32, metadata map[string]any) error
    SearchSimilar(ctx context.Context, sessionID string, queryVector []float32, limit int, threshold float32) ([]*VectorSearchResult, error)
    DeleteVector(ctx context.Context, sessionID, messageID string) error
    DeleteSessionVectors(ctx context.Context, sessionID string) error
    Close() error
}
```

### EnhancedStore 接口（组合接口）
```go
type EnhancedStore interface {
    Store
    VectorStore
}
```

## 存储后端对比

| 特性 | MemoryStore | SQLiteStore | PostgresStore |
|------|-------------|-------------|---------------|
| **持久化** | ❌ 内存临时 | ✅ 文件持久化 | ✅ 数据库持久化 |
| **向量搜索** | ❌ 不支持 | ✅ 余弦相似度 | ✅ 余弦相似度 |
| **并发安全** | ✅ 读写锁 | ✅ SQLite 锁 | ✅ 事务隔离 |
| **适用规模** | 小型测试 | 中小型应用 | 大型生产 |
| **部署复杂度** | 极简 | 简单 | 中等 |
| **性能** | 极高 | 中等 | 高 |

## SQLite 存储详解

### 基本使用

```go
import "github.com/agentscope/agentscope-golang/pkg/memory"

// 创建 SQLite 存储
store, err := memory.NewSQLiteStore(&memory.SQLiteConfig{
    DatabasePath: "./data/memory.db", // 文件路径
})
if err != nil {
    log.Fatal(err)
}
defer store.Close()

// 保存消息
msg := message.NewUserMessage("你好，世界！")
err = store.Save(ctx, "session-123", msg)

// 加载消息历史
messages, err := store.Load(ctx, "session-123", 10)
```

### 向量存储功能

```go
// 保存向量（通常由 embedding 模型生成）
vector := []float32{0.1, 0.2, 0.3, 0.4, 0.5}
metadata := map[string]any{
    "type": "user_message",
    "timestamp": time.Now(),
}

err = store.SaveVector(ctx, "session-123", "msg-456", vector, metadata)

// 语义搜索
queryVector := []float32{0.1, 0.2, 0.3, 0.4, 0.5}
results, err := store.SearchSimilar(ctx, "session-123", queryVector, 5, 0.7)

for _, result := range results {
    fmt.Printf("消息ID: %s, 相似度: %.3f\n", result.MessageID, result.Score)
}
```

### 性能特性

**优势**：
- ✅ 零配置，单文件部署
- ✅ 完整的 ACID 事务支持
- ✅ 自动创建索引优化查询
- ✅ 支持并发读取
- ✅ 文件级别的备份和恢复

**限制**：
- ⚠️ 向量搜索采用全量扫描（O(n) 复杂度）
- ⚠️ 向量存储为 JSON 格式（存储效率较低）
- ⚠️ 单写入者限制（SQLite 特性）
- ⚠️ 大规模向量集合性能下降

**适用场景**：
- 🎯 开发和测试环境
- 🎯 中小型应用（< 10万条消息，< 1万个向量）
- 🎯 单机部署场景
- 🎯 需要简单部署的原型项目

## PostgreSQL 存储

### 配置示例

```go
store, err := memory.NewPostgresStore(&memory.PostgresConfig{
    Host:     "localhost",
    Port:     5432,
    Database: "hzagent",
    Username: "postgres",
    Password: "password",
    SSLMode:  "disable",
})
```

### 优势
- ✅ 高并发读写支持
- ✅ 丰富的索引类型（B-tree、GIN、GiST）
- ✅ 可扩展的向量搜索（pgvector 扩展）
- ✅ 企业级可靠性和性能
- ✅ 支持分区和分片

## 内存存储

### 使用场景
```go
// 仅用于测试和开发
store := memory.NewMemoryStore()

// 注意：重启后数据丢失
```

## 最佳实践

### 1. 存储选择策略

```go
func NewMemoryStore(env string) (memory.EnhancedStore, error) {
    switch env {
    case "development", "test":
        return memory.NewMemoryStore(), nil
    case "staging":
        return memory.NewSQLiteStore(&memory.SQLiteConfig{
            DatabasePath: "./data/staging.db",
        })
    case "production":
        return memory.NewPostgresStore(&memory.PostgresConfig{
            Host:     os.Getenv("DB_HOST"),
            Database: os.Getenv("DB_NAME"),
            Username: os.Getenv("DB_USER"),
            Password: os.Getenv("DB_PASS"),
        })
    default:
        return nil, fmt.Errorf("unknown environment: %s", env)
    }
}
```

### 2. 向量搜索优化

```go
// 设置合理的相似度阈值
threshold := float32(0.7) // 70% 相似度

// 限制搜索结果数量
limit := 10

// 使用元数据过滤
metadata := map[string]any{
    "type": "important_message",
    "category": "technical",
}

results, err := store.SearchSimilar(ctx, sessionID, queryVector, limit, threshold)
```

### 3. 错误处理

```go
// 检查存储连接
if err := store.Save(ctx, sessionID, msg); err != nil {
    if errors.Is(err, context.DeadlineExceeded) {
        log.Warn("存储操作超时，使用降级策略")
        // 降级到内存存储或缓存
    } else {
        log.Error("存储操作失败: %v", err)
        return err
    }
}
```

### 4. 资源管理

```go
// 确保正确关闭存储连接
defer func() {
    if err := store.Close(); err != nil {
        log.Error("关闭存储连接失败: %v", err)
    }
}()

// 定期清理过期数据
go func() {
    ticker := time.NewTicker(24 * time.Hour)
    defer ticker.Stop()
    
    for range ticker.C {
        if err := store.DeleteOldSessions(ctx, 30*24*time.Hour); err != nil {
            log.Error("清理过期会话失败: %v", err)
        }
    }
}()
```

## 迁移和升级

### 从 SQLite 迁移到 PostgreSQL

```go
func MigrateToPostgres(sqliteStore *memory.SQLiteStore, pgStore *memory.PostgresStore) error {
    ctx := context.Background()
    
    // 获取所有会话
    sessions, err := sqliteStore.GetSessions(ctx)
    if err != nil {
        return err
    }
    
    for _, sessionID := range sessions {
        // 迁移消息
        messages, err := sqliteStore.LoadAll(ctx, sessionID)
        if err != nil {
            continue
        }
        
        for _, msg := range messages {
            if err := pgStore.Save(ctx, sessionID, msg); err != nil {
                log.Error("迁移消息失败: %v", err)
            }
        }
        
        // 迁移向量数据（如果支持）
        // 这里需要实现向量数据的批量迁移逻辑
    }
    
    return nil
}
```

## 监控和调试

### 性能监控

```go
// 添加性能监控
start := time.Now()
results, err := store.SearchSimilar(ctx, sessionID, vector, 10, 0.7)
duration := time.Since(start)

if duration > 100*time.Millisecond {
    log.Warn("向量搜索耗时过长: %v", duration)
}
```

### 调试工具

```go
// 查看向量存储统计
stats, err := store.GetVectorStats(ctx, sessionID)
if err == nil {
    log.Info("会话 %s 向量统计: 数量=%d, 平均维度=%d", 
        sessionID, stats.Count, stats.AvgDimension)
}
```
