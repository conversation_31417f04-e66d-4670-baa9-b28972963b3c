package llm

import (
	"testing"
	"time"
)

func TestFactory_CreateClient(t *testing.T) {
	factory := NewFactory()

	tests := []struct {
		name        string
		provider    *ProviderConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "创建 DeepSeek 客户端",
			provider: &ProviderConfig{
				Type:    "deepseek",
				APIKey:  "test-deepseek-key",
				BaseURL: "https://api.deepseek.com/v1",
				Model:   "deepseek-chat",
				Timeout: 30 * time.Second,
			},
			expectError: false,
		},
		{
			name: "创建 Qwen 客户端",
			provider: &ProviderConfig{
				Type:    "qwen",
				APIKey:  "test-qwen-key",
				BaseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
				Model:   "qwen-plus",
				Timeout: 30 * time.Second,
			},
			expectError: false,
		},
		{
			name: "创建豆包客户端",
			provider: &ProviderConfig{
				Type:    "doubao",
				APIKey:  "test-doubao-key",
				BaseURL: "https://ark.cn-beijing.volces.com/api/v3",
				Model:   "ep-test123",
				Timeout: 30 * time.Second,
			},
			expectError: false,
		},
		{
			name: "不支持的提供商类型",
			provider: &ProviderConfig{
				Type:   "unsupported",
				APIKey: "test-key",
			},
			expectError: true,
			errorMsg:    "不支持的 LLM 提供商类型",
		},
		{
			name:        "空提供商配置",
			provider:    nil,
			expectError: true,
			errorMsg:    "提供商配置不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := factory.CreateClient(tt.provider)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有返回错误")
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但返回了错误: %v", err)
				return
			}

			if client == nil {
				t.Errorf("客户端不应为空")
				return
			}

			// 验证客户端类型
			modelInfo := client.GetModelInfo()
			if modelInfo == nil {
				t.Errorf("模型信息不应为空")
				return
			}

			switch tt.provider.Type {
			case "deepseek":
				if modelInfo.OwnedBy != "deepseek" {
					t.Errorf("DeepSeek 客户端模型所有者应为 'deepseek'，实际 '%s'", modelInfo.OwnedBy)
				}
			case "qwen":
				if modelInfo.OwnedBy != "alibaba" {
					t.Errorf("Qwen 客户端模型所有者应为 'alibaba'，实际 '%s'", modelInfo.OwnedBy)
				}
			case "doubao":
				if modelInfo.OwnedBy != "bytedance" {
					t.Errorf("豆包客户端模型所有者应为 'bytedance'，实际 '%s'", modelInfo.OwnedBy)
				}
			}

			// 关闭客户端
			client.Close()
		})
	}
}

func TestFactory_CreateFromConfig(t *testing.T) {
	factory := NewFactory()

	// 创建测试配置
	llmConfig := &LLMFactoryConfig{
		DefaultProvider: "qwen",
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Providers: map[string]*ProviderConfig{
			"deepseek": {
				Type:    "deepseek",
				APIKey:  "test-deepseek-key",
				BaseURL: "https://api.deepseek.com/v1",
				Model:   "deepseek-chat",
			},
			"qwen": {
				Type:    "qwen",
				APIKey:  "test-qwen-key",
				BaseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
				Model:   "qwen-plus",
			},
			"doubao": {
				Type:    "doubao",
				APIKey:  "test-doubao-key",
				BaseURL: "https://ark.cn-beijing.volces.com/api/v3",
				Model:   "ep-test123",
			},
		},
		Parameters: map[string]interface{}{
			"temperature": 0.7,
			"max_tokens":  2048,
		},
	}

	tests := []struct {
		name         string
		config       *LLMFactoryConfig
		providerName string
		expectError  bool
		errorMsg     string
	}{
		{
			name:         "使用默认提供商",
			config:       llmConfig,
			providerName: "",
			expectError:  false,
		},
		{
			name:         "指定 DeepSeek 提供商",
			config:       llmConfig,
			providerName: "deepseek",
			expectError:  false,
		},
		{
			name:         "指定豆包提供商",
			config:       llmConfig,
			providerName: "doubao",
			expectError:  false,
		},
		{
			name:         "不存在的提供商",
			config:       llmConfig,
			providerName: "nonexistent",
			expectError:  true,
			errorMsg:     "找不到 LLM 提供商",
		},
		{
			name:         "空配置",
			config:       nil,
			providerName: "qwen",
			expectError:  true,
			errorMsg:     "LLM 配置不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := factory.CreateFromConfig(tt.config, tt.providerName)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有返回错误")
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但返回了错误: %v", err)
				return
			}

			if client == nil {
				t.Errorf("客户端不应为空")
				return
			}

			// 验证客户端
			modelInfo := client.GetModelInfo()
			if modelInfo == nil {
				t.Errorf("模型信息不应为空")
			}

			// 关闭客户端
			client.Close()
		})
	}
}

func TestFactory_GetSupportedProviders(t *testing.T) {
	factory := NewFactory()
	providers := factory.GetSupportedProviders()

	expectedProviders := []string{"deepseek", "qwen", "doubao"}
	if len(providers) != len(expectedProviders) {
		t.Errorf("支持的提供商数量不匹配，期望 %d，实际 %d", len(expectedProviders), len(providers))
	}

	for _, expected := range expectedProviders {
		found := false
		for _, provider := range providers {
			if provider == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("缺少支持的提供商: %s", expected)
		}
	}
}

func TestFactory_ValidateProviderConfig(t *testing.T) {
	factory := NewFactory()

	tests := []struct {
		name        string
		provider    *ProviderConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "有效的 DeepSeek 配置",
			provider: &ProviderConfig{
				Type:   "deepseek",
				APIKey: "test-key",
			},
			expectError: false,
		},
		{
			name: "有效的 Qwen 配置",
			provider: &ProviderConfig{
				Type:   "qwen",
				APIKey: "test-key",
			},
			expectError: false,
		},
		{
			name: "豆包配置缺少模型",
			provider: &ProviderConfig{
				Type:   "doubao",
				APIKey: "test-key",
			},
			expectError: true,
			errorMsg:    "豆包提供商必须指定模型",
		},
		{
			name: "有效的豆包配置",
			provider: &ProviderConfig{
				Type:   "doubao",
				APIKey: "test-key",
				Model:  "ep-test123",
			},
			expectError: false,
		},
		{
			name: "不支持的提供商类型",
			provider: &ProviderConfig{
				Type:   "unsupported",
				APIKey: "test-key",
			},
			expectError: true,
			errorMsg:    "不支持的提供商类型",
		},
		{
			name:        "空配置",
			provider:    nil,
			expectError: true,
			errorMsg:    "提供商配置不能为空",
		},
		{
			name: "缺少提供商类型",
			provider: &ProviderConfig{
				APIKey: "test-key",
			},
			expectError: true,
			errorMsg:    "提供商类型不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := factory.ValidateProviderConfig(tt.provider)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有返回错误")
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但返回了错误: %v", err)
				return
			}

			// 验证默认值是否已设置
			if tt.provider != nil {
				switch tt.provider.Type {
				case "deepseek":
					if tt.provider.BaseURL == "" {
						t.Errorf("DeepSeek 默认 BaseURL 未设置")
					}
					if tt.provider.Model == "" {
						t.Errorf("DeepSeek 默认模型未设置")
					}
				case "qwen":
					if tt.provider.BaseURL == "" {
						t.Errorf("Qwen 默认 BaseURL 未设置")
					}
					if tt.provider.Model == "" {
						t.Errorf("Qwen 默认模型未设置")
					}
				case "doubao":
					if tt.provider.BaseURL == "" {
						t.Errorf("豆包默认 BaseURL 未设置")
					}
				}

				if tt.provider.Timeout <= 0 {
					t.Errorf("默认超时时间未设置")
				}
				if tt.provider.MaxRetries < 0 {
					t.Errorf("默认重试次数设置错误")
				}
				if tt.provider.Parameters == nil {
					t.Errorf("默认参数映射未初始化")
				}
			}
		})
	}
}

func TestCreateClientByType(t *testing.T) {
	tests := []struct {
		name        string
		clientType  string
		config      *Config
		expectError bool
		errorMsg    string
	}{
		{
			name:       "创建 DeepSeek 客户端",
			clientType: "deepseek",
			config: &Config{
				APIKey: "test-key",
			},
			expectError: false,
		},
		{
			name:       "创建 Qwen 客户端",
			clientType: "qwen",
			config: &Config{
				APIKey: "test-key",
			},
			expectError: false,
		},
		{
			name:       "创建豆包客户端",
			clientType: "doubao",
			config: &Config{
				APIKey: "test-key",
			},
			expectError: false,
		},
		{
			name:        "不支持的客户端类型",
			clientType:  "unsupported",
			config:      &Config{APIKey: "test-key"},
			expectError: true,
			errorMsg:    "不支持的 LLM 客户端类型",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := CreateClientByType(tt.clientType, tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有返回错误")
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但返回了错误: %v", err)
				return
			}

			if client == nil {
				t.Errorf("客户端不应为空")
				return
			}

			// 关闭客户端
			client.Close()
		})
	}
}
