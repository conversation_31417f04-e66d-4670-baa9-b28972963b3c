package builtin

import (
	"context"
	"fmt"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// TimeTool 时间查询工具
type TimeTool struct{}

// NewTimeTool 创建时间工具
func NewTimeTool() *TimeTool {
	return &TimeTool{}
}

// Name 返回工具名称
func (t *TimeTool) Name() string {
	return "time"
}

// Description 返回工具描述
func (t *TimeTool) Description() string {
	return "Get current time information in various formats"
}

// Schema 返回工具参数模式
func (t *TimeTool) Schema() *tool.JSONSchema {
	return &tool.JSONSchema{
		Type: "object",
		Properties: map[string]*tool.JSONSchema{
			"format": {
				Type:        "string",
				Description: "Time format: 'timestamp', 'iso', 'rfc3339', 'unix', or custom Go time format",
				Enum:        []any{"timestamp", "iso", "rfc3339", "unix"},
			},
			"timezone": {
				Type:        "string",
				Description: "Timezone (e.g., 'UTC', 'Asia/Shanghai', 'America/New_York')",
			},
		},
		Description: "Get current time in specified format and timezone",
	}
}

// Execute 执行时间查询
func (t *TimeTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	format := "iso"
	if f, ok := params["format"].(string); ok {
		format = f
	}

	timezone := "UTC"
	if tz, ok := params["timezone"].(string); ok {
		timezone = tz
	}

	// 解析时区
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("invalid timezone: %v", err)
	}

	now := time.Now().In(loc)
	var result any

	switch format {
	case "timestamp":
		result = now.Format("2006-01-02 15:04:05")
	case "iso":
		result = now.Format(time.RFC3339)
	case "rfc3339":
		result = now.Format(time.RFC3339)
	case "unix":
		result = now.Unix()
	default:
		// 自定义格式
		result = now.Format(format)
	}

	return map[string]any{
		"format":   format,
		"timezone": timezone,
		"time":     result,
		"unix":     now.Unix(),
		"iso":      now.Format(time.RFC3339),
	}, nil
}
