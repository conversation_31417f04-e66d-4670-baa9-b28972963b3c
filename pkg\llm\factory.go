package llm

import (
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
)

// ProviderConfig LLM 提供商配置（避免循环导入）
type ProviderConfig struct {
	Type       string                 `yaml:"type" json:"type"`
	APIKey     string                 `yaml:"api_key,omitempty" json:"api_key,omitempty"`
	BaseURL    string                 `yaml:"base_url,omitempty" json:"base_url,omitempty"`
	Model      string                 `yaml:"model,omitempty" json:"model,omitempty"`
	Timeout    time.Duration          `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	MaxRetries int                    `yaml:"max_retries,omitempty" json:"max_retries,omitempty"`
	Parameters map[string]interface{} `yaml:"parameters,omitempty" json:"parameters,omitempty"`
}

// LLMFactoryConfig LLM 工厂配置（避免循环导入）
type LLMFactoryConfig struct {
	DefaultProvider string                     `yaml:"default_provider" json:"default_provider"`
	Providers       map[string]*ProviderConfig `yaml:"providers,omitempty" json:"providers,omitempty"`
	Timeout         time.Duration              `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	MaxRetries      int                        `yaml:"max_retries,omitempty" json:"max_retries,omitempty"`
	Parameters      map[string]interface{}     `yaml:"parameters,omitempty" json:"parameters,omitempty"`
}

// Factory LLM 客户端工厂
type Factory struct{}

// NewFactory 创建新的 LLM 工厂实例
func NewFactory() *Factory {
	return &Factory{}
}

// CreateClient 根据提供商配置创建 LLM 客户端
func (f *Factory) CreateClient(provider *ProviderConfig) (LLMClient, error) {
	if provider == nil {
		return nil, errors.NewValidationError(errors.CodeInvalidInput, "提供商配置不能为空")
	}

	// 转换为 LLM 配置
	llmConfig := &Config{
		Provider:   provider.Type,
		APIKey:     provider.APIKey,
		BaseURL:    provider.BaseURL,
		Model:      provider.Model,
		Timeout:    provider.Timeout,
		MaxRetries: provider.MaxRetries,
		Parameters: provider.Parameters,
	}

	// 根据提供商类型创建客户端
	var client LLMClient
	var err error

	switch strings.ToLower(provider.Type) {
	case "deepseek":
		client, err = NewDeepseekClient(llmConfig)
	case "qwen":
		client, err = NewQwenClient(llmConfig)
	case "doubao":
		client, err = NewDoubaoClient(llmConfig)
	default:
		return nil, errors.NewValidationError(errors.CodeInvalidInput,
			"不支持的 LLM 提供商类型: "+provider.Type)
	}

	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, errors.CodeLLMRequestFailed,
			"创建 LLM 客户端失败")
	}

	// 使用重试客户端包装
	retryConfig := toRetryConfig(provider)
	return NewRetryableClient(client, retryConfig), nil
}

// CreateFromConfig 根据全局 LLM 配置和提供商名称创建客户端
func (f *Factory) CreateFromConfig(llmConfig *LLMFactoryConfig, providerName string) (LLMClient, error) {
	if llmConfig == nil {
		return nil, errors.NewValidationError(errors.CodeInvalidInput, "LLM 配置不能为空")
	}

	// 如果未指定提供商名称，使用默认提供商
	if providerName == "" {
		providerName = llmConfig.DefaultProvider
	}

	// 查找提供商配置
	provider, exists := llmConfig.Providers[providerName]
	if !exists {
		return nil, errors.NewValidationError(errors.CodeInvalidInput,
			"找不到 LLM 提供商: "+providerName)
	}

	// 合并全局配置和提供商配置
	mergedProvider := f.mergeProviderConfig(llmConfig, provider)

	// 创建客户端
	return f.CreateClient(mergedProvider)
}

// CreateDefault 创建默认的 LLM 客户端（DeepSeek）
func (f *Factory) CreateDefault() (LLMClient, error) {
	defaultConfig := DefaultConfig()
	return NewDeepseekClient(defaultConfig)
}

// mergeProviderConfig 合并全局配置和提供商配置
func (f *Factory) mergeProviderConfig(global *LLMFactoryConfig, provider *ProviderConfig) *ProviderConfig {
	merged := &ProviderConfig{
		Type:       provider.Type,
		APIKey:     provider.APIKey,
		BaseURL:    provider.BaseURL,
		Model:      provider.Model,
		Timeout:    provider.Timeout,
		MaxRetries: provider.MaxRetries,
		Parameters: make(map[string]interface{}),
	}

	// 如果提供商配置中没有设置，使用全局配置
	if merged.Timeout <= 0 && global.Timeout > 0 {
		merged.Timeout = global.Timeout
	}
	if merged.MaxRetries <= 0 && global.MaxRetries > 0 {
		merged.MaxRetries = global.MaxRetries
	}

	// 合并参数：先复制全局参数，再覆盖提供商参数
	if global.Parameters != nil {
		for k, v := range global.Parameters {
			merged.Parameters[k] = v
		}
	}
	if provider.Parameters != nil {
		for k, v := range provider.Parameters {
			merged.Parameters[k] = v
		}
	}

	return merged
}

// toRetryConfig 将提供商配置转换为重试配置
func toRetryConfig(provider *ProviderConfig) *RetryConfig {
	retryConfig := DefaultRetryConfig()

	if provider.MaxRetries > 0 {
		retryConfig.MaxRetries = provider.MaxRetries
	}

	// 根据提供商类型调整重试策略
	switch strings.ToLower(provider.Type) {
	case "qwen":
		// Qwen 可能有不同的限流策略
		retryConfig.InitialDelay = 2 * time.Second
		retryConfig.MaxDelay = 60 * time.Second
	case "doubao":
		// 豆包可能有不同的限流策略
		retryConfig.InitialDelay = 1 * time.Second
		retryConfig.MaxDelay = 30 * time.Second
	default:
		// 使用默认配置
	}

	return retryConfig
}

// GetSupportedProviders 返回支持的提供商列表
func (f *Factory) GetSupportedProviders() []string {
	return []string{"deepseek", "qwen", "doubao"}
}

// ValidateProviderConfig 验证提供商配置
func (f *Factory) ValidateProviderConfig(provider *ProviderConfig) error {
	if provider == nil {
		return errors.NewValidationError(errors.CodeInvalidInput, "提供商配置不能为空")
	}

	if provider.Type == "" {
		return errors.NewValidationError(errors.CodeInvalidInput, "提供商类型不能为空")
	}

	// 检查是否支持该提供商
	supported := false
	for _, supportedProvider := range f.GetSupportedProviders() {
		if strings.ToLower(provider.Type) == supportedProvider {
			supported = true
			break
		}
	}

	if !supported {
		return errors.NewValidationError(errors.CodeInvalidInput,
			"不支持的提供商类型: "+provider.Type)
	}

	// 验证必需的字段
	switch strings.ToLower(provider.Type) {
	case "deepseek":
		if provider.BaseURL == "" {
			provider.BaseURL = "https://api.deepseek.com/v1"
		}
		if provider.Model == "" {
			provider.Model = "deepseek-chat"
		}
	case "qwen":
		if provider.BaseURL == "" {
			provider.BaseURL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
		}
		if provider.Model == "" {
			provider.Model = "qwen-plus"
		}
	case "doubao":
		if provider.BaseURL == "" {
			provider.BaseURL = "https://ark.cn-beijing.volces.com/api/v3"
		}
		if provider.Model == "" {
			return errors.NewValidationError(errors.CodeInvalidInput,
				"豆包提供商必须指定模型（通常是 Endpoint ID，如 ep-xxxxxx）")
		}
	}

	// 设置默认值
	if provider.Timeout <= 0 {
		provider.Timeout = 30 * time.Second
	}
	if provider.MaxRetries < 0 {
		provider.MaxRetries = 3
	}
	if provider.Parameters == nil {
		provider.Parameters = make(map[string]interface{})
	}

	return nil
}

// 全局工厂实例
var globalFactory = NewFactory()

// NewClientFromProvider 使用全局工厂创建客户端（便捷函数）
func NewClientFromProvider(provider *ProviderConfig) (LLMClient, error) {
	return globalFactory.CreateClient(provider)
}

// NewFromConfig 使用全局工厂从配置创建客户端（便捷函数）
func NewFromConfig(llmConfig *LLMFactoryConfig, providerName string) (LLMClient, error) {
	return globalFactory.CreateFromConfig(llmConfig, providerName)
}

// GetSupportedProviders 返回支持的提供商列表（便捷函数）
func GetSupportedProviders() []string {
	return globalFactory.GetSupportedProviders()
}

// ValidateProviderConfig 验证提供商配置（便捷函数）
func ValidateProviderConfig(provider *ProviderConfig) error {
	return globalFactory.ValidateProviderConfig(provider)
}

// CreateClientByType 根据类型字符串创建客户端（便捷函数，用于向后兼容）
func CreateClientByType(clientType string, llmConfig *Config) (LLMClient, error) {
	if llmConfig == nil {
		llmConfig = DefaultConfig()
	}

	// 设置提供商类型
	llmConfig.Provider = clientType

	switch strings.ToLower(clientType) {
	case "deepseek":
		return NewDeepseekClient(llmConfig)
	case "qwen":
		return NewQwenClient(llmConfig)
	case "doubao":
		return NewDoubaoClient(llmConfig)
	default:
		return nil, errors.NewValidationError(errors.CodeInvalidInput,
			"不支持的 LLM 客户端类型: "+clientType)
	}
}
