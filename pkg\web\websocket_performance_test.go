package web

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/gorilla/websocket"
)

// TestWebSocketPerformanceConfig 测试WebSocket性能配置
func TestWebSocketPerformanceConfig(t *testing.T) {
	config := DefaultWebSocketPerformanceConfig

	if config.MaxBatchSize <= 0 {
		t.Error("MaxBatchSize should be positive")
	}

	if config.BatchTimeout <= 0 {
		t.Error("BatchTimeout should be positive")
	}

	if config.WriteBufferSize <= 0 {
		t.Error("WriteBufferSize should be positive")
	}

	if config.ReadBufferSize <= 0 {
		t.Error("ReadBufferSize should be positive")
	}
}

// TestWebSocketBatchSending 测试WebSocket批量发送
func TestWebSocketBatchSending(t *testing.T) {
	// 创建测试服务器
	agentMgr := agent.NewManager()
	webConfig := &config.WebConfig{
		CORS: &config.CORSConfig{
			Enabled:        true,
			AllowedOrigins: []string{"*"},
		},
	}

	wsManager := NewWebSocketManager(agentMgr, webConfig)

	// 创建HTTP测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		wsManager.HandleWebSocket(w, r)
	}))
	defer server.Close()

	// 将HTTP URL转换为WebSocket URL
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")

	// 连接WebSocket，设置Origin头
	headers := http.Header{}
	headers.Set("Origin", "http://localhost")
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, headers)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}
	defer conn.Close()

	// 测试批量发送
	messageCount := 20
	messages := make([]WebSocketMessage, messageCount)

	for i := 0; i < messageCount; i++ {
		messages[i] = WebSocketMessage{
			Type: "test",
			Data: map[string]interface{}{
				"index":   i,
				"content": fmt.Sprintf("Test message %d", i),
			},
			Timestamp: time.Now(),
		}
	}

	// 发送消息
	start := time.Now()
	for _, msg := range messages {
		data, _ := json.Marshal(msg)
		if err := conn.WriteMessage(websocket.TextMessage, data); err != nil {
			t.Errorf("Failed to send message: %v", err)
		}
	}
	sendDuration := time.Since(start)

	// 接收响应
	receivedCount := 0
	for i := 0; i < messageCount; i++ {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
		receivedCount++
	}

	t.Logf("发送 %d 条消息耗时: %v", messageCount, sendDuration)
	t.Logf("接收到 %d 条响应", receivedCount)
}

// BenchmarkWebSocketBatchSending 基准测试WebSocket批量发送
func BenchmarkWebSocketBatchSending(b *testing.B) {
	// 创建模拟的WebSocketClient
	client := &WebSocketClient{
		id:          "test-client",
		perfConfig:  DefaultWebSocketPerformanceConfig,
		batchBuffer: bytes.NewBuffer(make([]byte, 0, DefaultWebSocketPerformanceConfig.WriteBufferSize)),
	}

	// 准备测试消息
	testMessage := WebSocketMessage{
		Type: "test",
		Data: map[string]interface{}{
			"content": "This is a test message for benchmarking",
		},
		Timestamp: time.Now(),
	}

	messageData, _ := json.Marshal(testMessage)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		client.batchMutex.Lock()

		// 模拟添加消息到批量缓冲区
		if client.batchBuffer.Len() > 0 {
			client.batchBuffer.WriteByte('\n')
		}
		client.batchBuffer.Write(messageData)

		// 检查是否需要刷新
		messageCount := bytes.Count(client.batchBuffer.Bytes(), []byte{'\n'}) + 1
		if messageCount >= client.perfConfig.MaxBatchSize {
			client.batchBuffer.Reset()
		}

		client.batchMutex.Unlock()
	}
}

// BenchmarkWebSocketSingleSending 基准测试WebSocket单条发送
func BenchmarkWebSocketSingleSending(b *testing.B) {
	// 准备测试消息
	testMessage := WebSocketMessage{
		Type: "test",
		Data: map[string]interface{}{
			"content": "This is a test message for benchmarking",
		},
		Timestamp: time.Now(),
	}

	messageData, _ := json.Marshal(testMessage)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 模拟单条消息处理
		buffer := bytes.NewBuffer(make([]byte, 0, len(messageData)))
		buffer.Write(messageData)
		buffer.Reset()
	}
}

// TestWebSocketBufferManagement 测试WebSocket缓冲区管理
func TestWebSocketBufferManagement(t *testing.T) {
	client := &WebSocketClient{
		id:          "test-client",
		perfConfig:  DefaultWebSocketPerformanceConfig,
		batchBuffer: bytes.NewBuffer(make([]byte, 0, DefaultWebSocketPerformanceConfig.WriteBufferSize)),
	}

	// 测试添加消息
	testMessages := [][]byte{
		[]byte(`{"type":"test1","data":"message1"}`),
		[]byte(`{"type":"test2","data":"message2"}`),
		[]byte(`{"type":"test3","data":"message3"}`),
	}

	for _, msg := range testMessages {
		client.batchMutex.Lock()

		if client.batchBuffer.Len() > 0 {
			client.batchBuffer.WriteByte('\n')
		}
		client.batchBuffer.Write(msg)

		client.batchMutex.Unlock()
	}

	// 验证缓冲区内容
	client.batchMutex.Lock()
	bufferContent := client.batchBuffer.String()
	client.batchMutex.Unlock()

	expectedParts := []string{
		`{"type":"test1","data":"message1"}`,
		`{"type":"test2","data":"message2"}`,
		`{"type":"test3","data":"message3"}`,
	}

	for _, part := range expectedParts {
		if !strings.Contains(bufferContent, part) {
			t.Errorf("Buffer should contain: %s", part)
		}
	}

	// 验证分隔符
	separatorCount := strings.Count(bufferContent, "\n")
	if separatorCount != len(testMessages)-1 {
		t.Errorf("Expected %d separators, got %d", len(testMessages)-1, separatorCount)
	}
}

// TestWebSocketConcurrentAccess 测试WebSocket并发访问
func TestWebSocketConcurrentAccess(t *testing.T) {
	client := &WebSocketClient{
		id:          "test-client",
		perfConfig:  DefaultWebSocketPerformanceConfig,
		batchBuffer: bytes.NewBuffer(make([]byte, 0, DefaultWebSocketPerformanceConfig.WriteBufferSize)),
	}

	const goroutineCount = 10
	const messagesPerGoroutine = 100

	var wg sync.WaitGroup
	wg.Add(goroutineCount)

	// 并发添加消息
	for i := 0; i < goroutineCount; i++ {
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < messagesPerGoroutine; j++ {
				message := fmt.Sprintf(`{"type":"test","data":"goroutine_%d_message_%d"}`, goroutineID, j)

				client.batchMutex.Lock()
				if client.batchBuffer.Len() > 0 {
					client.batchBuffer.WriteByte('\n')
				}
				client.batchBuffer.Write([]byte(message))
				client.batchMutex.Unlock()
			}
		}(i)
	}

	wg.Wait()

	// 验证没有数据竞争
	client.batchMutex.Lock()
	bufferLen := client.batchBuffer.Len()
	client.batchMutex.Unlock()

	if bufferLen == 0 {
		t.Error("Buffer should not be empty after concurrent writes")
	}

	t.Logf("并发测试完成，缓冲区大小: %d 字节", bufferLen)
}

// TestWebSocketPerformanceComparison 测试WebSocket性能对比
func TestWebSocketPerformanceComparison(t *testing.T) {
	messageCount := 1000

	// 准备测试消息
	testMessage := WebSocketMessage{
		Type: "performance_test",
		Data: map[string]interface{}{
			"content": "This is a performance test message with some content to simulate real usage",
			"index":   0,
		},
		Timestamp: time.Now(),
	}

	messageData, _ := json.Marshal(testMessage)

	// 测试批量发送性能
	t.Run("BatchSending", func(t *testing.T) {
		client := &WebSocketClient{
			id:          "batch-client",
			perfConfig:  DefaultWebSocketPerformanceConfig,
			batchBuffer: bytes.NewBuffer(make([]byte, 0, DefaultWebSocketPerformanceConfig.WriteBufferSize)),
		}

		start := time.Now()

		for i := 0; i < messageCount; i++ {
			client.batchMutex.Lock()

			if client.batchBuffer.Len() > 0 {
				client.batchBuffer.WriteByte('\n')
			}
			client.batchBuffer.Write(messageData)

			// 模拟批量刷新
			messageCount := bytes.Count(client.batchBuffer.Bytes(), []byte{'\n'}) + 1
			if messageCount >= client.perfConfig.MaxBatchSize {
				client.batchBuffer.Reset()
			}

			client.batchMutex.Unlock()
		}

		duration := time.Since(start)
		t.Logf("批量发送 %d 条消息耗时: %v (%.2f msg/ms)",
			messageCount, duration, float64(messageCount)/float64(duration.Milliseconds()))
	})

	// 测试单条发送性能
	t.Run("SingleSending", func(t *testing.T) {
		start := time.Now()

		for i := 0; i < messageCount; i++ {
			// 模拟单条消息处理
			buffer := bytes.NewBuffer(make([]byte, 0, len(messageData)))
			buffer.Write(messageData)
			// 模拟立即发送
			buffer.Reset()
		}

		duration := time.Since(start)
		t.Logf("单条发送 %d 条消息耗时: %v (%.2f msg/ms)",
			messageCount, duration, float64(messageCount)/float64(duration.Milliseconds()))
	})
}
