package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
)

// JSONSchema 表示 JSON Schema 定义
type JSONSchema struct {
	Type        string                 `json:"type"`
	Properties  map[string]*JSONSchema `json:"properties,omitempty"`
	Items       *JSONSchema            `json:"items,omitempty"`
	Required    []string               `json:"required,omitempty"`
	Description string                 `json:"description,omitempty"`
	Enum        []any                  `json:"enum,omitempty"`
	Format      string                 `json:"format,omitempty"`
	Minimum     *float64               `json:"minimum,omitempty"`
	Maximum     *float64               `json:"maximum,omitempty"`
	MinLength   *int                   `json:"minLength,omitempty"`
	MaxLength   *int                   `json:"maxLength,omitempty"`
}

// Tool 表示统一的工具接口
type Tool interface {
	// Name 返回工具名称
	Name() string
	// Description 返回工具描述
	Description() string
	// Schema 返回工具参数的 JSON Schema
	Schema() *JSONSchema
	// Execute 执行工具，使用map[string]any作为参数
	Execute(ctx context.Context, params map[string]any) (any, error)
}

// Registry 表示工具注册表接口
type Registry interface {
	// Register 注册工具
	Register(tool Tool) error
	// Get 获取工具
	Get(name string) (Tool, bool)
	// List 列出所有工具名称
	List() []string
	// Unregister 注销工具
	Unregister(name string) bool
	// Clear 清空所有工具
	Clear()
	// Size 返回注册的工具数量
	Size() int
}

// DefaultRegistry 默认工具注册表实现
type DefaultRegistry struct {
	tools map[string]Tool
	mu    sync.RWMutex
}

// NewRegistry 创建新的工具注册表
func NewRegistry() *DefaultRegistry {
	return &DefaultRegistry{
		tools: make(map[string]Tool),
	}
}

// Register 注册工具
func (r *DefaultRegistry) Register(tool Tool) error {
	if tool == nil {
		return fmt.Errorf("工具不能为空")
	}

	name := tool.Name()
	if name == "" {
		return fmt.Errorf("工具名称不能为空")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.tools[name]; exists {
		return fmt.Errorf("工具 %s 已存在", name)
	}

	r.tools[name] = tool
	return nil
}

// Get 获取工具
func (r *DefaultRegistry) Get(name string) (Tool, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	tool, exists := r.tools[name]
	return tool, exists
}

// List 列出所有工具名称
func (r *DefaultRegistry) List() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	names := make([]string, 0, len(r.tools))
	for name := range r.tools {
		names = append(names, name)
	}

	return names
}

// Unregister 注销工具
func (r *DefaultRegistry) Unregister(name string) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.tools[name]; exists {
		delete(r.tools, name)
		return true
	}

	return false
}

// Clear 清空所有工具
func (r *DefaultRegistry) Clear() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.tools = make(map[string]Tool)
}

// Size 返回注册的工具数量
func (r *DefaultRegistry) Size() int {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return len(r.tools)
}

// ValidateInput 验证输入参数是否符合 JSON Schema
func ValidateInput(schema *JSONSchema, input string) error {
	if schema == nil {
		return nil // 没有 schema 则不验证
	}

	// 解析输入 JSON
	var data any
	if err := json.Unmarshal([]byte(input), &data); err != nil {
		return fmt.Errorf("输入不是有效的 JSON: %w", err)
	}

	// 验证数据类型
	return validateValue(schema, data, "")
}

// validateValue 验证值是否符合 schema
func validateValue(schema *JSONSchema, value any, path string) error {
	if schema == nil {
		return nil
	}

	// 检查类型
	if err := validateType(schema, value, path); err != nil {
		return err
	}

	// 检查枚举值
	if len(schema.Enum) > 0 {
		found := false
		for _, enumValue := range schema.Enum {
			if value == enumValue {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("路径 %s: 值不在枚举范围内", path)
		}
	}

	// 根据类型进行具体验证
	switch schema.Type {
	case "object":
		return validateObject(schema, value, path)
	case "array":
		return validateArray(schema, value, path)
	case "string":
		return validateString(schema, value, path)
	case "number", "integer":
		return validateNumber(schema, value, path)
	}

	return nil
}

// validateType 验证基本类型
func validateType(schema *JSONSchema, value any, path string) error {
	switch schema.Type {
	case "object":
		if _, ok := value.(map[string]any); !ok {
			return fmt.Errorf("路径 %s: 期望对象类型，得到 %T", path, value)
		}
	case "array":
		if _, ok := value.([]any); !ok {
			return fmt.Errorf("路径 %s: 期望数组类型，得到 %T", path, value)
		}
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("路径 %s: 期望字符串类型，得到 %T", path, value)
		}
	case "number":
		switch value.(type) {
		case float64, int, int64:
			// 有效的数字类型
		default:
			return fmt.Errorf("路径 %s: 期望数字类型，得到 %T", path, value)
		}
	case "integer":
		switch v := value.(type) {
		case float64:
			if v != float64(int64(v)) {
				return fmt.Errorf("路径 %s: 期望整数类型，得到浮点数", path)
			}
		case int, int64:
			// 有效的整数类型
		default:
			return fmt.Errorf("路径 %s: 期望整数类型，得到 %T", path, value)
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("路径 %s: 期望布尔类型，得到 %T", path, value)
		}
	}

	return nil
}

// validateObject 验证对象类型
func validateObject(schema *JSONSchema, value any, path string) error {
	obj, ok := value.(map[string]any)
	if !ok {
		return fmt.Errorf("路径 %s: 期望对象类型", path)
	}

	// 检查必需字段
	for _, required := range schema.Required {
		if _, exists := obj[required]; !exists {
			return fmt.Errorf("路径 %s: 缺少必需字段 %s", path, required)
		}
	}

	// 验证属性
	for key, val := range obj {
		if propSchema, exists := schema.Properties[key]; exists {
			propPath := path + "." + key
			if path == "" {
				propPath = key
			}
			if err := validateValue(propSchema, val, propPath); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateArray 验证数组类型
func validateArray(schema *JSONSchema, value any, path string) error {
	arr, ok := value.([]any)
	if !ok {
		return fmt.Errorf("路径 %s: 期望数组类型", path)
	}

	// 验证数组元素
	if schema.Items != nil {
		for i, item := range arr {
			itemPath := fmt.Sprintf("%s[%d]", path, i)
			if err := validateValue(schema.Items, item, itemPath); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateString 验证字符串类型
func validateString(schema *JSONSchema, value any, path string) error {
	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("路径 %s: 期望字符串类型", path)
	}

	// 检查长度限制
	if schema.MinLength != nil && len(str) < *schema.MinLength {
		return fmt.Errorf("路径 %s: 字符串长度不能少于 %d", path, *schema.MinLength)
	}

	if schema.MaxLength != nil && len(str) > *schema.MaxLength {
		return fmt.Errorf("路径 %s: 字符串长度不能超过 %d", path, *schema.MaxLength)
	}

	return nil
}

// validateNumber 验证数字类型
func validateNumber(schema *JSONSchema, value any, path string) error {
	var num float64

	switch v := value.(type) {
	case float64:
		num = v
	case int:
		num = float64(v)
	case int64:
		num = float64(v)
	default:
		return fmt.Errorf("路径 %s: 期望数字类型", path)
	}

	// 检查数值范围
	if schema.Minimum != nil && num < *schema.Minimum {
		return fmt.Errorf("路径 %s: 数值不能小于 %f", path, *schema.Minimum)
	}

	if schema.Maximum != nil && num > *schema.Maximum {
		return fmt.Errorf("路径 %s: 数值不能大于 %f", path, *schema.Maximum)
	}

	return nil
}

// 全局默认注册表
var defaultRegistry = NewRegistry()

// Register 向默认注册表注册工具
func Register(tool Tool) error {
	return defaultRegistry.Register(tool)
}

// Get 从默认注册表获取工具
func Get(name string) (Tool, bool) {
	return defaultRegistry.Get(name)
}

// List 列出默认注册表中的所有工具
func List() []string {
	return defaultRegistry.List()
}

// Unregister 从默认注册表注销工具
func Unregister(name string) bool {
	return defaultRegistry.Unregister(name)
}

// Clear 清空默认注册表
func Clear() {
	defaultRegistry.Clear()
}

// Size 返回默认注册表中的工具数量
func Size() int {
	return defaultRegistry.Size()
}
