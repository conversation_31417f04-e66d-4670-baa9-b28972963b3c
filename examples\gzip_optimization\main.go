package main

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/web"
)

// TestResponse 测试响应结构
type TestResponse struct {
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Items     []string               `json:"items"`
}

// createTestHandler 创建测试处理器
func createTestHandler(contentType string, size int) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", contentType)

		switch contentType {
		case "application/json":
			// 创建JSON响应
			response := TestResponse{
				Message:   "这是一个测试响应，用于演示gzip压缩优化效果",
				Timestamp: time.Now(),
				Data: map[string]interface{}{
					"compression": "enabled",
					"size":        size,
					"type":        "json",
				},
			}

			// 添加重复项目以达到指定大小
			itemTemplate := "测试项目数据，包含一些重复内容用于压缩演示"
			itemsNeeded := size / len(itemTemplate)
			for i := 0; i < itemsNeeded; i++ {
				response.Items = append(response.Items, fmt.Sprintf("%s_%d", itemTemplate, i))
			}

			json.NewEncoder(w).Encode(response)

		case "text/html":
			// 创建HTML响应
			html := `<!DOCTYPE html>
<html>
<head>
    <title>Gzip压缩演示</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>智能Gzip压缩演示</h1>
    <p>这是一个用于演示智能gzip压缩策略的HTML页面。</p>
    <div class="content">
`
			// 添加重复内容
			contentBlock := `        <p>这是一段重复的内容，用于演示压缩效果。智能压缩中间件会根据内容大小、类型和压缩效果自动决定是否启用压缩。</p>
`
			blocksNeeded := size / len(contentBlock)
			for i := 0; i < blocksNeeded; i++ {
				html += contentBlock
			}

			html += `    </div>
</body>
</html>`
			w.Write([]byte(html))

		case "image/jpeg":
			// 模拟二进制数据（不可压缩）
			data := make([]byte, size)
			for i := range data {
				data[i] = byte(i % 256) // 创建伪随机数据
			}
			w.Write(data)

		default:
			// 纯文本
			text := strings.Repeat("这是一段用于测试压缩的重复文本内容。", size/60)
			w.Write([]byte(text))
		}
	}
}

// testCompressionScenario 测试压缩场景
func testCompressionScenario(name, contentType string, size int, expectCompressed bool) {
	fmt.Printf("\n=== %s ===\n", name)
	fmt.Printf("内容类型: %s\n", contentType)
	fmt.Printf("内容大小: %d 字节\n", size)

	// 创建处理器
	handler := createTestHandler(contentType, size)

	// 应用压缩中间件
	middleware := web.CompressionMiddleware()
	wrappedHandler := middleware(handler)

	// 测试不支持gzip的请求
	fmt.Println("\n1. 不支持gzip的客户端:")
	req1 := httptest.NewRequest("GET", "/test", nil)
	w1 := httptest.NewRecorder()
	wrappedHandler.ServeHTTP(w1, req1)

	resp1 := w1.Result()
	body1, _ := io.ReadAll(resp1.Body)
	resp1.Body.Close()

	fmt.Printf("   响应大小: %d 字节\n", len(body1))
	fmt.Printf("   压缩状态: %s\n", getCompressionStatus(resp1))

	// 测试支持gzip的请求
	fmt.Println("\n2. 支持gzip的客户端:")
	req2 := httptest.NewRequest("GET", "/test", nil)
	req2.Header.Set("Accept-Encoding", "gzip, deflate")
	w2 := httptest.NewRecorder()
	wrappedHandler.ServeHTTP(w2, req2)

	resp2 := w2.Result()
	body2, _ := io.ReadAll(resp2.Body)
	resp2.Body.Close()

	isCompressed := resp2.Header.Get("Content-Encoding") == "gzip"
	fmt.Printf("   响应大小: %d 字节\n", len(body2))
	fmt.Printf("   压缩状态: %s\n", getCompressionStatus(resp2))

	if isCompressed {
		// 解压并验证
		gzReader, err := gzip.NewReader(bytes.NewReader(body2))
		if err == nil {
			decompressed, err := io.ReadAll(gzReader)
			gzReader.Close()
			if err == nil {
				originalSize := len(decompressed)
				compressedSize := len(body2)
				ratio := float64(originalSize-compressedSize) / float64(originalSize) * 100
				fmt.Printf("   原始大小: %d 字节\n", originalSize)
				fmt.Printf("   压缩比率: %.1f%%\n", ratio)

				// 验证压缩效果
				if ratio > 50 {
					fmt.Printf("   ✅ 压缩效果优秀 (>50%%)\n")
				} else if ratio > 20 {
					fmt.Printf("   ✅ 压缩效果良好 (>20%%)\n")
				} else {
					fmt.Printf("   ⚠️  压缩效果一般 (<20%%)\n")
				}
			}
		}
	}

	// 验证期望结果
	if isCompressed == expectCompressed {
		fmt.Printf("   ✅ 压缩策略正确\n")
	} else {
		fmt.Printf("   ❌ 压缩策略错误 (期望: %v, 实际: %v)\n", expectCompressed, isCompressed)
	}
}

// getCompressionStatus 获取压缩状态描述
func getCompressionStatus(resp *http.Response) string {
	encoding := resp.Header.Get("Content-Encoding")
	if encoding == "gzip" {
		return "已压缩 (gzip)"
	}
	return "未压缩"
}

// benchmarkCompression 基准测试压缩性能
func benchmarkCompression() {
	fmt.Println("\n=== 压缩性能基准测试 ===")

	sizes := []int{1024, 5120, 10240, 51200} // 1KB, 5KB, 10KB, 50KB

	for _, size := range sizes {
		fmt.Printf("\n测试大小: %d 字节\n", size)

		// 创建测试内容
		content := strings.Repeat(`{"key":"value","data":"test content"}`, size/35)

		// 测试无压缩
		start := time.Now()
		for i := 0; i < 100; i++ {
			_ = []byte(content)
		}
		noCompressTime := time.Since(start)

		// 测试gzip压缩
		start = time.Now()
		var compressedSize int
		for i := 0; i < 100; i++ {
			var buf bytes.Buffer
			gzWriter := gzip.NewWriter(&buf)
			gzWriter.Write([]byte(content))
			gzWriter.Close()
			compressedSize = buf.Len()
		}
		compressTime := time.Since(start)

		// 计算压缩比
		originalSize := len(content)
		ratio := float64(originalSize-compressedSize) / float64(originalSize) * 100

		fmt.Printf("   无压缩: %v (100次)\n", noCompressTime)
		fmt.Printf("   压缩:   %v (100次)\n", compressTime)
		fmt.Printf("   压缩比: %.1f%% (%d -> %d 字节)\n", ratio, originalSize, compressedSize)
		fmt.Printf("   性能开销: %.1fx\n", float64(compressTime)/float64(noCompressTime))
	}
}

func main() {
	fmt.Println("=== 智能Gzip压缩策略演示 ===")
	fmt.Println("演示智能压缩中间件如何根据内容大小、类型和压缩效果自动决定是否启用压缩")

	// 测试场景1: 大JSON内容 (应该压缩)
	testCompressionScenario(
		"大JSON内容",
		"application/json",
		3000, // 3KB
		true,
	)

	// 测试场景2: 小JSON内容 (不应该压缩)
	testCompressionScenario(
		"小JSON内容",
		"application/json",
		500, // 500字节
		false,
	)

	// 测试场景3: 大HTML内容 (应该压缩)
	testCompressionScenario(
		"大HTML内容",
		"text/html",
		5000, // 5KB
		true,
	)

	// 测试场景4: 二进制内容 (不应该压缩)
	testCompressionScenario(
		"二进制内容 (JPEG)",
		"image/jpeg",
		3000, // 3KB
		false,
	)

	// 性能基准测试
	benchmarkCompression()

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("智能gzip压缩策略的优势:")
	fmt.Println("1. 🎯 只压缩值得压缩的内容 (大小阈值)")
	fmt.Println("2. 🎯 只压缩可压缩的类型 (内容类型检查)")
	fmt.Println("3. 🎯 避免压缩效果差的内容 (压缩效果检查)")
	fmt.Println("4. 🎯 在写header前决定压缩 (避免错误设置)")
	fmt.Println("5. ⚡ 优化性能和带宽使用")
}
