package session

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"
)

func TestConcurrentMapBasicOperations(t *testing.T) {
	session := New()

	// 测试 Set 和 Get
	session.Set("key1", "value1")
	session.Set("key2", 42)
	session.Set("key3", true)

	val1, exists1 := session.Get("key1")
	if !exists1 {
		t.<PERSON>rror("key1 应该存在")
	}
	if val1 != "value1" {
		t.<PERSON><PERSON><PERSON>("key1 的值不匹配: got %v, want %s", val1, "value1")
	}

	val2, exists2 := session.Get("key2")
	if !exists2 {
		t.<PERSON>rror("key2 应该存在")
	}
	if val2 != 42 {
		t.<PERSON>rrorf("key2 的值不匹配: got %v, want %d", val2, 42)
	}

	// 测试不存在的键
	_, exists := session.Get("nonexistent")
	if exists {
		t.<PERSON>rror("不存在的键应该返回 false")
	}

	// 测试 Size
	if session.Size() != 3 {
		t.<PERSON><PERSON><PERSON>("Size 不匹配: got %d, want %d", session.Size(), 3)
	}
}

func TestConcurrentMapDelete(t *testing.T) {
	session := New()

	session.Set("key1", "value1")
	session.Set("key2", "value2")

	// 删除存在的键
	session.Delete("key1")
	_, exists := session.Get("key1")
	if exists {
		t.Error("删除后 key1 不应该存在")
	}

	// 删除不存在的键（应该不会出错）
	session.Delete("nonexistent")

	if session.Size() != 1 {
		t.Errorf("删除后 Size 不匹配: got %d, want %d", session.Size(), 1)
	}
}

func TestConcurrentMapKeys(t *testing.T) {
	session := New()

	session.Set("key1", "value1")
	session.Set("key2", "value2")
	session.Set("key3", "value3")

	keys := session.Keys()
	if len(keys) != 3 {
		t.Errorf("Keys 数量不匹配: got %d, want %d", len(keys), 3)
	}

	// 检查所有键是否都存在
	keyMap := make(map[string]bool)
	for _, key := range keys {
		keyMap[key] = true
	}

	expectedKeys := []string{"key1", "key2", "key3"}
	for _, expectedKey := range expectedKeys {
		if !keyMap[expectedKey] {
			t.Errorf("缺少预期的键: %s", expectedKey)
		}
	}
}

func TestConcurrentMapClear(t *testing.T) {
	session := New()

	session.Set("key1", "value1")
	session.Set("key2", "value2")

	session.Clear()

	if session.Size() != 0 {
		t.Errorf("清空后 Size 应该为 0: got %d", session.Size())
	}

	_, exists := session.Get("key1")
	if exists {
		t.Error("清空后不应该有任何键存在")
	}
}

func TestConcurrentMapClone(t *testing.T) {
	session := New()

	session.Set("key1", "value1")
	session.Set("key2", 42)

	clone := session.Clone()

	// 检查克隆的内容
	val1, exists1 := clone.Get("key1")
	if !exists1 || val1 != "value1" {
		t.Error("克隆应该包含原始数据")
	}

	val2, exists2 := clone.Get("key2")
	if !exists2 || val2 != 42 {
		t.Error("克隆应该包含原始数据")
	}

	// 修改原始会话，不应该影响克隆
	session.Set("key3", "value3")
	_, exists3 := clone.Get("key3")
	if exists3 {
		t.Error("修改原始会话不应该影响克隆")
	}

	// 修改克隆，不应该影响原始会话
	clone.Set("key4", "value4")
	_, exists4 := session.Get("key4")
	if exists4 {
		t.Error("修改克隆不应该影响原始会话")
	}
}

func TestConcurrentMapConcurrency(t *testing.T) {
	session := New()
	const numGoroutines = 100
	const numOperations = 100

	var wg sync.WaitGroup

	// 并发写入
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				key := fmt.Sprintf("key_%d_%d", id, j)
				session.Set(key, fmt.Sprintf("value_%d_%d", id, j))
			}
		}(i)
	}

	// 并发读取
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				key := fmt.Sprintf("key_%d_%d", id, j)
				// 可能读到也可能读不到，但不应该 panic
				session.Get(key)
			}
		}(i)
	}

	// 并发删除
	for i := 0; i < numGoroutines/2; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations/2; j++ {
				key := fmt.Sprintf("key_%d_%d", id, j)
				session.Delete(key)
			}
		}(i)
	}

	// 等待所有操作完成
	done := make(chan bool)
	go func() {
		wg.Wait()
		done <- true
	}()

	select {
	case <-done:
		// 测试通过
	case <-time.After(10 * time.Second):
		t.Fatal("并发测试超时")
	}

	// 验证最终状态
	size := session.Size()
	if size < 0 {
		t.Errorf("Size 不应该为负数: got %d", size)
	}
}

func TestContextIntegration(t *testing.T) {
	ctx := context.Background()
	session := New()

	// 测试 WithSession 和 FromContext
	ctxWithSession := WithSession(ctx, session)
	retrievedSession, exists := FromContext(ctxWithSession)
	if !exists {
		t.Error("应该能从 context 中获取会话")
	}
	if retrievedSession != session {
		t.Error("获取的会话应该与原始会话相同")
	}

	// 测试没有会话的 context
	_, exists = FromContext(ctx)
	if exists {
		t.Error("没有会话的 context 应该返回 false")
	}

	// 测试 GetFromContext
	newSession := GetFromContext(ctx)
	if newSession == nil {
		t.Error("GetFromContext 不应该返回 nil")
	}

	existingSession := GetFromContext(ctxWithSession)
	if existingSession != session {
		t.Error("GetFromContext 应该返回现有会话")
	}
}

func TestContextHelperFunctions(t *testing.T) {
	session := New()
	ctx := WithSession(context.Background(), session)

	// 测试 SetValue 和 GetValue
	SetValue(ctx, "test_key", "test_value")

	val, exists := GetValue(ctx, "test_key")
	if !exists {
		t.Error("应该能获取设置的值")
	}
	if val != "test_value" {
		t.Errorf("值不匹配: got %v, want %s", val, "test_value")
	}

	// 验证值确实存储在会话中
	sessionVal, sessionExists := session.Get("test_key")
	if !sessionExists || sessionVal != "test_value" {
		t.Error("值应该存储在会话中")
	}

	// 测试 DeleteValue
	DeleteValue(ctx, "test_key")
	_, exists = GetValue(ctx, "test_key")
	if exists {
		t.Error("删除后不应该能获取值")
	}

	// 测试没有会话的 context
	emptyCtx := context.Background()
	SetValue(emptyCtx, "key", "value") // 应该不会 panic
	_, exists = GetValue(emptyCtx, "key")
	if exists {
		t.Error("没有会话的 context 不应该能获取值")
	}
}

func TestReadOnlyMap(t *testing.T) {
	session := New()
	session.Set("key1", "value1")
	session.Set("key2", "value2")

	readOnly := NewReadOnly(session)

	// 测试读取操作
	val, exists := readOnly.Get("key1")
	if !exists || val != "value1" {
		t.Error("只读会话应该能读取数据")
	}

	// 测试 Keys
	keys := readOnly.Keys()
	if len(keys) != 2 {
		t.Errorf("只读会话 Keys 数量不匹配: got %d, want %d", len(keys), 2)
	}

	// 测试 Size
	if readOnly.Size() != 2 {
		t.Errorf("只读会话 Size 不匹配: got %d, want %d", readOnly.Size(), 2)
	}

	// 测试写操作（应该被忽略）
	readOnly.Set("key3", "value3")
	_, exists = readOnly.Get("key3")
	if exists {
		t.Error("只读会话不应该允许设置新值")
	}

	// 验证原始会话也没有被修改
	_, exists = session.Get("key3")
	if exists {
		t.Error("只读会话的写操作不应该影响原始会话")
	}

	// 测试删除操作（应该被忽略）
	readOnly.Delete("key1")
	_, exists = readOnly.Get("key1")
	if !exists {
		t.Error("只读会话不应该允许删除")
	}

	// 测试清空操作（应该被忽略）
	readOnly.Clear()
	if readOnly.Size() != 2 {
		t.Error("只读会话不应该允许清空")
	}

	// 测试克隆
	clone := readOnly.Clone()
	if clone.Size() != 2 {
		t.Error("只读会话的克隆应该包含原始数据")
	}
}
