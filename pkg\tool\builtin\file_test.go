package builtin

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFileTool_Name(t *testing.T) {
	fileTool := NewFileTool([]string{"."})
	assert.Equal(t, "file", fileTool.Name())
}

func TestFileTool_Description(t *testing.T) {
	fileTool := NewFileTool([]string{"."})
	desc := fileTool.Description()
	assert.NotEmpty(t, desc)
	assert.Contains(t, desc, "File operations")
}

func TestFileTool_Schema(t *testing.T) {
	fileTool := NewFileTool([]string{"."})
	schema := fileTool.Schema()

	require.NotNil(t, schema)
	assert.Equal(t, "object", schema.Type)

	// 检查属性
	require.NotNil(t, schema.Properties)

	// 检查 operation 属性
	operationProp, exists := schema.Properties["operation"]
	require.True(t, exists)
	assert.Equal(t, "string", operationProp.Type)

	// 检查枚举值
	require.NotNil(t, operationProp.Enum)
	expectedOperations := []any{"read", "write", "list", "exists", "stat"}
	assert.ElementsMatch(t, expectedOperations, operationProp.Enum)

	// 检查必需字段
	require.Contains(t, schema.Required, "operation")
}

func TestFileTool_Execute_WriteAndRead(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "file_tool_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	fileTool := NewFileTool([]string{tempDir})
	ctx := context.Background()

	testFile := filepath.Join(tempDir, "test.txt")
	testContent := "Hello, World!\n这是测试内容。"

	// 测试写入文件
	writeParams := map[string]any{
		"operation": "write",
		"path":      testFile,
		"content":   testContent,
	}

	_, err = fileTool.Execute(ctx, writeParams)
	require.NoError(t, err)

	// 验证文件确实被创建
	_, err = os.Stat(testFile)
	require.NoError(t, err)

	// 测试读取文件
	readParams := map[string]any{
		"operation": "read",
		"path":      testFile,
	}

	_, err = fileTool.Execute(ctx, readParams)
	require.NoError(t, err)

	// 验证读取的内容
	content, err := os.ReadFile(testFile)
	require.NoError(t, err)
	assert.Equal(t, testContent, string(content))
}

func TestFileTool_Execute_List(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "file_tool_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	fileTool := NewFileTool([]string{tempDir})
	ctx := context.Background()

	// 创建一些测试文件
	testFiles := []string{"file1.txt", "file2.txt"}
	for _, name := range testFiles {
		path := filepath.Join(tempDir, name)
		err := os.WriteFile(path, []byte("test content"), 0644)
		require.NoError(t, err)
	}

	// 测试列出目录内容
	listParams := map[string]any{
		"operation": "list",
		"path":      tempDir,
	}

	result, err := fileTool.Execute(ctx, listParams)
	require.NoError(t, err)
	require.NotNil(t, result)
}

func TestFileTool_Execute_Exists(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "file_tool_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	fileTool := NewFileTool([]string{tempDir})
	ctx := context.Background()

	existingFile := filepath.Join(tempDir, "existing.txt")
	err = os.WriteFile(existingFile, []byte("test"), 0644)
	require.NoError(t, err)

	nonExistingFile := filepath.Join(tempDir, "nonexisting.txt")

	tests := []struct {
		name string
		path string
	}{
		{
			name: "存在的文件",
			path: existingFile,
		},
		{
			name: "不存在的文件",
			path: nonExistingFile,
		},
		{
			name: "目录",
			path: tempDir,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := map[string]any{
				"operation": "exists",
				"path":      tt.path,
			}

			result, err := fileTool.Execute(ctx, params)
			require.NoError(t, err)
			require.NotNil(t, result)
		})
	}
}

func TestFileTool_Execute_Stat(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "file_tool_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	fileTool := NewFileTool([]string{tempDir})
	ctx := context.Background()

	testFile := filepath.Join(tempDir, "test.txt")
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err)

	params := map[string]any{
		"operation": "stat",
		"path":      testFile,
	}

	result, err := fileTool.Execute(ctx, params)
	require.NoError(t, err)
	require.NotNil(t, result)
}

func TestFileTool_Execute_SecurityRestrictions(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "file_tool_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	fileTool := NewFileTool([]string{tempDir})
	ctx := context.Background()

	// 尝试访问允许目录之外的文件
	outsideFile := "/etc/passwd"

	params := map[string]any{
		"operation": "read",
		"path":      outsideFile,
	}

	result, err := fileTool.Execute(ctx, params)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "not allowed")
}

func TestFileTool_Execute_ErrorCases(t *testing.T) {
	fileTool := NewFileTool([]string{"."})
	ctx := context.Background()

	tests := []struct {
		name   string
		params map[string]any
	}{
		{
			name:   "缺少 operation 参数",
			params: map[string]any{},
		},
		{
			name: "无效的 operation",
			params: map[string]any{
				"operation": "invalid",
			},
		},
		{
			name: "读取时缺少 path",
			params: map[string]any{
				"operation": "read",
			},
		},
		{
			name: "读取不存在的文件",
			params: map[string]any{
				"operation": "read",
				"path":      "nonexistent.txt",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := fileTool.Execute(ctx, tt.params)
			assert.Error(t, err)
			assert.Nil(t, result)
		})
	}
}

func TestFileTool_Execute_NilContext(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "file_tool_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	fileTool := NewFileTool([]string{tempDir})

	params := map[string]any{
		"operation": "exists",
		"path":      tempDir,
	}

	// 即使 context 为 nil，也应该能正常工作
	result, err := fileTool.Execute(context.TODO(), params)
	require.NoError(t, err)
	require.NotNil(t, result)
}
