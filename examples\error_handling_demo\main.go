package main

import (
	"context"
	"fmt"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

func main() {
	fmt.Println("=== AgentScope 错误处理和日志增强功能演示 ===")

	// 创建增强的日志记录器
	enhancedLogger, err := logger.NewEnhancedLogger(&logger.EnhancedConfig{
		Config: &logger.Config{
			Level:  "debug",
			Format: "json",
			Output: "stdout",
		},
		EnableCaller:   true,
		EnableDuration: true,
	})
	if err != nil {
		panic(err)
	}

	// 演示1: 基本错误处理
	fmt.Println("\n1. 基本错误处理演示")
	demonstrateBasicErrorHandling(enhancedLogger)

	// 演示2: 安全执行和 panic 恢复
	fmt.Println("\n2. 安全执行和 panic 恢复演示")
	demonstrateSafeExecution(enhancedLogger)

	// 演示3: 断路器模式
	fmt.Println("\n3. 断路器模式演示")
	demonstrateCircuitBreaker(enhancedLogger)

	// 演示4: 增强日志功能
	fmt.Println("\n4. 增强日志功能演示")
	demonstrateEnhancedLogging(enhancedLogger)

	// 演示5: 性能监控
	fmt.Println("\n5. 性能监控演示")
	demonstratePerformanceLogging(enhancedLogger)

	fmt.Println("\n=== 演示完成 ===")
}

// demonstrateBasicErrorHandling 演示基本错误处理
func demonstrateBasicErrorHandling(log logger.Logger) {
	// 创建结构化错误
	validationErr := errors.NewValidationError("INVALID_INPUT", "用户输入无效")
	validationErr.WithContext("field", "email").WithContext("value", "invalid-email")

	log.WithField("demo", "basic_error").WithError(validationErr).Error("验证错误示例")

	// 包装错误
	networkErr := fmt.Errorf("连接超时")
	wrappedErr := errors.Wrap(networkErr, errors.ErrorTypeNetwork, "CONN_TIMEOUT", "网络连接失败")
	wrappedErr.WithContext("host", "api.example.com").WithContext("port", 443)

	log.WithField("demo", "wrapped_error").WithError(wrappedErr).Error("包装错误示例")

	// 错误链
	chainedErr := errors.Chain(validationErr, wrappedErr)
	log.WithField("demo", "chained_error").WithError(chainedErr).Error("错误链示例")
}

// demonstrateSafeExecution 演示安全执行和 panic 恢复
func demonstrateSafeExecution(log logger.Logger) {
	ctx := context.Background()
	handler := errors.NewDefaultRecoveryHandler(log)

	// 正常执行
	err := errors.SafeExecute(ctx, handler, func() error {
		log.WithField("demo", "safe_execution").Info("正常执行函数")
		return nil
	})
	if err != nil {
		log.WithError(err).Error("安全执行失败")
	}

	// 处理 panic
	err = errors.SafeExecute(ctx, handler, func() error {
		log.WithField("demo", "panic_recovery").Info("即将触发 panic")
		panic("模拟 panic 情况")
	})
	if err != nil {
		log.WithField("demo", "panic_recovered").WithError(err).Error("成功捕获并恢复 panic")
	}

	// 带返回值的安全执行
	result, err := errors.SafeExecuteWithResult(ctx, handler, func() (string, error) {
		return "成功结果", nil
	})
	if err != nil {
		log.WithError(err).Error("带返回值的安全执行失败")
	} else {
		log.WithField("demo", "safe_execution_result").WithField("result", result).Info("带返回值的安全执行成功")
	}
}

// demonstrateCircuitBreaker 演示断路器模式
func demonstrateCircuitBreaker(log logger.Logger) {
	// 创建断路器
	cb := errors.NewCircuitBreaker(errors.CircuitBreakerConfig{
		Name:         "demo_service",
		MaxFailures:  2,
		ResetTimeout: 100 * time.Millisecond,
		OnStateChange: func(from, to errors.CircuitBreakerState) {
			log.WithField("demo", "circuit_breaker").
				WithField("from", from.String()).
				WithField("to", to.String()).
				Info("断路器状态变化")
		},
	})

	ctx := context.Background()

	// 模拟服务调用
	callService := func(shouldFail bool) error {
		return cb.Execute(ctx, func() error {
			if shouldFail {
				return fmt.Errorf("服务调用失败")
			}
			log.WithField("demo", "circuit_breaker").Info("服务调用成功")
			return nil
		})
	}

	// 正常调用
	log.WithField("demo", "circuit_breaker").Info("开始断路器演示")
	callService(false)

	// 失败调用，触发断路器
	for i := 0; i < 3; i++ {
		err := callService(true)
		if err != nil {
			log.WithField("demo", "circuit_breaker").
				WithField("attempt", i+1).
				WithError(err).
				Warn("服务调用失败")
		}
	}

	// 等待断路器重置
	time.Sleep(150 * time.Millisecond)

	// 重试调用
	err := callService(false)
	if err != nil {
		log.WithField("demo", "circuit_breaker").WithError(err).Error("断路器重置后调用失败")
	} else {
		log.WithField("demo", "circuit_breaker").Info("断路器重置后调用成功")
	}
}

// demonstrateEnhancedLogging 演示增强日志功能
func demonstrateEnhancedLogging(baseLogger logger.Logger) {
	// 类型断言获取增强日志器
	enhancedLogger, ok := baseLogger.(*logger.EnhancedLogger)
	if !ok {
		fmt.Println("无法获取增强日志器")
		return
	}

	// 操作跟踪
	finishOp := enhancedLogger.WithOperation("数据处理")
	time.Sleep(50 * time.Millisecond) // 模拟操作耗时
	finishOp(nil)

	// 失败操作跟踪
	finishFailedOp := enhancedLogger.WithOperation("失败操作")
	time.Sleep(30 * time.Millisecond)
	finishFailedOp(fmt.Errorf("操作失败"))

	// 上下文日志
	ctx := context.WithValue(context.Background(), "request_id", "req-123")
	ctx = context.WithValue(ctx, "user_id", "user-456")
	
	contextLogger := enhancedLogger.WithContext(ctx)
	contextLogger.Info("带上下文的日志记录")

	// AgentScope 错误日志
	agentErr := errors.NewAgentError("AGT_001", "智能体执行失败")
	agentErr.WithContext("agent_id", "agent-123").WithContext("step", "reasoning")
	
	enhancedLogger.WithAgentScopeError(agentErr).Error("智能体错误")

	// 慢操作检测
	enhancedLogger.LogSlowOperation("数据库查询", 200*time.Millisecond, 100*time.Millisecond)

	// 内存使用情况
	enhancedLogger.LogMemoryUsage("demo_component")
}

// demonstratePerformanceLogging 演示性能监控
func demonstratePerformanceLogging(baseLogger logger.Logger) {
	// 创建性能日志记录器
	perfLogger := logger.NewPerformanceLogger(baseLogger, 50*time.Millisecond)

	// 快速操作
	finish1 := perfLogger.Track("快速操作")
	time.Sleep(20 * time.Millisecond)
	finish1()

	// 慢操作
	finish2 := perfLogger.Track("慢操作")
	time.Sleep(80 * time.Millisecond)
	finish2()

	// 带上下文的性能跟踪
	ctx := context.WithValue(context.Background(), "session_id", "session-789")
	finish3 := perfLogger.TrackWithContext(ctx, "带上下文的操作")
	time.Sleep(60 * time.Millisecond)
	finish3(nil)

	// 失败的操作
	finish4 := perfLogger.TrackWithContext(ctx, "失败的操作")
	time.Sleep(40 * time.Millisecond)
	finish4(fmt.Errorf("操作失败"))

	// 创建错误日志记录器
	errorLogger := logger.NewErrorLogger(baseLogger)

	// 记录错误
	ctx = context.WithValue(ctx, "operation_id", "op-999")
	testErr := errors.NewLLMError("LLM_001", "LLM 服务不可用")
	testErr.WithContext("provider", "deepseek").WithContext("model", "deepseek-chat")

	errorLogger.LogError(ctx, testErr, "LLM调用", map[string]interface{}{
		"retry_count": 3,
		"timeout":     "30s",
	})
}
