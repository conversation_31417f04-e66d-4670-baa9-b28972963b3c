package runtime

import (
	"context"
	"fmt"
	"testing"
	"time"
)

// TestCalculateOptimalBufferSize 测试缓冲区大小计算
func TestCalculateOptimalBufferSize(t *testing.T) {
	tests := []struct {
		name     string
		config   *BufferSizeConfig
		expected int
	}{
		{
			name:     "LLM流式输出配置",
			config:   LLMStreamingConfig,
			expected: 10, // 50 * 0.02 * 3 = 3, 但最小值是10，所以应该是10
		},
		{
			name:     "LLM批量输出配置",
			config:   LLMBatchConfig,
			expected: 5, // 5 * 0.1 * 2 = 1, 但最小值是5
		},
		{
			name:     "工具执行配置",
			config:   ToolExecutionConfig,
			expected: 3, // 10 * 0.05 * 2 = 1, 但最小值是3
		},
		{
			name:     "默认配置",
			config:   DefaultConfig,
			expected: 5, // 10 * 0.05 * 2 = 1, 但最小值是5
		},
		{
			name:     "空配置使用默认",
			config:   nil,
			expected: 5,
		},
		{
			name: "高频率场景",
			config: &BufferSizeConfig{
				ScenarioType:        "high_frequency",
				EventsPerSecond:     100.0,
				ProcessingLatencyMs: 100,
				IsStreaming:         true,
				MaxBufferSize:       500,
				MinBufferSize:       10,
			},
			expected: 30, // 100 * 0.1 * 3 = 30
		},
		{
			name: "超出最大限制",
			config: &BufferSizeConfig{
				ScenarioType:        "extreme",
				EventsPerSecond:     1000.0,
				ProcessingLatencyMs: 1000,
				IsStreaming:         true,
				MaxBufferSize:       100,
				MinBufferSize:       10,
			},
			expected: 100, // 计算值会很大，但被限制在最大值100
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateOptimalBufferSize(tt.config)
			if result != tt.expected {
				t.Errorf("CalculateOptimalBufferSize() = %d, want %d", result, tt.expected)
			}
		})
	}
}

// TestNewAsyncIteratorWithConfig 测试使用配置创建迭代器
func TestNewAsyncIteratorWithConfig(t *testing.T) {
	ctx := context.Background()

	// 测试LLM流式配置
	pair := NewAsyncIteratorWithConfig[string](ctx, LLMStreamingConfig)
	if pair == nil {
		t.Error("NewAsyncIteratorWithConfig should not return nil")
	}
	if pair.Generator == nil || pair.Iterator == nil {
		t.Error("Generator and Iterator should not be nil")
	}

	// 测试发送和接收
	err := pair.Generator.Send("test")
	if err != nil {
		t.Errorf("Send failed: %v", err)
	}

	pair.Generator.Close()

	value, ok := pair.Iterator.Next()
	if !ok || value != "test" {
		t.Error("Failed to receive sent value")
	}
}

// TestSpecializedConstructors 测试专用构造函数
func TestSpecializedConstructors(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name        string
		constructor func(context.Context) *GeneratorPair[string]
	}{
		{
			name:        "LLM流式输出",
			constructor: NewAsyncIteratorForLLMStreaming[string],
		},
		{
			name:        "LLM批量输出",
			constructor: NewAsyncIteratorForLLMBatch[string],
		},
		{
			name:        "工具执行",
			constructor: NewAsyncIteratorForToolExecution[string],
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pair := tt.constructor(ctx)
			if pair == nil {
				t.Error("Constructor should not return nil")
			}
			if pair.Generator == nil || pair.Iterator == nil {
				t.Error("Generator and Iterator should not be nil")
			}

			// 测试基本功能
			err := pair.Generator.Send("test")
			if err != nil {
				t.Errorf("Send failed: %v", err)
			}

			pair.Generator.Close()

			value, ok := pair.Iterator.Next()
			if !ok || value != "test" {
				t.Error("Failed to receive sent value")
			}
		})
	}
}

// BenchmarkBufferSizeCalculation 基准测试缓冲区大小计算
func BenchmarkBufferSizeCalculation(b *testing.B) {
	configs := []*BufferSizeConfig{
		LLMStreamingConfig,
		LLMBatchConfig,
		ToolExecutionConfig,
		DefaultConfig,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, config := range configs {
			CalculateOptimalBufferSize(config)
		}
	}
}

// TestBufferSizePerformance 测试不同缓冲区大小的性能影响
func TestBufferSizePerformance(t *testing.T) {
	ctx := context.Background()

	bufferSizes := []int{1, 5, 10, 50, 100, 200}
	messageCount := 1000

	for _, bufferSize := range bufferSizes {
		t.Run(fmt.Sprintf("BufferSize_%d", bufferSize), func(t *testing.T) {
			start := time.Now()

			pair := NewAsyncIterator[int](ctx, bufferSize)

			// 在goroutine中发送消息
			go func() {
				defer pair.Generator.Close()
				for i := 0; i < messageCount; i++ {
					if err := pair.Generator.Send(i); err != nil {
						t.Errorf("Send failed: %v", err)
						return
					}
				}
			}()

			// 接收所有消息
			received := 0
			for value, ok := pair.Iterator.Next(); ok; value, ok = pair.Iterator.Next() {
				received++
				_ = value // 避免未使用变量警告
			}

			duration := time.Since(start)

			if received != messageCount {
				t.Errorf("Expected %d messages, got %d", messageCount, received)
			}

			t.Logf("BufferSize %d: %d messages in %v (%.2f msg/ms)",
				bufferSize, messageCount, duration, float64(messageCount)/float64(duration.Milliseconds()))
		})
	}
}

// TestAdaptiveBuffering 测试自适应缓冲
func TestAdaptiveBuffering(t *testing.T) {
	ctx := context.Background()

	// 模拟不同的事件生成速度
	scenarios := []struct {
		name            string
		eventsPerSecond float64
		duration        time.Duration
	}{
		{"低频率", 5.0, 100 * time.Millisecond},
		{"中频率", 20.0, 100 * time.Millisecond},
		{"高频率", 100.0, 100 * time.Millisecond},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			config := &BufferSizeConfig{
				ScenarioType:        scenario.name,
				EventsPerSecond:     scenario.eventsPerSecond,
				ProcessingLatencyMs: 50,
				IsStreaming:         true,
				MaxBufferSize:       1000,
				MinBufferSize:       5,
			}

			bufferSize := CalculateOptimalBufferSize(config)
			pair := NewAsyncIterator[int](ctx, bufferSize)

			// 计算应该发送的事件数
			expectedEvents := int(scenario.eventsPerSecond * scenario.duration.Seconds())

			start := time.Now()

			// 发送事件
			go func() {
				defer pair.Generator.Close()
				interval := time.Duration(float64(time.Second) / scenario.eventsPerSecond)
				ticker := time.NewTicker(interval)
				defer ticker.Stop()

				eventCount := 0
				for {
					select {
					case <-ticker.C:
						if err := pair.Generator.Send(eventCount); err != nil {
							return
						}
						eventCount++
						if eventCount >= expectedEvents {
							return
						}
					case <-time.After(scenario.duration + 50*time.Millisecond):
						return
					}
				}
			}()

			// 接收事件
			received := 0
			for value, ok := pair.Iterator.Next(); ok; value, ok = pair.Iterator.Next() {
				received++
				_ = value
			}

			duration := time.Since(start)

			t.Logf("场景 %s: 缓冲区大小 %d, 发送 %d 事件, 接收 %d 事件, 耗时 %v",
				scenario.name, bufferSize, expectedEvents, received, duration)

			// 验证没有丢失事件
			if received < expectedEvents {
				t.Errorf("事件丢失: 期望 %d, 实际接收 %d", expectedEvents, received)
			}
		})
	}
}
