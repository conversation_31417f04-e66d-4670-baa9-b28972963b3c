package qwen

import (
	"context"
	"os"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/llm"
)

// QwenChatModel 将 QwenClient 适配为 ChatModel 接口
type QwenChatModel struct {
	client *llm.QwenClient
}

// NewQwenChatModel 创建新的 Qwen ChatModel 适配器
func NewQwenChatModel() (*QwenChatModel, error) {
	// 获取 API Key
	apiKey := os.Getenv("DASHSCOPE_API_KEY")
	if apiKey == "" {
		apiKey = os.Getenv("QWEN_API_KEY")
	}
	if apiKey == "" {
		return nil, &llm.ValidationError{
			Field:   "api_key",
			Message: "DASHSCOPE_API_KEY 或 QWEN_API_KEY 环境变量未设置",
		}
	}

	// 创建配置
	config := &llm.Config{
		Provider:   "qwen",
		APIKey:     apiKey,
		BaseURL:    "https://dashscope.aliyuncs.com/compatible-mode/v1",
		Model:      "qwen-plus",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		Parameters: make(map[string]interface{}),
	}

	// 创建 QwenClient
	client, err := llm.NewQwenClient(config)
	if err != nil {
		return nil, err
	}

	return &QwenChatModel{
		client: client,
	}, nil
}

// NewQwenChatModelWithConfig 使用配置创建 Qwen ChatModel 适配器
func NewQwenChatModelWithConfig(apiKey, baseURL, model string, timeout time.Duration, retries int) (*QwenChatModel, error) {
	if baseURL == "" {
		baseURL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
	}
	if model == "" {
		model = "qwen-plus"
	}

	config := &llm.Config{
		Provider:   "qwen",
		APIKey:     apiKey,
		BaseURL:    baseURL,
		Model:      model,
		Timeout:    timeout,
		MaxRetries: retries,
		Parameters: make(map[string]interface{}),
	}

	client, err := llm.NewQwenClient(config)
	if err != nil {
		return nil, err
	}

	return &QwenChatModel{
		client: client,
	}, nil
}

// Chat 发送聊天请求并返回完整响应
func (q *QwenChatModel) Chat(ctx context.Context, req *llm.ChatRequest) (*llm.ChatResponse, error) {
	// 转换请求格式
	generateReq := q.convertChatRequestToGenerateRequest(req)

	// 调用原始客户端
	generateResp, err := q.client.Generate(ctx, generateReq)
	if err != nil {
		return nil, err
	}

	// 转换响应格式
	return q.convertGenerateResponseToChatResponse(generateResp), nil
}

// ChatStream 发送流式聊天请求并返回增量响应通道
func (q *QwenChatModel) ChatStream(ctx context.Context, req *llm.ChatRequest) (<-chan *llm.ChatDelta, error) {
	// 转换请求格式
	generateReq := q.convertChatRequestToGenerateRequest(req)

	// 调用原始客户端的流式方法
	streamChan, err := q.client.GenerateStream(ctx, generateReq)
	if err != nil {
		return nil, err
	}

	// 创建转换后的响应通道
	deltaChan := make(chan *llm.ChatDelta, 10)

	go func() {
		defer close(deltaChan)

		for streamResp := range streamChan {
			// 转换流式响应为 ChatDelta
			delta := q.convertStreamResponseToChatDelta(streamResp)
			if delta != nil {
				select {
				case deltaChan <- delta:
				case <-ctx.Done():
					return
				}
			}

			// 如果流结束，退出
			if streamResp.Done {
				break
			}
		}
	}()

	return deltaChan, nil
}

// convertChatRequestToGenerateRequest 转换聊天请求为生成请求
func (q *QwenChatModel) convertChatRequestToGenerateRequest(req *llm.ChatRequest) *llm.GenerateRequest {
	generateReq := &llm.GenerateRequest{
		Messages:    req.Messages,
		Model:       req.Model,
		Temperature: req.Temperature,
		MaxTokens:   req.MaxTokens,
		TopP:        req.TopP,
		Stop:        req.Stop,
		Stream:      req.Stream,
		Parameters:  make(map[string]interface{}),
	}

	// 转换工具定义
	if len(req.Tools) > 0 {
		tools := make([]*llm.Tool, len(req.Tools))
		for i, tool := range req.Tools {
			tools[i] = &llm.Tool{
				Type: tool.Type,
				Function: &llm.ToolFunction{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					Parameters:  tool.Function.Parameters,
				},
			}
		}
		generateReq.Tools = tools
	}

	// 设置工具选择
	if req.ToolChoice != nil {
		generateReq.ToolChoice = req.ToolChoice
	}

	// 设置其他参数
	if req.FrequencyPenalty != nil {
		generateReq.Parameters["frequency_penalty"] = *req.FrequencyPenalty
	}
	if req.PresencePenalty != nil {
		generateReq.Parameters["presence_penalty"] = *req.PresencePenalty
	}
	if req.User != "" {
		generateReq.Parameters["user"] = req.User
	}
	if req.Seed != nil {
		generateReq.Parameters["seed"] = *req.Seed
	}

	return generateReq
}

// convertGenerateResponseToChatResponse 转换生成响应为聊天响应
func (q *QwenChatModel) convertGenerateResponseToChatResponse(resp *llm.GenerateResponse) *llm.ChatResponse {
	chatResp := &llm.ChatResponse{
		ID:      resp.ID,
		Object:  "chat.completion",
		Created: resp.Created,
		Model:   resp.Model,
	}

	// 转换选择
	if len(resp.Choices) > 0 {
		chatResp.Choices = make([]llm.ChatChoice, len(resp.Choices))
		for i, choice := range resp.Choices {
			chatResp.Choices[i] = llm.ChatChoice{
				Index:        choice.Index,
				Message:      *choice.Message,
				FinishReason: choice.FinishReason,
			}
		}
	}

	// 转换使用统计
	if resp.Usage != nil {
		chatResp.Usage = &llm.ChatUsage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		}
	}

	return chatResp
}

// convertStreamResponseToChatDelta 转换流式响应为聊天增量
func (q *QwenChatModel) convertStreamResponseToChatDelta(resp *llm.StreamResponse) *llm.ChatDelta {
	if resp.Error != nil {
		// 错误情况下返回 nil，让调用方处理
		return nil
	}

	delta := &llm.ChatDelta{
		ID:      resp.ID,
		Object:  "chat.completion.chunk",
		Created: resp.Created,
		Model:   resp.Model,
	}

	// 转换增量选择
	if len(resp.Choices) > 0 {
		delta.Choices = make([]llm.ChatDeltaChoice, len(resp.Choices))
		for i, choice := range resp.Choices {
			deltaChoice := llm.ChatDeltaChoice{
				Index: choice.Index,
			}

			// 转换增量内容
			if choice.Delta != nil {
				deltaChoice.Delta = llm.ChatDeltaContent{
					Role:    choice.Delta.Role,
					Content: q.extractContentString(choice.Delta.Content),
				}

				// 转换工具调用
				if len(choice.Delta.ToolCalls) > 0 {
					deltaChoice.Delta.ToolCalls = choice.Delta.ToolCalls
				}
			}

			// 设置结束原因
			if choice.FinishReason != "" {
				deltaChoice.FinishReason = &choice.FinishReason
			}

			delta.Choices[i] = deltaChoice
		}
	}

	return delta
}

// extractContentString 从 interface{} 中提取字符串内容
func (q *QwenChatModel) extractContentString(content interface{}) string {
	if content == nil {
		return ""
	}

	if str, ok := content.(string); ok {
		return str
	}

	return ""
}

// GetClient 获取底层客户端（用于测试）
func (q *QwenChatModel) GetClient() *llm.QwenClient {
	return q.client
}
