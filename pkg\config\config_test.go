package config

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	assert.NotNil(t, config)
	assert.Equal(t, "AgentScope-Golang", config.App.Name)
	assert.Equal(t, "0.1.0-MVP", config.App.Version)
	assert.Equal(t, "info", config.Logging.Level)
	assert.Equal(t, "text", config.Logging.Format)
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
	}{
		{
			name:        "valid config",
			config:      DefaultConfig(),
			expectError: false,
		},
		{
			name: "empty app name",
			config: &Config{
				App: &AppConfig{
					Name:    "",
					Version: "1.0.0",
				},
				Logging: &LoggingConfig{
					Level:  "info",
					Format: "json",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConfigCreation(t *testing.T) {
	config := &Config{
		App: &AppConfig{
			Name:    "Test App",
			Version: "1.0.0",
			Debug:   false,
		},
		Logging: &LoggingConfig{
			Level:  "info",
			Format: "json",
		},
	}

	assert.Equal(t, "Test App", config.App.Name)
	assert.Equal(t, "1.0.0", config.App.Version)
	assert.False(t, config.App.Debug)
	assert.Equal(t, "info", config.Logging.Level)
	assert.Equal(t, "json", config.Logging.Format)
}

// TestLoader 测试配置加载器
func TestLoader(t *testing.T) {
	loader := NewLoader()
	assert.NotNil(t, loader)
	assert.NotNil(t, loader.logger)
}

// TestLoadFromFile 测试从文件加载配置
func TestLoadFromFile(t *testing.T) {
	loader := NewLoader()

	// 测试空文件路径
	_, err := loader.LoadFromFile("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "file path cannot be empty")

	// 测试不存在的文件
	_, err = loader.LoadFromFile("nonexistent.yaml")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "config file not found")

	// 创建临时配置文件
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "test_config.yaml")

	configContent := `
app:
  name: "Test App"
  version: "1.0.0"
  environment: "test"
  debug: true

logging:
  level: "debug"
  format: "json"
  output: "stdout"

llm:
  default_provider: "deepseek"
  timeout: 30s
  max_retries: 3
  providers:
    deepseek:
      type: "deepseek"
      api_key: "test-key"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
      timeout: 30s
      max_retries: 3

web:
  enabled: true
  host: "localhost"
  port: 8080
  cors:
    enabled: true
    allowed_origins: ["*"]

audit:
  enabled: true
  driver: "sqlite"
  dsn: ":memory:"
  retention:
    enabled: true
    max_days: 30
  privacy:
    redact_pii: true
    pii_patterns: ["email", "phone"]
  batch:
    async: true
    chan_buffer: 1024
    flush_interval: 1s
`

	err = os.WriteFile(configFile, []byte(configContent), 0644)
	require.NoError(t, err)

	// 测试加载有效配置文件
	config, err := loader.LoadFromFile(configFile)
	assert.NoError(t, err)
	assert.NotNil(t, config)

	// 验证配置内容
	assert.Equal(t, "Test App", config.App.Name)
	assert.Equal(t, "1.0.0", config.App.Version)
	assert.Equal(t, "test", config.App.Environment)
	assert.True(t, config.App.Debug)

	assert.Equal(t, "debug", config.Logging.Level)
	assert.Equal(t, "json", config.Logging.Format)
	assert.Equal(t, "stdout", config.Logging.Output)

	assert.Equal(t, "deepseek", config.LLM.DefaultProvider)
	assert.Equal(t, 30*time.Second, config.LLM.Timeout)
	assert.Equal(t, 3, config.LLM.MaxRetries)
	assert.Contains(t, config.LLM.Providers, "deepseek")

	assert.True(t, config.Web.Enabled)
	assert.Equal(t, "localhost", config.Web.Host)
	assert.Equal(t, 8080, config.Web.Port)

	assert.True(t, config.Audit.Enabled)
	assert.Equal(t, "sqlite", config.Audit.Driver)
	assert.Equal(t, ":memory:", config.Audit.DSN)
}

// TestLoadFromFileInvalidYAML 测试加载无效YAML文件
func TestLoadFromFileInvalidYAML(t *testing.T) {
	loader := NewLoader()

	tempDir := t.TempDir()
	invalidFile := filepath.Join(tempDir, "invalid.yaml")

	invalidContent := `
app:
  name: "Test App"
  version: 1.0.0  # 这里应该是字符串
  invalid_yaml: [
`

	err := os.WriteFile(invalidFile, []byte(invalidContent), 0644)
	require.NoError(t, err)

	_, err = loader.LoadFromFile(invalidFile)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse config file")
}

// TestLoadFromEnv 测试从环境变量加载配置
func TestLoadFromEnv(t *testing.T) {
	loader := NewLoader()

	// 设置测试环境变量
	testEnvVars := map[string]string{
		"APP_NAME":         "EnvTest",
		"APP_VERSION":      "2.0.0",
		"APP_ENV":          "production",
		"APP_DEBUG":        "false",
		"LOG_LEVEL":        "warn",
		"LOG_FORMAT":       "text",
		"LOG_OUTPUT":       "file",
		"WEB_ENABLED":      "true",
		"WEB_HOST":         "0.0.0.0",
		"WEB_PORT":         "9090",
		"DEEPSEEK_API_KEY": "test-env-key",
		"LLM_PROVIDER":     "deepseek",
		"LLM_BASE_URL":     "https://test.api.com",
		"LLM_MODEL":        "test-model",
		"LLM_TIMEOUT":      "60",
		"LLM_MAX_RETRIES":  "5",
	}

	// 设置环境变量
	for key, value := range testEnvVars {
		os.Setenv(key, value)
	}

	// 清理环境变量
	defer func() {
		for key := range testEnvVars {
			os.Unsetenv(key)
		}
	}()

	config, err := loader.LoadFromEnv()
	assert.NoError(t, err)
	assert.NotNil(t, config)

	// 验证从环境变量加载的配置
	assert.Equal(t, "EnvTest", config.App.Name)
	assert.Equal(t, "2.0.0", config.App.Version)
	assert.Equal(t, "production", config.App.Environment)
	assert.False(t, config.App.Debug)

	assert.Equal(t, "warn", config.Logging.Level)
	assert.Equal(t, "text", config.Logging.Format)
	assert.Equal(t, "file", config.Logging.Output)

	assert.True(t, config.Web.Enabled)
	assert.Equal(t, "0.0.0.0", config.Web.Host)
	assert.Equal(t, 9090, config.Web.Port)

	assert.NotNil(t, config.LLM)
	assert.Equal(t, "deepseek", config.LLM.DefaultProvider)
	assert.Equal(t, 60*time.Second, config.LLM.Timeout)
	assert.Equal(t, 5, config.LLM.MaxRetries)
	assert.Contains(t, config.LLM.Providers, "deepseek")
	assert.Equal(t, "test-env-key", config.LLM.Providers["deepseek"].APIKey)
}

// TestLoadWithDefaults 测试带默认值的配置加载
func TestLoadWithDefaults(t *testing.T) {
	loader := NewLoader()

	// 测试不存在的文件，应该从环境变量加载
	config, err := loader.LoadWithDefaults("nonexistent.yaml")
	assert.NoError(t, err)
	assert.NotNil(t, config)

	// 验证默认值被应用
	assert.Equal(t, "AgentScope", config.App.Name)
	assert.Equal(t, "1.0.0", config.App.Version)
	assert.Equal(t, "development", config.App.Environment)

	assert.Equal(t, "info", config.Logging.Level)
	assert.Equal(t, "json", config.Logging.Format)
	assert.Equal(t, "stdout", config.Logging.Output)

	assert.Equal(t, "localhost", config.Web.Host)
	assert.Equal(t, 8080, config.Web.Port)

	assert.Equal(t, "deepseek", config.LLM.DefaultProvider)
	assert.Equal(t, 30*time.Second, config.LLM.Timeout)
	assert.Equal(t, 3, config.LLM.MaxRetries)
}

// TestConfigValidationDetailed 测试详细的配置验证
func TestConfigValidationDetailed(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
		errorField  string
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: false, // Validate会处理nil情况
		},
		{
			name: "empty app name",
			config: &Config{
				App: &AppConfig{
					Name:    "",
					Version: "1.0.0",
				},
			},
			expectError: true,
			errorField:  "app",
		},
		{
			name: "empty app version - should set default",
			config: &Config{
				App: &AppConfig{
					Name:    "Test",
					Version: "",
				},
			},
			expectError: false, // 会设置默认版本
		},
		{
			name: "invalid web port - negative",
			config: &Config{
				App: &AppConfig{
					Name:    "Test",
					Version: "1.0.0",
				},
				Web: &WebConfig{
					Port: -1,
				},
			},
			expectError: false, // WebConfig.Validate会设置默认端口
		},
		{
			name: "large web port - should be allowed",
			config: &Config{
				App: &AppConfig{
					Name:    "Test",
					Version: "1.0.0",
				},
				Web: &WebConfig{
					Port: 70000,
				},
			},
			expectError: false, // WebConfig.Validate不检查端口上限
		},
		{
			name: "empty LLM default provider",
			config: &Config{
				App: &AppConfig{
					Name:    "Test",
					Version: "1.0.0",
				},
				LLM: &LLMConfig{
					DefaultProvider: "",
				},
			},
			expectError: true,
			errorField:  "llm",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var err error
			if tt.config != nil {
				err = tt.config.Validate()
			}

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestLLMConfigValidation 测试LLM配置验证
func TestLLMConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *LLMConfig
		expectError bool
	}{
		{
			name: "valid LLM config",
			config: &LLMConfig{
				DefaultProvider: "deepseek",
				Providers: map[string]*LLMProvider{
					"deepseek": {
						Type:    "deepseek",
						APIKey:  "test-key",
						BaseURL: "https://api.deepseek.com",
						Model:   "deepseek-chat",
					},
				},
				Timeout:    30 * time.Second,
				MaxRetries: 3,
				Parameters: map[string]any{"temperature": 0.7},
			},
			expectError: false,
		},
		{
			name: "missing default provider",
			config: &LLMConfig{
				DefaultProvider: "",
				Providers:       map[string]*LLMProvider{},
			},
			expectError: true,
		},
		{
			name: "default provider not in providers",
			config: &LLMConfig{
				DefaultProvider: "nonexistent",
				Providers: map[string]*LLMProvider{
					"deepseek": {Type: "deepseek"},
				},
			},
			expectError: true,
		},
		{
			name: "nil providers map",
			config: &LLMConfig{
				DefaultProvider: "deepseek",
				Providers:       nil,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestLLMProviderValidation 测试LLM提供商配置验证
func TestLLMProviderValidation(t *testing.T) {
	tests := []struct {
		name        string
		provider    *LLMProvider
		expectError bool
	}{
		{
			name: "valid provider",
			provider: &LLMProvider{
				Type:    "deepseek",
				APIKey:  "test-key",
				BaseURL: "https://api.deepseek.com",
				Model:   "deepseek-chat",
			},
			expectError: false,
		},
		{
			name: "empty type",
			provider: &LLMProvider{
				Type:   "",
				APIKey: "test-key",
			},
			expectError: true,
		},
		{
			name: "empty API key - should be allowed",
			provider: &LLMProvider{
				Type:   "deepseek",
				APIKey: "",
			},
			expectError: false,
		},
		{
			name: "empty base URL - should be allowed",
			provider: &LLMProvider{
				Type:    "deepseek",
				APIKey:  "test-key",
				BaseURL: "",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.provider.Validate()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestLoggingConfigValidation 测试日志配置验证
func TestLoggingConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *LoggingConfig
		expectError bool
	}{
		{
			name: "valid logging config",
			config: &LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "stdout",
			},
			expectError: false,
		},
		{
			name: "empty level - should set default",
			config: &LoggingConfig{
				Level:  "",
				Format: "json",
				Output: "stdout",
			},
			expectError: false,
		},
		{
			name: "empty format - should set default",
			config: &LoggingConfig{
				Level:  "info",
				Format: "",
				Output: "stdout",
			},
			expectError: false,
		},
		{
			name: "empty output - should set default",
			config: &LoggingConfig{
				Level:  "info",
				Format: "json",
				Output: "",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestWebConfigValidation 测试Web配置验证
func TestWebConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *WebConfig
		expectError bool
	}{
		{
			name: "valid web config",
			config: &WebConfig{
				Enabled: true,
				Host:    "localhost",
				Port:    8080,
				CORS: &CORSConfig{
					Enabled:        true,
					AllowedOrigins: []string{"*"},
					AllowedMethods: []string{"GET", "POST"},
					AllowedHeaders: []string{"Content-Type"},
				},
			},
			expectError: false,
		},
		{
			name: "negative port - should set default",
			config: &WebConfig{
				Port: -1,
			},
			expectError: false,
		},
		{
			name: "zero port - should set default",
			config: &WebConfig{
				Port: 0,
			},
			expectError: false,
		},
		{
			name: "empty host - should set default",
			config: &WebConfig{
				Host: "",
				Port: 8080,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestAuditConfigValidation 测试审计配置验证
func TestAuditConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *AuditConfig
		expectError bool
	}{
		{
			name: "valid audit config",
			config: &AuditConfig{
				Enabled: true,
				Driver:  "sqlite",
				DSN:     ":memory:",
				Retention: &AuditRetentionConfig{
					Enabled: true,
					MaxDays: 30,
					Cron:    "@daily",
				},
				Privacy: &AuditPrivacyConfig{
					RedactPII:   true,
					PIIPatterns: []string{"email", "phone"},
				},
				Batch: &AuditBatchConfig{
					Async:         true,
					ChanBuffer:    1024,
					FlushInterval: time.Second,
				},
			},
			expectError: false,
		},
		{
			name: "empty driver - should use default",
			config: &AuditConfig{
				Enabled: true,
				Driver:  "",
				DSN:     ":memory:",
			},
			expectError: false,
		},
		{
			name: "empty DSN with sqlite - should use default",
			config: &AuditConfig{
				Enabled: true,
				Driver:  "sqlite",
				DSN:     "",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestGlobalLoaderFunctions 测试全局加载器函数
func TestGlobalLoaderFunctions(t *testing.T) {
	// 创建临时配置文件
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "global_test.yaml")

	configContent := `
app:
  name: "Global Test"
  version: "1.0.0"
logging:
  level: "info"
  format: "json"
`

	err := os.WriteFile(configFile, []byte(configContent), 0644)
	require.NoError(t, err)

	// 测试LoadConfig
	config, err := LoadConfig(configFile)
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, "Global Test", config.App.Name)

	// 测试LoadConfigFromEnv
	os.Setenv("APP_NAME", "EnvGlobalTest")
	defer os.Unsetenv("APP_NAME")

	envConfig, err := LoadConfigFromEnv()
	assert.NoError(t, err)
	assert.NotNil(t, envConfig)
	assert.Equal(t, "EnvGlobalTest", envConfig.App.Name)

	// 测试LoadConfigWithDefaults
	defaultConfig, err := LoadConfigWithDefaults("")
	assert.NoError(t, err)
	assert.NotNil(t, defaultConfig)
}

// TestEnvironmentVariableHelpers 测试环境变量辅助函数
func TestEnvironmentVariableHelpers(t *testing.T) {
	// 测试getEnvOrDefault
	os.Setenv("TEST_STRING", "test_value")
	defer os.Unsetenv("TEST_STRING")

	// 这些是内部函数，我们通过LoadFromEnv间接测试
	loader := NewLoader()

	// 设置各种类型的环境变量
	os.Setenv("APP_DEBUG", "true")
	os.Setenv("WEB_PORT", "9999")
	defer func() {
		os.Unsetenv("APP_DEBUG")
		os.Unsetenv("WEB_PORT")
	}()

	config, err := loader.LoadFromEnv()
	assert.NoError(t, err)
	assert.True(t, config.App.Debug)
	assert.Equal(t, 9999, config.Web.Port)
}

// TestConfigEdgeCases 测试配置边界情况
func TestConfigEdgeCases(t *testing.T) {
	// 测试空配置的验证
	emptyConfig := &Config{}
	err := emptyConfig.Validate()
	assert.NoError(t, err) // 应该应用默认值

	// 验证默认值被正确应用
	assert.NotNil(t, emptyConfig.App)
	assert.Equal(t, "AgentScope-Golang", emptyConfig.App.Name)

	// 测试部分配置
	partialConfig := &Config{
		App: &AppConfig{
			Name:    "Partial",
			Version: "1.0.0",
		},
	}
	err = partialConfig.Validate()
	assert.NoError(t, err)

	// 测试配置覆盖
	config := DefaultConfig()
	config.App.Name = "Override"
	assert.Equal(t, "Override", config.App.Name)
	assert.Equal(t, "0.1.0-MVP", config.App.Version) // 其他值保持默认
}

// TestComplexConfigScenarios 测试复杂配置场景
func TestComplexConfigScenarios(t *testing.T) {
	// 创建复杂的配置文件
	tempDir := t.TempDir()
	complexFile := filepath.Join(tempDir, "complex.yaml")

	complexContent := `
app:
  name: "Complex App"
  version: "2.0.0"
  environment: "staging"
  debug: false

llm:
  default_provider: "deepseek"
  timeout: 45s
  max_retries: 5
  parameters:
    temperature: 0.8
    max_tokens: 4096
  providers:
    deepseek:
      type: "deepseek"
      api_key: "complex-key"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
      timeout: 30s
      max_retries: 3
      parameters:
        temperature: 0.7
    qwen:
      type: "qwen"
      api_key: "qwen-key"
      base_url: "https://api.qwen.com"
      model: "qwen-turbo"

logging:
  level: "debug"
  format: "text"
  output: "file"

web:
  enabled: true
  host: "0.0.0.0"
  port: 8888
  cors:
    enabled: true
    allowed_origins: ["https://example.com", "https://test.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["Content-Type", "Authorization"]
    allow_credentials: true
    max_age: 3600

audit:
  enabled: true
  driver: "postgres"
  dsn: "postgres://user:pass@localhost/audit"
  retention:
    enabled: true
    max_days: 365
    cron: "0 2 * * 0"
  privacy:
    redact_pii: true
    pii_patterns: ["email", "phone", "idcard", "creditcard"]
    hash_content: false
    encrypt_at_rest: true
    encrypt_key_env: "AUDIT_ENCRYPTION_KEY"
  batch:
    async: true
    chan_buffer: 2048
    flush_interval: 5s
  web:
    allow_read_api: true
    allow_delete_api: false
`

	err := os.WriteFile(complexFile, []byte(complexContent), 0644)
	require.NoError(t, err)

	loader := NewLoader()
	config, err := loader.LoadFromFile(complexFile)
	assert.NoError(t, err)
	assert.NotNil(t, config)

	// 验证复杂配置的各个部分
	assert.Equal(t, "Complex App", config.App.Name)
	assert.Equal(t, "2.0.0", config.App.Version)
	assert.Equal(t, "staging", config.App.Environment)
	assert.False(t, config.App.Debug)

	assert.Equal(t, "deepseek", config.LLM.DefaultProvider)
	assert.Equal(t, 45*time.Second, config.LLM.Timeout)
	assert.Equal(t, 5, config.LLM.MaxRetries)
	assert.Contains(t, config.LLM.Providers, "deepseek")
	assert.Contains(t, config.LLM.Providers, "qwen")

	assert.Equal(t, "debug", config.Logging.Level)
	assert.Equal(t, "text", config.Logging.Format)
	assert.Equal(t, "file", config.Logging.Output)

	assert.True(t, config.Web.Enabled)
	assert.Equal(t, "0.0.0.0", config.Web.Host)
	assert.Equal(t, 8888, config.Web.Port)
	assert.True(t, config.Web.CORS.Enabled)
	assert.Contains(t, config.Web.CORS.AllowedOrigins, "https://example.com")

	assert.True(t, config.Audit.Enabled)
	assert.Equal(t, "postgres", config.Audit.Driver)
	assert.Equal(t, 365, config.Audit.Retention.MaxDays)
	assert.True(t, config.Audit.Privacy.EncryptAtRest)
	assert.Equal(t, 2048, config.Audit.Batch.ChanBuffer)
	assert.Equal(t, 5*time.Second, config.Audit.Batch.FlushInterval)

	// 验证配置有效性
	err = config.Validate()
	assert.NoError(t, err)
}
