package prompts

import (
	"bytes"
	"encoding/json"
	"fmt"
	"text/template"
)

// PromptTemplate 提示词模板
type PromptTemplate struct {
	Name        string            `json:"name" yaml:"name"`
	Description string            `json:"description" yaml:"description"`
	Template    string            `json:"template" yaml:"template"`
	Variables   []string          `json:"variables" yaml:"variables"`
	Examples    []PromptExample   `json:"examples" yaml:"examples"`
	Metadata    map[string]string `json:"metadata" yaml:"metadata"`
}

// PromptExample 提示词示例
type PromptExample struct {
	Input    map[string]interface{} `json:"input" yaml:"input"`
	Output   string                 `json:"output" yaml:"output"`
	Expected string                 `json:"expected" yaml:"expected"`
}

// 预定义的提示词模板
var (
	// 分类提示词模板
	ClassificationTemplate = &PromptTemplate{
		Name:        "classification",
		Description: "通用文本分类提示词模板",
		Template: `你是一个专业的文本分类专家。请根据以下类别对用户输入进行分类。

可选类别：
{{range .Categories}}
- {{.Name}}: {{.Description}}
  关键词: {{join .Keywords ", "}}
  {{if .Examples}}示例: {{join .Examples "; "}}{{end}}
{{end}}

用户输入: {{.Input}}

请以JSON格式返回分类结果：
{
  "category": "分类名称",
  "confidence": 0.95,
  "reasoning": "分类理由"
}`,
		Variables: []string{"Categories", "Input"},
	}

	// 情绪分析提示词模板
	EmotionAnalysisTemplate = &PromptTemplate{
		Name:        "emotion_analysis",
		Description: "情绪分析提示词模板",
		Template: `你是一个专业的情绪分析专家。请分析以下文本的情绪倾向。

文本内容: {{.Text}}

请从以下维度分析情绪：
1. 主要情绪类型：positive（积极）、negative（消极）、neutral（中性）
2. 情绪强度：0-1之间的数值，1表示情绪最强烈
3. 详细情绪：如愤怒、喜悦、焦虑、满意等具体情绪及其强度

请以JSON格式返回分析结果：
{
  "type": "情绪类型",
  "score": 0.8,
  "confidence": 0.95,
  "details": {
    "anger": 0.1,
    "joy": 0.8,
    "anxiety": 0.0,
    "satisfaction": 0.7
  },
  "reasoning": "分析理由"
}`,
		Variables: []string{"Text"},
	}

	// 质量评估提示词模板
	QualityAssessmentTemplate = &PromptTemplate{
		Name:        "quality_assessment",
		Description: "对话质量评估提示词模板",
		Template: `你是一个专业的客服质量评估专家。请评估以下对话的服务质量。

用户消息: {{.UserMessage}}
客服回复: {{.AgentResponse}}

评估维度：
1. 准确性：回复是否准确回答了用户问题
2. 完整性：回复是否完整，没有遗漏重要信息
3. 专业性：回复是否专业，用词得当
4. 友好性：回复是否友好，态度良好
5. 效率性：回复是否简洁高效

请以JSON格式返回评估结果：
{
  "overall_score": 85,
  "metrics": {
    "accuracy": 90,
    "completeness": 85,
    "professionalism": 88,
    "friendliness": 92,
    "efficiency": 80
  },
  "feedback": "整体表现良好，建议在效率方面进一步优化",
  "strengths": ["回复准确", "态度友好"],
  "weaknesses": ["回复略显冗长"]
}`,
		Variables: []string{"UserMessage", "AgentResponse"},
	}

	// 自动回复匹配提示词模板
	AutoReplyTemplate = &PromptTemplate{
		Name:        "auto_reply",
		Description: "自动回复匹配提示词模板",
		Template: `你是一个智能客服助手。请判断用户输入是否匹配以下预设的常见问题，如果匹配则返回对应回复。

常见问题库：
{{range .FAQs}}
问题: {{.Question}}
回复: {{.Answer}}
关键词: {{join .Keywords ", "}}
---
{{end}}

用户输入: {{.Input}}

请以JSON格式返回结果：
{
  "has_reply": true,
  "content": "匹配的回复内容",
  "confidence": 0.95,
  "source": "FAQ"
}

如果没有匹配的问题，请返回：
{
  "has_reply": false,
  "content": "",
  "confidence": 0.0,
  "source": ""
}`,
		Variables: []string{"FAQs", "Input"},
	}
)

// PromptBuilder 提示词构建器
type PromptBuilder struct {
	templates map[string]*PromptTemplate
	funcs     template.FuncMap
}

// NewPromptBuilder 创建提示词构建器
func NewPromptBuilder() *PromptBuilder {
	builder := &PromptBuilder{
		templates: make(map[string]*PromptTemplate),
		funcs: template.FuncMap{
			"join": func(slice []string, sep string) string {
				return fmt.Sprintf("%v", slice)
			},
			"toJSON": func(v interface{}) string {
				b, _ := json.Marshal(v)
				return string(b)
			},
		},
	}
	
	// 注册预定义模板
	builder.RegisterTemplate(ClassificationTemplate)
	builder.RegisterTemplate(EmotionAnalysisTemplate)
	builder.RegisterTemplate(QualityAssessmentTemplate)
	builder.RegisterTemplate(AutoReplyTemplate)
	
	return builder
}

// RegisterTemplate 注册提示词模板
func (pb *PromptBuilder) RegisterTemplate(tmpl *PromptTemplate) error {
	if tmpl.Name == "" {
		return fmt.Errorf("模板名称不能为空")
	}
	pb.templates[tmpl.Name] = tmpl
	return nil
}

// Build 构建提示词
func (pb *PromptBuilder) Build(templateName string, data map[string]interface{}) (string, error) {
	tmpl, exists := pb.templates[templateName]
	if !exists {
		return "", fmt.Errorf("模板 %s 不存在", templateName)
	}
	
	t, err := template.New(templateName).Funcs(pb.funcs).Parse(tmpl.Template)
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %w", err)
	}
	
	var buf bytes.Buffer
	if err := t.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("执行模板失败: %w", err)
	}
	
	return buf.String(), nil
}

// GetTemplate 获取模板
func (pb *PromptBuilder) GetTemplate(name string) (*PromptTemplate, error) {
	tmpl, exists := pb.templates[name]
	if !exists {
		return nil, fmt.Errorf("模板 %s 不存在", name)
	}
	return tmpl, nil
}

// ListTemplates 列出所有模板
func (pb *PromptBuilder) ListTemplates() []string {
	names := make([]string, 0, len(pb.templates))
	for name := range pb.templates {
		names = append(names, name)
	}
	return names
}
