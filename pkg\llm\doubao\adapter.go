package doubao

import (
	"context"
	"os"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/llm"
)

// DoubaoChateModel 将 DoubaoClient 适配为 ChatModel 接口
type DoubaoChateModel struct {
	client *llm.DoubaoClient
}

// NewDoubaoChateModel 创建新的 Doubao ChatModel 适配器
func NewDoubaoChateModel() (*DoubaoChateModel, error) {
	// 获取 API Key，优先级：ARK_API_KEY > DOUBAO_API_KEY > VOLC_API_KEY
	apiKey := os.Getenv("ARK_API_KEY")
	if apiKey == "" {
		apiKey = os.Getenv("DOUBAO_API_KEY")
	}
	if apiKey == "" {
		apiKey = os.Getenv("VOLC_API_KEY")
	}
	if apiKey == "" {
		return nil, &llm.ValidationError{
			Field:   "api_key",
			Message: "ARK_API_KEY、DOUBAO_API_KEY 或 VOLC_API_KEY 环境变量未设置",
		}
	}

	// 创建配置
	config := &llm.Config{
		Provider:   "doubao",
		APIKey:     apiKey,
		BaseURL:    "https://ark.cn-beijing.volces.com/api/v3",
		Model:      "ep-xxx", // Ark 常用 Endpoint ID
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		Parameters: make(map[string]interface{}),
	}

	// 创建 DoubaoClient
	client, err := llm.NewDoubaoClient(config)
	if err != nil {
		return nil, err
	}

	return &DoubaoChateModel{
		client: client,
	}, nil
}

// NewDoubaoChateModelWithConfig 使用配置创建 Doubao ChatModel 适配器
func NewDoubaoChateModelWithConfig(apiKey, baseURL, model string, timeout time.Duration, retries int) (*DoubaoChateModel, error) {
	if baseURL == "" {
		baseURL = "https://ark.cn-beijing.volces.com/api/v3"
	}
	if model == "" {
		model = "ep-xxx"
	}

	config := &llm.Config{
		Provider:   "doubao",
		APIKey:     apiKey,
		BaseURL:    baseURL,
		Model:      model,
		Timeout:    timeout,
		MaxRetries: retries,
		Parameters: make(map[string]interface{}),
	}

	client, err := llm.NewDoubaoClient(config)
	if err != nil {
		return nil, err
	}

	return &DoubaoChateModel{
		client: client,
	}, nil
}

// Chat 发送聊天请求并返回完整响应
func (d *DoubaoChateModel) Chat(ctx context.Context, req *llm.ChatRequest) (*llm.ChatResponse, error) {
	// 转换请求格式
	generateReq := d.convertChatRequestToGenerateRequest(req)

	// 调用原始客户端
	generateResp, err := d.client.Generate(ctx, generateReq)
	if err != nil {
		return nil, err
	}

	// 转换响应格式
	return d.convertGenerateResponseToChatResponse(generateResp), nil
}

// ChatStream 发送流式聊天请求并返回增量响应通道
func (d *DoubaoChateModel) ChatStream(ctx context.Context, req *llm.ChatRequest) (<-chan *llm.ChatDelta, error) {
	// 转换请求格式
	generateReq := d.convertChatRequestToGenerateRequest(req)

	// 调用原始客户端的流式方法
	streamChan, err := d.client.GenerateStream(ctx, generateReq)
	if err != nil {
		return nil, err
	}

	// 创建转换后的响应通道
	deltaChan := make(chan *llm.ChatDelta, 10)

	go func() {
		defer close(deltaChan)

		for streamResp := range streamChan {
			// 转换流式响应为 ChatDelta
			delta := d.convertStreamResponseToChatDelta(streamResp)
			if delta != nil {
				select {
				case deltaChan <- delta:
				case <-ctx.Done():
					return
				}
			}

			// 如果流结束，退出
			if streamResp.Done {
				break
			}
		}
	}()

	return deltaChan, nil
}

// convertChatRequestToGenerateRequest 转换聊天请求为生成请求
func (d *DoubaoChateModel) convertChatRequestToGenerateRequest(req *llm.ChatRequest) *llm.GenerateRequest {
	generateReq := &llm.GenerateRequest{
		Messages:    req.Messages,
		Model:       req.Model,
		Temperature: req.Temperature,
		MaxTokens:   req.MaxTokens,
		TopP:        req.TopP,
		Stop:        req.Stop,
		Stream:      req.Stream,
		Parameters:  make(map[string]interface{}),
	}

	// 转换工具定义
	if len(req.Tools) > 0 {
		tools := make([]*llm.Tool, len(req.Tools))
		for i, tool := range req.Tools {
			tools[i] = &llm.Tool{
				Type: tool.Type,
				Function: &llm.ToolFunction{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					Parameters:  tool.Function.Parameters,
				},
			}
		}
		generateReq.Tools = tools
	}

	// 设置工具选择
	if req.ToolChoice != nil {
		generateReq.ToolChoice = req.ToolChoice
	}

	// 设置其他参数
	if req.FrequencyPenalty != nil {
		generateReq.Parameters["frequency_penalty"] = *req.FrequencyPenalty
	}
	if req.PresencePenalty != nil {
		generateReq.Parameters["presence_penalty"] = *req.PresencePenalty
	}
	if req.User != "" {
		generateReq.Parameters["user"] = req.User
	}
	if req.Seed != nil {
		generateReq.Parameters["seed"] = *req.Seed
	}

	return generateReq
}

// convertGenerateResponseToChatResponse 转换生成响应为聊天响应
func (d *DoubaoChateModel) convertGenerateResponseToChatResponse(resp *llm.GenerateResponse) *llm.ChatResponse {
	chatResp := &llm.ChatResponse{
		ID:      resp.ID,
		Object:  "chat.completion",
		Created: resp.Created,
		Model:   resp.Model,
	}

	// 转换选择
	if len(resp.Choices) > 0 {
		chatResp.Choices = make([]llm.ChatChoice, len(resp.Choices))
		for i, choice := range resp.Choices {
			chatResp.Choices[i] = llm.ChatChoice{
				Index:        choice.Index,
				Message:      *choice.Message,
				FinishReason: choice.FinishReason,
			}
		}
	}

	// 转换使用统计
	if resp.Usage != nil {
		chatResp.Usage = &llm.ChatUsage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		}
	}

	return chatResp
}

// convertStreamResponseToChatDelta 转换流式响应为聊天增量
func (d *DoubaoChateModel) convertStreamResponseToChatDelta(resp *llm.StreamResponse) *llm.ChatDelta {
	if resp.Error != nil {
		// 错误情况下返回 nil，让调用方处理
		return nil
	}

	delta := &llm.ChatDelta{
		ID:      resp.ID,
		Object:  "chat.completion.chunk",
		Created: resp.Created,
		Model:   resp.Model,
	}

	// 转换增量选择
	if len(resp.Choices) > 0 {
		delta.Choices = make([]llm.ChatDeltaChoice, len(resp.Choices))
		for i, choice := range resp.Choices {
			deltaChoice := llm.ChatDeltaChoice{
				Index: choice.Index,
			}

			// 转换增量内容
			if choice.Delta != nil {
				deltaChoice.Delta = llm.ChatDeltaContent{
					Role:    choice.Delta.Role,
					Content: d.extractContentString(choice.Delta.Content),
				}

				// 转换工具调用
				if len(choice.Delta.ToolCalls) > 0 {
					deltaChoice.Delta.ToolCalls = choice.Delta.ToolCalls
				}
			}

			// 设置结束原因
			if choice.FinishReason != "" {
				deltaChoice.FinishReason = &choice.FinishReason
			}

			delta.Choices[i] = deltaChoice
		}
	}

	return delta
}

// extractContentString 从 interface{} 中提取字符串内容
func (d *DoubaoChateModel) extractContentString(content interface{}) string {
	if content == nil {
		return ""
	}

	if str, ok := content.(string); ok {
		return str
	}

	return ""
}

// GetClient 获取底层客户端（用于测试）
func (d *DoubaoChateModel) GetClient() *llm.DoubaoClient {
	return d.client
}
