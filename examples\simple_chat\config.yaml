app:
  name: "AgentScope-Golang Simple Chat"
  version: "0.1.0-MVP"
  environment: "development"
  debug: true

logging:
  level: "info"
  format: "text"
  output: "stdout"

llm:
  default_provider: "qwen"
  timeout: "30s"
  max_retries: 3
  providers:
    deepseek:
      type: "deepseek"
      api_key: "${DEEPSEEK_API_KEY}"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
      timeout: "30s"
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048
    qwen:
      type: "qwen"
      api_key: "${DASHSCOPE_API_KEY}"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model: "qwen-plus"
      timeout: "30s"
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048
    doubao:
      type: "doubao"
      api_key: "${ARK_API_KEY}"
      base_url: "https://ark.cn-beijing.volces.com/api/v3"
      model: "ep-test123"  # 测试用的 Endpoint ID
      timeout: "30s"
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048

agents:
  user_001:
    id: "user_001"
    name: "User"
    type: "user"
    description: "Human user agent for interactive chat"
    timeout: "30s"
    parameters:
      prompt: "You: "

  assistant_001:
    id: "assistant_001"
    name: "Assistant"
    type: "assistant"
    description: "AI assistant powered by Deepseek"
    llm_client: "deepseek"
    timeout: "30s"
    max_retries: 3
    parameters:
      system_prompt: "You are a helpful AI assistant. Provide clear, concise, and helpful responses to user questions."
      temperature: 0.7
      max_tokens: 2048

pipelines:
  simple_chat:
    name: "simple_chat"
    type: "sequential"
    description: "Simple chat pipeline with user and assistant"
    timeout: "60s"
    max_retries: 3

web:
  enabled: true
  host: "localhost"
  port: 8080

# 对话审计配置
audit:
  # 是否启用审计功能
  enabled: true

  # 数据库驱动：sqlite 或 postgres
  driver: "sqlite"

  # 数据库连接字符串
  # SQLite: 文件路径，如 "./audit.db"
  # PostgreSQL: "postgres://user:password@localhost/dbname?sslmode=disable"
  dsn: "./audit.db"

  # 数据隐私保护配置
  privacy:
    # 是否启用PII（个人身份信息）脱敏
    redact_pii: true

    # PII模式列表：email, phone, id_card, credit_card, ip
    pii_patterns:
      - "email"
      - "phone"
      - "id_card"

    # 是否对内容进行哈希处理（用于去重和隐私保护）
    hash_content: false

    # 是否启用静态加密存储
    encrypt_at_rest: false

    # 加密密钥环境变量名（启用加密时需要）
    encrypt_key_env: "AUDIT_AES_KEY"

  # 批处理配置
  batch:
    # 是否启用异步批处理（推荐）
    async: true

    # 通道缓冲区大小
    chan_buffer: 1024

    # 批量刷新间隔
    flush_interval: "1s"

  # 数据保留策略
  retention:
    # 是否启用自动数据清理
    enabled: true

    # 数据保留天数
    max_days: 90

    # 清理任务执行时间（cron格式）
    # @daily = 每天午夜执行
    # "0 2 * * *" = 每天凌晨2点执行
    cron: "@daily"

  # Web API配置
  web:
    # 是否允许通过API查询审计数据（生产环境建议关闭）
    allow_read_api: false

    # 是否允许通过API删除审计数据（生产环境强烈建议关闭）
    allow_delete_api: false