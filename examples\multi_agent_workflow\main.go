package main

import (
	"context"
	"fmt"
	"os"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/compose"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// MockAgent 模拟智能体用于演示
type MockAgent struct {
	name        string
	description string
	response    string
	delay       int // 模拟处理延迟（毫秒）
}

// NewMockAgent 创建模拟智能体
func NewMockAgent(name, description, response string, delay int) *MockAgent {
	return &MockAgent{
		name:        name,
		description: description,
		response:    response,
		delay:       delay,
	}
}

// Name 返回智能体名称
func (m *MockAgent) Name(ctx context.Context) string {
	return m.name
}

// Description 返回智能体描述
func (m *MockAgent) Description(ctx context.Context) string {
	return m.description
}

// Run 运行智能体
func (m *MockAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 模拟处理延迟
		if m.delay > 0 {
			select {
			case <-ctx.Done():
				return
			default:
				// 简化延迟模拟
			}
		}

		// 发送思考事件（模拟开始）
		thoughtEvent := &event.Event{
			Type: event.EventThought,
			Data: &event.ThoughtData{
				Content: fmt.Sprintf("智能体 %s 开始处理任务", m.name),
			},
		}
		pair.Generator.Send(thoughtEvent)

		// 发送最终结果事件
		finalEvent := &event.Event{
			Type: event.EventFinal,
			Data: &event.FinalData{
				Content: fmt.Sprintf("[%s] %s", m.name, m.response),
			},
		}
		pair.Generator.Send(finalEvent)
	}()

	return pair.Iterator
}

func main() {
	fmt.Println("=== AgentScope-Golang 多智能体工作流示例 ===")
	fmt.Println("这个示例展示了如何使用组合模式创建复杂的多智能体工作流")
	fmt.Println()

	ctx := context.Background()

	// 创建记忆存储
	memoryStore := memory.NewMemoryStore()

	// 创建模拟智能体
	agent1 := NewMockAgent("分析师", "负责数据分析", "完成数据分析任务", 100)
	agent2 := NewMockAgent("设计师", "负责界面设计", "完成界面设计任务", 150)
	agent3 := NewMockAgent("开发者", "负责代码实现", "完成代码实现任务", 200)
	agent4 := NewMockAgent("测试员", "负责质量测试", "完成测试任务", 120)
	agent5 := NewMockAgent("审核员", "负责最终审核", "完成审核任务", 80)

	// 演示1: 顺序执行工作流
	fmt.Println("=== 演示1: 顺序执行工作流 ===")
	fmt.Println("场景: 软件开发流水线 (分析 -> 设计 -> 开发 -> 测试 -> 审核)")
	fmt.Println()

	sequentialAgents := []agent.Agent{agent1, agent2, agent3, agent4, agent5}
	sequentialComposer, err := compose.NewSequentialAgent(&compose.SequentialConfig{
		Name:        "软件开发流水线",
		Description: "顺序执行的软件开发流程",
		Agents:      sequentialAgents,
		FailureMode: compose.FailureModeStop,
	})
	if err != nil {
		fmt.Printf("创建顺序组合器失败: %v\n", err)
		os.Exit(1)
	}

	// 运行顺序工作流
	input := &agent.Input{
		Messages: []*message.Message{
			message.NewUserMessage("请开始软件开发流程"),
		},
		Tools:   []tool.Tool{},
		Memory:  memoryStore,
		Options: map[string]any{"workflow": "sequential"},
	}

	fmt.Println("开始执行顺序工作流...")
	iterator := sequentialComposer.Run(ctx, input)

	for {
		event, ok := iterator.Next()
		if !ok {
			break
		}

		switch event.Type {
		case "thought":
			fmt.Printf("  ▶ 思考\n")
		case "final":
			fmt.Printf("  ✓ 完成\n")
		case "error":
			fmt.Printf("  ✗ 错误\n")
		default:
			fmt.Printf("  ▶ 事件: %s\n", event.Type)
		}
	}

	fmt.Println("顺序工作流执行完成！")
	fmt.Println()

	// 演示2: 并行执行工作流
	fmt.Println("=== 演示2: 并行执行工作流 ===")
	fmt.Println("场景: 并行开发任务 (分析师和设计师同时工作)")
	fmt.Println()

	parallelAgents := []agent.Agent{agent1, agent2}
	parallelComposer, err := compose.NewParallelAgent(&compose.ParallelConfig{
		Name:            "ParallelDemo",
		Description:     "并行执行演示",
		Agents:          parallelAgents,
		FailureStrategy: compose.FailureStrategyCollectAll,
	})
	if err != nil {
		fmt.Printf("创建并行组合器失败: %v\n", err)
		os.Exit(1)
	}

	// 运行并行工作流
	input2 := &agent.Input{
		Messages: []*message.Message{
			message.NewUserMessage("请并行执行分析和设计任务"),
		},
		Tools:   []tool.Tool{},
		Memory:  memoryStore,
		Options: map[string]any{"workflow": "parallel"},
	}

	fmt.Println("开始执行并行工作流...")
	iterator2 := parallelComposer.Run(ctx, input2)

	for {
		event, ok := iterator2.Next()
		if !ok {
			break
		}

		switch event.Type {
		case "final":
			fmt.Printf("  ✓ 并行完成\n")
		case "error":
			fmt.Printf("  ✗ 并行错误\n")
		default:
			fmt.Printf("  ▶ 并行事件: %s\n", event.Type)
		}
	}

	fmt.Println("并行工作流执行完成！")
	fmt.Println()

	// 演示3: 循环执行工作流
	fmt.Println("=== 演示3: 循环执行工作流 ===")
	fmt.Println("场景: 迭代开发过程 (开发 -> 测试，重复3次)")
	fmt.Println()

	loopAgent := NewMockAgent("迭代开发", "负责迭代开发", "完成一轮迭代开发", 100)
	loopComposer, err := compose.NewLoopAgent(&compose.LoopConfig{
		Name:          "LoopDemo",
		Description:   "循环执行演示",
		Agent:         loopAgent,
		MaxIterations: 3,
		StopCondition: func(iteration int, output any, history []any) bool {
			// 简单的停止条件：达到最大迭代次数
			return iteration >= 3
		},
	})
	if err != nil {
		fmt.Printf("创建循环组合器失败: %v\n", err)
		os.Exit(1)
	}

	// 运行循环工作流
	input3 := &agent.Input{
		Messages: []*message.Message{
			message.NewUserMessage("请开始迭代开发过程"),
		},
		Tools:   []tool.Tool{},
		Memory:  memoryStore,
		Options: map[string]any{"workflow": "loop"},
	}

	fmt.Println("开始执行循环工作流...")
	iterator3 := loopComposer.Run(ctx, input3)

	iterationCount := 0
	for {
		event, ok := iterator3.Next()
		if !ok {
			break
		}

		switch event.Type {
		case "final":
			iterationCount++
			fmt.Printf("  ✓ 第%d次迭代完成\n", iterationCount)
		case "error":
			fmt.Printf("  ✗ 第%d次迭代错误\n", iterationCount)
		default:
			fmt.Printf("  ▶ 循环事件: %s\n", event.Type)
		}
	}

	fmt.Println("循环工作流执行完成！")
	fmt.Println()

	// 演示4: 复合工作流
	fmt.Println("=== 演示4: 复合工作流 ===")
	fmt.Println("场景: 复杂项目流程 (并行准备 -> 顺序实施)")
	fmt.Println()

	// 第一阶段：并行准备
	prepAgents := []agent.Agent{
		NewMockAgent("需求分析", "分析项目需求", "需求分析完成", 80),
		NewMockAgent("技术调研", "进行技术调研", "技术调研完成", 90),
	}

	prepComposer, err := compose.NewParallelAgent(&compose.ParallelConfig{
		Name:            "PrepPhase",
		Description:     "准备阶段",
		Agents:          prepAgents,
		FailureStrategy: compose.FailureStrategyCollectAll,
	})
	if err != nil {
		fmt.Printf("创建准备阶段组合器失败: %v\n", err)
		os.Exit(1)
	}

	// 第二阶段：顺序实施
	implAgents := []agent.Agent{
		NewMockAgent("架构设计", "设计系统架构", "架构设计完成", 120),
		NewMockAgent("编码实现", "编写代码", "编码实现完成", 200),
		NewMockAgent("集成测试", "进行集成测试", "集成测试完成", 150),
	}

	implComposer, err := compose.NewSequentialAgent(&compose.SequentialConfig{
		Name:        "ImplPhase",
		Description: "实现阶段",
		Agents:      implAgents,
		FailureMode: compose.FailureModeStop,
	})
	if err != nil {
		fmt.Printf("创建实施阶段组合器失败: %v\n", err)
		os.Exit(1)
	}

	// 执行复合工作流
	fmt.Println("阶段1: 并行准备...")
	prepInput := &agent.Input{
		Messages: []*message.Message{
			message.NewUserMessage("开始项目准备阶段"),
		},
		Tools:   []tool.Tool{},
		Memory:  memoryStore,
		Options: map[string]any{"stage": "preparation"},
	}

	prepIterator := prepComposer.Run(ctx, prepInput)
	for {
		event, ok := prepIterator.Next()
		if !ok {
			break
		}

		switch event.Type {
		case "final":
			fmt.Printf("  ✓ 准备完成\n")
		default:
			fmt.Printf("  ▶ 准备事件: %s\n", event.Type)
		}
	}

	fmt.Println("阶段2: 顺序实施...")
	implInput := &agent.Input{
		Messages: []*message.Message{
			message.NewUserMessage("开始项目实施阶段"),
		},
		Tools:   []tool.Tool{},
		Memory:  memoryStore,
		Options: map[string]any{"stage": "implementation"},
	}

	implIterator := implComposer.Run(ctx, implInput)
	for {
		event, ok := implIterator.Next()
		if !ok {
			break
		}

		switch event.Type {
		case "final":
			fmt.Printf("  ✓ 实施完成\n")
		default:
			fmt.Printf("  ▶ 实施事件: %s\n", event.Type)
		}
	}

	fmt.Println("复合工作流执行完成！")
	fmt.Println()

	fmt.Println("=== 演示总结 ===")
	fmt.Println("✓ 顺序执行: 适用于有依赖关系的任务流")
	fmt.Println("✓ 并行执行: 适用于可以同时进行的独立任务")
	fmt.Println("✓ 循环执行: 适用于需要迭代优化的任务")
	fmt.Println("✓ 复合工作流: 适用于复杂的多阶段项目")
	fmt.Println()
	fmt.Println("AgentScope-Golang ADK架构演示完成！")
}
