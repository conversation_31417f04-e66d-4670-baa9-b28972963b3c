package tooling

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// AgentAsTool 将智能体包装为工具的适配器
type AgentAsTool struct {
	agent       agent.Agent
	name        string
	description string
	schema      string
	memory      memory.Store
}

// AgentAsToolConfig 智能体作为工具的配置
type AgentAsToolConfig struct {
	Agent       agent.Agent  // 要包装的智能体
	Name        string       // 工具名称（可选，默认使用智能体名称）
	Description string       // 工具描述（可选，默认使用智能体描述）
	Schema      string       // 工具参数模式（JSON Schema）
	Memory      memory.Store // 记忆存储（可选）
}

// NewAgentAsTool 创建智能体作为工具的适配器
func NewAgentAsTool(config *AgentAsToolConfig) (*AgentAsTool, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if config.Agent == nil {
		return nil, fmt.Errorf("智能体不能为空")
	}

	ctx := context.Background()

	name := config.Name
	if name == "" {
		name = config.Agent.Name(ctx)
	}

	description := config.Description
	if description == "" {
		description = config.Agent.Description(ctx)
	}

	schema := config.Schema
	if schema == "" {
		// 默认模式：接受任意字符串输入
		schema = `{
			"type": "object",
			"properties": {
				"input": {
					"type": "string",
					"description": "输入给智能体的消息"
				}
			},
			"required": ["input"]
		}`
	}

	memoryStore := config.Memory
	if memoryStore == nil {
		memoryStore = memory.NewMemoryStore()
	}

	return &AgentAsTool{
		agent:       config.Agent,
		name:        name,
		description: description,
		schema:      schema,
		memory:      memoryStore,
	}, nil
}

// Name 返回工具名称
func (a *AgentAsTool) Name() string {
	return a.name
}

// Description 返回工具描述
func (a *AgentAsTool) Description() string {
	return a.description
}

// Schema 返回工具参数模式
func (a *AgentAsTool) Schema() *tool.JSONSchema {
	// 解析JSON Schema字符串为JSONSchema结构
	var schema tool.JSONSchema
	if err := json.Unmarshal([]byte(a.schema), &schema); err != nil {
		// 如果解析失败，返回默认模式
		return &tool.JSONSchema{
			Type: "object",
			Properties: map[string]*tool.JSONSchema{
				"input": {
					Type:        "string",
					Description: "输入给智能体的消息",
				},
			},
			Required: []string{"input"},
		}
	}
	return &schema
}

// Execute 执行工具（运行智能体）
func (a *AgentAsTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	// 提取输入消息
	inputMessage, ok := params["input"].(string)
	if !ok {
		return "", fmt.Errorf("输入格式错误，需要包含 'input' 字段")
	}

	// 创建用户消息
	userMsg := message.NewUserMessage(inputMessage)

	// 构建智能体输入
	agentInput := runtime.NewInput()
	agentInput.AddMessage(userMsg)
	agentInput.Memory = a.memory

	// 运行智能体
	iterator := a.agent.Run(ctx, agentInput)

	// 收集智能体的输出
	var finalResult string
	var errorResult error

	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}

		// 处理不同类型的事件
		switch ev.Type {
		case event.EventFinal:
			if finalData, ok := ev.Data.(*event.FinalData); ok {
				finalResult = finalData.Content
			} else {
				finalResult = fmt.Sprintf("%v", ev.Data)
			}
		case event.EventError:
			if errorData, ok := ev.Data.(*event.ErrorData); ok {
				errorResult = fmt.Errorf("智能体执行错误: %s", errorData.Message)
			} else {
				errorResult = fmt.Errorf("智能体执行错误: %v", ev.Data)
			}
		}
	}

	// 检查执行结果
	if errorResult != nil {
		return "", errorResult
	}

	if finalResult == "" {
		return "", fmt.Errorf("智能体没有产生最终结果")
	}

	return finalResult, nil
}

// Validate 验证输入
func (a *AgentAsTool) Validate(input string) error {
	// 尝试解析JSON
	var inputData map[string]any
	if err := json.Unmarshal([]byte(input), &inputData); err != nil {
		return fmt.Errorf("输入必须是有效的JSON格式: %w", err)
	}

	// 检查必需字段
	if _, ok := inputData["input"]; !ok {
		return fmt.Errorf("输入必须包含 'input' 字段")
	}

	// 检查input字段类型
	if _, ok := inputData["input"].(string); !ok {
		return fmt.Errorf("'input' 字段必须是字符串类型")
	}

	return nil
}

// AgentAsToolRegistry 智能体作为工具的注册表
type AgentAsToolRegistry struct {
	tools map[string]*AgentAsTool
}

// NewAgentAsToolRegistry 创建智能体作为工具的注册表
func NewAgentAsToolRegistry() *AgentAsToolRegistry {
	return &AgentAsToolRegistry{
		tools: make(map[string]*AgentAsTool),
	}
}

// Register 注册智能体作为工具
func (r *AgentAsToolRegistry) Register(config *AgentAsToolConfig) error {
	tool, err := NewAgentAsTool(config)
	if err != nil {
		return fmt.Errorf("创建智能体工具失败: %w", err)
	}

	r.tools[tool.Name()] = tool
	return nil
}

// Get 获取注册的工具
func (r *AgentAsToolRegistry) Get(name string) (*AgentAsTool, bool) {
	tool, exists := r.tools[name]
	return tool, exists
}

// List 列出所有注册的工具
func (r *AgentAsToolRegistry) List() []tool.Tool {
	var tools []tool.Tool
	for _, tool := range r.tools {
		tools = append(tools, tool)
	}
	return tools
}

// Remove 移除注册的工具
func (r *AgentAsToolRegistry) Remove(name string) bool {
	if _, exists := r.tools[name]; exists {
		delete(r.tools, name)
		return true
	}
	return false
}

// Clear 清空所有注册的工具
func (r *AgentAsToolRegistry) Clear() {
	r.tools = make(map[string]*AgentAsTool)
}

// Count 返回注册的工具数量
func (r *AgentAsToolRegistry) Count() int {
	return len(r.tools)
}

// AgentToolChain 智能体工具链，用于组合多个智能体作为工具
type AgentToolChain struct {
	registry *AgentAsToolRegistry
	agents   []agent.Agent
}

// NewAgentToolChain 创建智能体工具链
func NewAgentToolChain() *AgentToolChain {
	return &AgentToolChain{
		registry: NewAgentAsToolRegistry(),
		agents:   make([]agent.Agent, 0),
	}
}

// AddAgent 添加智能体到工具链
func (c *AgentToolChain) AddAgent(ag agent.Agent, toolName string, toolDescription string) error {
	config := &AgentAsToolConfig{
		Agent:       ag,
		Name:        toolName,
		Description: toolDescription,
	}

	if err := c.registry.Register(config); err != nil {
		return fmt.Errorf("添加智能体到工具链失败: %w", err)
	}

	c.agents = append(c.agents, ag)
	return nil
}

// GetTools 获取工具链中的所有工具
func (c *AgentToolChain) GetTools() []tool.Tool {
	return c.registry.List()
}

// GetTool 获取指定名称的工具
func (c *AgentToolChain) GetTool(name string) (tool.Tool, bool) {
	tool, exists := c.registry.Get(name)
	if !exists {
		return nil, false
	}
	return tool, true
}

// ExecuteTool 执行指定名称的工具
func (c *AgentToolChain) ExecuteTool(ctx context.Context, toolName string, input string) (string, error) {
	tool, exists := c.registry.Get(toolName)
	if !exists {
		return "", fmt.Errorf("工具 %s 不存在", toolName)
	}

	// 将JSON字符串解析为map[string]any
	var params map[string]any
	if err := json.Unmarshal([]byte(input), &params); err != nil {
		return "", fmt.Errorf("解析输入参数失败: %w", err)
	}

	// 执行工具
	result, err := tool.Execute(ctx, params)
	if err != nil {
		return "", err
	}

	// 将结果转换为字符串
	if resultStr, ok := result.(string); ok {
		return resultStr, nil
	}

	// 如果不是字符串，尝试JSON序列化
	resultBytes, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("序列化结果失败: %w", err)
	}

	return string(resultBytes), nil
}

// GetAgents 获取工具链中的所有智能体
func (c *AgentToolChain) GetAgents() []agent.Agent {
	return c.agents
}

// Clear 清空工具链
func (c *AgentToolChain) Clear() {
	c.registry.Clear()
	c.agents = make([]agent.Agent, 0)
}
