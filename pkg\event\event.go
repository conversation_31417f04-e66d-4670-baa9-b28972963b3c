package event

import (
	"encoding/json"
	"time"
)

// Type 表示事件类型
type Type string

const (
	// EventToken 增量 Token 事件，用于流式输出
	EventToken Type = "token"
	// EventThought 推理思考事件，包含 Agent 的思考过程（可脱敏）
	EventThought Type = "thought"
	// EventToolCall 工具调用请求事件，包含工具名称和参数
	EventToolCall Type = "tool_call"
	// EventToolResult 工具调用结果事件，包含执行结果或错误
	EventToolResult Type = "tool_result"
	// EventTransfer 控制权转移事件，用于 Agent 间协作
	EventTransfer Type = "transfer"
	// EventCheckpoint Checkpoint 保存/恢复事件，用于中断与恢复
	EventCheckpoint Type = "checkpoint"
	// EventFinal 最终结果事件，表示 Agent 执行完成
	EventFinal Type = "final"
	// EventError 错误事件，包含执行过程中的错误信息
	EventError Type = "error"
)

// Event 表示统一的事件结构，是框架中所有运行时过程的统一表达
type Event struct {
	// Type 事件类型
	Type Type `json:"type"`
	// Data 事件数据，具体内容根据事件类型而定
	Data any `json:"data,omitempty"`
	// Err 错误信息，仅在 EventError 类型时有效
	Err error `json:"error,omitempty"`
	// At 事件发生时间戳
	At time.Time `json:"at"`
	// Metadata 可选的元数据，用于携带额外信息（如 TraceID、SpanID 等）
	Metadata map[string]any `json:"metadata,omitempty"`
}

// TokenData 表示 Token 事件的数据结构
type TokenData struct {
	// Content Token 内容
	Content string `json:"content"`
	// Delta 是否为增量内容
	Delta bool `json:"delta"`
}

// GetContent 获取Token内容
func (t *TokenData) GetContent() string {
	return t.Content
}

// ThoughtData 表示思考事件的数据结构
type ThoughtData struct {
	// Content 思考内容
	Content string `json:"content"`
	// Reasoning 推理过程描述
	Reasoning string `json:"reasoning,omitempty"`
}

// ToolCallData 表示工具调用事件的数据结构
type ToolCallData struct {
	// ID 工具调用的唯一标识
	ID string `json:"id"`
	// Name 工具名称
	Name string `json:"name"`
	// Args 工具参数
	Args map[string]any `json:"args"`
}

// ToolResultData 表示工具调用结果事件的数据结构
type ToolResultData struct {
	// ID 对应的工具调用标识
	ID string `json:"id"`
	// Name 工具名称
	Name string `json:"name"`
	// Result 执行结果
	Result map[string]any `json:"result,omitempty"`
	// Error 执行错误
	Error string `json:"error,omitempty"`
	// Success 是否执行成功
	Success bool `json:"success"`
}

// TransferData 表示控制权转移事件的数据结构
type TransferData struct {
	// FromAgent 源 Agent 名称
	FromAgent string `json:"from_agent"`
	// ToAgent 目标 Agent 名称
	ToAgent string `json:"to_agent"`
	// Reason 转移原因
	Reason string `json:"reason,omitempty"`
}

// CheckpointData 表示检查点事件的数据结构
type CheckpointData struct {
	// SessionID 会话标识
	SessionID string `json:"session_id"`
	// Action 检查点操作类型（save/load/delete）
	Action string `json:"action"`
	// Success 是否操作成功
	Success bool `json:"success"`
}

// FinalData 表示最终结果事件的数据结构
type FinalData struct {
	// Content 最终内容
	Content string `json:"content"`
	// Summary 结果摘要
	Summary string `json:"summary,omitempty"`
	// Usage 资源使用情况
	Usage map[string]any `json:"usage,omitempty"`
}

// GetContent 获取最终内容
func (f *FinalData) GetContent() string {
	return f.Content
}

// ErrorData 表示错误事件的数据结构
type ErrorData struct {
	// Message 错误消息
	Message string `json:"message"`
	// Code 错误代码
	Code string `json:"code,omitempty"`
	// Details 错误详情
	Details map[string]any `json:"details,omitempty"`
}

// GetMessage 获取错误消息
func (e *ErrorData) GetMessage() string {
	return e.Message
}

// NewTokenEvent 创建 Token 事件
func NewTokenEvent(content string, delta bool) *Event {
	return &Event{
		Type: EventToken,
		Data: &TokenData{
			Content: content,
			Delta:   delta,
		},
		At: time.Now(),
	}
}

// NewThoughtEvent 创建思考事件
func NewThoughtEvent(content, reasoning string) *Event {
	return &Event{
		Type: EventThought,
		Data: &ThoughtData{
			Content:   content,
			Reasoning: reasoning,
		},
		At: time.Now(),
	}
}

// NewToolCallEvent 创建工具调用事件
func NewToolCallEvent(id, name string, args map[string]any) *Event {
	return &Event{
		Type: EventToolCall,
		Data: &ToolCallData{
			ID:   id,
			Name: name,
			Args: args,
		},
		At: time.Now(),
	}
}

// NewToolResultEvent 创建工具调用结果事件
func NewToolResultEvent(id, name string, result map[string]any, err error) *Event {
	data := &ToolResultData{
		ID:      id,
		Name:    name,
		Success: err == nil,
	}

	if err != nil {
		data.Error = err.Error()
	} else {
		data.Result = result
	}

	return &Event{
		Type: EventToolResult,
		Data: data,
		At:   time.Now(),
	}
}

// NewTransferEvent 创建控制权转移事件
func NewTransferEvent(fromAgent, toAgent, reason string) *Event {
	return &Event{
		Type: EventTransfer,
		Data: &TransferData{
			FromAgent: fromAgent,
			ToAgent:   toAgent,
			Reason:    reason,
		},
		At: time.Now(),
	}
}

// NewCheckpointEvent 创建检查点事件
func NewCheckpointEvent(sessionID, action string, success bool) *Event {
	return &Event{
		Type: EventCheckpoint,
		Data: &CheckpointData{
			SessionID: sessionID,
			Action:    action,
			Success:   success,
		},
		At: time.Now(),
	}
}

// NewFinalEvent 创建最终结果事件
func NewFinalEvent(content, summary string, usage map[string]any) *Event {
	return &Event{
		Type: EventFinal,
		Data: &FinalData{
			Content: content,
			Summary: summary,
			Usage:   usage,
		},
		At: time.Now(),
	}
}

// NewErrorEvent 创建错误事件
func NewErrorEvent(err error, code string, details map[string]any) *Event {
	return &Event{
		Type: EventError,
		Data: &ErrorData{
			Message: err.Error(),
			Code:    code,
			Details: details,
		},
		Err: err,
		At:  time.Now(),
	}
}

// String 返回事件的字符串表示
func (e *Event) String() string {
	data, _ := json.Marshal(e)
	return string(data)
}

// IsError 判断是否为错误事件
func (e *Event) IsError() bool {
	return e.Type == EventError
}

// IsFinal 判断是否为最终结果事件
func (e *Event) IsFinal() bool {
	return e.Type == EventFinal
}

// WithMetadata 为事件添加元数据
func (e *Event) WithMetadata(key string, value any) *Event {
	if e.Metadata == nil {
		e.Metadata = make(map[string]any)
	}
	e.Metadata[key] = value
	return e
}

// GetMetadata 获取元数据
func (e *Event) GetMetadata(key string) (any, bool) {
	if e.Metadata == nil {
		return nil, false
	}
	value, exists := e.Metadata[key]
	return value, exists
}
