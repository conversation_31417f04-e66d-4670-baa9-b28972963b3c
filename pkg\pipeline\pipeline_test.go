//go:build ignore
// +build ignore

package pipeline

import (
	"context"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/message"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config.Type != PipelineTypeSequential {
		t.Errorf("Expected default type %s, got %s", PipelineTypeSequential, config.Type)
	}
	if config.Timeout != 60*time.Second {
		t.Errorf("Expected default timeout 60s, got %s", config.Timeout)
	}
	if config.MaxRetries != 3 {
		t.<PERSON>("Expected default max retries 3, got %d", config.MaxRetries)
	}
	if config.Parameters == nil {
		t.Error("Expected parameters map to be initialized")
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
	}{
		{
			name: "valid config",
			config: &Config{
				Name: "test-pipeline",
				Type: PipelineTypeSequential,
			},
			expectError: false,
		},
		{
			name: "empty name",
			config: &Config{
				Name: "",
				Type: PipelineTypeSequential,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError && err == nil {
				t.Error("Expected validation error, but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no validation error, but got: %v", err)
			}
		})
	}
}

func TestConfigClone(t *testing.T) {
	original := &Config{
		Name:        "test-pipeline",
		Type:        PipelineTypeSequential,
		Description: "Test description",
		Parameters: map[string]interface{}{
			"param1": "value1",
			"param2": 42,
		},
	}

	clone := original.Clone()

	// Verify clone is equal but not the same instance
	if clone == original {
		t.Error("Clone should not be the same instance as original")
	}
	if clone.Name != original.Name {
		t.Errorf("Name mismatch: expected '%s', got '%s'", original.Name, clone.Name)
	}

	// Verify deep copy of Parameters
	if len(clone.Parameters) == 0 && len(original.Parameters) == 0 {
		// Both empty, that's fine
	} else if len(clone.Parameters) != len(original.Parameters) {
		t.Error("Parameters should be deep copied with same length")
	}
	clone.Parameters["param1"] = "modified"
	if original.Parameters["param1"] != "value1" {
		t.Error("Original parameters should not be affected by clone modifications")
	}
}

func TestPipelineResult(t *testing.T) {
	result := NewPipelineResult()

	// Test initial state
	if result.Success {
		t.Error("Expected initial success to be false")
	}
	if result.StartTime.IsZero() {
		t.Error("Expected start time to be set")
	}
	if len(result.ExecutionLog) != 0 {
		t.Errorf("Expected empty execution log, got %d entries", len(result.ExecutionLog))
	}

	// Test adding steps
	step := ExecutionStep{
		AgentID:   "test-agent",
		AgentType: agent.AgentTypeCustom,
		StartTime: time.Now(),
		EndTime:   time.Now().Add(time.Second),
		Duration:  time.Second,
		Success:   true,
	}
	result.AddStep(step)

	if len(result.ExecutionLog) != 1 {
		t.Errorf("Expected 1 execution step, got %d", len(result.ExecutionLog))
	}

	// Test success setting
	testMsg := message.NewMessage("test", "test", message.NewTextContent("test"))
	result.SetSuccess(testMsg)

	if !result.Success {
		t.Error("Expected success to be true after SetSuccess")
	}
	if result.Message != testMsg {
		t.Error("Expected message to be set")
	}
	if result.EndTime.IsZero() {
		t.Error("Expected end time to be set")
	}
	if result.Duration < 0 {
		t.Error("Expected duration to be non-negative")
	}

	// Test success rate
	successRate := result.GetSuccessRate()
	if successRate != 1.0 {
		t.Errorf("Expected success rate 1.0, got %f", successRate)
	}

	// Add a failed step
	failedStep := ExecutionStep{
		AgentID:   "failed-agent",
		AgentType: agent.AgentTypeCustom,
		Success:   false,
	}
	result.AddStep(failedStep)

	successRate = result.GetSuccessRate()
	if successRate != 0.5 {
		t.Errorf("Expected success rate 0.5, got %f", successRate)
	}

	// Test successful and failed steps
	successfulSteps := result.GetSuccessfulSteps()
	if len(successfulSteps) != 1 {
		t.Errorf("Expected 1 successful step, got %d", len(successfulSteps))
	}

	failedSteps := result.GetFailedSteps()
	if len(failedSteps) != 1 {
		t.Errorf("Expected 1 failed step, got %d", len(failedSteps))
	}
}

func TestExecutionContext(t *testing.T) {
	ctx := NewExecutionContext("test-pipeline")

	if ctx.PipelineName != "test-pipeline" {
		t.Errorf("Expected pipeline name 'test-pipeline', got '%s'", ctx.PipelineName)
	}
	if ctx.RequestID == "" {
		t.Error("Expected request ID to be generated")
	}
	if ctx.StartTime.IsZero() {
		t.Error("Expected start time to be set")
	}

	// Test parameter operations
	ctx.SetParameter("test_param", "test_value")
	value, exists := ctx.GetParameter("test_param")
	if !exists {
		t.Error("Expected parameter to exist")
	}
	if value != "test_value" {
		t.Errorf("Expected parameter value 'test_value', got '%v'", value)
	}

	// Test metadata operations
	ctx.SetMetadata("test_meta", "meta_value")
	metaValue, exists := ctx.GetMetadata("test_meta")
	if !exists {
		t.Error("Expected metadata to exist")
	}
	if metaValue != "meta_value" {
		t.Errorf("Expected metadata value 'meta_value', got '%v'", metaValue)
	}

	// Test timeout functionality
	ctx.Timeout = 100 * time.Millisecond
	if ctx.IsExpired() {
		t.Error("Expected context to not be expired immediately")
	}

	time.Sleep(150 * time.Millisecond)
	if !ctx.IsExpired() {
		t.Error("Expected context to be expired after timeout")
	}
}

// Mock agent for testing
type mockAgent struct {
	id          string
	agentType   agent.AgentType
	replyFunc   func(ctx context.Context, msg *message.Message) (*message.Message, error)
	state       agent.AgentState
	shouldError bool
}

func newMockAgent(id string, agentType agent.AgentType) *mockAgent {
	return &mockAgent{
		id:        id,
		agentType: agentType,
		state:     agent.AgentStateIdle,
		replyFunc: func(ctx context.Context, msg *message.Message) (*message.Message, error) {
			return message.NewMessage(id, msg.Sender, message.NewTextContent("Mock response from "+id)), nil
		},
	}
}

func (ma *mockAgent) ID() string                              { return ma.id }
func (ma *mockAgent) Name() string                            { return ma.id }
func (ma *mockAgent) Type() agent.AgentType                   { return ma.agentType }
func (ma *mockAgent) Description() string                     { return "Mock agent" }
func (ma *mockAgent) GetState() agent.AgentState              { return ma.state }
func (ma *mockAgent) SetState(state agent.AgentState) error   { ma.state = state; return nil }
func (ma *mockAgent) Initialize() error                       { return nil }
func (ma *mockAgent) Shutdown() error                         { return nil }
func (ma *mockAgent) GetConfig() *agent.Config                { return &agent.Config{ID: ma.id} }
func (ma *mockAgent) UpdateConfig(config *agent.Config) error { return nil }

func (ma *mockAgent) Reply(ctx context.Context, msg *message.Message) (*message.Message, error) {
	if ma.shouldError {
		return nil, &ValidationError{Field: "test", Message: "mock error"}
	}
	return ma.replyFunc(ctx, msg)
}

func TestSequentialPipeline(t *testing.T) {
	config := &Config{
		Name:       "test-pipeline",
		Type:       PipelineTypeSequential,
		Timeout:    30 * time.Second,
		MaxRetries: 1,
	}

	pipeline, err := NewSequentialPipeline(config)
	if err != nil {
		t.Fatalf("Failed to create sequential pipeline: %v", err)
	}

	// Test initial state
	if pipeline.GetAgentCount() != 0 {
		t.Errorf("Expected 0 agents initially, got %d", pipeline.GetAgentCount())
	}

	// Create mock agents
	agent1 := newMockAgent("agent1", agent.AgentTypeCustom)
	agent2 := newMockAgent("agent2", agent.AgentTypeCustom)

	// Test adding agents
	err = pipeline.AddAgent(agent1)
	if err != nil {
		t.Fatalf("Failed to add agent1: %v", err)
	}

	err = pipeline.AddAgent(agent2)
	if err != nil {
		t.Fatalf("Failed to add agent2: %v", err)
	}

	if pipeline.GetAgentCount() != 2 {
		t.Errorf("Expected 2 agents, got %d", pipeline.GetAgentCount())
	}

	// Test duplicate agent addition
	err = pipeline.AddAgent(agent1)
	if err == nil {
		t.Error("Expected error when adding duplicate agent")
	}

	// Test pipeline execution
	ctx := context.Background()
	inputMsg := message.NewMessage("user", "agent1", message.NewTextContent("Hello"))

	result, err := pipeline.Execute(ctx, inputMsg)
	if err != nil {
		t.Fatalf("Failed to execute pipeline: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected successful execution, got error: %v", result.Error)
	}

	if len(result.ExecutionLog) != 2 {
		t.Errorf("Expected 2 execution steps, got %d", len(result.ExecutionLog))
	}

	// Verify execution order
	if result.ExecutionLog[0].AgentID != "agent1" {
		t.Errorf("Expected first step to be agent1, got %s", result.ExecutionLog[0].AgentID)
	}
	if result.ExecutionLog[1].AgentID != "agent2" {
		t.Errorf("Expected second step to be agent2, got %s", result.ExecutionLog[1].AgentID)
	}

	// Test agent removal
	err = pipeline.RemoveAgent("agent1")
	if err != nil {
		t.Fatalf("Failed to remove agent1: %v", err)
	}

	if pipeline.GetAgentCount() != 1 {
		t.Errorf("Expected 1 agent after removal, got %d", pipeline.GetAgentCount())
	}

	// Test removing non-existent agent
	err = pipeline.RemoveAgent("nonexistent")
	if err == nil {
		t.Error("Expected error when removing non-existent agent")
	}

	// Test clearing pipeline
	err = pipeline.Clear()
	if err != nil {
		t.Fatalf("Failed to clear pipeline: %v", err)
	}

	if pipeline.GetAgentCount() != 0 {
		t.Errorf("Expected 0 agents after clear, got %d", pipeline.GetAgentCount())
	}
}

func TestSequentialPipelineWithErrors(t *testing.T) {
	config := &Config{
		Name:       "test-pipeline",
		Type:       PipelineTypeSequential,
		Timeout:    30 * time.Second,
		MaxRetries: 1,
	}

	pipeline, err := NewSequentialPipeline(config)
	if err != nil {
		t.Fatalf("Failed to create sequential pipeline: %v", err)
	}

	// Create mock agents, one that will fail
	agent1 := newMockAgent("agent1", agent.AgentTypeCustom)
	agent2 := newMockAgent("agent2", agent.AgentTypeCustom)
	agent2.shouldError = true

	pipeline.AddAgent(agent1)
	pipeline.AddAgent(agent2)

	// Test pipeline execution with error
	ctx := context.Background()
	inputMsg := message.NewMessage("user", "agent1", message.NewTextContent("Hello"))

	result, err := pipeline.Execute(ctx, inputMsg)
	if err != nil {
		t.Fatalf("Failed to execute pipeline: %v", err)
	}

	if result.Success {
		t.Error("Expected failed execution due to agent error")
	}

	if result.Error == nil {
		t.Error("Expected error to be set in result")
	}

	// Should have 2 steps: one successful, one failed
	if len(result.ExecutionLog) != 2 {
		t.Errorf("Expected 2 execution steps, got %d", len(result.ExecutionLog))
	}

	if result.ExecutionLog[0].Success != true {
		t.Error("Expected first step to be successful")
	}

	if result.ExecutionLog[1].Success != false {
		t.Error("Expected second step to fail")
	}
}

func TestSequentialPipelineTimeout(t *testing.T) {
	config := &Config{
		Name:       "test-pipeline",
		Type:       PipelineTypeSequential,
		Timeout:    100 * time.Millisecond, // Very short timeout
		MaxRetries: 0,
	}

	pipeline, err := NewSequentialPipeline(config)
	if err != nil {
		t.Fatalf("Failed to create sequential pipeline: %v", err)
	}

	// Create mock agent with slow response
	slowAgent := &MockAgent{
		BaseAgent: &agent.BaseAgent{},
		id:        "slow-agent",
		agentType: agent.AgentTypeCustom,
		delay:     200 * time.Millisecond, // Longer than pipeline timeout
		response:  "Slow response",
	}

	pipeline.AddAgent(slowAgent)

	// Test pipeline execution with timeout
	// 设置一个很短的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Millisecond)
	defer cancel()

	inputMsg := message.NewMessage("user", "slow-agent", message.NewTextContent("Hello"))

	result, err := pipeline.Execute(ctx, inputMsg)
	// Pipeline可能返回result而不是error
	if err == nil && result != nil && result.Success {
		t.Error("Expected failed execution due to timeout")
	}

	// 检查是否有超时相关的错误
	if result != nil && result.Error == nil && result.Success {
		t.Error("Expected timeout error to be set in result or execution to fail")
	}
}

func TestSequentialPipelineEmptyAgents(t *testing.T) {
	config := &Config{
		Name: "empty-pipeline",
		Type: PipelineTypeSequential,
	}

	pipeline, err := NewSequentialPipeline(config)
	if err != nil {
		t.Fatalf("Failed to create sequential pipeline: %v", err)
	}

	// Test execution with no agents
	ctx := context.Background()
	inputMsg := message.NewMessage("user", "nobody", message.NewTextContent("Hello"))

	result, err := pipeline.Execute(ctx, inputMsg)
	if err != nil {
		t.Fatalf("Failed to execute pipeline: %v", err)
	}

	if result.Success {
		t.Error("Expected failed execution with no agents")
	}

	if result.Error == nil {
		t.Error("Expected error for empty pipeline")
	}
}

func TestSequentialPipelineConfigUpdate(t *testing.T) {
	config := &Config{
		Name: "test-pipeline",
		Type: PipelineTypeSequential,
	}

	pipeline, err := NewSequentialPipeline(config)
	if err != nil {
		t.Fatalf("Failed to create sequential pipeline: %v", err)
	}

	// Test config retrieval
	retrievedConfig := pipeline.GetConfig()
	if retrievedConfig.Name != config.Name {
		t.Errorf("Expected config name '%s', got '%s'", config.Name, retrievedConfig.Name)
	}

	// Test config update
	newConfig := &Config{
		Name:        "updated-pipeline",
		Type:        PipelineTypeSequential,
		Description: "Updated description",
	}

	err = pipeline.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	retrievedConfig = pipeline.GetConfig()
	if retrievedConfig.Name != newConfig.Name {
		t.Errorf("Expected updated config name '%s', got '%s'", newConfig.Name, retrievedConfig.Name)
	}
	if retrievedConfig.Description != newConfig.Description {
		t.Errorf("Expected updated description '%s', got '%s'", newConfig.Description, retrievedConfig.Description)
	}
}
func TestSequentialPipelineWithRealAgents(t *testing.T) {
	// 创建测试用的Agent配置
	userConfig := &agent.Config{
		ID:   "user-agent",
		Name: "User Agent",
		Type: agent.AgentTypeUser,
		Parameters: map[string]interface{}{
			"auto_input": "Hello, how are you?",
		},
	}

	// 创建Agent实例
	userAgent, err := agent.NewUserAgent(userConfig)
	if err != nil {
		t.Fatalf("Failed to create user agent: %v", err)
	}

	// 注意：这里我们不创建真实的AssistantAgent，因为它需要LLM API
	// 而是创建一个模拟的Agent用于测试
	mockAssistant := &MockAgent{
		BaseAgent: &agent.BaseAgent{},
		id:        "assistant-agent",
		agentType: agent.AgentTypeAssistant,
	}

	// 创建Pipeline
	pipeline, err := NewSequentialPipeline(&Config{
		Name: "Test Pipeline",
	})
	if err != nil {
		t.Fatalf("Failed to create pipeline: %v", err)
	}

	// 添加Agent到Pipeline
	err = pipeline.AddAgent(userAgent)
	if err != nil {
		t.Fatalf("Failed to add user agent: %v", err)
	}

	err = pipeline.AddAgent(mockAssistant)
	if err != nil {
		t.Fatalf("Failed to add assistant agent: %v", err)
	}

	// 创建初始消息
	initialMsg := message.NewMessage("system", "user-agent", message.NewTextContent("Start conversation"))

	// 执行Pipeline
	ctx := context.Background()
	result, err := pipeline.Execute(ctx, initialMsg)
	if err != nil {
		t.Fatalf("Pipeline execution failed: %v", err)
	}

	// 验证结果
	if result == nil {
		t.Fatal("Pipeline result should not be nil")
	}

	if !result.Success {
		t.Errorf("Pipeline should succeed, got error: %v", result.Error)
	}

	if result.Message == nil {
		t.Error("Pipeline should produce a message")
	}

	// 验证执行统计
	if result.Duration < 0 {
		t.Error("Execution duration should be non-negative")
	}

	if len(result.ExecutionLog) != 2 {
		t.Errorf("Expected 2 agents executed, got %d", len(result.ExecutionLog))
	}
}

func TestPipelineErrorHandling(t *testing.T) {
	pipeline, err := NewSequentialPipeline(&Config{
		Name: "Error Test Pipeline",
	})
	if err != nil {
		t.Fatalf("Failed to create pipeline: %v", err)
	}

	// 创建一个会出错的Agent
	errorAgent := &MockAgent{
		BaseAgent:   &agent.BaseAgent{},
		id:          "error-agent",
		agentType:   agent.AgentTypeCustom,
		shouldError: true,
	}

	err = pipeline.AddAgent(errorAgent)
	if err != nil {
		t.Fatalf("Failed to add error agent: %v", err)
	}

	// 执行Pipeline
	ctx := context.Background()
	initialMsg := message.NewMessage("system", "error-agent", message.NewTextContent("Test message"))

	result, err := pipeline.Execute(ctx, initialMsg)

	// Pipeline执行应该返回结果，但标记为失败
	if err != nil {
		t.Fatalf("Pipeline execution should not return error directly: %v", err)
	}

	if result == nil {
		t.Fatal("Pipeline result should not be nil")
	}

	if result.Success {
		t.Error("Pipeline should fail when agent returns error")
	}

	if result.Error == nil {
		t.Error("Pipeline result should contain error information")
	}
}

func TestPipelineContextCancellation(t *testing.T) {
	pipeline, err := NewSequentialPipeline(&Config{
		Name: "Cancel Test Pipeline",
	})
	if err != nil {
		t.Fatalf("Failed to create pipeline: %v", err)
	}

	// 创建一个慢速Agent
	slowAgent := &MockAgent{
		BaseAgent: &agent.BaseAgent{},
		id:        "slow-agent",
		agentType: agent.AgentTypeCustom,
		delay:     2 * time.Second,
	}

	err = pipeline.AddAgent(slowAgent)
	if err != nil {
		t.Fatalf("Failed to add slow agent: %v", err)
	}

	// 创建会被取消的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Millisecond)
	defer cancel()

	initialMsg := message.NewMessage("system", "slow-agent", message.NewTextContent("Test message"))

	result, err := pipeline.Execute(ctx, initialMsg)

	// 应该因为上下文取消而失败
	if err == nil && (result == nil || result.Success) {
		t.Error("Pipeline should return error or failed result when context is cancelled")
	}

	if result != nil && result.Success {
		t.Error("Pipeline should not succeed when context is cancelled")
	}
}

// MockAgent 用于测试的模拟Agent
type MockAgent struct {
	*agent.BaseAgent
	id          string
	agentType   agent.AgentType
	shouldError bool
	delay       time.Duration
	response    string
}

func (m *MockAgent) ID() string {
	return m.id
}

func (m *MockAgent) Name() string {
	return m.id
}

func (m *MockAgent) Type() agent.AgentType {
	return m.agentType
}

func (m *MockAgent) Reply(ctx context.Context, msg *message.Message) (*message.Message, error) {
	// 模拟延迟
	if m.delay > 0 {
		select {
		case <-time.After(m.delay):
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 模拟错误
	if m.shouldError {
		return nil, errors.NewAgentError("agent_error", "mock agent error")
	}

	// 返回模拟响应
	response := m.response
	if response == "" {
		response = "Mock response from " + m.id
	}

	return message.NewMessage(m.id, msg.Sender, message.NewTextContent(response)), nil
}

func (m *MockAgent) Initialize() error {
	return nil
}

func (m *MockAgent) Shutdown() error {
	return nil
}
