package llm

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"
)

func TestNewDoubaoClient(t *testing.T) {
	// 保存原始环境变量
	originalAPIKey := os.Getenv("ARK_API_KEY")
	defer func() {
		if originalAPIKey != "" {
			os.Setenv("ARK_API_KEY", originalAPIKey)
		} else {
			os.Unsetenv("ARK_API_KEY")
		}
	}()

	tests := []struct {
		name        string
		config      *Config
		envAPIKey   string
		expectError bool
		errorMsg    string
	}{
		{
			name: "有效配置",
			config: &Config{
				APIKey:  "test-api-key",
				BaseURL: "https://test.example.com",
				Model:   "ep-test123",
			},
			expectError: false,
		},
		{
			name: "使用环境变量 API Key",
			config: &Config{
				BaseURL: "https://test.example.com",
				Model:   "ep-test123",
			},
			envAPIKey:   "env-api-key",
			expectError: false,
		},
		{
			name: "缺少 API Key",
			config: &Config{
				BaseURL: "https://test.example.com",
				Model:   "ep-test123",
			},
			expectError: true,
			errorMsg:    "豆包 API key 是必需的",
		},
		{
			name:        "空配置使用默认值",
			config:      nil,
			envAPIKey:   "env-api-key",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置环境变量
			if tt.envAPIKey != "" {
				os.Setenv("ARK_API_KEY", tt.envAPIKey)
			} else {
				os.Unsetenv("ARK_API_KEY")
				os.Unsetenv("DOUBAO_API_KEY")
				os.Unsetenv("VOLC_API_KEY")
			}

			client, err := NewDoubaoClient(tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有返回错误")
				} else if !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("错误消息不匹配，期望包含 '%s'，实际 '%s'", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但返回了错误: %v", err)
				return
			}

			if client == nil {
				t.Errorf("客户端不应为空")
				return
			}

			// 验证默认值
			if tt.config == nil {
				if client.config.BaseURL != "https://ark.cn-beijing.volces.com/api/v3" {
					t.Errorf("默认 BaseURL 不正确，期望 'https://ark.cn-beijing.volces.com/api/v3'，实际 '%s'", client.config.BaseURL)
				}
				if client.config.Model != "ep-xxx" {
					t.Errorf("默认模型不正确，期望 'ep-xxx'，实际 '%s'", client.config.Model)
				}
			}

			// 验证模型信息
			modelInfo := client.GetModelInfo()
			if modelInfo == nil {
				t.Errorf("模型信息不应为空")
			} else {
				if modelInfo.OwnedBy != "bytedance" {
					t.Errorf("模型所有者不正确，期望 'bytedance'，实际 '%s'", modelInfo.OwnedBy)
				}
			}
		})
	}
}

func TestDoubaoClient_Generate(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法和路径
		if r.Method != "POST" {
			t.Errorf("期望 POST 请求，实际 %s", r.Method)
		}
		if r.URL.Path != "/chat/completions" {
			t.Errorf("期望路径 '/chat/completions'，实际 '%s'", r.URL.Path)
		}

		// 验证请求头
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望 Content-Type 'application/json'，实际 '%s'", r.Header.Get("Content-Type"))
		}
		if !strings.HasPrefix(r.Header.Get("Authorization"), "Bearer ") {
			t.Errorf("期望 Authorization 头以 'Bearer ' 开头，实际 '%s'", r.Header.Get("Authorization"))
		}

		// 模拟成功响应
		response := GenerateResponse{
			ID:      "test-id",
			Object:  "chat.completion",
			Created: time.Now().Unix(),
			Model:   "ep-test123",
			Choices: []*Choice{
				{
					Index: 0,
					Message: &ChatMessage{
						Role:    "assistant",
						Content: "这是豆包的测试响应",
					},
					FinishReason: "stop",
				},
			},
			Usage: &Usage{
				PromptTokens:     10,
				CompletionTokens: 8,
				TotalTokens:      18,
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建客户端
	config := &Config{
		APIKey:  "test-api-key",
		BaseURL: server.URL,
		Model:   "ep-test123",
	}
	client, err := NewDoubaoClient(config)
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}

	// 创建请求
	request := NewGenerateRequest([]*ChatMessage{
		NewUserMessage("你好"),
	})

	// 发起请求
	ctx := context.Background()
	response, err := client.Generate(ctx, request)

	if err != nil {
		t.Errorf("生成响应失败: %v", err)
		return
	}

	if response == nil {
		t.Errorf("响应不应为空")
		return
	}

	if response.GetContent() != "这是豆包的测试响应" {
		t.Errorf("响应内容不正确，期望 '这是豆包的测试响应'，实际 '%s'", response.GetContent())
	}
}

func TestDoubaoClient_GenerateStream(t *testing.T) {
	// 创建模拟流式服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证流式请求头
		if r.Header.Get("Accept") != "text/event-stream" {
			t.Errorf("期望 Accept 'text/event-stream'，实际 '%s'", r.Header.Get("Accept"))
		}

		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")

		// 发送流式响应
		flusher, ok := w.(http.Flusher)
		if !ok {
			t.Errorf("服务器不支持流式响应")
			return
		}

		// 第一个数据块
		streamResp1 := StreamResponse{
			ID:      "test-stream-id",
			Object:  "chat.completion.chunk",
			Created: time.Now().Unix(),
			Model:   "ep-test123",
			Choices: []*StreamChoice{
				{
					Index: 0,
					Delta: &ChatMessage{
						Role:    "assistant",
						Content: "豆包",
					},
				},
			},
		}
		data1, _ := json.Marshal(streamResp1)
		w.Write([]byte("data: " + string(data1) + "\n\n"))
		flusher.Flush()

		// 第二个数据块
		streamResp2 := StreamResponse{
			ID:      "test-stream-id",
			Object:  "chat.completion.chunk",
			Created: time.Now().Unix(),
			Model:   "ep-test123",
			Choices: []*StreamChoice{
				{
					Index: 0,
					Delta: &ChatMessage{
						Content: "回复",
					},
					FinishReason: "stop",
				},
			},
		}
		data2, _ := json.Marshal(streamResp2)
		w.Write([]byte("data: " + string(data2) + "\n\n"))
		flusher.Flush()

		// 结束标记
		w.Write([]byte("data: [DONE]\n\n"))
		flusher.Flush()
	}))
	defer server.Close()

	// 创建客户端
	config := &Config{
		APIKey:  "test-api-key",
		BaseURL: server.URL,
		Model:   "ep-test123",
	}
	client, err := NewDoubaoClient(config)
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}

	// 创建流式请求
	request := NewGenerateRequest([]*ChatMessage{
		NewUserMessage("你好"),
	})

	// 发起流式请求
	ctx := context.Background()
	responseChan, err := client.GenerateStream(ctx, request)

	if err != nil {
		t.Errorf("生成流式响应失败: %v", err)
		return
	}

	// 收集响应
	var contents []string
	var done bool

	for streamResp := range responseChan {
		if streamResp.Error != nil {
			t.Errorf("流式响应错误: %s", streamResp.Error.Message)
			return
		}

		if streamResp.Done {
			done = true
			break
		}

		content := streamResp.GetContent()
		if content != "" {
			contents = append(contents, content)
		}
	}

	if !done {
		t.Errorf("流式响应未正确结束")
	}

	expectedContents := []string{"豆包", "回复"}
	if len(contents) != len(expectedContents) {
		t.Errorf("内容数量不匹配，期望 %d，实际 %d", len(expectedContents), len(contents))
	}

	for i, expected := range expectedContents {
		if i < len(contents) && contents[i] != expected {
			t.Errorf("内容 %d 不匹配，期望 '%s'，实际 '%s'", i, expected, contents[i])
		}
	}
}

func TestDoubaoClient_Close(t *testing.T) {
	config := &Config{
		APIKey:  "test-api-key",
		BaseURL: "https://test.example.com",
		Model:   "ep-test123",
	}
	client, err := NewDoubaoClient(config)
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}

	err = client.Close()
	if err != nil {
		t.Errorf("关闭客户端失败: %v", err)
	}
}
