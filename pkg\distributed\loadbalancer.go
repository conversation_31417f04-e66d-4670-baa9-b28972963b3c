package distributed

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// LoadBalancer 负载均衡器接口
type LoadBalancer interface {
	// SelectService 选择服务实例
	SelectService(ctx context.Context, serviceName string) (*ServiceInfo, error)

	// UpdateServices 更新服务列表
	UpdateServices(serviceName string, services []*ServiceInfo)

	// ReportHealth 报告服务健康状态
	ReportHealth(serviceID string, healthy bool)

	// GetStats 获取负载均衡统计信息
	GetStats() *LoadBalancerStats
}

// LoadBalancerStats 负载均衡统计信息
type LoadBalancerStats struct {
	TotalRequests   int64                    `json:"total_requests"`
	SuccessRequests int64                    `json:"success_requests"`
	FailedRequests  int64                    `json:"failed_requests"`
	ServiceStats    map[string]*ServiceStats `json:"service_stats"`
	LastUpdated     time.Time                `json:"last_updated"`
}

// ServiceStats 服务统计信息
type ServiceStats struct {
	ServiceID       string    `json:"service_id"`
	RequestCount    int64     `json:"request_count"`
	SuccessCount    int64     `json:"success_count"`
	FailureCount    int64     `json:"failure_count"`
	AverageLatency  float64   `json:"average_latency"`
	LastRequestTime time.Time `json:"last_request_time"`
	Weight          int       `json:"weight"`
	Active          bool      `json:"active"`
}

// RoundRobinLoadBalancer 轮询负载均衡器
type RoundRobinLoadBalancer struct {
	services map[string][]*ServiceInfo
	counters map[string]*int64
	stats    *LoadBalancerStats
	mu       sync.RWMutex
	logger   logger.Logger
}

// NewRoundRobinLoadBalancer 创建轮询负载均衡器
func NewRoundRobinLoadBalancer() *RoundRobinLoadBalancer {
	return &RoundRobinLoadBalancer{
		services: make(map[string][]*ServiceInfo),
		counters: make(map[string]*int64),
		stats: &LoadBalancerStats{
			ServiceStats: make(map[string]*ServiceStats),
			LastUpdated:  time.Now(),
		},
		logger: logger.GetGlobalLogger(),
	}
}

// SelectService 选择服务实例
func (lb *RoundRobinLoadBalancer) SelectService(ctx context.Context, serviceName string) (*ServiceInfo, error) {
	lb.mu.RLock()
	services, exists := lb.services[serviceName]
	counter, counterExists := lb.counters[serviceName]
	lb.mu.RUnlock()

	if !exists || len(services) == 0 {
		return nil, errors.NewNotFoundError("no_services", fmt.Sprintf("no healthy services found for %s", serviceName))
	}

	if !counterExists {
		lb.mu.Lock()
		if lb.counters[serviceName] == nil {
			var c int64 = 0
			lb.counters[serviceName] = &c
		}
		counter = lb.counters[serviceName]
		lb.mu.Unlock()
	}

	// 轮询选择
	index := atomic.AddInt64(counter, 1) % int64(len(services))
	selected := services[index]

	// 更新统计信息
	lb.updateStats(selected.ID, true)
	atomic.AddInt64(&lb.stats.TotalRequests, 1)

	lb.logger.Debugf("Selected service %s for %s (round-robin)", selected.ID, serviceName)
	return selected, nil
}

// UpdateServices 更新服务列表
func (lb *RoundRobinLoadBalancer) UpdateServices(serviceName string, services []*ServiceInfo) {
	// 过滤健康的服务
	healthyServices := make([]*ServiceInfo, 0, len(services))
	for _, service := range services {
		if service.Status == ServiceStatusHealthy {
			healthyServices = append(healthyServices, service)
		}
	}

	lb.mu.Lock()
	lb.services[serviceName] = healthyServices
	lb.mu.Unlock()

	lb.logger.Debugf("Updated services for %s: %d healthy services", serviceName, len(healthyServices))
}

// ReportHealth 报告服务健康状态
func (lb *RoundRobinLoadBalancer) ReportHealth(serviceID string, healthy bool) {
	lb.updateStats(serviceID, healthy)
	if healthy {
		atomic.AddInt64(&lb.stats.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&lb.stats.FailedRequests, 1)
	}
}

// GetStats 获取负载均衡统计信息
func (lb *RoundRobinLoadBalancer) GetStats() *LoadBalancerStats {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	// 创建统计信息副本
	stats := &LoadBalancerStats{
		TotalRequests:   atomic.LoadInt64(&lb.stats.TotalRequests),
		SuccessRequests: atomic.LoadInt64(&lb.stats.SuccessRequests),
		FailedRequests:  atomic.LoadInt64(&lb.stats.FailedRequests),
		ServiceStats:    make(map[string]*ServiceStats),
		LastUpdated:     time.Now(),
	}

	for id, stat := range lb.stats.ServiceStats {
		stats.ServiceStats[id] = &ServiceStats{
			ServiceID:       stat.ServiceID,
			RequestCount:    atomic.LoadInt64(&stat.RequestCount),
			SuccessCount:    atomic.LoadInt64(&stat.SuccessCount),
			FailureCount:    atomic.LoadInt64(&stat.FailureCount),
			AverageLatency:  stat.AverageLatency,
			LastRequestTime: stat.LastRequestTime,
			Weight:          stat.Weight,
			Active:          stat.Active,
		}
	}

	return stats
}

// updateStats 更新统计信息
func (lb *RoundRobinLoadBalancer) updateStats(serviceID string, success bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	stat, exists := lb.stats.ServiceStats[serviceID]
	if !exists {
		stat = &ServiceStats{
			ServiceID: serviceID,
			Weight:    1,
			Active:    true,
		}
		lb.stats.ServiceStats[serviceID] = stat
	}

	atomic.AddInt64(&stat.RequestCount, 1)
	if success {
		atomic.AddInt64(&stat.SuccessCount, 1)
	} else {
		atomic.AddInt64(&stat.FailureCount, 1)
	}
	stat.LastRequestTime = time.Now()
}

// WeightedRoundRobinLoadBalancer 加权轮询负载均衡器
type WeightedRoundRobinLoadBalancer struct {
	services map[string][]*WeightedService
	stats    *LoadBalancerStats
	mu       sync.RWMutex
	logger   logger.Logger
}

// WeightedService 加权服务
type WeightedService struct {
	Service       *ServiceInfo
	Weight        int
	CurrentWeight int
}

// NewWeightedRoundRobinLoadBalancer 创建加权轮询负载均衡器
func NewWeightedRoundRobinLoadBalancer() *WeightedRoundRobinLoadBalancer {
	return &WeightedRoundRobinLoadBalancer{
		services: make(map[string][]*WeightedService),
		stats: &LoadBalancerStats{
			ServiceStats: make(map[string]*ServiceStats),
			LastUpdated:  time.Now(),
		},
		logger: logger.GetGlobalLogger(),
	}
}

// SelectService 选择服务实例
func (lb *WeightedRoundRobinLoadBalancer) SelectService(ctx context.Context, serviceName string) (*ServiceInfo, error) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	services, exists := lb.services[serviceName]
	if !exists || len(services) == 0 {
		return nil, errors.NewNotFoundError("no_services", fmt.Sprintf("no healthy services found for %s", serviceName))
	}

	// 加权轮询算法
	var selected *WeightedService
	totalWeight := 0

	for _, ws := range services {
		ws.CurrentWeight += ws.Weight
		totalWeight += ws.Weight

		if selected == nil || ws.CurrentWeight > selected.CurrentWeight {
			selected = ws
		}
	}

	if selected != nil {
		selected.CurrentWeight -= totalWeight
		lb.updateStats(selected.Service.ID, true)
		atomic.AddInt64(&lb.stats.TotalRequests, 1)

		lb.logger.Debugf("Selected service %s for %s (weighted round-robin)", selected.Service.ID, serviceName)
		return selected.Service, nil
	}

	return nil, errors.NewNotFoundError("no_services", "no service selected")
}

// UpdateServices 更新服务列表
func (lb *WeightedRoundRobinLoadBalancer) UpdateServices(serviceName string, services []*ServiceInfo) {
	weightedServices := make([]*WeightedService, 0, len(services))

	for _, service := range services {
		if service.Status == ServiceStatusHealthy {
			weight := 1
			if w, ok := service.Metadata["weight"]; ok {
				if wi, err := strconv.Atoi(w); err == nil {
					weight = wi
				}
			}

			weightedServices = append(weightedServices, &WeightedService{
				Service:       service,
				Weight:        weight,
				CurrentWeight: 0,
			})
		}
	}

	lb.mu.Lock()
	lb.services[serviceName] = weightedServices
	lb.mu.Unlock()

	lb.logger.Debugf("Updated weighted services for %s: %d services", serviceName, len(weightedServices))
}

// ReportHealth 报告服务健康状态
func (lb *WeightedRoundRobinLoadBalancer) ReportHealth(serviceID string, healthy bool) {
	lb.updateStats(serviceID, healthy)
	if healthy {
		atomic.AddInt64(&lb.stats.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&lb.stats.FailedRequests, 1)
	}
}

// GetStats 获取负载均衡统计信息
func (lb *WeightedRoundRobinLoadBalancer) GetStats() *LoadBalancerStats {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	stats := &LoadBalancerStats{
		TotalRequests:   atomic.LoadInt64(&lb.stats.TotalRequests),
		SuccessRequests: atomic.LoadInt64(&lb.stats.SuccessRequests),
		FailedRequests:  atomic.LoadInt64(&lb.stats.FailedRequests),
		ServiceStats:    make(map[string]*ServiceStats),
		LastUpdated:     time.Now(),
	}

	for id, stat := range lb.stats.ServiceStats {
		stats.ServiceStats[id] = &ServiceStats{
			ServiceID:       stat.ServiceID,
			RequestCount:    atomic.LoadInt64(&stat.RequestCount),
			SuccessCount:    atomic.LoadInt64(&stat.SuccessCount),
			FailureCount:    atomic.LoadInt64(&stat.FailureCount),
			AverageLatency:  stat.AverageLatency,
			LastRequestTime: stat.LastRequestTime,
			Weight:          stat.Weight,
			Active:          stat.Active,
		}
	}

	return stats
}

// updateStats 更新统计信息
func (lb *WeightedRoundRobinLoadBalancer) updateStats(serviceID string, success bool) {
	stat, exists := lb.stats.ServiceStats[serviceID]
	if !exists {
		stat = &ServiceStats{
			ServiceID: serviceID,
			Weight:    1,
			Active:    true,
		}
		lb.stats.ServiceStats[serviceID] = stat
	}

	atomic.AddInt64(&stat.RequestCount, 1)
	if success {
		atomic.AddInt64(&stat.SuccessCount, 1)
	} else {
		atomic.AddInt64(&stat.FailureCount, 1)
	}
	stat.LastRequestTime = time.Now()
}

// RandomLoadBalancer 随机负载均衡器
type RandomLoadBalancer struct {
	services map[string][]*ServiceInfo
	stats    *LoadBalancerStats
	mu       sync.RWMutex
	logger   logger.Logger
	rand     *rand.Rand
}

// NewRandomLoadBalancer 创建随机负载均衡器
func NewRandomLoadBalancer() *RandomLoadBalancer {
	return &RandomLoadBalancer{
		services: make(map[string][]*ServiceInfo),
		stats: &LoadBalancerStats{
			ServiceStats: make(map[string]*ServiceStats),
			LastUpdated:  time.Now(),
		},
		logger: logger.GetGlobalLogger(),
		rand:   rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// SelectService 选择服务实例
func (lb *RandomLoadBalancer) SelectService(ctx context.Context, serviceName string) (*ServiceInfo, error) {
	lb.mu.RLock()
	services, exists := lb.services[serviceName]
	lb.mu.RUnlock()

	if !exists || len(services) == 0 {
		return nil, errors.NewNotFoundError("no_services", fmt.Sprintf("no healthy services found for %s", serviceName))
	}

	// 随机选择
	index := lb.rand.Intn(len(services))
	selected := services[index]

	// 更新统计信息
	lb.updateStats(selected.ID, true)
	atomic.AddInt64(&lb.stats.TotalRequests, 1)

	lb.logger.Debugf("Selected service %s for %s (random)", selected.ID, serviceName)
	return selected, nil
}

// UpdateServices 更新服务列表
func (lb *RandomLoadBalancer) UpdateServices(serviceName string, services []*ServiceInfo) {
	healthyServices := make([]*ServiceInfo, 0, len(services))
	for _, service := range services {
		if service.Status == ServiceStatusHealthy {
			healthyServices = append(healthyServices, service)
		}
	}

	lb.mu.Lock()
	lb.services[serviceName] = healthyServices
	lb.mu.Unlock()

	lb.logger.Debugf("Updated services for %s: %d healthy services", serviceName, len(healthyServices))
}

// ReportHealth 报告服务健康状态
func (lb *RandomLoadBalancer) ReportHealth(serviceID string, healthy bool) {
	lb.updateStats(serviceID, healthy)
	if healthy {
		atomic.AddInt64(&lb.stats.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&lb.stats.FailedRequests, 1)
	}
}

// GetStats 获取负载均衡统计信息
func (lb *RandomLoadBalancer) GetStats() *LoadBalancerStats {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	stats := &LoadBalancerStats{
		TotalRequests:   atomic.LoadInt64(&lb.stats.TotalRequests),
		SuccessRequests: atomic.LoadInt64(&lb.stats.SuccessRequests),
		FailedRequests:  atomic.LoadInt64(&lb.stats.FailedRequests),
		ServiceStats:    make(map[string]*ServiceStats),
		LastUpdated:     time.Now(),
	}

	for id, stat := range lb.stats.ServiceStats {
		stats.ServiceStats[id] = &ServiceStats{
			ServiceID:       stat.ServiceID,
			RequestCount:    atomic.LoadInt64(&stat.RequestCount),
			SuccessCount:    atomic.LoadInt64(&stat.SuccessCount),
			FailureCount:    atomic.LoadInt64(&stat.FailureCount),
			AverageLatency:  stat.AverageLatency,
			LastRequestTime: stat.LastRequestTime,
			Weight:          stat.Weight,
			Active:          stat.Active,
		}
	}

	return stats
}

// updateStats 更新统计信息
func (lb *RandomLoadBalancer) updateStats(serviceID string, success bool) {
	stat, exists := lb.stats.ServiceStats[serviceID]
	if !exists {
		stat = &ServiceStats{
			ServiceID: serviceID,
			Weight:    1,
			Active:    true,
		}
		lb.stats.ServiceStats[serviceID] = stat
	}

	atomic.AddInt64(&stat.RequestCount, 1)
	if success {
		atomic.AddInt64(&stat.SuccessCount, 1)
	} else {
		atomic.AddInt64(&stat.FailureCount, 1)
	}
	stat.LastRequestTime = time.Now()
}

// LoadBalancerManager 负载均衡管理器
type LoadBalancerManager struct {
	balancers map[string]LoadBalancer
	discovery ServiceDiscovery
	mu        sync.RWMutex
	logger    logger.Logger
}

// NewLoadBalancerManager 创建负载均衡管理器
func NewLoadBalancerManager(discovery ServiceDiscovery) *LoadBalancerManager {
	return &LoadBalancerManager{
		balancers: make(map[string]LoadBalancer),
		discovery: discovery,
		logger:    logger.GetGlobalLogger(),
	}
}

// RegisterLoadBalancer 注册负载均衡器
func (lbm *LoadBalancerManager) RegisterLoadBalancer(serviceName string, lb LoadBalancer) {
	lbm.mu.Lock()
	defer lbm.mu.Unlock()

	lbm.balancers[serviceName] = lb
	lbm.logger.Info("Load balancer registered for service: %s", serviceName)

	// 初始化服务列表
	go lbm.updateServiceList(serviceName)
}

// SelectService 选择服务实例
func (lbm *LoadBalancerManager) SelectService(ctx context.Context, serviceName string) (*ServiceInfo, error) {
	lbm.mu.RLock()
	lb, exists := lbm.balancers[serviceName]
	lbm.mu.RUnlock()

	if !exists {
		// 使用默认的轮询负载均衡器
		lb = NewRoundRobinLoadBalancer()
		lbm.RegisterLoadBalancer(serviceName, lb)
	}

	return lb.SelectService(ctx, serviceName)
}

// updateServiceList 更新服务列表
func (lbm *LoadBalancerManager) updateServiceList(serviceName string) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		ctx := context.Background()
		services, err := lbm.discovery.Discover(ctx, serviceName)
		if err != nil {
			lbm.logger.Warnf("Failed to discover services for %s: %v", serviceName, err)
			continue
		}

		lbm.mu.RLock()
		lb, exists := lbm.balancers[serviceName]
		lbm.mu.RUnlock()

		if exists {
			lb.UpdateServices(serviceName, services)
		}
	}
}

// GetStats 获取所有负载均衡器统计信息
func (lbm *LoadBalancerManager) GetStats() map[string]*LoadBalancerStats {
	lbm.mu.RLock()
	defer lbm.mu.RUnlock()

	stats := make(map[string]*LoadBalancerStats)
	for serviceName, lb := range lbm.balancers {
		stats[serviceName] = lb.GetStats()
	}

	return stats
}
