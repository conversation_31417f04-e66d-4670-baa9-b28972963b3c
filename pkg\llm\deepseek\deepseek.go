package deepseek

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// DeepSeekClient DeepSeek API 客户端
type DeepSeekClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	timeout    time.Duration
	retries    int
}

// ChatMessage 聊天消息
type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ToolDefinition 工具定义
type ToolDefinition struct {
	Type     string                 `json:"type"`
	Function map[string]interface{} `json:"function"`
}

// ToolCall 工具调用
type ToolCall struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Function map[string]interface{} `json:"function"`
}

// ChatRequest 聊天请求
type ChatRequest struct {
	Model       string           `json:"model"`
	Messages    []ChatMessage    `json:"messages"`
	Temperature *float64         `json:"temperature,omitempty"`
	MaxTokens   *int             `json:"max_tokens,omitempty"`
	Stream      bool             `json:"stream,omitempty"`
	Tools       []ToolDefinition `json:"tools,omitempty"`
	ToolChoice  interface{}      `json:"tool_choice,omitempty"`
}

// ChatResponse 聊天响应
type ChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role      string     `json:"role"`
			Content   string     `json:"content"`
			ToolCalls []ToolCall `json:"tool_calls,omitempty"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// ChatDelta 流式响应增量
type ChatDelta struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index int `json:"index"`
		Delta struct {
			Role      string     `json:"role,omitempty"`
			Content   string     `json:"content,omitempty"`
			ToolCalls []ToolCall `json:"tool_calls,omitempty"`
		} `json:"delta"`
		FinishReason *string `json:"finish_reason"`
	} `json:"choices"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// NewDeepSeekClient 创建新的 DeepSeek 客户端
func NewDeepSeekClient() (*DeepSeekClient, error) {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("DEEPSEEK_API_KEY 环境变量未设置")
	}

	return &DeepSeekClient{
		apiKey:  apiKey,
		baseURL: "https://api.deepseek.com/v1",
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		timeout: 60 * time.Second,
		retries: 3,
	}, nil
}

// NewDeepSeekClientWithConfig 使用配置创建 DeepSeek 客户端
func NewDeepSeekClientWithConfig(apiKey, baseURL string, timeout time.Duration, retries int) *DeepSeekClient {
	if baseURL == "" {
		baseURL = "https://api.deepseek.com/v1"
	}

	return &DeepSeekClient{
		apiKey:  apiKey,
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
		retries: retries,
	}
}

// Chat 发送聊天请求
func (c *DeepSeekClient) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	if req.Model == "" {
		req.Model = "deepseek-chat"
	}

	// 序列化请求
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 发送请求（带重试）
	var response *ChatResponse
	var lastErr error

	for attempt := 0; attempt <= c.retries; attempt++ {
		response, lastErr = c.doRequest(ctx, reqBody)
		if lastErr == nil {
			break
		}

		// 如果是上下文取消，不重试
		if ctx.Err() != nil {
			return nil, ctx.Err()
		}

		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < c.retries {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(attempt+1) * time.Second):
				// 指数退避
			}
		}
	}

	if lastErr != nil {
		return nil, lastErr
	}

	return response, nil
}

// ChatStream 发送流式聊天请求
func (c *DeepSeekClient) ChatStream(ctx context.Context, req *ChatRequest) (<-chan *ChatDelta, error) {
	if req.Model == "" {
		req.Model = "deepseek-chat"
	}

	req.Stream = true

	// 序列化请求
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	httpReq.Header.Set("Accept", "text/event-stream")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)

		var errResp ErrorResponse
		if json.Unmarshal(body, &errResp) == nil && errResp.Error.Message != "" {
			return nil, fmt.Errorf("API 错误: %s", errResp.Error.Message)
		}

		return nil, fmt.Errorf("HTTP 错误: %d %s", resp.StatusCode, string(body))
	}

	// 创建流式响应通道
	deltaChan := make(chan *ChatDelta, 10)

	go func() {
		defer close(deltaChan)
		defer resp.Body.Close()

		// 使用 bufio.Scanner 逐行读取 SSE 流
		scanner := bufio.NewScanner(resp.Body)

		for scanner.Scan() {
			select {
			case <-ctx.Done():
				return
			default:
				line := scanner.Text()

				// 处理 SSE 格式
				if strings.HasPrefix(line, "data: ") {
					data := strings.TrimPrefix(line, "data: ")
					if data == "[DONE]" {
						return
					}

					var delta ChatDelta
					if err := json.Unmarshal([]byte(data), &delta); err == nil {
						select {
						case deltaChan <- &delta:
						case <-ctx.Done():
							return
						}
					}
				}
			}
		}
	}()

	return deltaChan, nil
}

// doRequest 执行单次请求
func (c *DeepSeekClient) doRequest(ctx context.Context, reqBody []byte) (*ChatResponse, error) {
	// 创建 HTTP 请求
	req, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		var errResp ErrorResponse
		if json.Unmarshal(body, &errResp) == nil && errResp.Error.Message != "" {
			return nil, fmt.Errorf("API 错误: %s", errResp.Error.Message)
		}

		return nil, fmt.Errorf("HTTP 错误: %d %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var chatResp ChatResponse
	if err := json.Unmarshal(body, &chatResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &chatResp, nil
}

// SetTimeout 设置超时时间
func (c *DeepSeekClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
	c.httpClient.Timeout = timeout
}

// SetRetries 设置重试次数
func (c *DeepSeekClient) SetRetries(retries int) {
	c.retries = retries
}

// SetBaseURL 设置基础 URL
func (c *DeepSeekClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// GetAPIKey 获取 API Key（用于测试）
func (c *DeepSeekClient) GetAPIKey() string {
	return c.apiKey
}

// SetAPIKey 设置 API Key（用于测试）
func (c *DeepSeekClient) SetAPIKey(apiKey string) {
	c.apiKey = apiKey
}

// GetHTTPClient 获取 HTTP 客户端（用于测试）
func (c *DeepSeekClient) GetHTTPClient() *http.Client {
	return c.httpClient
}

// SetHTTPClient 设置 HTTP 客户端（用于测试）
func (c *DeepSeekClient) SetHTTPClient(client *http.Client) {
	c.httpClient = client
}
