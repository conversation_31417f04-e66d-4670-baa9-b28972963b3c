package agent

import (
	"context"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// Agent 表示智能体接口
type Agent interface {
	// Name 返回智能体名称，用于标识与观测
	Name(ctx context.Context) string

	// Description 返回智能体描述，用于标识与观测
	Description(ctx context.Context) string

	// Run 启动异步任务并返回事件迭代器；调用方需要消费完毕
	Run(ctx context.Context, in *Input) *runtime.AsyncIterator[*event.Event]
}

// Input 是智能体输入的类型别名，统一使用 runtime.Input
type Input = runtime.Input

// BaseAgent 提供基础的智能体实现
type BaseAgent struct {
	name        string
	description string
}

// NewBaseAgent 创建基础智能体
func NewBaseAgent(name, description string) *BaseAgent {
	return &BaseAgent{
		name:        name,
		description: description,
	}
}

// Name 返回智能体名称
func (a *BaseAgent) Name(ctx context.Context) string {
	return a.name
}

// Description 返回智能体描述
func (a *BaseAgent) Description(ctx context.Context) string {
	return a.description
}

// SetName 设置智能体名称
func (a *BaseAgent) SetName(name string) {
	a.name = name
}

// SetDescription 设置智能体描述
func (a *BaseAgent) SetDescription(description string) {
	a.description = description
}
