# 开发示例和用法指南

本文档提供了 hzAgent 核心组件的详细开发示例和最佳实践。

## Runner 使用示例

Runner 是智能体执行的核心管理器，提供统一的执行环境和治理能力。

### 基本用法

```go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/agentscope/agentscope-golang/pkg/agent"
    "github.com/agentscope/agentscope-golang/pkg/runtime"
    "github.com/agentscope/agentscope-golang/pkg/message"
)

func main() {
    // 创建 Runner 配置
    config := &runtime.RunnerConfig{
        MaxConcurrency: 10,
        Timeout:        30 * time.Second,
        EnableMetrics:  true,
        EnableTracing:  true,
    }

    // 创建 Runner
    runner := runtime.NewRunner(config)
    defer runner.Close()

    // 创建智能体
    agent := createMyAgent()

    // 创建输入
    input := runtime.NewInput().
        AddMessage(message.NewUserMessage("你好，请介绍一下自己"))

    // 执行智能体
    ctx := context.Background()
    iterator := runner.Run(ctx, agent, input)

    // 处理事件流
    for event := range iterator.Channel() {
        switch event.Type {
        case "message":
            fmt.Printf("消息: %s\n", event.Data)
        case "error":
            fmt.Printf("错误: %s\n", event.Data)
        case "complete":
            fmt.Println("执行完成")
        }
    }

    if err := iterator.Error(); err != nil {
        fmt.Printf("执行失败: %v\n", err)
    }
}

func createMyAgent() agent.Agent {
    // 这里创建你的智能体实现
    // 返回实现了 agent.Agent 接口的对象
    return nil
}
```

### 高级配置

```go
// 创建带有完整配置的 Runner
config := &runtime.RunnerConfig{
    // 并发控制
    MaxConcurrency: 50,
    QueueSize:      1000,
    
    // 超时设置
    Timeout:        60 * time.Second,
    IdleTimeout:    5 * time.Minute,
    
    // 重试策略
    RetryPolicy: &runtime.RetryPolicy{
        MaxRetries:    3,
        InitialDelay:  100 * time.Millisecond,
        MaxDelay:      5 * time.Second,
        BackoffFactor: 2.0,
    },
    
    // 监控和追踪
    EnableMetrics: true,
    EnableTracing: true,
    MetricsPrefix: "hzagent_",
    
    // 错误处理
    ErrorHandler: func(ctx context.Context, err error) {
        log.WithError(err).Error("Runner 执行错误")
    },
}

runner := runtime.NewRunner(config)
```

## Interceptor 使用示例

Interceptor 提供了强大的中间件机制，可以在智能体执行前后插入自定义逻辑。

### 创建自定义 Interceptor

```go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/agentscope/agentscope-golang/pkg/agent"
    "github.com/agentscope/agentscope-golang/pkg/runtime"
    "github.com/agentscope/agentscope-golang/pkg/event"
)

// LoggingInterceptor 日志记录拦截器
type LoggingInterceptor struct {
    name string
}

func NewLoggingInterceptor(name string) *LoggingInterceptor {
    return &LoggingInterceptor{name: name}
}

func (i *LoggingInterceptor) Name() string {
    return i.name
}

func (i *LoggingInterceptor) Priority() int {
    return 100 // 优先级，数字越小优先级越高
}

func (i *LoggingInterceptor) Intercept(
    ctx context.Context,
    agent agent.Agent,
    input *runtime.Input,
    next runtime.InterceptorFunc,
) *runtime.AsyncIterator[*event.Event] {
    
    start := time.Now()
    fmt.Printf("[%s] 开始执行智能体: %s\n", i.name, agent.Name())
    
    // 调用下一个拦截器或智能体
    iterator := next(ctx, agent, input)
    
    // 包装事件流，添加日志
    return runtime.NewAsyncIterator(func() <-chan *event.Event {
        ch := make(chan *event.Event)
        
        go func() {
            defer close(ch)
            defer func() {
                duration := time.Since(start)
                fmt.Printf("[%s] 执行完成，耗时: %v\n", i.name, duration)
            }()
            
            for event := range iterator.Channel() {
                fmt.Printf("[%s] 事件: %s\n", i.name, event.Type)
                ch <- event
            }
            
            if err := iterator.Error(); err != nil {
                fmt.Printf("[%s] 执行错误: %v\n", i.name, err)
            }
        }()
        
        return ch
    })
}

// 使用拦截器
func main() {
    // 创建拦截器链
    interceptors := []runtime.Interceptor{
        NewLoggingInterceptor("Logger"),
        NewAuthInterceptor(),
        NewRateLimitInterceptor(),
        NewMetricsInterceptor(),
    }
    
    // 创建 Runner 并注册拦截器
    config := &runtime.RunnerConfig{
        Interceptors: interceptors,
    }
    
    runner := runtime.NewRunner(config)
    
    // 正常使用 Runner
    // ...
}
```

### 内置 Interceptor 示例

```go
// 认证拦截器
type AuthInterceptor struct{}

func NewAuthInterceptor() *AuthInterceptor {
    return &AuthInterceptor{}
}

func (i *AuthInterceptor) Name() string {
    return "auth"
}

func (i *AuthInterceptor) Priority() int {
    return 10 // 高优先级，最先执行
}

func (i *AuthInterceptor) Intercept(
    ctx context.Context,
    agent agent.Agent,
    input *runtime.Input,
    next runtime.InterceptorFunc,
) *runtime.AsyncIterator[*event.Event] {
    
    // 检查认证信息
    userID := ctx.Value("user_id")
    if userID == nil {
        return runtime.NewErrorIterator(fmt.Errorf("未认证的用户"))
    }
    
    // 检查权限
    if !hasPermission(userID, agent.Name()) {
        return runtime.NewErrorIterator(fmt.Errorf("权限不足"))
    }
    
    // 继续执行
    return next(ctx, agent, input)
}

// 速率限制拦截器
type RateLimitInterceptor struct {
    limiter *rate.Limiter
}

func NewRateLimitInterceptor() *RateLimitInterceptor {
    return &RateLimitInterceptor{
        limiter: rate.NewLimiter(rate.Limit(10), 100), // 每秒10个请求，突发100个
    }
}

func (i *RateLimitInterceptor) Name() string {
    return "rate_limit"
}

func (i *RateLimitInterceptor) Priority() int {
    return 20
}

func (i *RateLimitInterceptor) Intercept(
    ctx context.Context,
    agent agent.Agent,
    input *runtime.Input,
    next runtime.InterceptorFunc,
) *runtime.AsyncIterator[*event.Event] {
    
    // 检查速率限制
    if !i.limiter.Allow() {
        return runtime.NewErrorIterator(fmt.Errorf("请求频率过高，请稍后重试"))
    }
    
    return next(ctx, agent, input)
}

// 指标收集拦截器
type MetricsInterceptor struct {
    metrics *prometheus.CounterVec
}

func NewMetricsInterceptor() *MetricsInterceptor {
    metrics := prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "agent_executions_total",
            Help: "Total number of agent executions",
        },
        []string{"agent", "status"},
    )
    
    return &MetricsInterceptor{metrics: metrics}
}

func (i *MetricsInterceptor) Name() string {
    return "metrics"
}

func (i *MetricsInterceptor) Priority() int {
    return 1000 // 低优先级，最后执行
}

func (i *MetricsInterceptor) Intercept(
    ctx context.Context,
    agent agent.Agent,
    input *runtime.Input,
    next runtime.InterceptorFunc,
) *runtime.AsyncIterator[*event.Event] {
    
    iterator := next(ctx, agent, input)
    
    return runtime.NewAsyncIterator(func() <-chan *event.Event {
        ch := make(chan *event.Event)
        
        go func() {
            defer close(ch)
            
            var status string = "success"
            
            for event := range iterator.Channel() {
                ch <- event
            }
            
            if err := iterator.Error(); err != nil {
                status = "error"
            }
            
            // 记录指标
            i.metrics.WithLabelValues(agent.Name(), status).Inc()
        }()
        
        return ch
    })
}
```

## Checkpoint 使用示例

Checkpoint 提供了智能体状态的保存和恢复功能，支持长时间运行的任务和故障恢复。

### 基本 Checkpoint 使用

```go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/agentscope/agentscope-golang/pkg/runtime"
    "github.com/agentscope/agentscope-golang/pkg/memory"
)

func main() {
    // 创建存储后端
    store, err := memory.NewSQLiteStore(&memory.SQLiteConfig{
        DatabasePath: "./checkpoints.db",
    })
    if err != nil {
        panic(err)
    }
    defer store.Close()

    // 创建 Checkpoint 管理器
    checkpointManager := runtime.NewCheckpointManager(&runtime.CheckpointConfig{
        Store:           store,
        AutoSaveEnabled: true,
        SaveInterval:    30 * time.Second,
        MaxCheckpoints:  10,
    })

    // 创建支持 Checkpoint 的 Runner
    config := &runtime.RunnerConfig{
        CheckpointManager: checkpointManager,
        EnableCheckpoint:  true,
    }

    runner := runtime.NewRunner(config)
    defer runner.Close()

    ctx := context.Background()
    sessionID := "long-running-task-123"

    // 尝试从 Checkpoint 恢复
    checkpoint, err := checkpointManager.Load(ctx, sessionID)
    if err != nil {
        fmt.Printf("无法加载 Checkpoint: %v\n", err)
        // 开始新的执行
        startNewExecution(ctx, runner, sessionID)
    } else {
        fmt.Printf("从 Checkpoint 恢复: %s\n", checkpoint.ID)
        // 从 Checkpoint 恢复执行
        resumeFromCheckpoint(ctx, runner, checkpoint)
    }
}

func startNewExecution(ctx context.Context, runner *runtime.Runner, sessionID string) {
    // 创建带有会话 ID 的上下文
    ctx = context.WithValue(ctx, "session_id", sessionID)

    agent := createLongRunningAgent()
    input := runtime.NewInput().
        AddMessage(message.NewUserMessage("开始长时间运行的任务"))

    iterator := runner.Run(ctx, agent, input)

    for event := range iterator.Channel() {
        fmt.Printf("事件: %s\n", event.Type)

        // 手动保存 Checkpoint
        if event.Type == "milestone_reached" {
            if err := runner.SaveCheckpoint(ctx, sessionID); err != nil {
                fmt.Printf("保存 Checkpoint 失败: %v\n", err)
            } else {
                fmt.Println("Checkpoint 已保存")
            }
        }
    }
}

func resumeFromCheckpoint(ctx context.Context, runner *runtime.Runner, checkpoint *runtime.Checkpoint) {
    // 从 Checkpoint 数据恢复智能体状态
    agent := restoreAgentFromCheckpoint(checkpoint)

    // 创建恢复输入
    input := runtime.NewInput().
        SetCheckpoint(checkpoint).
        AddMessage(message.NewSystemMessage("从 Checkpoint 恢复执行"))

    iterator := runner.Run(ctx, agent, input)

    for event := range iterator.Channel() {
        fmt.Printf("恢复事件: %s\n", event.Type)
    }
}
```

### 自定义 Checkpoint 策略

```go
// 自定义 Checkpoint 策略
type CustomCheckpointStrategy struct {
    saveOnError    bool
    saveOnComplete bool
    saveInterval   time.Duration
}

func NewCustomCheckpointStrategy() *CustomCheckpointStrategy {
    return &CustomCheckpointStrategy{
        saveOnError:    true,
        saveOnComplete: true,
        saveInterval:   60 * time.Second,
    }
}

func (s *CustomCheckpointStrategy) ShouldSave(ctx context.Context, event *event.Event) bool {
    switch event.Type {
    case "error":
        return s.saveOnError
    case "complete":
        return s.saveOnComplete
    case "milestone":
        return true
    case "user_request":
        // 用户明确请求保存
        return true
    default:
        return false
    }
}

func (s *CustomCheckpointStrategy) GetSaveInterval() time.Duration {
    return s.saveInterval
}

// 智能体状态序列化
type StatefulAgent struct {
    name  string
    state map[string]interface{}
}

func (a *StatefulAgent) Name() string {
    return a.name
}

func (a *StatefulAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
    return runtime.NewAsyncIterator(func() <-chan *event.Event {
        ch := make(chan *event.Event)

        go func() {
            defer close(ch)

            // 从输入中恢复状态
            if checkpoint := input.GetCheckpoint(); checkpoint != nil {
                a.restoreState(checkpoint.Data)
                ch <- event.NewEvent("state_restored", "状态已恢复")
            }

            // 执行业务逻辑
            for i := 0; i < 10; i++ {
                // 更新状态
                a.state["step"] = i
                a.state["timestamp"] = time.Now()

                ch <- event.NewEvent("progress", fmt.Sprintf("步骤 %d 完成", i))

                // 在关键点触发 Checkpoint
                if i%3 == 0 {
                    ch <- event.NewEvent("milestone", fmt.Sprintf("里程碑 %d", i))
                }

                time.Sleep(1 * time.Second)
            }

            ch <- event.NewEvent("complete", "任务完成")
        }()

        return ch
    })
}

func (a *StatefulAgent) GetState() map[string]interface{} {
    return a.state
}

func (a *StatefulAgent) restoreState(data map[string]interface{}) {
    if data != nil {
        a.state = data
    }
}
```

## Audit 使用示例

Audit 系统提供了完整的会话和消息审计功能，支持合规性要求和数据分析。

### 基本 Audit 配置

```go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/agentscope/agentscope-golang/pkg/audit"
    "github.com/agentscope/agentscope-golang/pkg/config"
)

func main() {
    // 创建审计配置
    auditConfig := &config.AuditConfig{
        Enabled: true,
        Store: config.AuditStoreConfig{
            Type: "sqlite",
            SQLite: &config.SQLiteConfig{
                DatabasePath: "./audit.db",
            },
        },
        Retention: config.RetentionConfig{
            Enabled: true,
            MaxDays: 90,
            Cron:    "@daily",
        },
        Web: config.AuditWebConfig{
            AllowReadAPI:   true,
            AllowDeleteAPI: false,
        },
        Privacy: config.PrivacyConfig{
            EnableMasking:    true,
            MaskPatterns:     []string{`\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b`}, // 信用卡号
            RedactFields:     []string{"password", "token", "secret"},
            EnableEncryption: true,
            EncryptionKey:    "your-32-byte-encryption-key-here",
        },
    }

    // 创建审计写入器
    auditWriter, err := audit.NewAuditWriter(auditConfig)
    if err != nil {
        panic(err)
    }
    defer auditWriter.Close()

    // 创建审计读取器
    auditReader, err := audit.NewAuditReader(auditConfig)
    if err != nil {
        panic(err)
    }
    defer auditReader.Close()

    ctx := context.Background()

    // 记录审计事件
    recordAuditEvents(ctx, auditWriter)

    // 查询审计数据
    queryAuditData(ctx, auditReader)
}

func recordAuditEvents(ctx context.Context, writer *audit.AuditWriter) {
    sessionID := "demo-session-123"
    userID := "user-456"

    // 记录用户消息
    userRecord := &audit.Record{
        SessionID: sessionID,
        UserID:    userID,
        Role:      "user",
        MsgType:   "text",
        Content:   "你好，请帮我查询账户余额",
        Metadata: map[string]interface{}{
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "channel":    "web",
        },
    }

    if err := writer.WriteRecord(ctx, userRecord); err != nil {
        fmt.Printf("记录用户消息失败: %v\n", err)
    }

    // 记录智能体响应
    agentRecord := &audit.Record{
        SessionID: sessionID,
        UserID:    userID,
        Role:      "assistant",
        MsgType:   "text",
        Content:   "您的账户余额为 ¥1,234.56",
        AgentID:   "banking-assistant",
        Metadata: map[string]interface{}{
            "model":         "deepseek-chat",
            "response_time": "1.2s",
            "tokens_used":   150,
        },
    }

    if err := writer.WriteRecord(ctx, agentRecord); err != nil {
        fmt.Printf("记录智能体响应失败: %v\n", err)
    }

    // 记录工具调用
    toolRecord := &audit.Record{
        SessionID: sessionID,
        UserID:    userID,
        Role:      "tool",
        MsgType:   "tool_call",
        Content:   `{"tool": "account_balance", "params": {"user_id": "user-456"}}`,
        AgentID:   "banking-assistant",
        Metadata: map[string]interface{}{
            "tool_name":     "account_balance",
            "execution_time": "0.5s",
            "success":       true,
        },
    }

    if err := writer.WriteRecord(ctx, toolRecord); err != nil {
        fmt.Printf("记录工具调用失败: %v\n", err)
    }
}

func queryAuditData(ctx context.Context, reader *audit.AuditReader) {
    // 查询会话列表
    sessions, err := reader.QuerySessions(ctx, audit.SessionQuery{
        UserID:   "user-456",
        Limit:    10,
        Offset:   0,
    })
    if err != nil {
        fmt.Printf("查询会话失败: %v\n", err)
        return
    }

    fmt.Printf("找到 %d 个会话\n", len(sessions))
    for _, session := range sessions {
        fmt.Printf("会话: %s, 用户: %s, 消息数: %d\n",
            session.SessionID, session.UserID, session.MessageCount)
    }

    // 查询特定会话的消息
    if len(sessions) > 0 {
        sessionID := sessions[0].SessionID
        messages, err := reader.QueryMessages(ctx, audit.Query{
            SessionID: &sessionID,
            Limit:     50,
            Offset:    0,
        })
        if err != nil {
            fmt.Printf("查询消息失败: %v\n", err)
            return
        }

        fmt.Printf("会话 %s 中有 %d 条消息\n", sessionID, len(messages.Records))
        for _, msg := range messages.Records {
            fmt.Printf("[%s] %s: %s\n", msg.CreatedAt.Format("15:04:05"), msg.Role, msg.Content)
        }
    }

    // 搜索包含关键词的消息
    searchResults, err := reader.QueryMessages(ctx, audit.Query{
        Keyword: "账户余额",
        Limit:   20,
        Offset:  0,
    })
    if err != nil {
        fmt.Printf("搜索消息失败: %v\n", err)
        return
    }

    fmt.Printf("搜索到 %d 条包含'账户余额'的消息\n", len(searchResults.Records))
}
```

### 自定义审计拦截器

```go
// 审计拦截器
type AuditInterceptor struct {
    writer *audit.AuditWriter
}

func NewAuditInterceptor(writer *audit.AuditWriter) *AuditInterceptor {
    return &AuditInterceptor{writer: writer}
}

func (i *AuditInterceptor) Name() string {
    return "audit"
}

func (i *AuditInterceptor) Priority() int {
    return 50 // 中等优先级
}

func (i *AuditInterceptor) Intercept(
    ctx context.Context,
    agent agent.Agent,
    input *runtime.Input,
    next runtime.InterceptorFunc,
) *runtime.AsyncIterator[*event.Event] {

    sessionID := getSessionID(ctx)
    userID := getUserID(ctx)

    // 记录输入消息
    for _, msg := range input.GetMessages() {
        record := &audit.Record{
            SessionID: sessionID,
            UserID:    userID,
            Role:      msg.Role,
            MsgType:   msg.Type,
            Content:   fmt.Sprintf("%v", msg.Content),
            Metadata:  extractMetadata(ctx),
        }

        if err := i.writer.WriteRecord(ctx, record); err != nil {
            // 记录错误但不中断执行
            fmt.Printf("审计记录失败: %v\n", err)
        }
    }

    // 执行下一个拦截器
    iterator := next(ctx, agent, input)

    // 包装输出，记录响应事件
    return runtime.NewAsyncIterator(func() <-chan *event.Event {
        ch := make(chan *event.Event)

        go func() {
            defer close(ch)

            for event := range iterator.Channel() {
                // 转发事件
                ch <- event

                // 记录特定类型的事件
                if shouldAuditEvent(event) {
                    record := &audit.Record{
                        SessionID: sessionID,
                        UserID:    userID,
                        Role:      "assistant",
                        MsgType:   event.Type,
                        Content:   fmt.Sprintf("%v", event.Data),
                        AgentID:   agent.Name(),
                        Metadata:  event.Metadata,
                    }

                    if err := i.writer.WriteRecord(ctx, record); err != nil {
                        fmt.Printf("审计事件记录失败: %v\n", err)
                    }
                }
            }

            // 记录执行结果
            if err := iterator.Error(); err != nil {
                errorRecord := &audit.Record{
                    SessionID: sessionID,
                    UserID:    userID,
                    Role:      "system",
                    MsgType:   "error",
                    Content:   err.Error(),
                    AgentID:   agent.Name(),
                }

                i.writer.WriteRecord(ctx, errorRecord)
            }
        }()

        return ch
    })
}

func shouldAuditEvent(event *event.Event) bool {
    auditableTypes := []string{
        "message", "tool_call", "tool_result",
        "error", "complete", "milestone",
    }

    for _, t := range auditableTypes {
        if event.Type == t {
            return true
        }
    }

    return false
}

func extractMetadata(ctx context.Context) map[string]interface{} {
    metadata := make(map[string]interface{})

    if ip := ctx.Value("ip_address"); ip != nil {
        metadata["ip_address"] = ip
    }

    if ua := ctx.Value("user_agent"); ua != nil {
        metadata["user_agent"] = ua
    }

    if channel := ctx.Value("channel"); channel != nil {
        metadata["channel"] = channel
    }

    metadata["timestamp"] = time.Now()

    return metadata
}
```

### 审计数据分析

```go
// 审计数据分析器
type AuditAnalyzer struct {
    reader *audit.AuditReader
}

func NewAuditAnalyzer(reader *audit.AuditReader) *AuditAnalyzer {
    return &AuditAnalyzer{reader: reader}
}

// 分析用户活动模式
func (a *AuditAnalyzer) AnalyzeUserActivity(ctx context.Context, userID string, days int) (*UserActivityReport, error) {
    since := time.Now().AddDate(0, 0, -days)

    activity, err := a.reader.GetUserActivity(ctx, userID, &since)
    if err != nil {
        return nil, err
    }

    // 查询详细消息
    messages, err := a.reader.QueryMessages(ctx, audit.Query{
        UserID: &userID,
        Since:  &since,
        Limit:  1000,
    })
    if err != nil {
        return nil, err
    }

    report := &UserActivityReport{
        UserID:       userID,
        Period:       fmt.Sprintf("最近 %d 天", days),
        TotalSessions: activity.SessionCount,
        TotalMessages: activity.MessageCount,
        AvgSessionDuration: activity.AvgSessionDuration,
    }

    // 分析消息类型分布
    report.MessageTypeDistribution = analyzeMessageTypes(messages.Records)

    // 分析活跃时间段
    report.ActiveHours = analyzeActiveHours(messages.Records)

    // 分析常用功能
    report.TopFeatures = analyzeTopFeatures(messages.Records)

    return report, nil
}

type UserActivityReport struct {
    UserID                  string                 `json:"user_id"`
    Period                  string                 `json:"period"`
    TotalSessions          int                    `json:"total_sessions"`
    TotalMessages          int                    `json:"total_messages"`
    AvgSessionDuration     time.Duration          `json:"avg_session_duration"`
    MessageTypeDistribution map[string]int         `json:"message_type_distribution"`
    ActiveHours            map[int]int            `json:"active_hours"`
    TopFeatures            []FeatureUsage         `json:"top_features"`
}

type FeatureUsage struct {
    Feature string `json:"feature"`
    Count   int    `json:"count"`
}

func analyzeMessageTypes(records []*audit.Record) map[string]int {
    distribution := make(map[string]int)
    for _, record := range records {
        distribution[record.MsgType]++
    }
    return distribution
}

func analyzeActiveHours(records []*audit.Record) map[int]int {
    hours := make(map[int]int)
    for _, record := range records {
        hour := record.CreatedAt.Hour()
        hours[hour]++
    }
    return hours
}

func analyzeTopFeatures(records []*audit.Record) []FeatureUsage {
    features := make(map[string]int)

    for _, record := range records {
        if record.MsgType == "tool_call" {
            // 从工具调用中提取功能名称
            if toolName := extractToolName(record.Content); toolName != "" {
                features[toolName]++
            }
        }
    }

    // 转换为排序列表
    var usage []FeatureUsage
    for feature, count := range features {
        usage = append(usage, FeatureUsage{Feature: feature, Count: count})
    }

    // 按使用次数排序
    sort.Slice(usage, func(i, j int) bool {
        return usage[i].Count > usage[j].Count
    })

    // 返回前10个
    if len(usage) > 10 {
        usage = usage[:10]
    }

    return usage
}

func extractToolName(content string) string {
    // 简单的工具名称提取逻辑
    // 实际实现可能需要更复杂的解析
    var data map[string]interface{}
    if err := json.Unmarshal([]byte(content), &data); err != nil {
        return ""
    }

    if tool, ok := data["tool"].(string); ok {
        return tool
    }

    return ""
}
```

## 完整集成示例

以下是一个完整的示例，展示如何将 Runner、Interceptor、Checkpoint 和 Audit 集成在一起：

```go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/agentscope/agentscope-golang/pkg/agent"
    "github.com/agentscope/agentscope-golang/pkg/audit"
    "github.com/agentscope/agentscope-golang/pkg/config"
    "github.com/agentscope/agentscope-golang/pkg/memory"
    "github.com/agentscope/agentscope-golang/pkg/runtime"
)

func main() {
    // 1. 创建存储后端
    store, err := memory.NewSQLiteStore(&memory.SQLiteConfig{
        DatabasePath: "./hzagent.db",
    })
    if err != nil {
        panic(err)
    }
    defer store.Close()

    // 2. 创建审计系统
    auditConfig := &config.AuditConfig{
        Enabled: true,
        Store: config.AuditStoreConfig{
            Type: "sqlite",
            SQLite: &config.SQLiteConfig{
                DatabasePath: "./audit.db",
            },
        },
    }

    auditWriter, err := audit.NewAuditWriter(auditConfig)
    if err != nil {
        panic(err)
    }
    defer auditWriter.Close()

    // 3. 创建 Checkpoint 管理器
    checkpointManager := runtime.NewCheckpointManager(&runtime.CheckpointConfig{
        Store:           store,
        AutoSaveEnabled: true,
        SaveInterval:    60 * time.Second,
        MaxCheckpoints:  5,
    })

    // 4. 创建拦截器链
    interceptors := []runtime.Interceptor{
        NewAuthInterceptor(),           // 认证
        NewRateLimitInterceptor(),      // 速率限制
        NewAuditInterceptor(auditWriter), // 审计
        NewLoggingInterceptor("main"),   // 日志
        NewMetricsInterceptor(),        // 指标
    }

    // 5. 创建 Runner
    config := &runtime.RunnerConfig{
        MaxConcurrency:    20,
        Timeout:          120 * time.Second,
        CheckpointManager: checkpointManager,
        EnableCheckpoint:  true,
        Interceptors:     interceptors,
        EnableMetrics:    true,
        EnableTracing:    true,
    }

    runner := runtime.NewRunner(config)
    defer runner.Close()

    // 6. 创建智能体
    agent := createProductionAgent()

    // 7. 执行任务
    ctx := context.Background()
    ctx = context.WithValue(ctx, "user_id", "user-123")
    ctx = context.WithValue(ctx, "session_id", "session-456")
    ctx = context.WithValue(ctx, "ip_address", "*************")

    input := runtime.NewInput().
        AddMessage(message.NewUserMessage("请帮我分析最近的销售数据"))

    iterator := runner.Run(ctx, agent, input)

    // 8. 处理结果
    for event := range iterator.Channel() {
        fmt.Printf("事件: %s - %v\n", event.Type, event.Data)
    }

    if err := iterator.Error(); err != nil {
        fmt.Printf("执行失败: %v\n", err)
    }
}

func createProductionAgent() agent.Agent {
    // 创建生产级智能体
    // 这里应该返回实际的智能体实现
    return &ProductionAgent{
        name: "sales-analyst",
        tools: []string{"database_query", "chart_generator", "report_builder"},
    }
}

type ProductionAgent struct {
    name  string
    tools []string
}

func (a *ProductionAgent) Name() string {
    return a.name
}

func (a *ProductionAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
    return runtime.NewAsyncIterator(func() <-chan *event.Event {
        ch := make(chan *event.Event)

        go func() {
            defer close(ch)

            // 模拟智能体执行过程
            ch <- event.NewEvent("start", "开始分析销售数据")

            // 模拟工具调用
            for _, tool := range a.tools {
                ch <- event.NewEvent("tool_call", fmt.Sprintf("调用工具: %s", tool))
                time.Sleep(500 * time.Millisecond)
                ch <- event.NewEvent("tool_result", fmt.Sprintf("工具 %s 执行完成", tool))
            }

            // 模拟生成报告
            ch <- event.NewEvent("progress", "生成分析报告")
            time.Sleep(1 * time.Second)

            ch <- event.NewEvent("message", "销售数据分析完成，报告已生成")
            ch <- event.NewEvent("complete", "任务完成")
        }()

        return ch
    })
}
```

## 最佳实践

### 1. Runner 配置最佳实践

- **合理设置并发数**：根据系统资源和负载特点调整 `MaxConcurrency`
- **设置适当超时**：避免长时间运行的任务阻塞系统
- **启用监控**：在生产环境中启用指标和追踪
- **错误处理**：提供自定义错误处理器

### 2. Interceptor 设计原则

- **单一职责**：每个拦截器只负责一个特定功能
- **优先级设计**：认证和授权应该有最高优先级
- **错误处理**：拦截器中的错误不应该影响其他拦截器
- **性能考虑**：避免在拦截器中执行耗时操作

### 3. Checkpoint 使用建议

- **合理的保存频率**：平衡性能和数据安全
- **状态序列化**：确保智能体状态可以正确序列化和反序列化
- **清理策略**：定期清理过期的 Checkpoint 数据
- **测试恢复**：定期测试从 Checkpoint 恢复的功能

### 4. Audit 合规建议

- **数据隐私**：启用敏感数据脱敏和加密
- **保留策略**：根据法规要求设置数据保留期限
- **访问控制**：严格控制审计数据的访问权限
- **监控告警**：对异常访问模式设置告警

### 5. 性能优化

- **批量操作**：使用批量写入减少数据库压力
- **异步处理**：审计和日志记录使用异步方式
- **缓存策略**：对频繁查询的数据使用缓存
- **资源监控**：监控内存和 CPU 使用情况
```
```
