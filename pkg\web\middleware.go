package web

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"golang.org/x/time/rate"
)

// CompressionConfig 压缩配置
type CompressionConfig struct {
	// 启用压缩的最小字节数阈值
	MinSize int
	// 压缩级别 (1-9, 1=最快, 9=最佳压缩)
	Level int
	// 可压缩的内容类型
	CompressibleTypes []string
}

// DefaultCompressionConfig 默认压缩配置
var DefaultCompressionConfig = &CompressionConfig{
	MinSize: 1024, // 1KB以上才压缩
	Level:   gzip.DefaultCompression,
	CompressibleTypes: []string{
		"text/",
		"application/json",
		"application/javascript",
		"application/xml",
		"application/x-javascript",
		"image/svg+xml",
		"application/rss+xml",
		"application/atom+xml",
	},
}

// CORSMiddleware CORS中间件
func CORSMiddleware(config *config.CORSConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if !config.Enabled {
				next.ServeHTTP(w, r)
				return
			}

			origin := r.Header.Get("Origin")

			// 检查允许的源
			if isOriginAllowed(origin, config.AllowedOrigins) {
				w.Header().Set("Access-Control-Allow-Origin", origin)
			}

			// 设置允许的方法
			if len(config.AllowedMethods) > 0 {
				w.Header().Set("Access-Control-Allow-Methods", strings.Join(config.AllowedMethods, ", "))
			}

			// 设置允许的头部
			if len(config.AllowedHeaders) > 0 {
				w.Header().Set("Access-Control-Allow-Headers", strings.Join(config.AllowedHeaders, ", "))
			}

			// 设置是否允许凭据
			if config.AllowCredentials {
				w.Header().Set("Access-Control-Allow-Credentials", "true")
			}

			// 处理预检请求
			if r.Method == "OPTIONS" {
				w.Header().Set("Access-Control-Max-Age", "86400") // 24小时
				w.WriteHeader(http.StatusOK)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 防止点击劫持
			w.Header().Set("X-Frame-Options", "DENY")

			// 防止MIME类型嗅探
			w.Header().Set("X-Content-Type-Options", "nosniff")

			// XSS保护
			w.Header().Set("X-XSS-Protection", "1; mode=block")

			// 强制HTTPS（在生产环境中）
			w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

			// 内容安全策略
			w.Header().Set("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")

			// 引用策略
			w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")

			// 权限策略
			w.Header().Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

			next.ServeHTTP(w, r)
		})
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(config *RateLimitConfig) func(http.Handler) http.Handler {
	limiter := NewRateLimiter(config)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			clientIP := getClientIP(r)

			if !limiter.Allow(clientIP) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusTooManyRequests)

				errorResp := ErrorResponse{
					Error:  "超过限流阈值",
					Status: http.StatusTooManyRequests,
					Time:   time.Now(),
				}

				json.NewEncoder(w).Encode(errorResp)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// LoggingMiddleware 日志中间件
func LoggingMiddleware(logger logger.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// 创建响应记录器
			recorder := &responseRecorder{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}

			next.ServeHTTP(recorder, r)

			duration := time.Since(start)

			logger.Infof("HTTP %s %s %d %v %s",
				r.Method,
				r.URL.Path,
				recorder.statusCode,
				duration,
				getClientIP(r),
			)
		})
	}
}

// CompressionMiddleware 智能压缩中间件
func CompressionMiddleware() func(http.Handler) http.Handler {
	return CompressionMiddlewareWithConfig(DefaultCompressionConfig)
}

// CompressionMiddlewareWithConfig 带配置的压缩中间件
func CompressionMiddlewareWithConfig(config *CompressionConfig) func(http.Handler) http.Handler {
	if config == nil {
		config = DefaultCompressionConfig
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 检查客户端是否支持gzip
			if !strings.Contains(r.Header.Get("Accept-Encoding"), "gzip") {
				next.ServeHTTP(w, r)
				return
			}

			// 创建智能压缩响应写入器
			crw := &compressionResponseWriter{
				ResponseWriter: w,
				config:         config,
				buffer:         &bytes.Buffer{},
			}

			// 执行下一个处理器
			next.ServeHTTP(crw, r)

			// 完成响应处理
			crw.finalize()
		})
	}
}

// TimeoutMiddleware 超时中间件
func TimeoutMiddleware(timeout time.Duration) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.TimeoutHandler(next, timeout, "请求超时")
	}
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	RequestsPerMinute int           `json:"requests_per_minute"`
	BurstSize         int           `json:"burst_size"`
	CleanupInterval   time.Duration `json:"cleanup_interval"`
}

// RateLimiter 限流器
type RateLimiter struct {
	config     *RateLimitConfig
	clients    map[string]*rate.Limiter
	lastAccess map[string]time.Time
	mu         sync.RWMutex
	logger     logger.Logger
}

// NewRateLimiter 创建限流器
func NewRateLimiter(config *RateLimitConfig) *RateLimiter {
	limiter := &RateLimiter{
		config:     config,
		clients:    make(map[string]*rate.Limiter),
		lastAccess: make(map[string]time.Time),
		logger:     logger.GetGlobalLogger(),
	}

	// 启动清理协程
	go limiter.cleanup()

	return limiter
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow(clientIP string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	client, exists := rl.clients[clientIP]
	if !exists {
		// 计算每秒请求数：每分钟请求数 / 60
		rps := rate.Limit(float64(rl.config.RequestsPerMinute) / 60.0)
		client = rate.NewLimiter(rps, rl.config.BurstSize)
		rl.clients[clientIP] = client
	}

	// 更新最后访问时间
	rl.lastAccess[clientIP] = time.Now()

	// 使用标准库的Allow方法，提供更精确的限流控制
	return client.Allow()
}

// cleanup 清理过期的客户端限流器
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(rl.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		rl.mu.Lock()
		now := time.Now()

		// 清理超过1小时未访问的客户端
		for clientIP, lastAccess := range rl.lastAccess {
			if now.Sub(lastAccess) > time.Hour {
				delete(rl.clients, clientIP)
				delete(rl.lastAccess, clientIP)
			}
		}

		rl.mu.Unlock()
	}
}

// responseRecorder 响应记录器
type responseRecorder struct {
	http.ResponseWriter
	statusCode int
}

func (rr *responseRecorder) WriteHeader(code int) {
	rr.statusCode = code
	rr.ResponseWriter.WriteHeader(code)
}

// compressionResponseWriter 智能压缩响应写入器
type compressionResponseWriter struct {
	http.ResponseWriter
	config        *CompressionConfig
	buffer        *bytes.Buffer
	headerWritten bool
	statusCode    int
	contentType   string
}

// Header 返回响应头
func (crw *compressionResponseWriter) Header() http.Header {
	return crw.ResponseWriter.Header()
}

// WriteHeader 写入状态码
func (crw *compressionResponseWriter) WriteHeader(code int) {
	if crw.headerWritten {
		return
	}
	crw.statusCode = code
	crw.contentType = crw.Header().Get("Content-Type")
	// 不立即写入header，等到finalize时决定是否压缩
}

// Write 写入数据到缓冲区
func (crw *compressionResponseWriter) Write(data []byte) (int, error) {
	if !crw.headerWritten {
		// 如果没有显式设置状态码，默认为200
		if crw.statusCode == 0 {
			crw.statusCode = http.StatusOK
		}
		// 如果没有设置Content-Type，尝试检测
		if crw.contentType == "" {
			detectedType := http.DetectContentType(data)
			// 只有在没有明确设置Content-Type时才使用检测结果
			if crw.Header().Get("Content-Type") == "" {
				crw.contentType = detectedType
				crw.Header().Set("Content-Type", crw.contentType)
			} else {
				crw.contentType = crw.Header().Get("Content-Type")
			}
		}
	}

	// 将数据写入缓冲区
	return crw.buffer.Write(data)
}

// finalize 完成响应处理，决定是否压缩
func (crw *compressionResponseWriter) finalize() {
	if crw.headerWritten {
		return
	}

	// 获取缓冲区数据
	data := crw.buffer.Bytes()
	dataSize := len(data)

	// 决定是否压缩
	shouldCompress := crw.shouldCompressContent(dataSize)

	if shouldCompress {
		// 压缩数据
		var compressedBuffer bytes.Buffer
		gzWriter, err := gzip.NewWriterLevel(&compressedBuffer, crw.config.Level)
		if err != nil {
			// 压缩失败，直接发送原始数据
			crw.sendUncompressed(data)
			return
		}

		_, err = gzWriter.Write(data)
		if err != nil {
			gzWriter.Close()
			crw.sendUncompressed(data)
			return
		}

		err = gzWriter.Close()
		if err != nil {
			crw.sendUncompressed(data)
			return
		}

		compressedData := compressedBuffer.Bytes()

		// 检查压缩效果，如果压缩后反而更大，则不压缩
		if len(compressedData) >= dataSize {
			crw.sendUncompressed(data)
			return
		}

		// 发送压缩数据
		crw.sendCompressed(compressedData)
	} else {
		// 不压缩，直接发送
		crw.sendUncompressed(data)
	}
}

// shouldCompressContent 判断是否应该压缩内容
func (crw *compressionResponseWriter) shouldCompressContent(size int) bool {
	// 检查大小阈值
	if size < crw.config.MinSize {
		return false
	}

	// 检查内容类型
	if !crw.isCompressibleType(crw.contentType) {
		return false
	}

	// 检查状态码，只压缩成功响应
	if crw.statusCode < 200 || crw.statusCode >= 300 {
		return false
	}

	return true
}

// isCompressibleType 检查内容类型是否可压缩
func (crw *compressionResponseWriter) isCompressibleType(contentType string) bool {
	for _, ct := range crw.config.CompressibleTypes {
		if strings.HasPrefix(contentType, ct) {
			return true
		}
	}
	return false
}

// sendCompressed 发送压缩数据
func (crw *compressionResponseWriter) sendCompressed(data []byte) {
	crw.Header().Set("Content-Encoding", "gzip")
	crw.Header().Set("Vary", "Accept-Encoding")
	crw.Header().Set("Content-Length", strconv.Itoa(len(data)))

	crw.ResponseWriter.WriteHeader(crw.statusCode)
	crw.headerWritten = true

	crw.ResponseWriter.Write(data)
}

// sendUncompressed 发送未压缩数据
func (crw *compressionResponseWriter) sendUncompressed(data []byte) {
	crw.Header().Set("Content-Length", strconv.Itoa(len(data)))

	crw.ResponseWriter.WriteHeader(crw.statusCode)
	crw.headerWritten = true

	crw.ResponseWriter.Write(data)
}

// 辅助函数

// isOriginAllowed 检查源是否被允许
func isOriginAllowed(origin string, allowedOrigins []string) bool {
	for _, allowed := range allowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}

		// 支持通配符匹配
		if strings.HasPrefix(allowed, "*.") {
			domain := strings.TrimPrefix(allowed, "*.")
			if strings.HasSuffix(origin, domain) {
				return true
			}
		}
	}
	return false
}

// getClientIP 获取客户端IP
func getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For头
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}

	// 检查X-Real-IP头
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// 使用RemoteAddr
	ip := r.RemoteAddr
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}

	return ip
}
