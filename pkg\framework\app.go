package framework

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/agent/chatmodel"
	"github.com/agentscope/agentscope-golang/pkg/audit"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/pipeline"
	"github.com/agentscope/agentscope-golang/pkg/web"
)

// App 应用程序封装，负责框架级生命周期管理
// 该层将 Web 启动、LLM 验证、Agent 与 Pipeline 的初始化收敛，业务侧只需调用入口 API
type App struct {
	cfg          *config.Config
	agentMgr     *agent.Manager
	pipelineMgr  *pipeline.Manager
	webServer    *web.WebServer
	auditStore   audit.Store
	auditWriter  *audit.AuditWriter
	auditReader  *audit.AuditReader
	retentionMgr *audit.RetentionManager
}

// NewApp 通过配置创建应用实例（不会启动）
func NewApp(cfg *config.Config) (*App, error) {
	if cfg == nil {
		return nil, fmt.Errorf("配置不能为空")
	}
	// 配置校验与默认值应用
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("配置校验失败: %w", err)
	}

	app := &App{
		cfg:         cfg,
		agentMgr:    agent.NewManager(),
		pipelineMgr: pipeline.NewManager(),
	}

	// 1) LLM 提前验证（API Key / 模型等），避免运行时失败
	if err := app.validateLLM(); err != nil {
		return nil, err
	}

	// 2) 初始化审计模块（如果启用）
	if err := app.initializeAudit(); err != nil {
		return nil, err
	}

	// 3) 初始化默认的 User/Assistant 以及简单对话 Pipeline（若业务未自定义）
	if err := app.ensureDefaultAgentsAndPipeline(); err != nil {
		return nil, err
	}

	// 4) 如启用 Web，则构建 WebServer（启动由 Start 执行）
	if cfg.Web != nil && cfg.Web.Enabled {
		app.webServer = web.NewWebServerWithAudit(cfg.Web, app.agentMgr, app.pipelineMgr, app.auditWriter, cfg.Audit)
	}

	return app, nil
}

// Start 启动应用，阻塞直至收到退出信号或 ctx 取消
func (a *App) Start(ctx context.Context) error {
	_ = logger.GetGlobalLogger()

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 安装优雅退出
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan
		logger.Info("收到退出信号，开始关闭应用")
		cancel()
	}()

	// 启动 Web 或命令行模式
	if a.webServer != nil {
		go func() {
			logger.Infof("启动 Web 服务: http://%s:%d", a.cfg.Web.Host, a.cfg.Web.Port)
			if err := a.webServer.Start(); err != nil && err != http.ErrServerClosed {
				logger.WithError(err).Error("Web 服务启动失败")
			}
		}()
		// 给予 Web Server 一点启动时间
		time.Sleep(100 * time.Millisecond)
		logger.Info("Web 服务启动成功")
		fmt.Printf("Web interface available at: http://%s:%d\n", a.cfg.Web.Host, a.cfg.Web.Port)
		fmt.Printf("API endpoints available at: http://%s:%d/api/v1\n", a.cfg.Web.Host, a.cfg.Web.Port)
		fmt.Printf("Health check: http://%s:%d/api/v1/health\n\n", a.cfg.Web.Host, a.cfg.Web.Port)

		logger.Info("已进入 Web 模式，请通过浏览器访问进行对话")
		// 阻塞等待退出
		<-ctx.Done()
		return a.shutdown(context.Background())
	}

	// 否则进入命令行简单对话循环
	logger.Info("进入命令行对话模式，输入 Ctrl+C 退出")
	fmt.Println("=== AgentScope-Golang Simple Chat ===")
	fmt.Println("Welcome! You can start chatting with the AI assistant.")
	fmt.Println()

	// 默认简单循环：执行一次 Pipeline，用系统提示开始
	p, err := a.pipelineMgr.GetPipeline("simple_chat")
	if err != nil {
		return err
	}
	sp, ok := p.(*pipeline.SequentialPipeline)
	if !ok {
		return fmt.Errorf("默认管道类型错误")
	}

	initial := message.NewMessage("system", "Hello! How can I help you today?")
	for {
		select {
		case <-ctx.Done():
			return a.shutdown(context.Background())
		default:
		}
		res, err := sp.Execute(ctx, initial)
		if err != nil {
			return fmt.Errorf("pipeline 执行失败: %w", err)
		}
		if !res.Success {
			if res.Error != nil {
				return fmt.Errorf("pipeline 执行失败: %w", res.Error)
			}
			return fmt.Errorf("pipeline 执行失败（未知原因）")
		}
		if res.Message != nil && res.Message.Content != nil {
			fmt.Printf("\nAssistant: %v\n\n", res.Message.Content)
		}
		initial = message.NewMessage("system", "Please continue the conversation.")
	}
}

// shutdown 统一清理资源
func (a *App) shutdown(ctx context.Context) error {
	if a.webServer != nil {
		logger.Info("停止 Web 服务")
		if err := a.webServer.Stop(ctx); err != nil {
			logger.WithError(err).Error("Web 服务停止失败")
		} else {
			logger.Info("Web 服务停止成功")
		}
	}
	// 清理 Pipeline 与 Agent
	if p, err := a.pipelineMgr.GetPipeline("simple_chat"); err == nil {
		if sp, ok := p.(*pipeline.SequentialPipeline); ok {
			sp.Clear()
		}
	}
	return nil
}

// validateLLM 对默认提供商进行快速验证（API Key 等）
func (a *App) validateLLM() error {
	if a.cfg.LLM == nil {
		return fmt.Errorf("LLM 配置缺失")
	}
	// 将 config.LLM 转换为 llm 工厂可用配置
	factoryCfg := &llm.LLMFactoryConfig{
		DefaultProvider: a.cfg.LLM.DefaultProvider,
		Providers:       make(map[string]*llm.ProviderConfig),
		Timeout:         a.cfg.LLM.Timeout,
		MaxRetries:      a.cfg.LLM.MaxRetries,
		Parameters:      a.cfg.LLM.Parameters,
	}
	for name, p := range a.cfg.LLM.Providers {
		factoryCfg.Providers[name] = &llm.ProviderConfig{
			Type:       p.Type,
			APIKey:     p.APIKey,
			BaseURL:    p.BaseURL,
			Model:      p.Model,
			Timeout:    p.Timeout,
			MaxRetries: p.MaxRetries,
			Parameters: p.Parameters,
		}
	}
	cli, err := llm.NewFromConfig(factoryCfg, "")
	if err != nil {
		return fmt.Errorf("LLM 验证失败: %w", err)
	}
	defer cli.Close()
	return nil
}

// ensureDefaultAgentsAndPipeline 确保内置 user/assistant 与 simple_chat 管道可用
func (a *App) ensureDefaultAgentsAndPipeline() error {
	logger.Info("正在初始化默认Agent和Pipeline...")

	// 1. 创建默认的assistant Agent
	assistantAgent, err := a.createDefaultAssistantAgent()
	if err != nil {
		return fmt.Errorf("创建assistant Agent失败: %w", err)
	}

	// 2. 注册assistant Agent到管理器
	ctx := context.Background()
	if err := a.agentMgr.RegisterAgent(ctx, assistantAgent); err != nil {
		return fmt.Errorf("注册assistant Agent失败: %w", err)
	}

	// 3. 创建默认顺序管道
	sp, err := pipeline.NewSequentialPipeline(&pipeline.Config{
		Name:        "simple_chat",
		Type:        pipeline.PipelineTypeSequential,
		Description: "Simple chat pipeline with assistant agent",
		Timeout:     60 * time.Second,
		MaxRetries:  3,
	})
	if err != nil {
		return fmt.Errorf("创建管道失败: %w", err)
	}

	// 4. 将assistant Agent添加到管道
	if err := sp.AddAgent(assistantAgent); err != nil {
		return fmt.Errorf("添加Agent到管道失败: %w", err)
	}

	// 5. 注册管道到管理器
	if err := a.pipelineMgr.AddPipeline("simple_chat", sp); err != nil {
		return fmt.Errorf("注册管道失败: %w", err)
	}

	logger.Info("默认Agent和Pipeline初始化完成")
	return nil
}

// createDefaultAssistantAgent 创建默认的assistant Agent
func (a *App) createDefaultAssistantAgent() (agent.Agent, error) {
	// 创建LLM工厂
	factory := llm.NewFactory()

	// 直接使用默认配置（DeepSeek），因为我们有DEEPSEEK_API_KEY
	llmClient, err := factory.CreateDefault()
	if err != nil {
		return nil, fmt.Errorf("创建LLM客户端失败: %w", err)
	}

	// 创建LLMClient到ChatModel的适配器
	chatModel := &LLMClientAdapter{client: llmClient}

	// 使用真实的ChatModelAgent替代简化实现
	config := &chatmodel.Config{
		Name:          "assistant_001",
		Description:   "Default assistant agent for simple chat",
		Model:         chatModel,
		SystemPrompt:  "你是一个有用的AI助手。请用中文回答用户的问题，保持友好和专业的态度。",
		MaxIterations: 5,
	}

	return chatmodel.NewChatModelAgent(config)
}

// LLMClientAdapter 将LLMClient适配为ChatModel接口
type LLMClientAdapter struct {
	client llm.LLMClient
}

// Chat 实现ChatModel接口的Chat方法
func (a *LLMClientAdapter) Chat(ctx context.Context, req *llm.ChatRequest) (*llm.ChatResponse, error) {
	// 转换ChatRequest为GenerateRequest
	generateReq := &llm.GenerateRequest{
		Messages:    req.Messages,
		Model:       req.Model,
		Temperature: req.Temperature,
		MaxTokens:   req.MaxTokens,
		TopP:        req.TopP,
		Stop:        req.Stop,
		Stream:      req.Stream,
	}

	// 调用LLMClient的Generate方法
	resp, err := a.client.Generate(ctx, generateReq)
	if err != nil {
		return nil, err
	}

	// 转换GenerateResponse为ChatResponse
	chatResp := &llm.ChatResponse{
		ID:      resp.ID,
		Object:  resp.Object,
		Created: resp.Created,
		Model:   resp.Model,
		Choices: make([]llm.ChatChoice, len(resp.Choices)),
	}

	// 转换Usage
	if resp.Usage != nil {
		chatResp.Usage = &llm.ChatUsage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		}
	}

	// 转换Choices
	for i, choice := range resp.Choices {
		chatResp.Choices[i] = llm.ChatChoice{
			Index:        choice.Index,
			Message:      *choice.Message,
			FinishReason: choice.FinishReason,
		}
	}

	return chatResp, nil
}

// ChatStream 实现ChatModel接口的ChatStream方法
func (a *LLMClientAdapter) ChatStream(ctx context.Context, req *llm.ChatRequest) (<-chan *llm.ChatDelta, error) {
	// 转换ChatRequest为GenerateRequest
	generateReq := &llm.GenerateRequest{
		Messages:    req.Messages,
		Model:       req.Model,
		Temperature: req.Temperature,
		MaxTokens:   req.MaxTokens,
		TopP:        req.TopP,
		Stop:        req.Stop,
		Stream:      true,
	}

	// 调用LLMClient的GenerateStream方法
	streamChan, err := a.client.GenerateStream(ctx, generateReq)
	if err != nil {
		return nil, err
	}

	// 创建ChatDelta通道
	deltaChan := make(chan *llm.ChatDelta, 10)

	go func() {
		defer close(deltaChan)
		for streamResp := range streamChan {
			if streamResp.Error != nil {
				// 发送错误事件
				continue
			}

			for _, choice := range streamResp.Choices {
				// 转换ChatMessage为ChatDeltaContent
				deltaContent := llm.ChatDeltaContent{
					Role:    choice.Delta.Role,
					Content: "",
				}

				// 提取内容
				if choice.Delta.Content != nil {
					if str, ok := choice.Delta.Content.(string); ok {
						deltaContent.Content = str
					}
				}

				delta := &llm.ChatDelta{
					ID:      streamResp.ID,
					Object:  streamResp.Object,
					Created: streamResp.Created,
					Model:   streamResp.Model,
					Choices: []llm.ChatDeltaChoice{
						{
							Index: choice.Index,
							Delta: deltaContent,
						},
					},
				}

				select {
				case deltaChan <- delta:
				case <-ctx.Done():
					return
				}
			}

			if streamResp.Done {
				break
			}
		}
	}()

	return deltaChan, nil
}

// 便捷入口：从文件加载并运行（是否启用 Web 由配置决定）
func RunFromFile(configPath string) error {
	cfg, err := config.LoadConfigWithDefaults(configPath)
	if err != nil {
		return err
	}
	app, err := NewApp(cfg)
	if err != nil {
		return err
	}
	return app.Start(context.Background())
}

// initializeAudit 初始化审计模块
func (a *App) initializeAudit() error {
	if a.cfg.Audit == nil || !a.cfg.Audit.Enabled {
		return nil // 未启用审计
	}

	// 创建审计存储
	var store audit.Store
	var err error

	switch a.cfg.Audit.Driver {
	case "sqlite":
		store, err = audit.NewSQLiteStore(a.cfg.Audit.DSN, convertAuditConfig(a.cfg.Audit))
	case "postgres":
		store, err = audit.NewPostgresStore(a.cfg.Audit.DSN, convertAuditConfig(a.cfg.Audit))
	default:
		return fmt.Errorf("不支持的审计存储驱动: %s", a.cfg.Audit.Driver)
	}

	if err != nil {
		return fmt.Errorf("初始化审计存储失败: %w", err)
	}

	a.auditStore = store

	// 创建数据脱敏器
	sanitizer, err := audit.NewSanitizer(convertPrivacyConfig(a.cfg.Audit.Privacy))
	if err != nil {
		return fmt.Errorf("初始化数据脱敏器失败: %w", err)
	}

	// 创建审计写入器
	a.auditWriter = audit.NewAuditWriter(store, sanitizer, convertBatchConfig(a.cfg.Audit.Batch))

	// 启动审计写入器
	if err := a.auditWriter.Start(context.Background()); err != nil {
		return fmt.Errorf("启动审计写入器失败: %w", err)
	}

	// 创建审计查询器
	a.auditReader = audit.NewAuditReader(store, sanitizer)

	// 创建保留策略管理器
	if a.cfg.Audit.Retention != nil && a.cfg.Audit.Retention.Enabled {
		a.retentionMgr = audit.NewRetentionManager()
		retentionJob := audit.NewRetentionJob(store, convertRetentionConfig(a.cfg.Audit.Retention))
		a.retentionMgr.AddJob("default", retentionJob)

		// 启动保留策略
		if err := a.retentionMgr.StartAll(context.Background()); err != nil {
			return fmt.Errorf("启动保留策略失败: %w", err)
		}
	}

	return nil
}

// convertAuditConfig 转换审计配置
func convertAuditConfig(cfg *config.AuditConfig) *audit.Config {
	if cfg == nil {
		return audit.DefaultConfig()
	}

	return &audit.Config{
		Enabled: cfg.Enabled,
		Driver:  cfg.Driver,
		DSN:     cfg.DSN,
		Retention: audit.RetentionConfig{
			Enabled: cfg.Retention != nil && cfg.Retention.Enabled,
			MaxDays: func() int {
				if cfg.Retention != nil {
					return cfg.Retention.MaxDays
				}
				return 90
			}(),
			Cron: func() string {
				if cfg.Retention != nil {
					return cfg.Retention.Cron
				}
				return "@daily"
			}(),
		},
		Privacy: audit.PrivacyConfig{
			RedactPII: cfg.Privacy != nil && cfg.Privacy.RedactPII,
			PIIPatterns: func() []string {
				if cfg.Privacy != nil {
					return cfg.Privacy.PIIPatterns
				}
				return []string{"email", "phone"}
			}(),
			HashContent:   cfg.Privacy != nil && cfg.Privacy.HashContent,
			EncryptAtRest: cfg.Privacy != nil && cfg.Privacy.EncryptAtRest,
			EncryptKeyEnv: func() string {
				if cfg.Privacy != nil {
					return cfg.Privacy.EncryptKeyEnv
				}
				return "AUDIT_AES_KEY"
			}(),
		},
		Batch: audit.BatchConfig{
			Async: cfg.Batch == nil || cfg.Batch.Async,
			ChanBuffer: func() int {
				if cfg.Batch != nil {
					return cfg.Batch.ChanBuffer
				}
				return 1024
			}(),
			FlushInterval: func() time.Duration {
				if cfg.Batch != nil {
					return cfg.Batch.FlushInterval
				}
				return time.Second
			}(),
		},
		Web: audit.WebConfig{
			AllowReadAPI:   cfg.Web != nil && cfg.Web.AllowReadAPI,
			AllowDeleteAPI: cfg.Web != nil && cfg.Web.AllowDeleteAPI,
		},
	}
}

// convertPrivacyConfig 转换隐私配置
func convertPrivacyConfig(cfg *config.AuditPrivacyConfig) *audit.PrivacyConfig {
	if cfg == nil {
		return &audit.PrivacyConfig{
			RedactPII:     true,
			PIIPatterns:   []string{"email", "phone"},
			HashContent:   false,
			EncryptAtRest: false,
			EncryptKeyEnv: "AUDIT_AES_KEY",
		}
	}

	return &audit.PrivacyConfig{
		RedactPII:     cfg.RedactPII,
		PIIPatterns:   cfg.PIIPatterns,
		HashContent:   cfg.HashContent,
		EncryptAtRest: cfg.EncryptAtRest,
		EncryptKeyEnv: cfg.EncryptKeyEnv,
	}
}

// convertBatchConfig 转换批处理配置
func convertBatchConfig(cfg *config.AuditBatchConfig) *audit.BatchConfig {
	if cfg == nil {
		return &audit.BatchConfig{
			Async:         true,
			ChanBuffer:    1024,
			FlushInterval: time.Second,
		}
	}

	return &audit.BatchConfig{
		Async:         cfg.Async,
		ChanBuffer:    cfg.ChanBuffer,
		FlushInterval: cfg.FlushInterval,
	}
}

// convertRetentionConfig 转换保留策略配置
func convertRetentionConfig(cfg *config.AuditRetentionConfig) *audit.RetentionConfig {
	if cfg == nil {
		return &audit.RetentionConfig{
			Enabled: false,
			MaxDays: 90,
			Cron:    "@daily",
		}
	}

	return &audit.RetentionConfig{
		Enabled: cfg.Enabled,
		MaxDays: cfg.MaxDays,
		Cron:    cfg.Cron,
	}
}

// GetAuditStore 获取审计存储
func (a *App) GetAuditStore() audit.Store {
	return a.auditStore
}

// GetAuditWriter 获取审计写入器
func (a *App) GetAuditWriter() *audit.AuditWriter {
	return a.auditWriter
}

// GetAuditReader 获取审计查询器
func (a *App) GetAuditReader() *audit.AuditReader {
	return a.auditReader
}

// GetRetentionManager 获取保留策略管理器
func (a *App) GetRetentionManager() *audit.RetentionManager {
	return a.retentionMgr
}

// 便捷入口：启用 Web 并从文件启动
func StartWithWebUIFromFile(configPath string) error {
	cfg, err := config.LoadConfigWithDefaults(configPath)
	if err != nil {
		return err
	}
	if cfg.Web == nil {
		cfg.Web = &config.WebConfig{}
	}
	cfg.Web.Enabled = true
	app, err := NewApp(cfg)
	if err != nil {
		return err
	}
	return app.Start(context.Background())
}
