package memory

import (
	"context"
	"sync"

	"github.com/agentscope/agentscope-golang/pkg/message"
)

// Store 表示记忆存储接口
type Store interface {
	// Save 保存消息到记忆
	Save(ctx context.Context, sessionID string, msg *message.Message) error

	// Load 加载指定会话的消息历史
	Load(ctx context.Context, sessionID string, limit int) ([]*message.Message, error)

	// LoadAll 加载指定会话的所有消息
	LoadAll(ctx context.Context, sessionID string) ([]*message.Message, error)

	// Delete 删除指定会话的消息
	Delete(ctx context.Context, sessionID string) error

	// DeleteMessage 删除指定消息
	DeleteMessage(ctx context.Context, sessionID, messageID string) error

	// Search 搜索消息（基于内容）
	Search(ctx context.Context, sessionID, query string, limit int) ([]*message.Message, error)

	// GetSessions 获取所有会话ID
	GetSessions(ctx context.Context) ([]string, error)

	// Close 关闭存储
	Close() error
}

// VectorStore 表示向量存储接口（用于语义搜索）
type VectorStore interface {
	// SaveVector 保存消息的向量表示
	SaveVector(ctx context.Context, sessionID, messageID string, vector []float32, metadata map[string]any) error

	// SearchSimilar 基于向量相似度搜索消息
	SearchSimilar(ctx context.Context, sessionID string, queryVector []float32, limit int, threshold float32) ([]*VectorSearchResult, error)

	// DeleteVector 删除指定消息的向量
	DeleteVector(ctx context.Context, sessionID, messageID string) error

	// DeleteSessionVectors 删除指定会话的所有向量
	DeleteSessionVectors(ctx context.Context, sessionID string) error

	// Close 关闭向量存储
	Close() error
}

// VectorSearchResult 向量搜索结果
type VectorSearchResult struct {
	MessageID string         `json:"message_id"`
	SessionID string         `json:"session_id"`
	Score     float32        `json:"score"`    // 相似度分数
	Vector    []float32      `json:"vector"`   // 向量数据
	Metadata  map[string]any `json:"metadata"` // 元数据
}

// EnhancedStore 增强的存储接口，结合了基础存储和向量存储
type EnhancedStore interface {
	Store
	VectorStore
}

// MemoryStore 内存实现的记忆存储
type MemoryStore struct {
	mu       sync.RWMutex // 读写锁保护并发访问
	sessions map[string][]*message.Message
}

// NewMemoryStore 创建内存记忆存储
func NewMemoryStore() *MemoryStore {
	return &MemoryStore{
		sessions: make(map[string][]*message.Message),
	}
}

// Save 保存消息到记忆
func (m *MemoryStore) Save(ctx context.Context, sessionID string, msg *message.Message) error {
	if msg == nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息不能为空",
		}
	}

	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if err := msg.Validate(); err != nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息验证失败: " + err.Error(),
		}
	}

	// 克隆消息以避免外部修改
	clonedMsg := msg.Clone()

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.sessions[sessionID] == nil {
		m.sessions[sessionID] = make([]*message.Message, 0)
	}

	m.sessions[sessionID] = append(m.sessions[sessionID], clonedMsg)
	return nil
}

// Load 加载指定会话的消息历史
func (m *MemoryStore) Load(ctx context.Context, sessionID string, limit int) ([]*message.Message, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	messages, exists := m.sessions[sessionID]
	if !exists {
		return []*message.Message{}, nil
	}

	if limit <= 0 {
		return m.cloneMessages(messages), nil
	}

	start := len(messages) - limit
	if start < 0 {
		start = 0
	}

	return m.cloneMessages(messages[start:]), nil
}

// LoadAll 加载指定会话的所有消息
func (m *MemoryStore) LoadAll(ctx context.Context, sessionID string) ([]*message.Message, error) {
	return m.Load(ctx, sessionID, 0)
}

// Delete 删除指定会话的消息
func (m *MemoryStore) Delete(ctx context.Context, sessionID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.sessions, sessionID)
	return nil
}

// DeleteMessage 删除指定消息
func (m *MemoryStore) DeleteMessage(ctx context.Context, sessionID, messageID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if messageID == "" {
		return &ValidationError{
			Field:   "messageID",
			Message: "消息ID不能为空",
		}
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	messages, exists := m.sessions[sessionID]
	if !exists {
		return nil // 会话不存在，视为删除成功
	}

	for i, msg := range messages {
		if msg.ID == messageID {
			// 删除消息
			m.sessions[sessionID] = append(messages[:i], messages[i+1:]...)
			return nil
		}
	}

	return nil // 消息不存在，视为删除成功
}

// Search 搜索消息（基于内容）
func (m *MemoryStore) Search(ctx context.Context, sessionID, query string, limit int) ([]*message.Message, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if query == "" {
		return []*message.Message{}, nil
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	messages, exists := m.sessions[sessionID]
	if !exists {
		return []*message.Message{}, nil
	}

	var results []*message.Message
	for _, msg := range messages {
		content := msg.GetContentString()
		if content != "" && contains(content, query) {
			results = append(results, msg.Clone())
			if limit > 0 && len(results) >= limit {
				break
			}
		}
	}

	return results, nil
}

// GetSessions 获取所有会话ID
func (m *MemoryStore) GetSessions(ctx context.Context) ([]string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	sessions := make([]string, 0, len(m.sessions))
	for sessionID := range m.sessions {
		sessions = append(sessions, sessionID)
	}
	return sessions, nil
}

// Close 关闭存储
func (m *MemoryStore) Close() error {
	// 内存存储无需关闭操作
	return nil
}

// cloneMessages 克隆消息列表
func (m *MemoryStore) cloneMessages(messages []*message.Message) []*message.Message {
	cloned := make([]*message.Message, len(messages))
	for i, msg := range messages {
		cloned[i] = msg.Clone()
	}
	return cloned
}

// contains 简单的字符串包含检查（忽略大小写）
func contains(text, query string) bool {
	// 简单实现，实际应用中可能需要更复杂的搜索算法
	return len(text) >= len(query) &&
		(text == query ||
			len(text) > len(query) &&
				(text[:len(query)] == query ||
					text[len(text)-len(query):] == query ||
					findSubstring(text, query)))
}

// findSubstring 查找子字符串
func findSubstring(text, query string) bool {
	for i := 0; i <= len(text)-len(query); i++ {
		if text[i:i+len(query)] == query {
			return true
		}
	}
	return false
}

// ValidationError 表示验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return "验证错误 [" + e.Field + "]: " + e.Message
}
