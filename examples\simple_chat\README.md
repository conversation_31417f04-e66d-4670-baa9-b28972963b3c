# Simple Chat Example

This example demonstrates a basic chat application using AgentScope-Golang with a UserAgent and AssistantAgent powered by Deepseek API.

## Features

- Interactive chat with AI assistant
- Real-time conversation using Deepseek LLM
- Command system for user interaction
- Graceful shutdown handling
- Configuration management

## Prerequisites

1. Go 1.21 or higher
2. Deepseek API key

## Setup

1. Get your Deepseek API key from [https://platform.deepseek.com/](https://platform.deepseek.com/)

2. Set the environment variable:
   ```bash
   export DEEPSEEK_API_KEY="your_api_key_here"
   ```

3. Navigate to the example directory:
   ```bash
   cd examples/simple_chat
   ```

## Running the Example

### Method 1: Using go run (Recommended for development)

```bash
go run main.go
```

### Method 2: Using the Makefile from project root

```bash
# From the project root directory
make run
```

### Method 3: Build and run

```bash
# From the project root directory
make build
./bin/agentscope-golang
```

## Usage

Once the application starts, you'll see:

```
=== AgentScope-Golang Simple Chat ===
Welcome! You can start chatting with the AI assistant.
Type '/help' for available commands or '/quit' to exit.

You: 
```

### Available Commands

- `/help` - Show available commands
- `/quit` or `/exit` - Exit the chat
- `/clear` - Clear conversation (placeholder)
- `/status` - Show agent status
- `/config` - Show agent configuration
- `/config set <key> <value>` - Set configuration parameter

### Example Conversation

```
You: Hello, how are you?