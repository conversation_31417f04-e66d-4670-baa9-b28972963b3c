package distributed

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// MessageRouter 分布式消息路由器
type MessageRouter struct {
	discovery     ServiceDiscovery
	localAgents   map[string]agent.Agent
	remoteClients map[string]*RemoteAgentClient
	routingTable  map[string]string // agentID -> serviceID
	mu            sync.RWMutex
	logger        logger.Logger
}

// NewMessageRouter 创建消息路由器
func NewMessageRouter(discovery ServiceDiscovery) *MessageRouter {
	return &MessageRouter{
		discovery:     discovery,
		localAgents:   make(map[string]agent.Agent),
		remoteClients: make(map[string]*RemoteAgentClient),
		routingTable:  make(map[string]string),
		logger:        logger.GetGlobalLogger(),
	}
}

// RegisterLocalAgent 注册本地Agent
func (mr *MessageRouter) RegisterLocalAgent(ag agent.Agent) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	ctx := context.Background()
	agentName := ag.Name(ctx)
	mr.localAgents[agentName] = ag
	mr.logger.Infof("Local agent registered: %s", agentName)
}

// UnregisterLocalAgent 注销本地Agent
func (mr *MessageRouter) UnregisterLocalAgent(agentID string) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	delete(mr.localAgents, agentID)
	delete(mr.routingTable, agentID)
	mr.logger.Infof("Local agent unregistered: %s", agentID)
}

// RouteMessage 路由消息
func (mr *MessageRouter) RouteMessage(ctx context.Context, targetAgentID string, msg *message.Message) (*message.Message, error) {
	// 检查是否为本地Agent
	mr.mu.RLock()
	localAgent, isLocal := mr.localAgents[targetAgentID]
	mr.mu.RUnlock()

	if isLocal {
		return mr.routeToLocalAgent(ctx, localAgent, msg)
	}

	// 路由到远程Agent
	return mr.routeToRemoteAgent(ctx, targetAgentID, msg)
}

// routeToLocalAgent 路由到本地Agent
func (mr *MessageRouter) routeToLocalAgent(ctx context.Context, ag agent.Agent, msg *message.Message) (*message.Message, error) {
	agentName := ag.Name(ctx)
	mr.logger.Debugf("Routing message to local agent: %s", agentName)

	// 使用新的Agent接口：Run方法返回事件迭代器
	input := runtime.NewInput().AddMessage(msg)

	iter := ag.Run(ctx, input)

	// 从事件迭代器中提取响应消息
	var responseContent string
	var finalMessage *message.Message

	for {
		event, hasMore := iter.Next()
		if !hasMore {
			break
		}

		// 处理不同类型的事件
		switch event.Type {
		case "token":
			// 累积token内容
			if data, ok := event.Data.(interface{ GetContent() string }); ok {
				responseContent += data.GetContent()
			} else if dataMap, ok := event.Data.(map[string]interface{}); ok {
				if content, ok := dataMap["content"].(string); ok {
					responseContent += content
				}
			}
		case "final":
			// 获取最终响应
			if data, ok := event.Data.(interface{ GetContent() string }); ok {
				responseContent = data.GetContent()
			} else if dataMap, ok := event.Data.(map[string]interface{}); ok {
				if content, ok := dataMap["content"].(string); ok {
					responseContent = content
				}
			}
			// 创建响应消息
			finalMessage = message.NewAssistantMessage(responseContent)
		case "error":
			// 处理错误事件
			if data, ok := event.Data.(interface{ GetMessage() string }); ok {
				return nil, fmt.Errorf("Agent执行失败: %s", data.GetMessage())
			} else if dataMap, ok := event.Data.(map[string]interface{}); ok {
				if errMsg, ok := dataMap["message"].(string); ok {
					return nil, fmt.Errorf("Agent执行失败: %s", errMsg)
				}
			}
		}
	}

	// 如果没有获得最终消息，使用累积的内容创建响应
	if finalMessage == nil && responseContent != "" {
		finalMessage = message.NewAssistantMessage(responseContent)
	}

	// 如果仍然没有响应，返回默认消息
	if finalMessage == nil {
		finalMessage = message.NewAssistantMessage("Agent处理完成，但未返回具体响应")
	}

	return finalMessage, nil
}

// routeToRemoteAgent 路由到远程Agent
func (mr *MessageRouter) routeToRemoteAgent(ctx context.Context, agentID string, msg *message.Message) (*message.Message, error) {
	// 查找Agent所在的服务
	serviceID, err := mr.findAgentService(ctx, agentID)
	if err != nil {
		return nil, fmt.Errorf("failed to find service for agent %s: %w", agentID, err)
	}

	// 获取或创建远程客户端
	client, err := mr.getRemoteClient(ctx, serviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get remote client for service %s: %w", serviceID, err)
	}

	mr.logger.Debugf("Routing message to remote agent: %s via service: %s", agentID, serviceID)
	return client.SendMessage(ctx, agentID, msg)
}

// findAgentService 查找Agent所在的服务
func (mr *MessageRouter) findAgentService(ctx context.Context, agentID string) (string, error) {
	mr.mu.RLock()
	serviceID, exists := mr.routingTable[agentID]
	mr.mu.RUnlock()

	if exists {
		return serviceID, nil
	}

	// 从服务发现中查找
	services, err := mr.discovery.ListServices(ctx)
	if err != nil {
		return "", err
	}

	for _, service := range services {
		if service.Status == ServiceStatusHealthy {
			// 检查服务是否包含该Agent
			if mr.serviceHasAgent(ctx, service, agentID) {
				mr.mu.Lock()
				mr.routingTable[agentID] = service.ID
				mr.mu.Unlock()
				return service.ID, nil
			}
		}
	}

	return "", errors.NewNotFoundError("agent_not_found", fmt.Sprintf("agent %s not found in any service", agentID))
}

// serviceHasAgent 检查服务是否包含指定Agent
func (mr *MessageRouter) serviceHasAgent(ctx context.Context, service *ServiceInfo, agentID string) bool {
	// 这里可以实现具体的检查逻辑
	// 例如通过HTTP API查询服务的Agent列表
	return true // 简化实现
}

// getRemoteClient 获取远程客户端
func (mr *MessageRouter) getRemoteClient(ctx context.Context, serviceID string) (*RemoteAgentClient, error) {
	mr.mu.RLock()
	client, exists := mr.remoteClients[serviceID]
	mr.mu.RUnlock()

	if exists {
		return client, nil
	}

	// 创建新的远程客户端
	service, err := mr.discovery.GetService(ctx, serviceID)
	if err != nil {
		return nil, err
	}

	client = NewRemoteAgentClient(service)

	mr.mu.Lock()
	mr.remoteClients[serviceID] = client
	mr.mu.Unlock()

	return client, nil
}

// BroadcastMessage 广播消息
func (mr *MessageRouter) BroadcastMessage(ctx context.Context, msg *message.Message) error {
	// 广播到本地Agent
	mr.mu.RLock()
	localAgents := make([]agent.Agent, 0, len(mr.localAgents))
	for _, ag := range mr.localAgents {
		localAgents = append(localAgents, ag)
	}
	mr.mu.RUnlock()

	for _, ag := range localAgents {
		go func(ag agent.Agent) {
			agentName := ag.Name(ctx)
			// 使用新的Agent接口
			input := runtime.NewInput().AddMessage(msg)
			eventIter := ag.Run(ctx, input)

			// 处理Agent响应事件
			for {
				ev, hasMore := eventIter.Next()
				if !hasMore {
					break
				}

				// 记录事件处理
				if ev.Type == "error" {
					mr.logger.Errorf("Agent %s processing error: %v", agentName, ev.Data)
				} else {
					mr.logger.Debugf("Agent %s event: %s", agentName, ev.Type)
				}
			}

			mr.logger.Debugf("Broadcasted message to local agent: %s", agentName)
		}(ag)
	}

	// 广播到远程服务
	services, err := mr.discovery.ListServices(ctx)
	if err != nil {
		return err
	}

	for _, service := range services {
		if service.Status == ServiceStatusHealthy {
			go func(svc *ServiceInfo) {
				client, err := mr.getRemoteClient(ctx, svc.ID)
				if err != nil {
					mr.logger.Warnf("Failed to get remote client for service %s: %v", svc.ID, err)
					return
				}

				err = client.BroadcastMessage(ctx, msg)
				if err != nil {
					mr.logger.Warnf("Failed to broadcast to service %s: %v", svc.ID, err)
				}
			}(service)
		}
	}

	return nil
}

// GetAgentLocation 获取Agent位置
func (mr *MessageRouter) GetAgentLocation(ctx context.Context, agentID string) (*AgentLocation, error) {
	// 检查是否为本地Agent
	mr.mu.RLock()
	_, isLocal := mr.localAgents[agentID]
	mr.mu.RUnlock()

	if isLocal {
		return &AgentLocation{
			AgentID:   agentID,
			ServiceID: "local",
			IsLocal:   true,
		}, nil
	}

	// 查找远程Agent
	serviceID, err := mr.findAgentService(ctx, agentID)
	if err != nil {
		return nil, err
	}

	service, err := mr.discovery.GetService(ctx, serviceID)
	if err != nil {
		return nil, err
	}

	return &AgentLocation{
		AgentID:   agentID,
		ServiceID: serviceID,
		IsLocal:   false,
		Address:   service.Address,
		Port:      service.Port,
	}, nil
}

// AgentLocation Agent位置信息
type AgentLocation struct {
	AgentID   string `json:"agent_id"`
	ServiceID string `json:"service_id"`
	IsLocal   bool   `json:"is_local"`
	Address   string `json:"address,omitempty"`
	Port      int    `json:"port,omitempty"`
}

// RemoteAgentClient 远程Agent客户端
type RemoteAgentClient struct {
	service *ServiceInfo
	client  *http.Client
	logger  logger.Logger
}

// NewRemoteAgentClient 创建远程Agent客户端
func NewRemoteAgentClient(service *ServiceInfo) *RemoteAgentClient {
	return &RemoteAgentClient{
		service: service,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger.GetGlobalLogger(),
	}
}

// SendMessage 发送消息到远程Agent
func (rac *RemoteAgentClient) SendMessage(ctx context.Context, agentID string, msg *message.Message) (*message.Message, error) {
	url := fmt.Sprintf("%s://%s:%d/api/v1/agents/%s/reply",
		rac.service.Protocol, rac.service.Address, rac.service.Port, agentID)

	// 构建请求
	reqBody := map[string]interface{}{
		"message": map[string]interface{}{
			"id":      msg.ID,
			"role":    msg.Role,
			"name":    msg.Name,
			"content": msg.Content,
		},
	}

	reqData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqData))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := rac.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("remote agent request failed with status: %d", resp.StatusCode)
	}

	// 解析响应
	var respData map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&respData); err != nil {
		return nil, err
	}

	// 构建响应消息
	replyMsg := message.NewMessage(
		respData["role"].(string),
		respData["content"].(string),
	)
	replyMsg.Name = respData["name"].(string)
	replyMsg.ID = respData["id"].(string)

	return replyMsg, nil
}

// BroadcastMessage 广播消息到远程服务
func (rac *RemoteAgentClient) BroadcastMessage(ctx context.Context, msg *message.Message) error {
	url := fmt.Sprintf("%s://%s:%d/api/v1/broadcast",
		rac.service.Protocol, rac.service.Address, rac.service.Port)

	// 构建请求
	reqBody := map[string]interface{}{
		"message": map[string]interface{}{
			"id":      msg.ID,
			"role":    msg.Role,
			"name":    msg.Name,
			"content": msg.Content,
		},
	}

	reqData, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := rac.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("broadcast request failed with status: %d", resp.StatusCode)
	}

	return nil
}

// MessageRoutingTable 消息路由表
type MessageRoutingTable struct {
	routes map[string]*RouteInfo
	mu     sync.RWMutex
	logger logger.Logger
}

// RouteInfo 路由信息
type RouteInfo struct {
	AgentID     string    `json:"agent_id"`
	ServiceID   string    `json:"service_id"`
	LastUpdated time.Time `json:"last_updated"`
	HopCount    int       `json:"hop_count"`
}

// NewMessageRoutingTable 创建消息路由表
func NewMessageRoutingTable() *MessageRoutingTable {
	return &MessageRoutingTable{
		routes: make(map[string]*RouteInfo),
		logger: logger.GetGlobalLogger(),
	}
}

// AddRoute 添加路由
func (mrt *MessageRoutingTable) AddRoute(agentID, serviceID string, hopCount int) {
	mrt.mu.Lock()
	defer mrt.mu.Unlock()

	mrt.routes[agentID] = &RouteInfo{
		AgentID:     agentID,
		ServiceID:   serviceID,
		LastUpdated: time.Now(),
		HopCount:    hopCount,
	}

	mrt.logger.Debug("Route added: %s -> %s (hops: %d)", agentID, serviceID, hopCount)
}

// GetRoute 获取路由
func (mrt *MessageRoutingTable) GetRoute(agentID string) (*RouteInfo, bool) {
	mrt.mu.RLock()
	defer mrt.mu.RUnlock()

	route, exists := mrt.routes[agentID]
	return route, exists
}

// RemoveRoute 移除路由
func (mrt *MessageRoutingTable) RemoveRoute(agentID string) {
	mrt.mu.Lock()
	defer mrt.mu.Unlock()

	delete(mrt.routes, agentID)
	mrt.logger.Debug("Route removed: %s", agentID)
}

// ListRoutes 列出所有路由
func (mrt *MessageRoutingTable) ListRoutes() []*RouteInfo {
	mrt.mu.RLock()
	defer mrt.mu.RUnlock()

	routes := make([]*RouteInfo, 0, len(mrt.routes))
	for _, route := range mrt.routes {
		routes = append(routes, route)
	}

	return routes
}

// CleanupExpiredRoutes 清理过期路由
func (mrt *MessageRoutingTable) CleanupExpiredRoutes(maxAge time.Duration) {
	mrt.mu.Lock()
	defer mrt.mu.Unlock()

	now := time.Now()
	var expiredRoutes []string

	for agentID, route := range mrt.routes {
		if now.Sub(route.LastUpdated) > maxAge {
			expiredRoutes = append(expiredRoutes, agentID)
		}
	}

	for _, agentID := range expiredRoutes {
		delete(mrt.routes, agentID)
		mrt.logger.Debug("Expired route removed: %s", agentID)
	}

	if len(expiredRoutes) > 0 {
		mrt.logger.Info("Cleaned up %d expired routes", len(expiredRoutes))
	}
}
