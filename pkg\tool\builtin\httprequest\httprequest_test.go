package httprequest

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHTTPRequestTool_Name(t *testing.T) {
	tool := NewHTTPRequestTool()
	assert.Equal(t, "httprequest", tool.Name())
}

func TestHTTPRequestTool_Description(t *testing.T) {
	tool := NewHTTPRequestTool()
	desc := tool.Description()
	assert.NotEmpty(t, desc)
	assert.Contains(t, desc, "HTTP")
}

func TestHTTPRequestTool_Schema(t *testing.T) {
	tool := NewHTTPRequestTool()
	schema := tool.Schema()

	require.NotNil(t, schema)
	assert.Equal(t, "object", schema.Type)

	// 检查必需的属性
	require.NotNil(t, schema.Properties)

	// 检查 url 属性
	urlProp, exists := schema.Properties["url"]
	require.True(t, exists)
	assert.Equal(t, "string", urlProp.Type)

	// 检查 method 属性
	methodProp, exists := schema.Properties["method"]
	require.True(t, exists)
	assert.Equal(t, "string", methodProp.Type)

	// 检查枚举值
	require.NotNil(t, methodProp.Enum)
	expectedMethods := []any{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
	assert.ElementsMatch(t, expectedMethods, methodProp.Enum)

	// 检查必需字段
	require.Contains(t, schema.Required, "url")
}

func TestHTTPRequestTool_Execute_GET(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "GET", r.Method)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"message": "Hello, World!",
			"method":  r.Method,
		})
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	params := map[string]any{
		"url":    server.URL,
		"method": "GET",
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok, "结果应该是 map[string]interface{}")

	// 检查响应字段
	statusCode, ok := resultMap["status_code"]
	require.True(t, ok)

	// 处理可能的类型（int 或 float64）
	var statusCodeInt int
	switch v := statusCode.(type) {
	case int:
		statusCodeInt = v
	case float64:
		statusCodeInt = int(v)
	default:
		t.Fatalf("Unexpected status_code type: %T", v)
	}
	assert.Equal(t, http.StatusOK, statusCodeInt)
	assert.Contains(t, resultMap, "headers")
	assert.Contains(t, resultMap, "body")
	assert.Contains(t, resultMap, "success")

	// 检查响应体
	body, exists := resultMap["body"]
	require.True(t, exists)

	bodyStr, ok := body.(string)
	require.True(t, ok)

	var responseData map[string]string
	err = json.Unmarshal([]byte(bodyStr), &responseData)
	require.NoError(t, err)

	assert.Equal(t, "Hello, World!", responseData["message"])
	assert.Equal(t, "GET", responseData["method"])
}

func TestHTTPRequestTool_Execute_POST(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "POST", r.Method)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

		var requestData map[string]string
		err := json.NewDecoder(r.Body).Decode(&requestData)
		require.NoError(t, err)

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"received": requestData,
			"status":   "created",
		})
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	requestBody := map[string]string{
		"name":  "test",
		"value": "data",
	}
	bodyJSON, err := json.Marshal(requestBody)
	require.NoError(t, err)

	params := map[string]any{
		"url":    server.URL,
		"method": "POST",
		"headers": map[string]any{
			"Content-Type": "application/json",
		},
		"body": string(bodyJSON),
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 处理状态码类型
	statusCode, ok := resultMap["status_code"]
	require.True(t, ok)
	var statusCodeInt int
	switch v := statusCode.(type) {
	case int:
		statusCodeInt = v
	case float64:
		statusCodeInt = int(v)
	default:
		t.Fatalf("Unexpected status_code type: %T", v)
	}
	assert.Equal(t, http.StatusCreated, statusCodeInt)

	// 检查响应体
	body, exists := resultMap["body"]
	require.True(t, exists)

	var responseData map[string]interface{}
	err = json.Unmarshal([]byte(body.(string)), &responseData)
	require.NoError(t, err)

	assert.Equal(t, "created", responseData["status"])
	assert.Contains(t, responseData, "received")
}

func TestHTTPRequestTool_Execute_WithHeaders(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查自定义头部
		assert.Equal(t, "test-value", r.Header.Get("X-Test-Header"))
		assert.Equal(t, "Bearer token123", r.Header.Get("Authorization"))

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	params := map[string]any{
		"url":    server.URL,
		"method": "GET",
		"headers": map[string]any{
			"X-Test-Header": "test-value",
			"Authorization": "Bearer token123",
		},
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 处理状态码类型
	statusCode, ok := resultMap["status_code"]
	require.True(t, ok)
	var statusCodeInt int
	switch v := statusCode.(type) {
	case int:
		statusCodeInt = v
	case float64:
		statusCodeInt = int(v)
	default:
		t.Fatalf("Unexpected status_code type: %T", v)
	}
	assert.Equal(t, http.StatusOK, statusCodeInt)
}

func TestHTTPRequestTool_Execute_WithTimeout(t *testing.T) {
	// 创建慢响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	params := map[string]any{
		"url":     server.URL,
		"method":  "GET",
		"timeout": 1, // 1秒 超时
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err)
	require.NotNil(t, result)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 检查超时错误
	assert.False(t, resultMap["success"].(bool))
	assert.Contains(t, resultMap["error"].(string), "context deadline exceeded")
}

func TestHTTPRequestTool_Execute_ErrorCases(t *testing.T) {
	tool := NewHTTPRequestTool()
	ctx := context.Background()

	tests := []struct {
		name   string
		params map[string]any
	}{
		{
			name:   "缺少 URL",
			params: map[string]any{},
		},
		{
			name: "无效的 URL",
			params: map[string]any{
				"url": "invalid-url",
			},
		},
		{
			name: "无效的方法",
			params: map[string]any{
				"url":    "http://example.com",
				"method": "INVALID",
			},
		},
		{
			name: "无效的超时值",
			params: map[string]any{
				"url":     "http://example.com",
				"timeout": "invalid",
			},
		},
		{
			name: "无效的头部格式",
			params: map[string]any{
				"url":     "http://example.com",
				"headers": "invalid",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(ctx, tt.params)
			assert.Error(t, err)
			assert.Nil(t, result)
		})
	}
}

func TestHTTPRequestTool_Execute_HTTPErrors(t *testing.T) {
	// 创建返回错误状态码的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusNotFound)
		w.Write([]byte("Not Found"))
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	params := map[string]any{
		"url":    server.URL,
		"method": "GET",
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err) // HTTP 错误不应该导致工具执行失败

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 处理状态码类型
	statusCode, ok := resultMap["status_code"]
	require.True(t, ok)
	var statusCodeInt int
	switch v := statusCode.(type) {
	case int:
		statusCodeInt = v
	case float64:
		statusCodeInt = int(v)
	default:
		t.Fatalf("Unexpected status_code type: %T", v)
	}
	assert.Equal(t, http.StatusNotFound, statusCodeInt)
	assert.Equal(t, "Not Found", resultMap["body"])
}

func TestHTTPRequestTool_Execute_DefaultMethod(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "GET", r.Method) // 默认应该是 GET
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	params := map[string]any{
		"url":    server.URL,
		"method": "GET", // 明确指定方法
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 处理状态码类型
	statusCode, ok := resultMap["status_code"]
	require.True(t, ok)
	var statusCodeInt int
	switch v := statusCode.(type) {
	case int:
		statusCodeInt = v
	case float64:
		statusCodeInt = int(v)
	default:
		t.Fatalf("Unexpected status_code type: %T", v)
	}
	assert.Equal(t, http.StatusOK, statusCodeInt)
}

func TestHTTPRequestTool_Execute_NilContext(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()

	params := map[string]any{
		"url":    server.URL,
		"method": "GET",
	}

	// 使用 context.TODO() 而不是 nil
	result, err := tool.Execute(context.TODO(), params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 处理状态码类型
	statusCode, ok := resultMap["status_code"]
	require.True(t, ok)
	var statusCodeInt int
	switch v := statusCode.(type) {
	case int:
		statusCodeInt = v
	case float64:
		statusCodeInt = int(v)
	default:
		t.Fatalf("Unexpected status_code type: %T", v)
	}
	assert.Equal(t, http.StatusOK, statusCodeInt)
}

func TestHTTPRequestTool_Execute_ResponseHeaders(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("X-Custom-Header", "custom-value")
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "test"}`))
	}))
	defer server.Close()

	tool := NewHTTPRequestTool()
	ctx := context.Background()

	params := map[string]any{
		"url":    server.URL,
		"method": "GET",
	}

	result, err := tool.Execute(ctx, params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	// 检查响应头部
	headers, exists := resultMap["headers"]
	require.True(t, exists)

	headersMap, ok := headers.(map[string]string)
	require.True(t, ok)

	// 检查自定义头部
	customHeader, exists := headersMap["X-Custom-Header"]
	require.True(t, exists)
	assert.Equal(t, "custom-value", customHeader)

	// 检查 Content-Type
	contentType, exists := headersMap["Content-Type"]
	require.True(t, exists)
	assert.Equal(t, "application/json", contentType)
}
