# LLM 提供商支持指南

AgentScope-Golang 支持多种大语言模型提供商，提供统一的接口和配置方式。本文档详细介绍各提供商的配置和使用方法。

## 支持的提供商

### 1. DeepSeek

DeepSeek 是一个高性能的中文大语言模型。

**配置示例：**
```yaml
llm:
  default_provider: "deepseek"
  providers:
    deepseek:
      type: "deepseek"
      api_key: "${DEEPSEEK_API_KEY}"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
      timeout: "30s"
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048
```

**环境变量：**
- `DEEPSEEK_API_KEY`：DeepSeek API 密钥

**支持的模型：**
- `deepseek-chat`：通用对话模型
- `deepseek-coder`：代码生成模型

### 2. Qwen（通义千问）

Qwen 是阿里云 DashScope 平台提供的大语言模型。

**配置示例：**
```yaml
llm:
  default_provider: "qwen"
  providers:
    qwen:
      type: "qwen"
      api_key: "${DASHSCOPE_API_KEY}"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model: "qwen-plus"
      timeout: "30s"
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048
```

**环境变量：**
- `DASHSCOPE_API_KEY`：DashScope API 密钥（优先）
- `QWEN_API_KEY`：Qwen API 密钥（备选）

**支持的模型：**
- `qwen-plus`：增强版模型
- `qwen-turbo`：快速响应模型
- `qwen-max`：最大性能模型

### 3. 豆包（Doubao）

豆包是字节跳动火山引擎 Ark 平台提供的大语言模型。

**配置示例：**
```yaml
llm:
  default_provider: "doubao"
  providers:
    doubao:
      type: "doubao"
      api_key: "${ARK_API_KEY}"
      base_url: "https://ark.cn-beijing.volces.com/api/v3"
      model: "ep-xxxxxxxx"  # 请替换为实际的 Endpoint ID
      timeout: "30s"
      max_retries: 3
      parameters:
        temperature: 0.7
        max_tokens: 2048
```

**环境变量：**
- `ARK_API_KEY`：Ark API 密钥（优先）
- `DOUBAO_API_KEY`：豆包 API 密钥（备选）
- `VOLC_API_KEY`：火山引擎 API 密钥（备选）

**重要说明：**
- 豆包使用 Endpoint ID 作为模型标识符，格式为 `ep-xxxxxxxx`
- 需要在火山引擎控制台创建推理接入点获取 Endpoint ID

## 多提供商配置

### 完整配置示例

```yaml
llm:
  default_provider: "qwen"  # 默认使用 Qwen
  timeout: "30s"
  max_retries: 3
  parameters:
    temperature: 0.7
    max_tokens: 2048
  providers:
    deepseek:
      type: "deepseek"
      api_key: "${DEEPSEEK_API_KEY}"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
    qwen:
      type: "qwen"
      api_key: "${DASHSCOPE_API_KEY}"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model: "qwen-plus"
    doubao:
      type: "doubao"
      api_key: "${ARK_API_KEY}"
      base_url: "https://ark.cn-beijing.volces.com/api/v3"
      model: "ep-xxxxxxxx"
```

### 动态切换提供商

在代码中可以通过修改 Agent 配置来切换 LLM 提供商：

```go
// 创建使用 Qwen 的 Assistant Agent
assistantConfig := &agent.Config{
    ID:        "assistant_001",
    Name:      "Assistant",
    Type:      agent.AgentTypeAssistant,
    LLMClient: "qwen",  // 指定使用 Qwen
}

assistant, err := agent.NewAssistantAgent(assistantConfig)
```

## 工厂模式支持

AgentScope-Golang 提供了 LLM 工厂模式，支持统一的客户端创建：

```go
import "github.com/agentscope/agentscope-golang/pkg/llm"

// 创建工厂
factory := llm.NewFactory()

// 创建提供商配置
providerConfig := &llm.ProviderConfig{
    Type:    "qwen",
    APIKey:  "your-api-key",
    BaseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
    Model:   "qwen-plus",
}

// 创建客户端
client, err := factory.CreateClient(providerConfig)
if err != nil {
    log.Fatal(err)
}
defer client.Close()
```

## 错误处理和重试

所有 LLM 客户端都支持统一的错误处理和重试机制：

### 重试配置

```yaml
llm:
  max_retries: 3
  providers:
    qwen:
      max_retries: 5  # 覆盖全局设置
```

### 错误类型

- `CodeInvalidCredentials`：API 密钥无效
- `CodeLLMQuotaExceeded`：配额超限
- `CodeLLMRateLimited`：请求频率限制
- `CodeLLMServiceUnavailable`：服务不可用

## 性能优化

### 连接池配置

所有 LLM 客户端都使用 HTTP 连接池来提高性能：

```go
// 默认配置
MaxIdleConns:        100
MaxIdleConnsPerHost: 10
IdleConnTimeout:     90 * time.Second
```

### 超时设置

```yaml
llm:
  timeout: "30s"  # 全局超时
  providers:
    qwen:
      timeout: "60s"  # 提供商特定超时
```

## 最佳实践

1. **API 密钥管理**：使用环境变量存储 API 密钥，不要硬编码在配置文件中
2. **错误处理**：实现适当的错误处理和重试逻辑
3. **监控**：监控 API 调用的成功率、延迟和错误率
4. **配额管理**：合理设置请求频率，避免超出 API 配额限制
5. **模型选择**：根据具体需求选择合适的模型（速度 vs 质量）

## 故障排查

### 常见问题

1. **API 密钥错误**
   ```
   [validation:INVALID_INPUT] Qwen API key not found
   ```
   解决：检查环境变量是否正确设置

2. **网络连接问题**
   ```
   context deadline exceeded
   ```
   解决：检查网络连接，增加超时时间

3. **模型不存在**
   ```
   model not found
   ```
   解决：检查模型名称是否正确，对于豆包检查 Endpoint ID

### 调试模式

启用详细日志来调试问题：

```yaml
logging:
  level: "debug"
```

这将输出详细的 HTTP 请求和响应信息，帮助诊断问题。
