package web

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/gorilla/websocket"
)

// WebSocketManager WebSocket管理器
type WebSocketManager struct {
	clients    map[string]*WebSocketClient
	broadcast  chan []byte
	register   chan *WebSocketClient
	unregister chan *WebSocketClient
	agentMgr   *agent.Manager
	logger     logger.Logger
	webConfig  *config.WebConfig
	mu         sync.RWMutex
}

// WebSocketPerformanceConfig WebSocket性能配置
type WebSocketPerformanceConfig struct {
	// 批量发送的最大消息数
	MaxBatchSize int
	// 批量发送的最大等待时间
	BatchTimeout time.Duration
	// 写缓冲区大小
	WriteBufferSize int
	// 读缓冲区大小
	ReadBufferSize int
	// 是否启用压缩
	EnableCompression bool
}

// DefaultWebSocketPerformanceConfig 默认WebSocket性能配置
var DefaultWebSocketPerformanceConfig = &WebSocketPerformanceConfig{
	MaxBatchSize:      10,
	BatchTimeout:      10 * time.Millisecond,
	WriteBufferSize:   4096,
	ReadBufferSize:    4096,
	EnableCompression: true,
}

// WebSocketClient WebSocket客户端
type WebSocketClient struct {
	id       string
	conn     *websocket.Conn
	send     chan []byte
	manager  *WebSocketManager
	agentID  string
	metadata map[string]interface{}

	// 性能优化相关
	perfConfig  *WebSocketPerformanceConfig
	batchBuffer *bytes.Buffer
	batchTimer  *time.Timer
	batchMutex  sync.Mutex
}

// WebSocketMessage WebSocket消息
type WebSocketMessage struct {
	Type      string                 `json:"type"`
	Data      interface{}            `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	ClientID  string                 `json:"client_id,omitempty"`
	AgentID   string                 `json:"agent_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// checkOrigin 检查WebSocket连接的Origin是否被允许
func checkOrigin(r *http.Request, allowedOrigins []string) bool {
	origin := r.Header.Get("Origin")
	if origin == "" {
		// 如果没有Origin头，拒绝连接（防止非浏览器客户端绕过检查）
		return false
	}

	// 如果没有配置允许的Origin，默认拒绝所有跨域请求
	if len(allowedOrigins) == 0 {
		// 只允许同源请求
		// 解析Origin URL来获取host部分
		if strings.Contains(origin, "://") {
			parts := strings.Split(origin, "://")
			if len(parts) == 2 {
				originHost := parts[1]
				// 移除路径部分
				if strings.Contains(originHost, "/") {
					originHost = strings.Split(originHost, "/")[0]
				}
				return originHost == r.Host
			}
		}
		return false
	}

	// 检查Origin是否在白名单中
	for _, allowed := range allowedOrigins {
		if allowed == "*" {
			// 通配符允许所有Origin（不推荐在生产环境使用）
			return true
		}
		if origin == allowed {
			return true
		}
		// 支持子域名匹配（如 *.example.com）
		if strings.HasPrefix(allowed, "*.") {
			domain := allowed[2:] // 移除 "*."
			if strings.HasSuffix(origin, "://"+domain) || strings.Contains(origin, "://"+domain+":") {
				return true
			}
			// 检查子域名
			if strings.Contains(origin, "."+domain) {
				return true
			}
		}
	}

	return false
}

// createUpgrader 创建WebSocket升级器
func createUpgrader(webConfig *config.WebConfig) websocket.Upgrader {
	return websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			var allowedOrigins []string
			if webConfig != nil && webConfig.CORS != nil && webConfig.CORS.Enabled {
				allowedOrigins = webConfig.CORS.AllowedOrigins
			}
			return checkOrigin(r, allowedOrigins)
		},
		ReadBufferSize:    DefaultWebSocketPerformanceConfig.ReadBufferSize,
		WriteBufferSize:   DefaultWebSocketPerformanceConfig.WriteBufferSize,
		EnableCompression: DefaultWebSocketPerformanceConfig.EnableCompression,
	}
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(agentMgr *agent.Manager, webConfig *config.WebConfig) *WebSocketManager {
	return &WebSocketManager{
		clients:    make(map[string]*WebSocketClient),
		broadcast:  make(chan []byte),
		register:   make(chan *WebSocketClient),
		unregister: make(chan *WebSocketClient),
		agentMgr:   agentMgr,
		logger:     logger.GetGlobalLogger(),
		webConfig:  webConfig,
	}
}

// Start 启动WebSocket管理器
func (wsm *WebSocketManager) Start() {
	go wsm.run()
}

// run 运行WebSocket管理器
func (wsm *WebSocketManager) run() {
	for {
		select {
		case client := <-wsm.register:
			wsm.mu.Lock()
			wsm.clients[client.id] = client
			wsm.mu.Unlock()

			wsm.logger.Info("WebSocket client connected: %s", client.id)

			// 发送欢迎消息
			welcome := WebSocketMessage{
				Type:      "welcome",
				Data:      map[string]string{"message": "Connected successfully"},
				Timestamp: time.Now(),
				ClientID:  client.id,
			}
			client.sendMessage(welcome)

		case client := <-wsm.unregister:
			wsm.mu.Lock()
			if _, ok := wsm.clients[client.id]; ok {
				delete(wsm.clients, client.id)
				close(client.send)
			}
			wsm.mu.Unlock()

			wsm.logger.Info("WebSocket client disconnected: %s", client.id)

		case message := <-wsm.broadcast:
			wsm.mu.RLock()
			for _, client := range wsm.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(wsm.clients, client.id)
				}
			}
			wsm.mu.RUnlock()
		}
	}
}

// HandleWebSocket 处理WebSocket连接
func (wsm *WebSocketManager) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	upgrader := createUpgrader(wsm.webConfig)
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		wsm.logger.Error("WebSocket upgrade failed: %v", err)
		return
	}

	clientID := generateClientID()
	client := &WebSocketClient{
		id:          clientID,
		conn:        conn,
		send:        make(chan []byte, 256),
		manager:     wsm,
		metadata:    make(map[string]interface{}),
		perfConfig:  DefaultWebSocketPerformanceConfig,
		batchBuffer: bytes.NewBuffer(make([]byte, 0, DefaultWebSocketPerformanceConfig.WriteBufferSize)),
	}

	wsm.register <- client

	go client.writePump()
	go client.readPump()
}

// BroadcastMessage 广播消息
func (wsm *WebSocketManager) BroadcastMessage(msg WebSocketMessage) {
	data, err := json.Marshal(msg)
	if err != nil {
		wsm.logger.Error("Failed to marshal WebSocket message: %v", err)
		return
	}

	wsm.broadcast <- data
}

// SendToClient 发送消息给特定客户端
func (wsm *WebSocketManager) SendToClient(clientID string, msg WebSocketMessage) {
	wsm.mu.RLock()
	client, exists := wsm.clients[clientID]
	wsm.mu.RUnlock()

	if exists {
		client.sendMessage(msg)
	}
}

// GetConnectedClients 获取连接的客户端列表
func (wsm *WebSocketManager) GetConnectedClients() []string {
	wsm.mu.RLock()
	defer wsm.mu.RUnlock()

	clients := make([]string, 0, len(wsm.clients))
	for id := range wsm.clients {
		clients = append(clients, id)
	}
	return clients
}

// readPump 读取消息
func (c *WebSocketClient) readPump() {
	defer func() {
		c.manager.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(512)
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, messageData, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.manager.logger.Error("WebSocket error: %v", err)
			}
			break
		}

		var wsMsg WebSocketMessage
		if err := json.Unmarshal(messageData, &wsMsg); err != nil {
			c.manager.logger.Warn("Invalid WebSocket message from client %s: %v", c.id, err)
			continue
		}

		wsMsg.ClientID = c.id
		wsMsg.Timestamp = time.Now()

		c.handleMessage(wsMsg)
	}
}

// writePump 写入消息 - 优化版本，支持智能批量发送
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	batchTicker := time.NewTicker(c.perfConfig.BatchTimeout)

	defer func() {
		ticker.Stop()
		batchTicker.Stop()
		if c.batchTimer != nil {
			c.batchTimer.Stop()
		}
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				// 发送剩余的批量消息
				c.flushBatch()
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// 添加消息到批量缓冲区
			c.addToBatch(message)

		case <-batchTicker.C:
			// 定期刷新批量缓冲区
			c.flushBatch()

		case <-ticker.C:
			// 发送ping消息保持连接
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// addToBatch 添加消息到批量缓冲区
func (c *WebSocketClient) addToBatch(message []byte) {
	c.batchMutex.Lock()
	defer c.batchMutex.Unlock()

	// 如果缓冲区不为空，添加分隔符
	if c.batchBuffer.Len() > 0 {
		c.batchBuffer.WriteByte('\n')
	}

	// 添加消息到缓冲区
	c.batchBuffer.Write(message)

	// 检查是否需要立即发送
	shouldFlush := false

	// 检查批量大小限制
	messageCount := bytes.Count(c.batchBuffer.Bytes(), []byte{'\n'}) + 1
	if messageCount >= c.perfConfig.MaxBatchSize {
		shouldFlush = true
	}

	// 检查缓冲区大小限制
	if c.batchBuffer.Len() >= c.perfConfig.WriteBufferSize {
		shouldFlush = true
	}

	if shouldFlush {
		c.flushBatchUnsafe()
	}
}

// flushBatch 刷新批量缓冲区（线程安全版本）
func (c *WebSocketClient) flushBatch() {
	c.batchMutex.Lock()
	defer c.batchMutex.Unlock()
	c.flushBatchUnsafe()
}

// flushBatchUnsafe 刷新批量缓冲区（非线程安全，需要在锁内调用）
func (c *WebSocketClient) flushBatchUnsafe() {
	if c.batchBuffer.Len() == 0 {
		return
	}

	// 设置写入超时
	c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))

	// 获取写入器
	w, err := c.conn.NextWriter(websocket.TextMessage)
	if err != nil {
		c.manager.logger.Error("Failed to get WebSocket writer for client %s: %v", c.id, err)
		return
	}

	// 写入批量数据
	_, err = w.Write(c.batchBuffer.Bytes())
	if err != nil {
		c.manager.logger.Error("Failed to write batch data for client %s: %v", c.id, err)
		w.Close()
		return
	}

	// 关闭写入器
	if err := w.Close(); err != nil {
		c.manager.logger.Error("Failed to close WebSocket writer for client %s: %v", c.id, err)
		return
	}

	// 清空缓冲区
	c.batchBuffer.Reset()
}

// sendMessage 发送消息
func (c *WebSocketClient) sendMessage(msg WebSocketMessage) {
	data, err := json.Marshal(msg)
	if err != nil {
		c.manager.logger.Error("Failed to marshal message for client %s: %v", c.id, err)
		return
	}

	select {
	case c.send <- data:
	default:
		close(c.send)
		c.manager.mu.Lock()
		delete(c.manager.clients, c.id)
		c.manager.mu.Unlock()
	}
}

// handleMessage 处理接收到的消息
func (c *WebSocketClient) handleMessage(wsMsg WebSocketMessage) {
	switch wsMsg.Type {
	case "agent_message":
		c.handleAgentMessage(wsMsg)
	case "subscribe_agent":
		c.handleSubscribeAgent(wsMsg)
	case "unsubscribe_agent":
		c.handleUnsubscribeAgent(wsMsg)
	case "ping":
		c.handlePing(wsMsg)
	default:
		c.manager.logger.Warn("Unknown message type from client %s: %s", c.id, wsMsg.Type)
	}
}

// handleAgentMessage 处理Agent消息
func (c *WebSocketClient) handleAgentMessage(wsMsg WebSocketMessage) {
	data, ok := wsMsg.Data.(map[string]interface{})
	if !ok {
		c.sendError("Invalid agent message format")
		return
	}

	agentID, ok := data["agent_id"].(string)
	if !ok {
		c.sendError("Missing agent_id in message")
		return
	}

	content, ok := data["content"].(string)
	if !ok {
		c.sendError("Missing content in message")
		return
	}

	// 获取Agent
	ag, err := c.manager.agentMgr.GetAgent(agentID)
	if err != nil {
		c.sendError("Agent not found: " + agentID)
		return
	}

	// 创建消息
	msg := message.NewMessage("user", content)
	msg.Name = c.id

	// 异步处理Agent回复
	go func() {
		ctx := context.Background()
		// 使用新的Agent接口
		input := runtime.NewInput()
		input.AddMessage(msg)
		eventIter := ag.Run(ctx, input)

		// 从事件迭代器中提取响应消息
		var responseContent string = "Agent已处理您的请求"
		var hasResponse bool = false

		// 处理事件流
		for {
			ev, hasMore := eventIter.Next()
			if !hasMore {
				break
			}

			// 提取最终响应内容
			if ev.Type == "final" || ev.Type == "token" {
				if data, ok := ev.Data.(map[string]interface{}); ok {
					if content, ok := data["content"].(string); ok && content != "" {
						responseContent = content
						hasResponse = true
					}
				}
			}
		}

		// 如果没有获取到响应，使用默认消息
		if !hasResponse {
			responseContent = "Agent已处理您的请求"
		}

		response := WebSocketMessage{
			Type: "agent_reply",
			Data: map[string]interface{}{
				"agent_id":   agentID,
				"content":    responseContent,
				"message_id": msg.ID,
			},
			Timestamp: time.Now(),
			ClientID:  c.id,
			AgentID:   agentID,
		}

		c.sendMessage(response)
	}()
}

// handleSubscribeAgent 处理订阅Agent
func (c *WebSocketClient) handleSubscribeAgent(wsMsg WebSocketMessage) {
	data, ok := wsMsg.Data.(map[string]interface{})
	if !ok {
		c.sendError("Invalid subscribe format")
		return
	}

	agentID, ok := data["agent_id"].(string)
	if !ok {
		c.sendError("Missing agent_id in subscribe")
		return
	}

	c.agentID = agentID
	c.manager.logger.Info("Client %s subscribed to agent %s", c.id, agentID)

	response := WebSocketMessage{
		Type: "subscribed",
		Data: map[string]string{
			"agent_id": agentID,
			"status":   "subscribed",
		},
		Timestamp: time.Now(),
		ClientID:  c.id,
	}

	c.sendMessage(response)
}

// handleUnsubscribeAgent 处理取消订阅Agent
func (c *WebSocketClient) handleUnsubscribeAgent(wsMsg WebSocketMessage) {
	c.agentID = ""
	c.manager.logger.Info("Client %s unsubscribed from agent", c.id)

	response := WebSocketMessage{
		Type: "unsubscribed",
		Data: map[string]string{
			"status": "unsubscribed",
		},
		Timestamp: time.Now(),
		ClientID:  c.id,
	}

	c.sendMessage(response)
}

// handlePing 处理ping消息
func (c *WebSocketClient) handlePing(wsMsg WebSocketMessage) {
	response := WebSocketMessage{
		Type: "pong",
		Data: map[string]string{
			"message": "pong",
		},
		Timestamp: time.Now(),
		ClientID:  c.id,
	}

	c.sendMessage(response)
}

// sendError 发送错误消息
func (c *WebSocketClient) sendError(errorMsg string) {
	response := WebSocketMessage{
		Type: "error",
		Data: map[string]string{
			"error": errorMsg,
		},
		Timestamp: time.Now(),
		ClientID:  c.id,
	}

	c.sendMessage(response)
}

// generateClientID 生成客户端ID
func generateClientID() string {
	return fmt.Sprintf("client_%d", time.Now().UnixNano())
}
