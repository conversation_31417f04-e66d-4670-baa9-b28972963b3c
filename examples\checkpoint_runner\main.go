package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/session"
)

// DemoAgent 演示智能体，模拟一个多步骤的任务处理
type DemoAgent struct {
	name string
}

func NewDemoAgent(name string) *DemoAgent {
	return &DemoAgent{name: name}
}

func (a *DemoAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *DemoAgent) Description(ctx context.Context) string {
	return fmt.Sprintf("演示智能体: %s", a.name)
}

func (a *DemoAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 模拟多步骤处理
		steps := []string{
			"开始处理用户请求",
			"分析输入内容",
			"生成响应策略",
			"执行处理逻辑",
			"准备最终结果",
		}

		for i, step := range steps {
			// 发送思考事件
			thoughtEvent := event.NewThoughtEvent(step, fmt.Sprintf("执行第%d步", i+1))
			if err := pair.Generator.Send(thoughtEvent); err != nil {
				return
			}

			// 发送Token事件
			tokenEvent := event.NewTokenEvent(fmt.Sprintf("步骤%d: %s\n", i+1, step), true)
			if err := pair.Generator.Send(tokenEvent); err != nil {
				return
			}

			// 模拟处理时间
			time.Sleep(100 * time.Millisecond)

			// 更新会话状态
			if input.Session != nil {
				input.Session.Set("current_step", i+1)
				input.Session.Set(fmt.Sprintf("step_%d_completed", i+1), true)
			}
		}

		// 发送最终事件
		finalEvent := event.NewFinalEvent("任务完成", "所有步骤已成功执行", map[string]any{
			"total_steps": len(steps),
			"duration":    "500ms",
		})
		pair.Generator.Send(finalEvent)
	}()

	return pair.Iterator
}

func main() {
	ctx := context.Background()

	// 创建Runner配置，启用检查点功能
	config := runtime.DefaultRunnerConfig()
	config.EnableCheckpoint = true
	config.CheckpointStore = runtime.NewMemoryCheckpointStore() // 使用内存存储演示
	config.CheckpointInterval = 2                              // 每2个事件保存一次检查点

	// 创建Runner
	runner := runtime.NewRunner(ctx, config)
	defer config.CheckpointStore.Close()

	fmt.Println("=== 检查点功能演示 ===")
	fmt.Printf("检查点间隔: 每%d个事件\n", config.CheckpointInterval)
	fmt.Println()

	// 创建智能体
	agent := NewDemoAgent("checkpoint-demo-agent")

	// 创建输入
	input := runtime.NewInput()
	input.AddMessage(message.NewUserMessage("请执行一个多步骤任务"))

	// 设置会话
	sessionMap := session.New()
	sessionMap.Set("user_id", "demo_user_123")
	sessionMap.Set("task_type", "multi_step_demo")
	input.SetSession(sessionMap)

	fmt.Println("开始执行智能体...")

	// 执行智能体并收集事件
	events, err := runner.RunAndCollect(ctx, agent, input)
	if err != nil {
		log.Fatalf("执行失败: %v", err)
	}

	fmt.Printf("执行完成，共收到 %d 个事件\n", len(events))
	fmt.Println()

	// 显示事件
	fmt.Println("=== 事件流 ===")
	for i, ev := range events {
		fmt.Printf("[%d] %s: ", i+1, ev.Type)
		switch ev.Type {
		case event.EventThought:
			if data, ok := ev.Data.(*event.ThoughtData); ok {
				fmt.Printf("%s\n", data.Content)
			}
		case event.EventToken:
			if data, ok := ev.Data.(*event.TokenData); ok {
				fmt.Printf("%s", data.Content)
			}
		case event.EventFinal:
			if data, ok := ev.Data.(*event.FinalData); ok {
				fmt.Printf("%s\n", data.Content)
			}
		case event.EventError:
			if data, ok := ev.Data.(*event.ErrorData); ok {
				fmt.Printf("警告: %s\n", data.Message)
			}
		default:
			fmt.Printf("%v\n", ev.Data)
		}
	}
	fmt.Println()

	// 获取会话ID并检查检查点
	sessionID := getSessionID(input)
	fmt.Printf("会话ID: %s\n", sessionID)

	// 验证检查点是否存在
	exists, err := config.CheckpointStore.Exists(ctx, sessionID)
	if err != nil {
		log.Fatalf("检查检查点失败: %v", err)
	}

	if exists {
		fmt.Println("✓ 检查点已保存")

		// 加载并显示检查点数据
		checkpointData, err := config.CheckpointStore.Load(ctx, sessionID)
		if err != nil {
			log.Fatalf("加载检查点失败: %v", err)
		}

		fmt.Println("\n=== 检查点数据 ===")
		fmt.Printf("智能体名称: %s\n", checkpointData.AgentName)
		fmt.Printf("创建时间: %s\n", checkpointData.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Printf("更新时间: %s\n", checkpointData.UpdatedAt.Format("2006-01-02 15:04:05"))

		fmt.Println("\n状态数据:")
		for key, value := range checkpointData.State {
			fmt.Printf("  %s: %v\n", key, value)
		}

		fmt.Println("\n元数据:")
		for key, value := range checkpointData.Metadata {
			fmt.Printf("  %s: %v\n", key, value)
		}
	} else {
		fmt.Println("✗ 检查点未找到")
	}

	fmt.Println("\n=== 演示完成 ===")
}

// getSessionID 获取会话ID（简化版本）
func getSessionID(input *runtime.Input) string {
	if input.Session == nil {
		return ""
	}

	if sessionID, exists := input.Session.Get("session_id"); exists {
		if id, ok := sessionID.(string); ok {
			return id
		}
	}

	return ""
}
