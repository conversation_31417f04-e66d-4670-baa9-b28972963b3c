package compose

import (
	"context"
	"fmt"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// SequentialConfig 顺序组合配置
type SequentialConfig struct {
	Name        string        // 组合名称
	Description string        // 组合描述
	Agents      []agent.Agent // 智能体列表
	FailureMode FailureMode   // 失败处理模式
}

// FailureMode 失败处理模式
type FailureMode string

const (
	// FailureModeStop 遇到错误立即停止
	FailureModeStop FailureMode = "stop"
	// FailureModeContinue 遇到错误继续执行下一个智能体
	FailureModeContinue FailureMode = "continue"
	// FailureModeRetry 遇到错误重试当前智能体
	FailureModeRetry FailureMode = "retry"
)

// SequentialAgent 顺序组合智能体
type SequentialAgent struct {
	*agent.BaseAgent
	agents      []agent.Agent
	failureMode FailureMode
	maxRetries  int
}

// NewSequentialAgent 创建顺序组合智能体
func NewSequentialAgent(config *SequentialConfig) (*SequentialAgent, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if len(config.Agents) == 0 {
		return nil, fmt.Errorf("智能体列表不能为空")
	}

	if config.Name == "" {
		config.Name = "SequentialAgent"
	}

	if config.Description == "" {
		config.Description = "顺序执行多个智能体的组合智能体"
	}

	if config.FailureMode == "" {
		config.FailureMode = FailureModeStop
	}

	return &SequentialAgent{
		BaseAgent:   agent.NewBaseAgent(config.Name, config.Description),
		agents:      config.Agents,
		failureMode: config.FailureMode,
		maxRetries:  3, // 默认最大重试3次
	}, nil
}

// Run 运行顺序组合智能体
func (s *SequentialAgent) Run(ctx context.Context, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()
		emit := func(ev *event.Event) {
			if err := pair.Generator.Send(ev); err != nil {
				// 如果发送失败，记录错误但不中断执行
				fmt.Printf("发送事件失败: %v\n", err)
			}
		}
		defer func() {
			if r := recover(); r != nil {
				emit(event.NewErrorEvent(fmt.Errorf("顺序组合执行发生panic: %v", r), "PANIC", nil))
			}
		}()

		if err := s.validateInput(in); err != nil {
			emit(event.NewErrorEvent(err, "VALIDATION_ERROR", nil))
			return
		}

		// 发送开始事件
		emit(event.NewTransferEvent(s.Name(ctx), "Sequential", "开始顺序执行"))

		currentInput := in
		var lastOutput any

		for i, ag := range s.agents {
			agentName := ag.Name(ctx)

			// 发送智能体开始事件
			emit(event.NewTransferEvent(s.Name(ctx), agentName, fmt.Sprintf("开始执行智能体 %d/%d", i+1, len(s.agents))))

			// 执行智能体
			if !s.executeAgent(ctx, ag, agentName, i, currentInput, emit, &lastOutput) {
				// 智能体执行失败且需要停止
				return
			}

			// 发送智能体完成事件
			emit(event.NewTransferEvent(agentName, s.Name(ctx), fmt.Sprintf("智能体 %d/%d 执行完成", i+1, len(s.agents))))

			// 为下一个智能体准备输入（可以基于前一个智能体的输出）
			// 这里简化处理，保持原始输入
			// 在实际应用中，可能需要根据前一个智能体的输出来修改输入
		}

		// 发送最终完成事件
		var finalContent string
		if lastOutput != nil {
			finalContent = fmt.Sprintf("%v", lastOutput)
		} else {
			finalContent = "顺序组合执行完成"
		}
		emit(event.NewFinalEvent(finalContent, "顺序组合执行完成", map[string]any{
			"total_agents": len(s.agents),
			"failure_mode": string(s.failureMode),
		}))
	}()

	return pair.Iterator
}

// validateInput 验证输入
func (s *SequentialAgent) validateInput(in *agent.Input) error {
	if in == nil {
		return fmt.Errorf("输入不能为空")
	}
	return nil
}

// wrapEvent 包装事件，添加来源信息
func (s *SequentialAgent) wrapEvent(originalEvent *event.Event, agentName string, agentIndex int) *event.Event {
	// 简化实现：直接转发原始事件，在元数据中添加来源信息
	wrappedEvent := &event.Event{
		Type: originalEvent.Type,
		Data: originalEvent.Data,
		Err:  originalEvent.Err,
		At:   originalEvent.At,
		Metadata: map[string]any{
			"source_agent":       agentName,
			"source_agent_index": agentIndex,
		},
	}

	// 如果原始事件有元数据，合并它们
	if originalEvent.Metadata != nil {
		for k, v := range originalEvent.Metadata {
			wrappedEvent.Metadata[k] = v
		}
	}

	return wrappedEvent
}

// SetMaxRetries 设置最大重试次数
func (s *SequentialAgent) SetMaxRetries(maxRetries int) {
	if maxRetries >= 0 {
		s.maxRetries = maxRetries
	}
}

// GetAgents 获取智能体列表
func (s *SequentialAgent) GetAgents() []agent.Agent {
	return s.agents
}

// GetFailureMode 获取失败处理模式
func (s *SequentialAgent) GetFailureMode() FailureMode {
	return s.failureMode
}

// executeAgent 执行单个智能体，返回是否应该继续执行后续智能体
func (s *SequentialAgent) executeAgent(ctx context.Context, ag agent.Agent, agentName string, agentIndex int, input *agent.Input, emit func(*event.Event), lastOutput *any) bool {
	retryCount := 0

	for retryCount <= s.maxRetries {
		// 运行智能体
		iterator := ag.Run(ctx, input)

		var finalEvent *event.Event
		var errorEvent *event.Event

		// 收集智能体的所有事件
		for {
			ev, ok := iterator.Next()
			if !ok {
				break
			}

			// 转发事件，添加来源信息
			forwardedEvent := s.wrapEvent(ev, agentName, agentIndex)
			emit(forwardedEvent)

			// 记录最终事件和错误事件
			if ev.Type == event.EventFinal {
				finalEvent = ev
			} else if ev.Type == event.EventError {
				errorEvent = ev
			}
		}

		// 检查执行结果
		if errorEvent != nil {
			// 处理失败
			switch s.failureMode {
			case FailureModeStop:
				emit(event.NewErrorEvent(fmt.Errorf("智能体 %s 执行失败: %v", agentName, errorEvent.Data), "AGENT_FAILED", map[string]any{
					"agent_name":  agentName,
					"agent_index": agentIndex,
					"error":       errorEvent.Data,
				}))
				return false // 停止执行
			case FailureModeContinue:
				emit(event.NewTransferEvent(s.Name(ctx), agentName, fmt.Sprintf("智能体 %s 执行失败，继续执行下一个智能体", agentName)))
				return true // 继续执行下一个智能体
			case FailureModeRetry:
				retryCount++
				// 发送重试事件
				if retryCount <= s.maxRetries {
					emit(event.NewTransferEvent(s.Name(ctx), agentName, fmt.Sprintf("智能体执行失败，进行第 %d 次重试", retryCount)))
					continue // 重试当前智能体
				} else {
					// 重试次数用完，失败
					emit(event.NewErrorEvent(fmt.Errorf("智能体 %s 重试 %d 次后仍然失败", agentName, s.maxRetries), "MAX_RETRIES_EXCEEDED", map[string]any{
						"agent_name":  agentName,
						"agent_index": agentIndex,
						"max_retries": s.maxRetries,
					}))
					return false // 停止执行
				}
			}
		} else if finalEvent != nil {
			*lastOutput = finalEvent.Data
			return true // 成功，继续执行下一个智能体
		} else {
			// 没有最终事件也没有错误事件，认为是异常情况
			emit(event.NewErrorEvent(fmt.Errorf("智能体 %s 没有产生最终事件或错误事件", agentName), "NO_FINAL_EVENT", map[string]any{
				"agent_name":  agentName,
				"agent_index": agentIndex,
			}))
			if s.failureMode == FailureModeStop {
				return false // 停止执行
			}
			return true // 在继续模式下继续执行
		}
	}

	// 不应该到达这里
	return false
}
