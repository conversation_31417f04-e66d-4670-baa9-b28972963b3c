package runtime

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	_ "github.com/lib/pq"           // PostgreSQL 驱动
	_ "github.com/mattn/go-sqlite3" // SQLite 驱动
)

// CheckpointData 表示检查点数据
type CheckpointData struct {
	// SessionID 会话ID
	SessionID string `json:"session_id"`
	// AgentName 智能体名称
	AgentName string `json:"agent_name"`
	// State 状态数据
	State map[string]any `json:"state"`
	// Metadata 元数据
	Metadata map[string]any `json:"metadata"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// CheckpointStore 表示检查点存储接口
type CheckpointStore interface {
	// Save 保存检查点
	Save(ctx context.Context, sessionID string, data *CheckpointData) error
	// Load 加载检查点
	Load(ctx context.Context, sessionID string) (*CheckpointData, error)
	// Delete 删除检查点
	Delete(ctx context.Context, sessionID string) error
	// List 列出所有检查点
	List(ctx context.Context) ([]string, error)
	// Exists 检查检查点是否存在
	Exists(ctx context.Context, sessionID string) (bool, error)
	// Close 关闭存储
	Close() error
}

// SQLiteCheckpointStore SQLite 检查点存储实现
type SQLiteCheckpointStore struct {
	db   *sql.DB
	mu   sync.RWMutex
	path string
}

// NewSQLiteCheckpointStore 创建新的 SQLite 检查点存储
func NewSQLiteCheckpointStore(dbPath string) (*SQLiteCheckpointStore, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %w", err)
	}

	store := &SQLiteCheckpointStore{
		db:   db,
		path: dbPath,
	}

	if err := store.init(); err != nil {
		db.Close()
		return nil, fmt.Errorf("初始化数据库失败: %w", err)
	}

	return store, nil
}

// init 初始化数据库表
func (s *SQLiteCheckpointStore) init() error {
	query := `
	CREATE TABLE IF NOT EXISTS checkpoints (
		session_id TEXT PRIMARY KEY,
		agent_name TEXT NOT NULL,
		state_data TEXT NOT NULL,
		metadata TEXT,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL
	);

	CREATE INDEX IF NOT EXISTS idx_checkpoints_agent_name ON checkpoints(agent_name);
	CREATE INDEX IF NOT EXISTS idx_checkpoints_created_at ON checkpoints(created_at);
	`

	_, err := s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建表失败: %w", err)
	}

	return nil
}

// Save 保存检查点
func (s *SQLiteCheckpointStore) Save(ctx context.Context, sessionID string, data *CheckpointData) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 序列化状态数据
	stateJSON, err := json.Marshal(data.State)
	if err != nil {
		return fmt.Errorf("序列化状态数据失败: %w", err)
	}

	// 序列化元数据
	metadataJSON, err := json.Marshal(data.Metadata)
	if err != nil {
		return fmt.Errorf("序列化元数据失败: %w", err)
	}

	now := time.Now()

	// 检查是否已存在
	exists, err := s.existsLocked(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("检查检查点是否存在失败: %w", err)
	}

	if exists {
		// 更新现有检查点
		query := `
		UPDATE checkpoints
		SET agent_name = ?, state_data = ?, metadata = ?, updated_at = ?
		WHERE session_id = ?
		`
		_, err = s.db.ExecContext(ctx, query, data.AgentName, string(stateJSON), string(metadataJSON), now, sessionID)
		if err != nil {
			return fmt.Errorf("更新检查点失败: %w", err)
		}
	} else {
		// 插入新检查点
		query := `
		INSERT INTO checkpoints (session_id, agent_name, state_data, metadata, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
		`
		_, err = s.db.ExecContext(ctx, query, sessionID, data.AgentName, string(stateJSON), string(metadataJSON), now, now)
		if err != nil {
			return fmt.Errorf("插入检查点失败: %w", err)
		}
	}

	return nil
}

// Load 加载检查点
func (s *SQLiteCheckpointStore) Load(ctx context.Context, sessionID string) (*CheckpointData, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	query := `
	SELECT agent_name, state_data, metadata, created_at, updated_at
	FROM checkpoints
	WHERE session_id = ?
	`

	row := s.db.QueryRowContext(ctx, query, sessionID)

	var agentName, stateData, metadataData string
	var createdAt, updatedAt time.Time

	err := row.Scan(&agentName, &stateData, &metadataData, &createdAt, &updatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("检查点不存在: %s", sessionID)
		}
		return nil, fmt.Errorf("查询检查点失败: %w", err)
	}

	// 反序列化状态数据
	var state map[string]any
	if err := json.Unmarshal([]byte(stateData), &state); err != nil {
		return nil, fmt.Errorf("反序列化状态数据失败: %w", err)
	}

	// 反序列化元数据
	var metadata map[string]any
	if metadataData != "" {
		if err := json.Unmarshal([]byte(metadataData), &metadata); err != nil {
			return nil, fmt.Errorf("反序列化元数据失败: %w", err)
		}
	}

	return &CheckpointData{
		SessionID: sessionID,
		AgentName: agentName,
		State:     state,
		Metadata:  metadata,
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}, nil
}

// Delete 删除检查点
func (s *SQLiteCheckpointStore) Delete(ctx context.Context, sessionID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	query := `DELETE FROM checkpoints WHERE session_id = ?`

	result, err := s.db.ExecContext(ctx, query, sessionID)
	if err != nil {
		return fmt.Errorf("删除检查点失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("检查点不存在: %s", sessionID)
	}

	return nil
}

// List 列出所有检查点
func (s *SQLiteCheckpointStore) List(ctx context.Context) ([]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	query := `SELECT session_id FROM checkpoints ORDER BY created_at DESC`

	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询检查点列表失败: %w", err)
	}
	defer rows.Close()

	var sessionIDs []string
	for rows.Next() {
		var sessionID string
		if err := rows.Scan(&sessionID); err != nil {
			return nil, fmt.Errorf("扫描检查点ID失败: %w", err)
		}
		sessionIDs = append(sessionIDs, sessionID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历检查点失败: %w", err)
	}

	return sessionIDs, nil
}

// Exists 检查检查点是否存在
func (s *SQLiteCheckpointStore) Exists(ctx context.Context, sessionID string) (bool, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.existsLocked(ctx, sessionID)
}

// existsLocked 检查检查点是否存在（需要持有锁）
func (s *SQLiteCheckpointStore) existsLocked(ctx context.Context, sessionID string) (bool, error) {
	query := `SELECT 1 FROM checkpoints WHERE session_id = ? LIMIT 1`

	row := s.db.QueryRowContext(ctx, query, sessionID)

	var exists int
	err := row.Scan(&exists)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, fmt.Errorf("检查检查点是否存在失败: %w", err)
	}

	return true, nil
}

// Close 关闭存储
func (s *SQLiteCheckpointStore) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.db != nil {
		err := s.db.Close()
		s.db = nil
		return err
	}

	return nil
}

// GetPath 获取数据库路径
func (s *SQLiteCheckpointStore) GetPath() string {
	return s.path
}

// Stats 获取存储统计信息
func (s *SQLiteCheckpointStore) Stats(ctx context.Context) (map[string]any, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	stats := make(map[string]any)

	// 获取检查点总数
	var count int
	err := s.db.QueryRowContext(ctx, "SELECT COUNT(*) FROM checkpoints").Scan(&count)
	if err != nil {
		return nil, fmt.Errorf("获取检查点总数失败: %w", err)
	}
	stats["total_checkpoints"] = count

	// 获取最新检查点时间
	var latestTime sql.NullTime
	err = s.db.QueryRowContext(ctx, "SELECT MAX(updated_at) FROM checkpoints").Scan(&latestTime)
	if err != nil {
		return nil, fmt.Errorf("获取最新检查点时间失败: %w", err)
	}
	if latestTime.Valid {
		stats["latest_checkpoint"] = latestTime.Time
	}

	// 获取数据库文件大小（近似）
	var pageCount, pageSize int
	err = s.db.QueryRowContext(ctx, "PRAGMA page_count").Scan(&pageCount)
	if err == nil {
		err = s.db.QueryRowContext(ctx, "PRAGMA page_size").Scan(&pageSize)
		if err == nil {
			stats["db_size_bytes"] = pageCount * pageSize
		}
	}

	stats["db_path"] = s.path

	return stats, nil
}

// MemoryCheckpointStore 内存检查点存储实现（用于测试）
type MemoryCheckpointStore struct {
	data map[string]*CheckpointData
	mu   sync.RWMutex
}

// NewMemoryCheckpointStore 创建新的内存检查点存储
func NewMemoryCheckpointStore() *MemoryCheckpointStore {
	return &MemoryCheckpointStore{
		data: make(map[string]*CheckpointData),
	}
}

// Save 保存检查点
func (m *MemoryCheckpointStore) Save(ctx context.Context, sessionID string, data *CheckpointData) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()

	// 复制数据以避免外部修改
	stateCopy := make(map[string]any)
	for k, v := range data.State {
		stateCopy[k] = v
	}

	metadataCopy := make(map[string]any)
	for k, v := range data.Metadata {
		metadataCopy[k] = v
	}

	checkpoint := &CheckpointData{
		SessionID: sessionID,
		AgentName: data.AgentName,
		State:     stateCopy,
		Metadata:  metadataCopy,
		UpdatedAt: now,
	}

	// 如果是新检查点，设置创建时间
	if existing, exists := m.data[sessionID]; exists {
		checkpoint.CreatedAt = existing.CreatedAt
	} else {
		checkpoint.CreatedAt = now
	}

	m.data[sessionID] = checkpoint
	return nil
}

// Load 加载检查点
func (m *MemoryCheckpointStore) Load(ctx context.Context, sessionID string) (*CheckpointData, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	data, exists := m.data[sessionID]
	if !exists {
		return nil, fmt.Errorf("检查点不存在: %s", sessionID)
	}

	// 返回副本以避免外部修改
	stateCopy := make(map[string]any)
	for k, v := range data.State {
		stateCopy[k] = v
	}

	metadataCopy := make(map[string]any)
	for k, v := range data.Metadata {
		metadataCopy[k] = v
	}

	return &CheckpointData{
		SessionID: data.SessionID,
		AgentName: data.AgentName,
		State:     stateCopy,
		Metadata:  metadataCopy,
		CreatedAt: data.CreatedAt,
		UpdatedAt: data.UpdatedAt,
	}, nil
}

// Delete 删除检查点
func (m *MemoryCheckpointStore) Delete(ctx context.Context, sessionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.data[sessionID]; !exists {
		return fmt.Errorf("检查点不存在: %s", sessionID)
	}

	delete(m.data, sessionID)
	return nil
}

// List 列出所有检查点
func (m *MemoryCheckpointStore) List(ctx context.Context) ([]string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	sessionIDs := make([]string, 0, len(m.data))
	for sessionID := range m.data {
		sessionIDs = append(sessionIDs, sessionID)
	}

	return sessionIDs, nil
}

// Exists 检查检查点是否存在
func (m *MemoryCheckpointStore) Exists(ctx context.Context, sessionID string) (bool, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	_, exists := m.data[sessionID]
	return exists, nil
}

// Close 关闭存储
func (m *MemoryCheckpointStore) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.data = make(map[string]*CheckpointData)
	return nil
}

// PostgresCheckpointStore PostgreSQL 检查点存储实现
type PostgresCheckpointStore struct {
	db *sql.DB
	mu sync.RWMutex
}

// PostgresCheckpointConfig PostgreSQL 检查点存储配置
type PostgresCheckpointConfig struct {
	Host     string // 主机地址
	Port     int    // 端口
	Database string // 数据库名
	Username string // 用户名
	Password string // 密码
	SSLMode  string // SSL模式
}

// NewPostgresCheckpointStore 创建新的 PostgreSQL 检查点存储
func NewPostgresCheckpointStore(config *PostgresCheckpointConfig) (*PostgresCheckpointStore, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	// 构建连接字符串
	connStr := fmt.Sprintf("host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		config.Host, config.Port, config.Database, config.Username, config.Password, config.SSLMode)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("打开PostgreSQL数据库失败: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(20)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("连接PostgreSQL数据库失败: %w", err)
	}

	store := &PostgresCheckpointStore{
		db: db,
	}

	if err := store.init(); err != nil {
		db.Close()
		return nil, fmt.Errorf("初始化数据库失败: %w", err)
	}

	return store, nil
}

// init 初始化数据库表
func (p *PostgresCheckpointStore) init() error {
	query := `
	CREATE TABLE IF NOT EXISTS checkpoints (
		session_id TEXT PRIMARY KEY,
		agent_name TEXT NOT NULL,
		state_data JSONB NOT NULL,
		metadata JSONB,
		created_at TIMESTAMP WITH TIME ZONE NOT NULL,
		updated_at TIMESTAMP WITH TIME ZONE NOT NULL
	);

	CREATE INDEX IF NOT EXISTS idx_checkpoints_agent_name ON checkpoints(agent_name);
	CREATE INDEX IF NOT EXISTS idx_checkpoints_created_at ON checkpoints(created_at);
	`

	_, err := p.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建表失败: %w", err)
	}

	return nil
}

// Save 保存检查点
func (p *PostgresCheckpointStore) Save(ctx context.Context, sessionID string, data *CheckpointData) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 序列化状态数据
	stateJSON, err := json.Marshal(data.State)
	if err != nil {
		return fmt.Errorf("序列化状态数据失败: %w", err)
	}

	// 序列化元数据
	metadataJSON, err := json.Marshal(data.Metadata)
	if err != nil {
		return fmt.Errorf("序列化元数据失败: %w", err)
	}

	now := time.Now()

	// 使用 UPSERT 语法（PostgreSQL 9.5+）
	query := `
	INSERT INTO checkpoints (session_id, agent_name, state_data, metadata, created_at, updated_at)
	VALUES ($1, $2, $3, $4, $5, $6)
	ON CONFLICT (session_id)
	DO UPDATE SET
		agent_name = EXCLUDED.agent_name,
		state_data = EXCLUDED.state_data,
		metadata = EXCLUDED.metadata,
		updated_at = EXCLUDED.updated_at
	`

	_, err = p.db.ExecContext(ctx, query, sessionID, data.AgentName, string(stateJSON), string(metadataJSON), now, now)
	if err != nil {
		return fmt.Errorf("保存检查点失败: %w", err)
	}

	return nil
}

// Load 加载检查点
func (p *PostgresCheckpointStore) Load(ctx context.Context, sessionID string) (*CheckpointData, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	query := `
	SELECT agent_name, state_data, metadata, created_at, updated_at
	FROM checkpoints
	WHERE session_id = $1
	`

	row := p.db.QueryRowContext(ctx, query, sessionID)

	var agentName, stateData, metadataData string
	var createdAt, updatedAt time.Time

	err := row.Scan(&agentName, &stateData, &metadataData, &createdAt, &updatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("检查点不存在: %s", sessionID)
		}
		return nil, fmt.Errorf("查询检查点失败: %w", err)
	}

	// 反序列化状态数据
	var state map[string]any
	if err := json.Unmarshal([]byte(stateData), &state); err != nil {
		return nil, fmt.Errorf("反序列化状态数据失败: %w", err)
	}

	// 反序列化元数据
	var metadata map[string]any
	if err := json.Unmarshal([]byte(metadataData), &metadata); err != nil {
		return nil, fmt.Errorf("反序列化元数据失败: %w", err)
	}

	return &CheckpointData{
		SessionID: sessionID,
		AgentName: agentName,
		State:     state,
		Metadata:  metadata,
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}, nil
}

// Delete 删除检查点
func (p *PostgresCheckpointStore) Delete(ctx context.Context, sessionID string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	query := `DELETE FROM checkpoints WHERE session_id = $1`

	result, err := p.db.ExecContext(ctx, query, sessionID)
	if err != nil {
		return fmt.Errorf("删除检查点失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("检查点不存在: %s", sessionID)
	}

	return nil
}

// List 列出所有检查点
func (p *PostgresCheckpointStore) List(ctx context.Context) ([]string, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	query := `SELECT session_id FROM checkpoints ORDER BY created_at DESC`

	rows, err := p.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询检查点列表失败: %w", err)
	}
	defer rows.Close()

	var sessionIDs []string
	for rows.Next() {
		var sessionID string
		if err := rows.Scan(&sessionID); err != nil {
			return nil, fmt.Errorf("扫描检查点ID失败: %w", err)
		}
		sessionIDs = append(sessionIDs, sessionID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历检查点失败: %w", err)
	}

	return sessionIDs, nil
}

// Exists 检查检查点是否存在
func (p *PostgresCheckpointStore) Exists(ctx context.Context, sessionID string) (bool, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	query := `SELECT 1 FROM checkpoints WHERE session_id = $1 LIMIT 1`

	row := p.db.QueryRowContext(ctx, query, sessionID)

	var exists int
	err := row.Scan(&exists)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, fmt.Errorf("检查检查点是否存在失败: %w", err)
	}

	return true, nil
}

// Close 关闭存储
func (p *PostgresCheckpointStore) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.db != nil {
		return p.db.Close()
	}
	return nil
}
