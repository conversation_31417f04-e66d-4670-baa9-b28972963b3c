//go:build disttests

package distributed

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInMemoryServiceDiscovery(t *testing.T) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	// Test RegisterService
	service := &ServiceInfo{
		ID:       "test-service-1",
		Name:     "test-service",
		Address:  "localhost:8080",
		Port:     8080,
		Status:   ServiceStatusHealthy,
		Metadata: map[string]interface{}{"version": "1.0.0"},
	}

	err := discovery.RegisterService(ctx, service)
	require.NoError(t, err)

	// Test GetService
	retrieved, err := discovery.GetService(ctx, "test-service-1")
	require.NoError(t, err)
	assert.Equal(t, service.ID, retrieved.ID)
	assert.Equal(t, service.Name, retrieved.Name)
	assert.Equal(t, service.Address, retrieved.Address)

	// Test ListServices
	services, err := discovery.ListServices(ctx)
	require.NoError(t, err)
	assert.Len(t, services, 1)
	assert.Equal(t, service.ID, services[0].ID)

	// Test DiscoverServices
	discovered, err := discovery.DiscoverServices(ctx, "test-service")
	require.NoError(t, err)
	assert.Len(t, discovered, 1)
	assert.Equal(t, service.ID, discovered[0].ID)

	// Test HealthCheck
	err = discovery.HealthCheck(ctx, "test-service-1")
	assert.NoError(t, err)

	// Test UnregisterService
	err = discovery.UnregisterService(ctx, "test-service-1")
	require.NoError(t, err)

	// Verify service is removed
	_, err = discovery.GetService(ctx, "test-service-1")
	assert.Error(t, err)
}

func TestServiceDiscoveryWithMultipleServices(t *testing.T) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	// Register multiple services
	services := []*ServiceInfo{
		{
			ID:      "service-1",
			Name:    "web-service",
			Address: "localhost:8080",
			Port:    8080,
			Status:  ServiceStatusHealthy,
		},
		{
			ID:      "service-2",
			Name:    "web-service",
			Address: "localhost:8081",
			Port:    8081,
			Status:  ServiceStatusHealthy,
		},
		{
			ID:      "service-3",
			Name:    "api-service",
			Address: "localhost:9000",
			Port:    9000,
			Status:  ServiceStatusUnhealthy,
		},
	}

	for _, service := range services {
		err := discovery.RegisterService(ctx, service)
		require.NoError(t, err)
	}

	// Test ListServices
	allServices, err := discovery.ListServices(ctx)
	require.NoError(t, err)
	assert.Len(t, allServices, 3)

	// Test DiscoverServices by name
	webServices, err := discovery.DiscoverServices(ctx, "web-service")
	require.NoError(t, err)
	assert.Len(t, webServices, 2)

	apiServices, err := discovery.DiscoverServices(ctx, "api-service")
	require.NoError(t, err)
	assert.Len(t, apiServices, 1)
	assert.Equal(t, ServiceStatusUnhealthy, apiServices[0].Status)

	// Test non-existent service
	nonExistent, err := discovery.DiscoverServices(ctx, "non-existent")
	require.NoError(t, err)
	assert.Len(t, nonExistent, 0)
}

func TestServiceDiscoveryHealthCheck(t *testing.T) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	// Test health check for non-existent service
	err := discovery.HealthCheck(ctx, "non-existent")
	assert.Error(t, err)

	// Register a service
	service := &ServiceInfo{
		ID:      "health-test",
		Name:    "health-service",
		Address: "localhost:8080",
		Port:    8080,
		Status:  ServiceStatusHealthy,
	}

	err = discovery.RegisterService(ctx, service)
	require.NoError(t, err)

	// Test health check for existing healthy service
	err = discovery.HealthCheck(ctx, "health-test")
	assert.NoError(t, err)

	// Update service to unhealthy
	service.Status = ServiceStatusUnhealthy
	err = discovery.RegisterService(ctx, service)
	require.NoError(t, err)

	// Test health check for unhealthy service
	err = discovery.HealthCheck(ctx, "health-test")
	assert.Error(t, err)
}

func TestServiceDiscoveryWatcher(t *testing.T) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	// Create a watcher
	watcher, err := discovery.Watch(ctx, "test-service")
	require.NoError(t, err)
	defer watcher.Stop()

	// Register a service in a goroutine
	go func() {
		time.Sleep(100 * time.Millisecond)
		service := &ServiceInfo{
			ID:      "watch-test",
			Name:    "test-service",
			Address: "localhost:8080",
			Port:    8080,
			Status:  ServiceStatusHealthy,
		}
		discovery.RegisterService(ctx, service)
	}()

	// Wait for the event
	select {
	case event := <-watcher.Events():
		assert.Equal(t, ServiceEventRegistered, event.Type)
		assert.Equal(t, "watch-test", event.Service.ID)
	case <-time.After(1 * time.Second):
		t.Fatal("Timeout waiting for service event")
	}
}

func TestServiceDiscoveryErrors(t *testing.T) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	// Test GetService with non-existent ID
	_, err := discovery.GetService(ctx, "non-existent")
	assert.Error(t, err)

	// Test UnregisterService with non-existent ID
	err = discovery.UnregisterService(ctx, "non-existent")
	assert.Error(t, err)

	// Test RegisterService with nil service
	err = discovery.RegisterService(ctx, nil)
	assert.Error(t, err)

	// Test RegisterService with empty ID
	service := &ServiceInfo{
		Name:    "test-service",
		Address: "localhost:8080",
		Port:    8080,
		Status:  ServiceStatusHealthy,
	}
	err = discovery.RegisterService(ctx, service)
	assert.Error(t, err)
}

func TestServiceWatcher(t *testing.T) {
	watcher := NewServiceWatcher("test-service", make(chan *ServiceEvent, 10))

	// Test initial state
	assert.Equal(t, "test-service", watcher.serviceName)
	assert.False(t, watcher.stopped)

	// Test Events method
	events := watcher.Events()
	assert.NotNil(t, events)

	// Test Stop method
	watcher.Stop()
	assert.True(t, watcher.stopped)

	// Test that channel is closed after stop
	select {
	case _, ok := <-events:
		assert.False(t, ok, "Channel should be closed after stop")
	case <-time.After(100 * time.Millisecond):
		t.Fatal("Channel should be closed immediately after stop")
	}
}

func TestServiceEvent(t *testing.T) {
	service := &ServiceInfo{
		ID:      "event-test",
		Name:    "test-service",
		Address: "localhost:8080",
		Port:    8080,
		Status:  ServiceStatusHealthy,
	}

	event := &ServiceEvent{
		Type:      ServiceEventRegistered,
		Service:   service,
		Timestamp: time.Now(),
	}

	assert.Equal(t, ServiceEventRegistered, event.Type)
	assert.Equal(t, service, event.Service)
	assert.False(t, event.Timestamp.IsZero())
}

func BenchmarkServiceDiscoveryRegister(b *testing.B) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service := &ServiceInfo{
			ID:      fmt.Sprintf("bench-service-%d", i),
			Name:    "bench-service",
			Address: "localhost:8080",
			Port:    8080,
			Status:  ServiceStatusHealthy,
		}
		discovery.RegisterService(ctx, service)
	}
}

func BenchmarkServiceDiscoveryDiscover(b *testing.B) {
	discovery := NewInMemoryServiceDiscovery()
	ctx := context.Background()

	// Pre-populate with services
	for i := 0; i < 100; i++ {
		service := &ServiceInfo{
			ID:      fmt.Sprintf("bench-service-%d", i),
			Name:    "bench-service",
			Address: "localhost:8080",
			Port:    8080,
			Status:  ServiceStatusHealthy,
		}
		discovery.RegisterService(ctx, service)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		discovery.DiscoverServices(ctx, "bench-service")
	}
}
