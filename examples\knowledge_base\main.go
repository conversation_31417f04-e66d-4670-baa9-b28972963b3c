package main

import (
	"context"
	"fmt"
	"log"

	"github.com/agentscope/agentscope-golang/pkg/knowledge"
)

func main() {
	fmt.Println("=== AgentScope-Golang Knowledge Base Example ===")

	ctx := context.Background()

	// Create a knowledge base
	kb := knowledge.NewSimpleKnowledgeBase()

	// Example 1: Basic document operations
	fmt.Println("\n1. Adding documents...")

	docs := []*knowledge.Document{
		{
			Title:   "Machine Learning Fundamentals",
			Content: "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.",
			Type:    knowledge.DocumentTypeText,
			Tags:    []string{"ml", "ai", "fundamentals"},
			Metadata: map[string]interface{}{
				"author":     "AI Researcher",
				"difficulty": "beginner",
			},
		},
		{
			Title:   "Deep Learning Networks",
			Content: "Deep learning uses neural networks with multiple layers to model and understand complex patterns in data.",
			Type:    knowledge.DocumentTypeText,
			Tags:    []string{"dl", "neural", "advanced"},
			Metadata: map[string]interface{}{
				"author":     "ML Expert",
				"difficulty": "advanced",
			},
		},
		{
			Title:   "Natural Language Processing",
			Content: "NLP is a branch of AI that helps computers understand, interpret and manipulate human language.",
			Type:    knowledge.DocumentTypeText,
			Tags:    []string{"nlp", "language", "text"},
			Metadata: map[string]interface{}{
				"author":     "NLP Specialist",
				"difficulty": "intermediate",
			},
		},
	}

	for _, doc := range docs {
		err := kb.AddDocument(ctx, doc)
		if err != nil {
			log.Fatal(err)
		}
		fmt.Printf("  Added: %s\n", doc.Title)
	}

	// Example 2: Search operations
	fmt.Println("\n2. Searching documents...")

	query := &knowledge.SearchQuery{
		Query: "machine learning",
		Limit: 10,
	}

	results, err := kb.SearchDocuments(ctx, query)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("  Found %d documents for 'machine learning':\n", len(results))
	for _, result := range results {
		fmt.Printf("    - %s (Score: %.2f)\n", result.Document.Title, result.Score)
	}

	// Example 3: Tag-based filtering
	fmt.Println("\n3. Tag-based search...")

	tagQuery := &knowledge.SearchQuery{
		Tags:  []string{"ai"},
		Limit: 10,
	}

	tagResults, err := kb.SearchDocuments(ctx, tagQuery)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("  Found %d documents with 'ai' tag:\n", len(tagResults))
	for _, result := range tagResults {
		fmt.Printf("    - %s\n", result.Document.Title)
	}

	// Example 4: Entity management
	fmt.Println("\n4. Managing entities...")

	entities := []*knowledge.Entity{
		{
			Name:        "Neural Networks",
			Type:        "concept",
			Description: "Computing systems inspired by biological neural networks",
			Properties: map[string]interface{}{
				"field":      "machine learning",
				"complexity": "high",
			},
		},
		{
			Name:        "Artificial Intelligence",
			Type:        "field",
			Description: "Intelligence demonstrated by machines",
			Properties: map[string]interface{}{
				"scope":        "broad",
				"applications": []string{"robotics", "nlp", "computer vision"},
			},
		},
	}

	for _, entity := range entities {
		err := kb.AddEntity(ctx, entity)
		if err != nil {
			log.Fatal(err)
		}
		fmt.Printf("  Added entity: %s (%s)\n", entity.Name, entity.Type)
	}

	// Example 5: Relations
	fmt.Println("\n5. Creating relations...")

	// Get entities to create relations
	entityQuery := &knowledge.EntityQuery{
		Type:  "concept",
		Limit: 10,
	}

	conceptEntities, err := kb.SearchEntities(ctx, entityQuery)
	if err != nil {
		log.Fatal(err)
	}

	fieldQuery := &knowledge.EntityQuery{
		Type:  "field",
		Limit: 10,
	}

	fieldEntities, err := kb.SearchEntities(ctx, fieldQuery)
	if err != nil {
		log.Fatal(err)
	}

	if len(conceptEntities) > 0 && len(fieldEntities) > 0 {
		relation := &knowledge.Relation{
			FromEntity:  conceptEntities[0].ID,
			ToEntity:    fieldEntities[0].ID,
			Type:        "is_part_of",
			Description: "Neural Networks is part of Artificial Intelligence",
			Confidence:  0.9,
		}

		err := kb.AddRelation(ctx, relation)
		if err != nil {
			log.Fatal(err)
		}
		fmt.Printf("  Created relation: %s -> %s (%s)\n",
			conceptEntities[0].Name, fieldEntities[0].Name, relation.Type)

		// Find related entities
		related, err := kb.GetRelatedEntities(ctx, conceptEntities[0].ID, nil)
		if err != nil {
			log.Fatal(err)
		}

		fmt.Printf("  Related entities to %s:\n", conceptEntities[0].Name)
		for _, rel := range related {
			fmt.Printf("    - %s\n", rel.Name)
		}
	}

	// Example 6: Statistics
	fmt.Println("\n6. Knowledge base statistics...")

	stats, err := kb.GetStats(ctx)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("  Documents: %d\n", stats.DocumentCount)
	fmt.Printf("  Entities: %d\n", stats.EntityCount)
	fmt.Printf("  Relations: %d\n", stats.RelationCount)

	fmt.Printf("  Document types:\n")
	for docType, count := range stats.TypeCounts {
		fmt.Printf("    - %s: %d\n", docType, count)
	}

	fmt.Printf("  Entity types:\n")
	for entityType, count := range stats.EntityTypes {
		fmt.Printf("    - %s: %d\n", entityType, count)
	}

	// Example 7: Agent integration
	fmt.Println("\n7. Agent knowledge integration...")

	akm := knowledge.NewAgentKnowledgeManager(kb, "demo-agent")

	// Add factual knowledge
	err = akm.AddFactualKnowledge(ctx,
		"Go Programming Language",
		"Go is a statically typed, compiled programming language designed at Google. It is syntactically similar to C, but with memory safety, garbage collection, structural typing, and CSP-style concurrency.",
		[]string{"programming", "go", "language", "google"})
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("  Added factual knowledge about Go programming")

	// Search for relevant knowledge
	relevantDocs, err := akm.SearchRelevantKnowledge(ctx, "programming language", 3)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("  Found %d relevant documents for 'programming language':\n", len(relevantDocs))
	for _, doc := range relevantDocs {
		fmt.Printf("    - %s\n", doc.Title)
	}

	// Build context from knowledge
	context, err := akm.BuildContextFromKnowledge(ctx, "artificial intelligence", 500)
	if err != nil {
		log.Fatal(err)
	}

	if context != "" {
		fmt.Printf("  Built knowledge context (truncated):\n%s\n",
			truncateString(context, 200))
	}

	fmt.Println("\n=== Knowledge Base Example Completed ===")
}

// Helper function to truncate strings for display
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
