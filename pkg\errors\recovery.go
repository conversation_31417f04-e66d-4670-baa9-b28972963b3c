package errors

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// RecoveryHandler 恢复处理器接口
type RecoveryHandler interface {
	// HandlePanic 处理 panic 恢复
	HandlePanic(ctx context.Context, recovered interface{}, stack []byte) error
	// HandleError 处理错误恢复
	HandleError(ctx context.Context, err error) error
}

// DefaultRecoveryHandler 默认恢复处理器
type DefaultRecoveryHandler struct {
	logger logger.Logger
}

// NewDefaultRecoveryHandler 创建默认恢复处理器
func NewDefaultRecoveryHandler(log logger.Logger) *DefaultRecoveryHandler {
	if log == nil {
		log = logger.GetGlobalLogger()
	}
	return &DefaultRecoveryHandler{
		logger: log,
	}
}

// HandlePanic 处理 panic 恢复
func (h *DefaultRecoveryHandler) HandlePanic(ctx context.Context, recovered interface{}, stack []byte) error {
	h.logger.WithField("component", "recovery").
		WithField("panic_value", recovered).
		WithField("stack_trace", string(stack)).
		Error("捕获到 panic，正在恢复")

	return NewInternalError(CodePanicRecovered, fmt.Sprintf("系统发生 panic: %v", recovered)).
		WithContext("panic_value", recovered).
		WithContext("stack_trace", string(stack))
}

// HandleError 处理错误恢复
func (h *DefaultRecoveryHandler) HandleError(ctx context.Context, err error) error {
	h.logger.WithField("component", "recovery").
		WithError(err).
		Warn("处理错误恢复")

	// 如果已经是 AgentScopeError，直接返回
	if IsAgentScopeError(err) {
		return err
	}

	// 包装为内部错误
	return Wrap(err, ErrorTypeInternal, CodeInternalError, "内部错误")
}

// SafeExecute 安全执行函数，捕获 panic 并转换为错误
func SafeExecute(ctx context.Context, handler RecoveryHandler, fn func() error) (err error) {
	if handler == nil {
		handler = NewDefaultRecoveryHandler(nil)
	}

	defer func() {
		if r := recover(); r != nil {
			stack := debug.Stack()
			err = handler.HandlePanic(ctx, r, stack)
		}
	}()

	err = fn()
	if err != nil {
		err = handler.HandleError(ctx, err)
	}

	return err
}

// SafeExecuteWithResult 安全执行带返回值的函数
func SafeExecuteWithResult[T any](ctx context.Context, handler RecoveryHandler, fn func() (T, error)) (result T, err error) {
	if handler == nil {
		handler = NewDefaultRecoveryHandler(nil)
	}

	defer func() {
		if r := recover(); r != nil {
			stack := debug.Stack()
			err = handler.HandlePanic(ctx, r, stack)
		}
	}()

	result, err = fn()
	if err != nil {
		err = handler.HandleError(ctx, err)
	}

	return result, err
}

// CircuitBreaker 断路器实现
type CircuitBreaker struct {
	mu                sync.RWMutex
	name              string
	maxFailures       int
	resetTimeout      time.Duration
	failureCount      int
	lastFailureTime   time.Time
	state             CircuitBreakerState
	logger            logger.Logger
	onStateChange     func(from, to CircuitBreakerState)
}

// CircuitBreakerState 断路器状态
type CircuitBreakerState int

const (
	// StateClosed 关闭状态（正常工作）
	StateClosed CircuitBreakerState = iota
	// StateOpen 开启状态（拒绝请求）
	StateOpen
	// StateHalfOpen 半开状态（试探性允许请求）
	StateHalfOpen
)

// String 返回状态的字符串表示
func (s CircuitBreakerState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreakerConfig 断路器配置
type CircuitBreakerConfig struct {
	Name         string        // 断路器名称
	MaxFailures  int           // 最大失败次数
	ResetTimeout time.Duration // 重置超时时间
	OnStateChange func(from, to CircuitBreakerState) // 状态变化回调
}

// NewCircuitBreaker 创建新的断路器
func NewCircuitBreaker(config CircuitBreakerConfig) *CircuitBreaker {
	if config.MaxFailures <= 0 {
		config.MaxFailures = 5
	}
	if config.ResetTimeout <= 0 {
		config.ResetTimeout = 60 * time.Second
	}

	return &CircuitBreaker{
		name:          config.Name,
		maxFailures:   config.MaxFailures,
		resetTimeout:  config.ResetTimeout,
		state:         StateClosed,
		logger:        logger.GetGlobalLogger(),
		onStateChange: config.OnStateChange,
	}
}

// Execute 执行操作，如果断路器开启则直接返回错误
func (cb *CircuitBreaker) Execute(ctx context.Context, operation func() error) error {
	// 检查是否允许执行
	if !cb.allowRequest() {
		return NewInternalError(CodeCircuitBreakerOpen, 
			fmt.Sprintf("断路器 %s 处于开启状态，拒绝请求", cb.name)).
			WithContext("circuit_breaker", cb.name).
			WithContext("state", cb.state.String())
	}

	// 执行操作
	err := operation()

	// 记录结果
	cb.recordResult(err)

	return err
}

// allowRequest 检查是否允许请求
func (cb *CircuitBreaker) allowRequest() bool {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		// 检查是否可以转换到半开状态
		if time.Since(cb.lastFailureTime) > cb.resetTimeout {
			cb.setState(StateHalfOpen)
			return true
		}
		return false
	case StateHalfOpen:
		return true
	default:
		return false
	}
}

// recordResult 记录操作结果
func (cb *CircuitBreaker) recordResult(err error) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	if err != nil {
		cb.failureCount++
		cb.lastFailureTime = time.Now()

		switch cb.state {
		case StateClosed:
			if cb.failureCount >= cb.maxFailures {
				cb.setState(StateOpen)
			}
		case StateHalfOpen:
			cb.setState(StateOpen)
		}
	} else {
		// 成功执行
		switch cb.state {
		case StateHalfOpen:
			cb.setState(StateClosed)
			cb.failureCount = 0
		case StateClosed:
			cb.failureCount = 0
		}
	}
}

// setState 设置状态并触发回调
func (cb *CircuitBreaker) setState(newState CircuitBreakerState) {
	oldState := cb.state
	cb.state = newState

	cb.logger.WithField("component", "circuit_breaker").
		WithField("name", cb.name).
		WithField("from_state", oldState.String()).
		WithField("to_state", newState.String()).
		Info("断路器状态变化")

	if cb.onStateChange != nil {
		cb.onStateChange(oldState, newState)
	}
}

// GetState 获取当前状态
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetFailureCount 获取失败次数
func (cb *CircuitBreaker) GetFailureCount() int {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.failureCount
}

// Reset 重置断路器
func (cb *CircuitBreaker) Reset() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.setState(StateClosed)
	cb.failureCount = 0
	cb.lastFailureTime = time.Time{}
}

// 新增错误代码
const (
	// CodePanicRecovered panic 恢复错误代码
	CodePanicRecovered = "PANIC_RECOVERED"
	// CodeCircuitBreakerOpen 断路器开启错误代码
	CodeCircuitBreakerOpen = "CIRCUIT_BREAKER_OPEN"
)
