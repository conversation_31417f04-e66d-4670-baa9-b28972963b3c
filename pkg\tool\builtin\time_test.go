package builtin

import (
"context"
"testing"
"time"

"github.com/stretchr/testify/assert"
"github.com/stretchr/testify/require"
)

func TestTimeTool_Name(t *testing.T) {
timeTool := NewTimeTool()
assert.Equal(t, "time", timeTool.Name())
}

func TestTimeTool_Description(t *testing.T) {
timeTool := NewTimeTool()
desc := timeTool.Description()
assert.NotEmpty(t, desc)
assert.Contains(t, desc, "time")
}

func TestTimeTool_Schema(t *testing.T) {
timeTool := NewTimeTool()
schema := timeTool.Schema()

require.NotNil(t, schema)
assert.Equal(t, "object", schema.Type)

// 检查属性
require.NotNil(t, schema.Properties)

// 检查 format 属性
formatProp, exists := schema.Properties["format"]
require.True(t, exists)
assert.Equal(t, "string", formatProp.Type)
assert.NotEmpty(t, formatProp.Description)

// 检查枚举值
require.NotNil(t, formatProp.Enum)
expectedFormats := []any{"timestamp", "iso", "rfc3339", "unix"}
assert.ElementsMatch(t, expectedFormats, formatProp.Enum)

// 检查 timezone 属性
timezoneProp, exists := schema.Properties["timezone"]
require.True(t, exists)
assert.Equal(t, "string", timezoneProp.Type)
}

func TestTimeTool_Execute_DefaultFormat(t *testing.T) {
timeTool := NewTimeTool()
ctx := context.Background()

// 不传任何参数，应该使用默认格式
params := map[string]any{}

result, err := timeTool.Execute(ctx, params)
require.NoError(t, err)

resultMap, ok := result.(map[string]any)
require.True(t, ok, "结果应该是 map[string]any")

// 检查必需字段
assert.Contains(t, resultMap, "format")
assert.Contains(t, resultMap, "timezone")
assert.Contains(t, resultMap, "time")
assert.Contains(t, resultMap, "unix")
assert.Contains(t, resultMap, "iso")

// 检查默认值
assert.Equal(t, "iso", resultMap["format"])
assert.Equal(t, "UTC", resultMap["timezone"])

// 验证时间格式
timeStr, ok := resultMap["time"].(string)
require.True(t, ok)

// 应该能解析为有效的时间
parsedTime, err := time.Parse(time.RFC3339, timeStr)
require.NoError(t, err)

// 时间应该在合理范围内（当前时间前后1分钟）
now := time.Now()
assert.WithinDuration(t, now, parsedTime, time.Minute)
}

func TestTimeTool_Execute_NilContext(t *testing.T) {
timeTool := NewTimeTool()

params := map[string]any{
"format": "iso",
}

// 即使 context 为 nil，也应该能正常工作
result, err := timeTool.Execute(context.TODO(), params)
require.NoError(t, err)

resultMap, ok := result.(map[string]any)
require.True(t, ok)

assert.Contains(t, resultMap, "time")
assert.Contains(t, resultMap, "unix")
assert.Contains(t, resultMap, "iso")
}
