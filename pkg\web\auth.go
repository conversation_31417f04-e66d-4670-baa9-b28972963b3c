package web

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/golang-jwt/jwt/v5"
)

// AuthManager 身份验证管理器
type AuthManager struct {
	config    *config.AuthConfig
	secretKey []byte
	users     map[string]*User
	sessions  map[string]*Session
	logger    logger.Logger
}

// User 用户信息
type User struct {
	ID       string            `json:"id"`
	Username string            `json:"username"`
	Email    string            `json:"email"`
	Password string            `json:"-"` // 不在JSON中显示
	Roles    []string          `json:"roles"`
	Metadata map[string]string `json:"metadata,omitempty"`
	Created  time.Time         `json:"created"`
	Updated  time.Time         `json:"updated"`
	Active   bool              `json:"active"`
}

// Session 会话信息
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	Created   time.Time `json:"created"`
	LastUsed  time.Time `json:"last_used"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
}

// Claims JWT声明
type Claims struct {
	UserID   string   `json:"user_id"`
	Username string   `json:"username"`
	Roles    []string `json:"roles"`
	jwt.RegisteredClaims
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	User      *User     `json:"user"`
}

// NewAuthManager 创建身份验证管理器
func NewAuthManager(config *config.AuthConfig) *AuthManager {
	secretKey := []byte(config.SecretKey)
	if len(secretKey) == 0 {
		// 生成随机密钥
		secretKey = make([]byte, 32)
		rand.Read(secretKey)
	}

	return &AuthManager{
		config:    config,
		secretKey: secretKey,
		users:     make(map[string]*User),
		sessions:  make(map[string]*Session),
		logger:    logger.GetGlobalLogger(),
	}
}

// Initialize 初始化身份验证管理器
func (am *AuthManager) Initialize() error {
	// 创建默认管理员用户
	adminUser := &User{
		ID:       "admin",
		Username: "admin",
		Email:    "<EMAIL>",
		Password: hashPassword("admin123"), // 在生产环境中应该使用更安全的密码
		Roles:    []string{"admin"},
		Created:  time.Now(),
		Updated:  time.Now(),
		Active:   true,
	}

	am.users[adminUser.Username] = adminUser
	am.logger.Info("Authentication manager initialized with default admin user")
	return nil
}

// Login 用户登录
func (am *AuthManager) Login(req *LoginRequest, ipAddress, userAgent string) (*LoginResponse, error) {
	user, exists := am.users[req.Username]
	if !exists || !user.Active {
		return nil, errors.NewAuthError("invalid_credentials", "Invalid username or password")
	}

	if !verifyPassword(req.Password, user.Password) {
		return nil, errors.NewAuthError("invalid_credentials", "Invalid username or password")
	}

	// 生成JWT令牌
	token, expiresAt, err := am.generateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 创建会话
	session := &Session{
		ID:        generateSessionID(),
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: expiresAt,
		Created:   time.Now(),
		LastUsed:  time.Now(),
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	am.sessions[session.ID] = session

	// 更新用户最后使用时间
	user.Updated = time.Now()

	am.logger.Info("User %s logged in successfully", user.Username)

	return &LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      am.sanitizeUser(user),
	}, nil
}

// Logout 用户登出
func (am *AuthManager) Logout(token string) error {
	claims, err := am.validateToken(token)
	if err != nil {
		return err
	}

	// 删除会话
	for sessionID, session := range am.sessions {
		if session.Token == token {
			delete(am.sessions, sessionID)
			am.logger.Info("User %s logged out", claims.Username)
			break
		}
	}

	return nil
}

// ValidateToken 验证令牌
func (am *AuthManager) ValidateToken(token string) (*Claims, error) {
	return am.validateToken(token)
}

// generateToken 生成JWT令牌
func (am *AuthManager) generateToken(user *User) (string, time.Time, error) {
	expiresAt := time.Now().Add(am.config.Expiration)

	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Roles:    user.Roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "agentscope-golang",
			Subject:   user.ID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(am.secretKey)
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiresAt, nil
}

// validateToken 验证JWT令牌
func (am *AuthManager) validateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return am.secretKey, nil
	})

	if err != nil {
		return nil, errors.NewAuthError("invalid_token", "Invalid token")
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, errors.NewAuthError("invalid_token", "Invalid token")
	}

	// 检查用户是否仍然存在且活跃
	user, exists := am.users[claims.Username]
	if !exists || !user.Active {
		return nil, errors.NewAuthError("user_inactive", "User is inactive")
	}

	return claims, nil
}

// AuthMiddleware 身份验证中间件
func (am *AuthManager) AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 跳过某些路径的身份验证
		if am.shouldSkipAuth(r.URL.Path) {
			next.ServeHTTP(w, r)
			return
		}

		// 从请求头中获取令牌
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			am.writeAuthError(w, "Missing authorization header")
			return
		}

		// 解析Bearer令牌
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			am.writeAuthError(w, "Invalid authorization header format")
			return
		}

		token := parts[1]
		claims, err := am.validateToken(token)
		if err != nil {
			am.writeAuthError(w, "Invalid token")
			return
		}

		// 更新会话最后使用时间
		am.updateSessionLastUsed(token)

		// 将用户信息添加到请求上下文
		ctx := r.Context()
		ctx = setUserInContext(ctx, claims)
		r = r.WithContext(ctx)

		next.ServeHTTP(w, r)
	})
}

// RoleMiddleware 角色验证中间件
func (am *AuthManager) RoleMiddleware(requiredRoles ...string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			claims := getUserFromContext(r.Context())
			if claims == nil {
				am.writeAuthError(w, "Authentication required")
				return
			}

			// 检查用户是否具有所需角色
			if !am.hasAnyRole(claims.Roles, requiredRoles) {
				am.writeAuthError(w, "Insufficient permissions")
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// CreateUser 创建用户
func (am *AuthManager) CreateUser(user *User) error {
	if _, exists := am.users[user.Username]; exists {
		return errors.NewValidationError("user_exists", "User already exists")
	}

	user.ID = generateUserID()
	user.Password = hashPassword(user.Password)
	user.Created = time.Now()
	user.Updated = time.Now()
	user.Active = true

	am.users[user.Username] = user
	am.logger.Info("User %s created", user.Username)
	return nil
}

// UpdateUser 更新用户
func (am *AuthManager) UpdateUser(username string, updates *User) error {
	user, exists := am.users[username]
	if !exists {
		return errors.NewNotFoundError("user_not_found", "User not found")
	}

	if updates.Email != "" {
		user.Email = updates.Email
	}
	if updates.Roles != nil {
		user.Roles = updates.Roles
	}
	if updates.Metadata != nil {
		user.Metadata = updates.Metadata
	}

	user.Updated = time.Now()
	am.logger.Info("User %s updated", username)
	return nil
}

// DeleteUser 删除用户
func (am *AuthManager) DeleteUser(username string) error {
	if _, exists := am.users[username]; !exists {
		return errors.NewNotFoundError("user_not_found", "User not found")
	}

	delete(am.users, username)
	am.logger.Info("User %s deleted", username)
	return nil
}

// GetUser 获取用户
func (am *AuthManager) GetUser(username string) (*User, error) {
	user, exists := am.users[username]
	if !exists {
		return nil, errors.NewNotFoundError("user_not_found", "User not found")
	}

	return am.sanitizeUser(user), nil
}

// ListUsers 列出用户
func (am *AuthManager) ListUsers() []*User {
	users := make([]*User, 0, len(am.users))
	for _, user := range am.users {
		users = append(users, am.sanitizeUser(user))
	}
	return users
}

// 辅助函数

func (am *AuthManager) shouldSkipAuth(path string) bool {
	skipPaths := []string{
		"/api/v1/auth/login",
		"/api/v1/health",
		"/",
	}

	for _, skipPath := range skipPaths {
		if path == skipPath || strings.HasPrefix(path, "/static/") {
			return true
		}
	}

	return false
}

func (am *AuthManager) writeAuthError(w http.ResponseWriter, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusUnauthorized)

	errorResp := ErrorResponse{
		Error:  message,
		Status: http.StatusUnauthorized,
		Time:   time.Now(),
	}

	json.NewEncoder(w).Encode(errorResp)
}

func (am *AuthManager) hasAnyRole(userRoles, requiredRoles []string) bool {
	for _, userRole := range userRoles {
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole || userRole == "admin" {
				return true
			}
		}
	}
	return false
}

func (am *AuthManager) updateSessionLastUsed(token string) {
	for _, session := range am.sessions {
		if session.Token == token {
			session.LastUsed = time.Now()
			break
		}
	}
}

func (am *AuthManager) sanitizeUser(user *User) *User {
	sanitized := *user
	sanitized.Password = "" // 不返回密码
	return &sanitized
}

// 密码哈希函数（简化实现，生产环境应使用bcrypt）
func hashPassword(password string) string {
	return fmt.Sprintf("hashed_%s", password)
}

func verifyPassword(password, hash string) bool {
	return hashPassword(password) == hash
}

// ID生成函数
func generateUserID() string {
	return fmt.Sprintf("user_%d", time.Now().UnixNano())
}

func generateSessionID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// 上下文相关函数
type contextKey string

const userContextKey contextKey = "user"

func setUserInContext(ctx context.Context, claims *Claims) context.Context {
	return context.WithValue(ctx, userContextKey, claims)
}

func getUserFromContext(ctx context.Context) *Claims {
	if claims, ok := ctx.Value(userContextKey).(*Claims); ok {
		return claims
	}
	return nil
}
