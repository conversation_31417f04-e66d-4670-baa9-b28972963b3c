# hzAgent 新一代事件驱动 AI 智能体框架详细设计

> 设计目标：在完全破坏性重构的前提下，基于 Eino ADK 的优秀理念（事件流、Agent 组合、Runner 治理），结合 Go 语言特性，打造一套可扩展、模块化、易用、生产级的 AI 智能体框架。框架需完整支持对话、推理、记忆、工具调用、MCP Server 调用，严格遵循“文档即契约、真实 API、完整错误处理、并发安全”规范。

- 文档角色：本设计为后续实现的唯一标准。若后续发现重大缺陷，必须先修改本设计，经评审确认后再开发。
- 术语：
  - 事件（Event）：运行期一切过程的统一表达（Token/Thought/ToolCall/Result/Final/Error 等）
  - 事件流（Event Stream）：基于通道的异步事件迭代器
  - 组合（Compose）：多 Agent 协作的结构化编排（顺序/并行/循环/Transfer/AgentAsTool）
  - Runner：治理中枢（拦截器、流控、超时/重试、Checkpoint、Session）

---

## 1. 架构原则与总体设计

- 事件为一等公民：所有执行过程都产出标准化事件；天然支持流式输出、监控与追踪。
- 组合优先：用统一 Agent 抽象 + 组合原语表达协作；拓扑清晰、可预测、可测试。
- 治理集中：Runner 统一负责治理能力（拦截器、超时/重试、Checkpoint、会话）。
- 解耦适配：模型/工具/记忆/MCP 通过抽象接口解耦；实现可插拔。
- 并发安全：事件通道关闭、会话数据、并行编排严格同步；资源及时释放。
- 真实 API：所有外部调用需真实认证（环境变量），超时与重试完备，错误可观测。

---

## 2. 核心接口定义（稳定契约）

> 以下接口为代码实现的强约束；所有公开注释须使用中文。

### 2.1 Agent 抽象（事件流）
```go
package agent

type Agent interface {
    // Name/Description 用于标识与观测
    Name(ctx context.Context) string
    Description(ctx context.Context) string
    // Run 启动异步任务并返回事件迭代器；调用方需要消费完毕
    Run(ctx context.Context, in *Input) *runtime.AsyncIterator[*event.Event]
}

// Input 表示智能体的输入（统一定义在 runtime 包中）
type Input struct {
    Messages []*message.Message        // 输入消息
    Tools    []tool.Tool               // 可用工具（含 MCP 注入）
    Memory   memory.Store              // 记忆存储引用
    Session  session.Map               // 并发安全会话（跨 Agent 可见）
    Options  map[string]any            // 执行可选项（如温度、top_k、tool_choice）
}

// agent.Input 是 runtime.Input 的类型别名，确保接口一致性
type Input = runtime.Input
```

### 2.2 事件模型
```go
package event

type Type string
const (
    EventToken       Type = "token"          // 增量 Token
    EventThought     Type = "thought"        // 推理思考（可脱敏）
    EventToolCall    Type = "tool_call"      // 工具调用请求
    EventToolResult  Type = "tool_result"    // 工具调用结果
    EventTransfer    Type = "transfer"       // 控制权转移
    EventCheckpoint  Type = "checkpoint"     // Checkpoint 保存/恢复
    EventFinal       Type = "final"          // 最终结果
    EventError       Type = "error"          // 错误事件
)

type Event struct {
    Type Type
    Data any
    Err  error
    At   time.Time
    // TraceID/SpanID 等可通过拦截器注入到 Data 中或使用 context 携带
}
```

### 2.3 Runtime/Async 与 Runner
```go
package runtime

type AsyncIterator[T any] struct { ch <-chan T }
func (it *AsyncIterator[T]) Next() (T, bool) { v, ok := <-it.ch; return v, ok }

// 拦截器：统一前置/后置/异常处理（日志、指标、Tracing、风控等）
// 支持两种模式：接口式（推荐）和函数式（兼容）
type Interceptor interface {
    Name() string
    BeforeRun(ctx context.Context, agentName string, input any) error
    AfterRun(ctx context.Context, agentName string, input any, err error) error
    OnEvent(ctx context.Context, agentName string, ev *event.Event) error
    OnError(ctx context.Context, agentName string, err error) error
}

// 函数式拦截器（用于特殊场景，如审计）
type FunctionalInterceptor func(ctx context.Context, in *Input, next func(context.Context, *Input) *AsyncIterator[*event.Event]) *AsyncIterator[*event.Event]

type RunnerConfig struct {
    EnableStreaming    bool
    BufferSize         int
    Timeout            time.Duration
    MaxRetries         int
    RetryDelay         time.Duration
    CheckpointStore    CheckpointStore  // 检查点存储（可为 nil）
    EnableCheckpoint   bool             // 是否启用检查点功能
    CheckpointInterval int              // 检查点保存间隔（事件数）
}

type Runner struct { cfg RunnerConfig }

func NewRunner(cfg RunnerConfig) *Runner
func (r *Runner) Run(ctx context.Context, a agent.Agent, in *agent.Input) *AsyncIterator[*event.Event]
```

```go
// CheckpointStore：用于中断/恢复的持久化接口
type CheckpointStore interface {
    Save(ctx context.Context, sessionID string, data *CheckpointData) error
    Load(ctx context.Context, sessionID string) (*CheckpointData, error)
    Delete(ctx context.Context, sessionID string) error
    List(ctx context.Context) ([]string, error)
    Exists(ctx context.Context, sessionID string) (bool, error)
    Close() error
}

// CheckpointData 检查点数据结构
type CheckpointData struct {
    SessionID string
    AgentName string
    State     map[string]any
    Metadata  map[string]any
    CreatedAt time.Time
    UpdatedAt time.Time
}
```


### 2.4 Session（并发安全会话）
```go
package session

type Map interface {
    Set(key string, val any)
    Get(key string) (any, bool)
    Delete(key string)
    Keys() []string
}

func New() Map // 默认并发安全实现
```

### 2.5 Tool 工具与注册表
```go
package tool

type Tool interface {
    Name() string
    Description() string
    Schema() *jsonschema.Schema
    Execute(ctx context.Context, params map[string]any) (any, error) // 真实 API
}

type Registry interface {
    Register(tool Tool) error
    Unregister(name string) error
    Get(name string) (Tool, bool)
    List() []Tool
    Clear()
}

// 全局注册表函数
func Register(tool Tool) error
func Unregister(name string) error
func Get(name string) (Tool, bool)
func List() []Tool
func Clear()
```

### 2.6 LLM 抽象
```go
package llm

type ChatRequest struct {
    Messages []*message.Message
    Tools    []ToolDefinition // 供模型函数调用声明
    Params   map[string]any   // 模型参数（温度、top_p、max_tokens 等）
}

type ChatDelta struct { Content string; ToolCall *ToolCall } // 流式增量

type ChatResponse struct {
    Content   string
    ToolCalls []*ToolCall
    Usage     map[string]int
}

type ChatModel interface {
    Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
    ChatStream(ctx context.Context, req *ChatRequest) (<-chan *ChatDelta, error)
}
```

```go
// ToolDefinition/ToolCall：用于模型函数调用与参数传递
type ToolDefinition struct {
    Name        string
    Description string
    Parameters  map[string]any // JSONSchema 简化表示；实现中使用 jsonschema.Schema
}

type ToolCall struct {
    Name string
    Args map[string]any
}
```


### 2.7 Memory 记忆
```go
package memory

type Entry struct {
    ID        string
    Type      string
    Content   any
    Score     float64
    Timestamp time.Time
    Metadata  map[string]any
}

type Store interface {
    Put(ctx context.Context, e *Entry) error
    Get(ctx context.Context, id string) (*Entry, error)
    Query(ctx context.Context, q string, k int) ([]*Entry, error) // 可用于检索增强
}
```

### 2.8 MCP 集成
```go
package mcp

type Provider interface {
    // 将远端 MCP Server 暴露的工具同步到 Registry
    Sync(ctx context.Context, r tool.Registry) error
}
```

### 2.9 组合原语（Compose）
```go
package compose

func NewSequential(ctx context.Context, subs []agent.Agent, name, desc string) (agent.Agent, error)
func NewParallel(ctx context.Context, subs []agent.Agent, name, desc string) (agent.Agent, error)
func NewLoop(ctx context.Context, subs []agent.Agent, maxIter int, name, desc string) (agent.Agent, error)
```

---

## 3. 目录结构规划

```
hzAgent/
├── cmd/
│   └── main.go
├── pkg/
│   ├── agent/                 # Agent 抽象与内置 Agent
│   │   ├── agent.go
│   │   ├── types.go
│   │   └── agents/
│   │       ├── chatmodel/
│   │       │   ├── react.go
│   │       │   └── config.go
│   │       ├── supervisor/
│   │       └── planexec/
│   ├── compose/               # 组合原语（顺序/并行/循环/Transfer）
│   │   └── workflow.go
│   ├── runtime/               # Runner/Async/拦截器/Checkpoint
│   │   ├── runner.go
│   │   ├── async.go
│   │   └── interceptors.go
│   ├── event/
│   │   └── event.go
│   ├── session/
│   │   └── session.go
│   ├── tool/
│   │   ├── tool.go
│   │   ├── registry.go
│   │   └── builtin/
│   ├── tooling/
│   │   └── agent_as_tool.go
│   ├── llm/
│   │   ├── model.go
│   │   ├── router.go
│   │   └── providers/
│   │       ├── deepseek/
│   │       ├── qwen/
│   │       └── doubao/
│   ├── memory/
│   │   ├── store.go
│   │   ├── conversation.go
│   │   ├── vector.go
│   │   ├── persistence_sqlite.go
│   │   └── persistence_postgres.go
│   ├── mcp/
│   │   ├── client.go
│   │   └── integration.go
│   ├── monitoring/
│   │   ├── metrics.go
│   │   └── tracing.go
│   └── errors/
│       └── types.go
├── examples/
│   ├── simple_chat_adk/
│   └── multi_agent_workflow/
└── docs/
    ├── architecture-design.md
    └── migration-guide.md
```

---

## 4. 关键组件实现思路

### 4.1 事件与 AsyncIterator
- 采用有界缓冲 chan，生成端（Agent 内部 goroutine）写事件；严格 close，panic recover 转换为 EventError。
- 背压与取消：尊重 ctx.Done；Runner 可配置缓冲大小与消费策略。
- 事件字段包含时间戳；Trace 使用拦截器注入。

### 4.2 Runner（治理中枢）
- Interceptor 链：
  - 支持接口式拦截器（BeforeRun/AfterRun/OnEvent/OnError）
  - 支持函数式拦截器（用于审计等特殊场景）
  - 前置（入参校验、会话预处理）
  - 后置（统计 Usage、打点）
  - 异常（统一错误分类、结构化日志）
- 超时与重试：
  - 使用 context.WithTimeout 控制整体/阶段超时
  - 对 LLM/Tool 调用采用指数退避重试（幂等性校验）
- Checkpoint：
  - 支持 SQLite、PostgreSQL、内存三种存储后端
  - 在事件流处理过程中按间隔自动保存检查点
  - 支持会话恢复：从检查点恢复 Session 状态
  - CheckpointData 包含会话ID、智能体名称、状态数据、元数据
- Session：
  - 并发安全 Map，跨 Agent/子流程共享
  - 自动注入到 context 中，支持上下文传递
  - 支持检查点保存和恢复
  - 提供 Clone、只读视图等高级功能

### 4.3 组合原语（Compose）
- Sequential：顺序执行子 Agent；上游全对话作为上下文策略；失败策略（fail-fast/continue）。
- Parallel：并行启动多个 Agent，将事件按时间序聚合；错误聚合策略（优先 fail-fast）。
- Loop：基于迭代次数或停止条件循环；需防止活锁，设置 maxIter。
- Transfer：当前 Agent 通过事件显式移交控制权给目标 Agent。

### 4.4 ChatModelAgent（ReAct）
- Prompt Template：严格约束工具调用 JSON 序列化结构，避免幻觉调用。
- 工具调用：
  - 模型返回 tool_call → Emit EventToolCall
  - Agent 调用 Registry.Execute → Emit EventToolResult
  - 结果回注入对话上下文，直到生成 Final
- 流式：
  - ChatStream 增量转 EventToken/EventThought；保序输出
- 记忆：
  - 会话记忆（滚动窗口+重要性评分）+ 可选检索增强（vector）

### 4.5 工具系统与 MCP 集成
- Tool 接口统一：Name/Description/Schema/Execute 四个方法
- Tool.Schema 使用 JSONSchema，执行前严格校验参数；错误可操作（缺参、类型错误）
- Registry 并发安全，提供全局注册表函数：Register/Unregister/Get/List/Clear
- 支持工具执行器（Executor）模式，统一工具调用流程
- MCP Provider：
  - MCPToolAdapter 将 MCP 工具适配为标准 Tool 接口
  - 从远端拉取工具定义映射为 Tool；Execute 通过 MCP Client 真实调用
  - 支持工具参数验证和错误处理

### 4.6 LLM 与路由
- ChatModel 统一抽象；providers 实现真实 API（deepseek/qwen/doubao）。
- ModelRouter：按标签/成本/延迟路由，具熔断与降级策略（可后续迭代）。

### 4.7 记忆与持久化
- Store：统一接口，Conversation/Vector 两类能力；
- 后端：sqlite/postgres（事务、索引、过期策略）；
- 用于上下文裁剪、RAG 检索、Checkpoint。

#### SQLite 向量存储特性与限制
**功能特性**：
- ✅ 完整的向量存储接口实现（SaveVector、SearchSimilar、DeleteVector、DeleteSessionVectors）
- ✅ 基于余弦相似度的语义搜索算法
- ✅ JSON 格式向量序列化存储
- ✅ 支持向量元数据存储和查询
- ✅ 会话级别的向量管理和批量删除
- ✅ 输入验证和错误处理

**性能特性**：
- 📊 适用场景：中小规模向量集合（< 10万个向量）
- 📊 搜索方式：全量扫描 + 内存计算相似度
- 📊 存储格式：JSON 字符串（便于调试，但存储效率较低）
- 📊 索引策略：基于向量维度的简单过滤

**使用建议**：
- 🎯 **开发测试**：完全适用，无需额外依赖
- 🎯 **小规模生产**：向量数量 < 1万，查询频率较低
- 🎯 **大规模生产**：建议使用专业向量数据库（如 Pinecone、Weaviate、Milvus）

**升级路径**：
- 实现 VectorStore 接口的专业向量数据库适配器
- 保持接口兼容性，支持无缝切换存储后端
- 提供向量数据迁移工具

### 4.8 可观测性与错误处理
- 统一 errors 包：错误类型、上下文、可操作信息（中文）。
- 监控：QPS、延迟、错误率、Token 用量；Trace 跨事件打通。

---
### 4.9 对话审计（Conversation Audit）

> 目标：在不破坏现有架构的前提下，为所有用户⇄Agent 的交互与事件建立合规、可检索、可追溯、并发安全的审计能力，支持生产环境的数据保留与隐私保护。

- 覆盖范围：用户请求、Agent 生成（含流式 Token/Final/Error）、工具调用结果、分布式路由转发后的最终响应
- 特性：持久化（SQLite/PostgreSQL）、查询检索、数据保留策略、PII 脱敏/加密、与事件流透明集成（拦截器/管道）

#### 架构图（模块与数据流）
```mermaid
flowchart LR
    subgraph Web/API
      H[HTTP/WS 层]
    end

    subgraph Runtime
      R[Runner/Interceptors]
      Agt[Agent]
      ES[(Event Stream)]
    end

    subgraph Audit
      AS[AuditService]
      AW[AuditWriter\n(批量落库, 并发安全)]
      AR[AuditReader\n(查询检索)]
      SZ[Sanitizer/Redactor\n(隐私脱敏/哈希/加密)]
      RT[Retention Job\n(定期清理)]
    end

    subgraph Storage
      DB[(SQLite/PostgreSQL)]
    end

    H <--> R
    R -->|事件: Token/Final/Error/Tool*| ES
    ES --> AS
    AS --> SZ --> AW --> DB
    AR --> DB
    RT --> DB
    H -->|审计查询API| AR
```

#### 数据模型设计
- 采用两张核心表：会话（audit_sessions）与消息（audit_messages），提供高效查询与按会话聚合能力
- 针对 PostgreSQL 使用 JSONB/索引；SQLite 使用 TEXT/普通索引

SQL（SQLite）：
```sql
-- 会话表
CREATE TABLE IF NOT EXISTS audit_sessions (
  session_id TEXT PRIMARY KEY,
  user_id    TEXT NOT NULL,
  created_at DATETIME NOT NULL,
  last_active_at DATETIME NOT NULL,
  metadata   TEXT
);
CREATE INDEX IF NOT EXISTS idx_audit_sessions_user ON audit_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_sessions_last_active ON audit_sessions(last_active_at);

-- 消息表
CREATE TABLE IF NOT EXISTS audit_messages (
  id          TEXT PRIMARY KEY,
  session_id  TEXT NOT NULL,
  user_id     TEXT,
  agent_id    TEXT,
  role        TEXT NOT NULL,      -- user/assistant/system/tool
  msg_type    TEXT NOT NULL,      -- text/tool_result/error/token/final
  content     TEXT,               -- 可选加密/脱敏后的内容
  content_hash TEXT,              -- 可用于去重/敏感场景不存明文
  event_type  TEXT,               -- 对应 event.Type（token/final/error/...）
  tool_name   TEXT,
  error_code  TEXT,
  trace_id    TEXT,
  span_id     TEXT,
  created_at  DATETIME NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_audit_messages_session ON audit_messages(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_messages_user ON audit_messages(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_messages_agent ON audit_messages(agent_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_messages_type ON audit_messages(msg_type, event_type);
```

SQL（PostgreSQL）：
```sql
CREATE TABLE IF NOT EXISTS audit_sessions (
  session_id TEXT PRIMARY KEY,
  user_id    TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL,
  last_active_at TIMESTAMPTZ NOT NULL,
  metadata   JSONB
);
CREATE INDEX IF NOT EXISTS idx_audit_sessions_user ON audit_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_sessions_last_active ON audit_sessions(last_active_at);

CREATE TABLE IF NOT EXISTS audit_messages (
  id          TEXT PRIMARY KEY,
  session_id  TEXT NOT NULL REFERENCES audit_sessions(session_id) ON DELETE CASCADE,
  user_id     TEXT,
  agent_id    TEXT,
  role        TEXT NOT NULL,
  msg_type    TEXT NOT NULL,
  content     TEXT,
  content_hash TEXT,
  event_type  TEXT,
  tool_name   TEXT,
  error_code  TEXT,
  trace_id    TEXT,
  span_id     TEXT,
  created_at  TIMESTAMPTZ NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_audit_messages_session ON audit_messages(session_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_messages_user ON audit_messages(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_messages_agent ON audit_messages(agent_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_messages_type ON audit_messages(msg_type, event_type);
```

#### API 接口设计
Go 接口（pkg/audit）：
```go
package audit

type Record struct {
    ID         string
    SessionID  string
    UserID     string
    AgentID    string
    Role       string    // user/assistant/system/tool
    MsgType    string    // text/tool_result/error/token/final
    Content    string    // 可能已脱敏/加密
    ContentHash string
    EventType  string
    ToolName   string
    ErrorCode  string
    TraceID    string
    SpanID     string
    CreatedAt  time.Time
}

type Query struct {
    SessionID *string
    UserID    *string
    AgentID   *string
    Types     []string
    Since, Until *time.Time
    Keyword   string   // 简单 LIKE/ILIKE；PG 可选全文检索扩展
    Limit, Offset int
}

type Store interface {
    SaveMessage(ctx context.Context, r *Record) error
    SaveSession(ctx context.Context, sessionID, userID string, meta map[string]any) error
    TouchSession(ctx context.Context, sessionID string, at time.Time) error
    QueryMessages(ctx context.Context, q Query) ([]*Record, int, error)
    RunRetention(ctx context.Context) error
    Close() error
}
```

HTTP API（默认仅在开发模式允许读，生产需显式开启）：
- POST /api/v1/audit/sessions/{session_id}/messages 记录消息（由Web层或拦截器内部调用）
- GET  /api/v1/audit/sessions/{session_id}/messages?type=&agent_id=&q=&page=&page_size=
- GET  /api/v1/audit/sessions?user_id=&page=&page_size=
- POST /api/v1/audit/retention/run 触发一次保留策略清理（需要管理员权限）

#### 配置参数
YAML：
```yaml
audit:
  enabled: true
  driver: sqlite              # sqlite | postgres
  dsn: "file:audit.db?_journal_mode=WAL"
  retention:
    enabled: true
    max_days: 90
    cron: "@daily"          # 可与外部调度集成
  privacy:
    redact_pii: true          # 正则脱敏（邮箱/电话/身份证等）
    pii_patterns:
      - email
      - phone
    hash_content: false       # 仅存哈希，不落明文（高敏环境）
    encrypt_at_rest: false    # 静态加密，需提供密钥
    encrypt_key_env: AUDIT_AES_KEY
  batch:
    async: true
    chan_buffer: 1024
    flush_interval: 1s
  web:
    allow_read_api: false     # 生产默认关闭查询API
    allow_delete_api: false
```

#### 与现有模块的集成方式
- 事件驱动集成（推荐）：在 Runner Interceptor 链中注册 AuditInterceptor。
  - 入站：收到用户请求（pkg/web）时，记录 user 消息（role=user，msg_type=text）。
  - 出站：消费事件流时：
    - EventToken：可选记录增量（带序号与聚合键）或仅在 Final 汇总正文；默认仅记录 Final，配置可启用 Token 级审计。
    - EventFinal：记录 assistant 最终内容（role=assistant，msg_type=final），关联 trace/span。
    - EventError：记录错误码与上下文。
    - EventToolCall/ToolResult：可根据合规策略记录工具名与摘要，参数与结果可脱敏/裁剪。
- pkg/web：
  - 为请求生成/传递 session_id（Cookie/Header/Query 任一）；缺省时由服务端生成。
  - Web API 的执行路径在返回响应前，将 audit 最终消息写入（防止丢失）。
- pkg/memory：
  - 会话ID与审计共用标识；审计不依赖记忆内容，避免强绑定，提升容错。
- 存储与并发：
  - AuditWriter 使用单写协程+带缓冲通道，实现批量写入与背压；DB 操作设置超时、重试与熔断。
  - Query 为只读多协程安全；Store 实现需保证连接与事务的正确释放。
- 隐私与安全：
  - Redactor 在入库前对 PII 进行正则脱敏（邮箱/手机号等）。
  - 可选仅存 content_hash（不存明文）；或启用静态加密（AES-GCM）。
  - HTTP 查询接口在生产默认关闭；仅管理员可临时开启或通过独立审计导出工具读取。
- 兼容性：
  - 模块以 pkg/audit 独立存在，不影响现有 pkg/event/pkg/web；各模块通过接口解耦。

#### 运行时与测试建议
- 单元测试：
  - Store 接口的内存桩实现 + SQLite/Postgres 实例测试；覆盖异常（网络中断、锁超时、重复主键）
- 并发测试：
  - 100 并发写入+查询回放；验证无死锁，无数据丢失，顺序一致性满足按会话排序
- 集成测试：
  - examples/simple_chat* 通过 Web/API 发起对话，确认审计表出现记录；验证保留策略清理
```


## 5. 数据流图（示意）

```mermaid
sequenceDiagram
    participant U as User
    participant R as Runner
    participant A as Agent
    participant T as ToolRegistry/MCP
    participant M as ChatModel
    participant MEM as Memory

    U->>R: Run(Agent, Input)
    R->>A: Start Run(ctx, Input)
    A->>M: Chat/ChatStream
    M-->>A: Token/Thought/ToolCall
    A-->>R: EventToken/EventThought/EventToolCall
    A->>T: Execute Tool (Schema 校验/真实API)
    T-->>A: ToolResult
    A-->>R: EventToolResult (注入上下文)
    A->>MEM: Store/Query (会话/Checkpoint)
    A-->>R: EventFinal
    R-->>U: 事件流（供消费/显示/统计）
```

---

## 6. 开发者 API 示例

### 6.1 构建与运行单 Agent（流式）
```go
ctx := context.Background()
config := runtime.DefaultRunnerConfig()
config.EnableStreaming = true
runner := runtime.NewRunner(ctx, config)

model := providers.NewDeepseek(os.Getenv("DEEPSEEK_API_KEY"))
mem := memory.NewSQLiteStore("file:mem.db")

chat := agents.NewChatModelAgent(agents.ChatModelAgentConfig{
    Name: "Assistant", Model: model, Memory: mem,
})

// 创建输入
input := runtime.NewInput()
input.AddMessage(message.NewUserMessage("你好"))
input.SetSession(session.New())

// 执行并收集事件
events, err := runner.RunAndCollect(ctx, chat, input)
if err != nil {
    log.Fatal(err)
}
```

### 6.2 检查点与Session共享示例
```go
ctx := context.Background()
config := runtime.DefaultRunnerConfig()

// 启用检查点功能
config.EnableCheckpoint = true
config.CheckpointStore = runtime.NewSQLiteCheckpointStore("checkpoints.db")
config.CheckpointInterval = 3 // 每3个事件保存一次

runner := runtime.NewRunner(ctx, config)
defer config.CheckpointStore.Close()

// 创建共享Session
sharedSession := session.New()
sharedSession.Set("user_id", "12345")
sharedSession.Set("workflow", "multi_agent_demo")

// 第一个Agent
agent1 := NewDataCollectorAgent("collector")
input1 := runtime.NewInput()
input1.AddMessage(message.NewUserMessage("收集数据"))
input1.SetSession(sharedSession)

events1, err := runner.RunAndCollect(ctx, agent1, input1)

// 第二个Agent使用相同Session
agent2 := NewDataAnalyzerAgent("analyzer")
input2 := runtime.NewInput()
input2.AddMessage(message.NewUserMessage("分析数据"))
input2.SetSession(sharedSession) // 共享Session

events2, err := runner.RunAndCollect(ctx, agent2, input2)
```

### 6.3 拦截器与审计示例
```go
// 添加审计拦截器
auditStore := audit.NewSQLiteStore("audit.db")
auditInterceptor := audit.NewAuditInterceptor(auditStore)

// 通过适配器集成到Runner
adapter := runtime.NewRuntimeInterceptorAdapter(auditInterceptor)
runner.AddInterceptor(adapter)

// 添加指标拦截器
metricsInterceptor := runtime.NewMetricsInterceptor("metrics")
runner.AddInterceptor(metricsInterceptor)

it := runner.Run(ctx, chat, &agent.Input{Messages: msgs, Tools: reg.List(), Memory: mem, Session: sess})
for ev, ok := it.Next(); ok; ev, ok = it.Next() { render(ev) }
```

### 6.2 多 Agent 编排（顺序/并行）
```go
planner := agents.NewChatModelAgent(...)
researcher := agents.NewChatModelAgent(...)
writer := agents.NewChatModelAgent(...)

seq, _ := compose.NewSequential(ctx, []agent.Agent{planner, researcher, writer}, "pipeline", "plan->research->write")
it := runner.Run(ctx, seq, &agent.Input{Messages: msgs, Session: sess})
consume(it)
```

### 6.3 将 Agent 暴露为 Tool（AgentAsTool）
```go
tool := tooling.NewAgentTool(chat, tooling.WithName("sub_agent"))
reg.Register(tool)
```

---

## 7. 实施计划（阶段与质量门槛）

- 阶段 1：内核与治理（1–1.5 周）
  - event、runtime（Async/Runner/Interceptors/Checkpoint）、session、tool registry
  - 单测≥80%，并发/关闭/取消/错误覆盖；文档与示例
- 阶段 2：LLM 与 ChatModelAgent（1 周）
  - llm.ChatModel 抽象；deepseek/qwen/doubao 真实 API 适配；ReAct Agent
  - examples/simple_chat_adk 可跑
- 阶段 3：组合原语与记忆（1 周）
  - compose 顺序/并行/循环；memory Store + sqlite/postgres；Checkpoint 打通
- 阶段 4：MCP 与可观测（1 周）
  - MCP Provider 注入工具；metrics/tracing；压测与鲁棒性

质量与规范：
- go fmt/vet/golint 通过；单测覆盖率≥80%；集成测试覆盖主路径
- 真实 API/认证凭据通过环境变量；不硬编码；错误信息中文且可操作
- “文档即契约”：公开接口变更须先改文档并评审

---

## 8. 迁移策略

- 丢弃旧 pkg/agent Reply 同步模式；迁移为事件流 Run。
- 现有 pkg/llm DeepSeek/Qwen/豆包 客户端适配为新 llm.ChatModel；保留超时与重试。
- pkg/tool 升级为 JSONSchema + Execute；旧工具重写参数校验。
- pkg/pipeline 中顺序/并行迁移至 compose，其余弃用。
- pkg/memory 接口统一为 Store；Conversation/Vector 能力收敛；持久化沿用。
- pkg/mcp 通过 Provider 同步工具；执行走真实 MCP Client。

---

## 9. 变更与评审流程

- 任意公开接口/行为变更：先更新本设计文档 → 代码评审仅以文档为准。
- 示例与文档需同步更新：示例可运行、说明完整。
- 引入新外部服务：必须声明认证方式、超时/重试、错误语义与安全注意点。

