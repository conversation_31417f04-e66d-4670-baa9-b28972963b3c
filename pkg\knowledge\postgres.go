package knowledge

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/migration"
	_ "github.com/jackc/pgx/v5/stdlib"
)

// PostgresKnowledgeBase 使用PostgreSQL的知识库实现（真实数据库）
type PostgresKnowledgeBase struct {
	db     *sql.DB
	logger logger.Logger
}

// NewPostgresKnowledgeBase 创建PostgreSQL知识库
// dsn 示例：postgres://user:pass@localhost:5432/hzagent?sslmode=disable
func NewPostgresKnowledgeBase(dsn string) (*PostgresKnowledgeBase, error) {
	db, err := sql.Open("pgx", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %w", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := db.PingContext(ctx); err != nil {
		_ = db.Close()
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}
	// 执行数据库迁移（忽略 ErrNoChange 等非致命错误），后续 ensureSchema 兜底
	_ = migration.AutoMigrate(context.Background(), "postgres", dsn, "migrations")
	kb := &PostgresKnowledgeBase{db: db, logger: logger.GetGlobalLogger()}
	if err := kb.ensureSchema(context.Background()); err != nil {
		_ = db.Close()
		return nil, err
	}
	return kb, nil
}

// ensureSchema 确保表结构存在
func (kb *PostgresKnowledgeBase) ensureSchema(ctx context.Context) error {
	sqlStmt := `
CREATE TABLE IF NOT EXISTS kb_documents (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT NOT NULL,
    source TEXT NULL,
    language TEXT NULL,
    tags TEXT[] NULL,
    metadata JSONB NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    version INT NOT NULL DEFAULT 1,
    hash TEXT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_kb_documents_type ON kb_documents(type);
CREATE INDEX IF NOT EXISTS idx_kb_documents_created_at ON kb_documents(created_at);

CREATE TABLE IF NOT EXISTS kb_chunks (
    id TEXT PRIMARY KEY,
    document_id TEXT NOT NULL REFERENCES kb_documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    start_pos INT NOT NULL,
    end_pos INT NOT NULL,
    chunk_index INT NOT NULL,
    metadata JSONB NULL,
    vector_json JSONB NULL,
    created_at TIMESTAMPTZ NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_kb_chunks_doc ON kb_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_kb_chunks_index ON kb_chunks(chunk_index);

CREATE TABLE IF NOT EXISTS kb_entities (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT NULL,
    properties JSONB NULL,
    aliases TEXT[] NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE IF NOT EXISTS kb_document_entities (
    document_id TEXT NOT NULL REFERENCES kb_documents(id) ON DELETE CASCADE,
    entity_id TEXT NOT NULL REFERENCES kb_entities(id) ON DELETE CASCADE,
    PRIMARY KEY(document_id, entity_id)
);

CREATE TABLE IF NOT EXISTS kb_relations (
    id TEXT PRIMARY KEY,
    from_entity TEXT NOT NULL REFERENCES kb_entities(id) ON DELETE CASCADE,
    to_entity TEXT NOT NULL REFERENCES kb_entities(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    description TEXT NULL,
    properties JSONB NULL,
    confidence DOUBLE PRECISION NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE IF NOT EXISTS kb_relation_documents (
    relation_id TEXT NOT NULL REFERENCES kb_relations(id) ON DELETE CASCADE,
    document_id TEXT NOT NULL REFERENCES kb_documents(id) ON DELETE CASCADE,
    PRIMARY KEY(relation_id, document_id)
);
`
	_, err := kb.db.ExecContext(ctx, sqlStmt)
	if err != nil {
		return fmt.Errorf("初始化知识库表结构失败: %w", err)
	}
	return nil
}

// AddDocument 添加文档（含分块、实体、关系）
func (kb *PostgresKnowledgeBase) AddDocument(ctx context.Context, doc *Document) error {
	tx, err := kb.db.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	var mdJSON []byte
	if doc.Metadata != nil {
		mdJSON, _ = json.Marshal(doc.Metadata)
	}
	tags := toPGArray(doc.Tags)
	_, err = tx.ExecContext(ctx, `INSERT INTO kb_documents(id,title,content,type,source,language,tags,metadata,created_at,updated_at,version,hash)
VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12)`,
		doc.ID, doc.Title, doc.Content, string(doc.Type), doc.Source, doc.Language, tags, jsonRawOrNull(mdJSON),
		doc.CreatedAt, doc.UpdatedAt, doc.Version, doc.Hash,
	)
	if err != nil {
		return fmt.Errorf("插入文档失败: %w", err)
	}

	// chunks
	for _, ch := range doc.Chunks {
		var chMD []byte
		if ch.Metadata != nil {
			chMD, _ = json.Marshal(ch.Metadata)
		}
		var vec []byte
		if len(ch.Vector) > 0 {
			vec, _ = json.Marshal(ch.Vector)
		}
		_, err = tx.ExecContext(ctx, `INSERT INTO kb_chunks(id,document_id,content,start_pos,end_pos,chunk_index,metadata,vector_json,created_at)
VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)`, ch.ID, doc.ID, ch.Content, ch.StartPos, ch.EndPos, ch.ChunkIndex, jsonRawOrNull(chMD), jsonRawOrNull(vec), ch.CreatedAt)
		if err != nil {
			return fmt.Errorf("插入分块失败: %w", err)
		}
	}

	// entities & joins
	for _, e := range doc.Entities {
		var props []byte
		if e.Properties != nil {
			props, _ = json.Marshal(e.Properties)
		}
		aliases := toPGArray(e.Aliases)
		_, err = tx.ExecContext(ctx, `INSERT INTO kb_entities(id,name,type,description,properties,aliases,created_at,updated_at)
VALUES($1,$2,$3,$4,$5,$6,$7,$8)
ON CONFLICT (id) DO UPDATE SET name=EXCLUDED.name,type=EXCLUDED.type,description=EXCLUDED.description,properties=EXCLUDED.properties,aliases=EXCLUDED.aliases,updated_at=EXCLUDED.updated_at`,
			e.ID, e.Name, e.Type, e.Description, jsonRawOrNull(props), aliases, e.CreatedAt, e.UpdatedAt)
		if err != nil {
			return fmt.Errorf("写入实体失败: %w", err)
		}
		_, err = tx.ExecContext(ctx, `INSERT INTO kb_document_entities(document_id,entity_id) VALUES($1,$2) ON CONFLICT DO NOTHING`, doc.ID, e.ID)
		if err != nil {
			return fmt.Errorf("写入文档-实体关系失败: %w", err)
		}
	}

	// relations & joins
	for _, r := range doc.Relations {
		var props []byte
		if r.Properties != nil {
			props, _ = json.Marshal(r.Properties)
		}
		_, err = tx.ExecContext(ctx, `INSERT INTO kb_relations(id,from_entity,to_entity,type,description,properties,confidence,created_at,updated_at)
VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)
ON CONFLICT (id) DO UPDATE SET from_entity=EXCLUDED.from_entity,to_entity=EXCLUDED.to_entity,type=EXCLUDED.type,description=EXCLUDED.description,properties=EXCLUDED.properties,confidence=EXCLUDED.confidence,updated_at=EXCLUDED.updated_at`,
			r.ID, r.FromEntity, r.ToEntity, r.Type, r.Description, jsonRawOrNull(props), r.Confidence, r.CreatedAt, r.UpdatedAt)
		if err != nil {
			return fmt.Errorf("写入关系失败: %w", err)
		}
		_, err = tx.ExecContext(ctx, `INSERT INTO kb_relation_documents(relation_id,document_id) VALUES($1,$2) ON CONFLICT DO NOTHING`, r.ID, doc.ID)
		if err != nil {
			return fmt.Errorf("写入关系-文档关系失败: %w", err)
		}
	}

	if err = tx.Commit(); err != nil {
		return err
	}
	return nil
}

// GetDocument 获取文档（含分块、实体、关系）
func (kb *PostgresKnowledgeBase) GetDocument(ctx context.Context, id string) (*Document, error) {
	row := kb.db.QueryRowContext(ctx, `SELECT id,title,content,type,source,language,tags,metadata,created_at,updated_at,version,hash FROM kb_documents WHERE id=$1`, id)
	var (
		doc  Document
		tags sql.NullString
		md   sql.NullString
		typ  string
	)
	if err := row.Scan(&doc.ID, &doc.Title, &doc.Content, &typ, &doc.Source, &doc.Language, &tags, &md, &doc.CreatedAt, &doc.UpdatedAt, &doc.Version, &doc.Hash); err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("document not found")
		}
		return nil, err
	}
	doc.Type = DocumentType(typ)
	doc.Tags = parsePGArray(tags.String)
	if md.Valid && md.String != "" {
		_ = json.Unmarshal([]byte(md.String), &doc.Metadata)
	}

	// chunks
	chRows, err := kb.db.QueryContext(ctx, `SELECT id,content,start_pos,end_pos,chunk_index,metadata,vector_json,created_at FROM kb_chunks WHERE document_id=$1 ORDER BY chunk_index ASC`, id)
	if err != nil {
		return nil, err
	}
	defer chRows.Close()
	for chRows.Next() {
		var ch DocumentChunk
		var chMD, vec sql.NullString
		if err := chRows.Scan(&ch.ID, &ch.Content, &ch.StartPos, &ch.EndPos, &ch.ChunkIndex, &chMD, &vec, &ch.CreatedAt); err != nil {
			return nil, err
		}
		ch.DocumentID = id
		if chMD.Valid && chMD.String != "" {
			_ = json.Unmarshal([]byte(chMD.String), &ch.Metadata)
		}
		if vec.Valid && vec.String != "" {
			_ = json.Unmarshal([]byte(vec.String), &ch.Vector)
		}
		doc.Chunks = append(doc.Chunks, &ch)
	}

	// entities
	entRows, err := kb.db.QueryContext(ctx, `SELECT e.id,e.name,e.type,e.description,e.properties,e.aliases,e.created_at,e.updated_at FROM kb_entities e JOIN kb_document_entities de ON e.id=de.entity_id WHERE de.document_id=$1`, id)
	if err == nil {
		defer entRows.Close()
		for entRows.Next() {
			var e Entity
			var props sql.NullString
			var aliases sql.NullString
			if err := entRows.Scan(&e.ID, &e.Name, &e.Type, &e.Description, &props, &aliases, &e.CreatedAt, &e.UpdatedAt); err != nil {
				return nil, err
			}
			if props.Valid && props.String != "" {
				_ = json.Unmarshal([]byte(props.String), &e.Properties)
			}
			e.Aliases = parsePGArray(aliases.String)
			doc.Entities = append(doc.Entities, &e)
		}
	}
	// relations
	relRows, err := kb.db.QueryContext(ctx, `SELECT r.id,r.from_entity,r.to_entity,r.type,r.description,r.properties,r.confidence,r.created_at,r.updated_at FROM kb_relations r JOIN kb_relation_documents rd ON r.id=rd.relation_id WHERE rd.document_id=$1`, id)
	if err == nil {
		defer relRows.Close()
		for relRows.Next() {
			var r Relation
			var props sql.NullString
			if err := relRows.Scan(&r.ID, &r.FromEntity, &r.ToEntity, &r.Type, &r.Description, &props, &r.Confidence, &r.CreatedAt, &r.UpdatedAt); err != nil {
				return nil, err
			}
			if props.Valid && props.String != "" {
				_ = json.Unmarshal([]byte(props.String), &r.Properties)
			}
			doc.Relations = append(doc.Relations, &r)
		}
	}
	return &doc, nil
}

// UpdateDocument 更新文档（覆盖基本字段与分块）
func (kb *PostgresKnowledgeBase) UpdateDocument(ctx context.Context, doc *Document) error {
	tx, err := kb.db.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	var mdJSON []byte
	if doc.Metadata != nil {
		mdJSON, _ = json.Marshal(doc.Metadata)
	}
	tags := toPGArray(doc.Tags)
	res, err := tx.ExecContext(ctx, `UPDATE kb_documents SET title=$2,content=$3,type=$4,source=$5,language=$6,tags=$7,metadata=$8,updated_at=$9,version=$10,hash=$11 WHERE id=$1`,
		doc.ID, doc.Title, doc.Content, string(doc.Type), doc.Source, doc.Language, tags, jsonRawOrNull(mdJSON), doc.UpdatedAt, doc.Version, doc.Hash)
	if err != nil {
		return err
	}
	if n, _ := res.RowsAffected(); n == 0 {
		return fmt.Errorf("document not found")
	}

	// 替换分块
	if _, err = tx.ExecContext(ctx, `DELETE FROM kb_chunks WHERE document_id=$1`, doc.ID); err != nil {
		return err
	}
	for _, ch := range doc.Chunks {
		var chMD, vec []byte
		if ch.Metadata != nil {
			chMD, _ = json.Marshal(ch.Metadata)
		}
		if len(ch.Vector) > 0 {
			vec, _ = json.Marshal(ch.Vector)
		}
		if _, err = tx.ExecContext(ctx, `INSERT INTO kb_chunks(id,document_id,content,start_pos,end_pos,chunk_index,metadata,vector_json,created_at) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)`,
			ch.ID, doc.ID, ch.Content, ch.StartPos, ch.EndPos, ch.ChunkIndex, jsonRawOrNull(chMD), jsonRawOrNull(vec), ch.CreatedAt); err != nil {
			return err
		}
	}

	if err = tx.Commit(); err != nil {
		return err
	}
	return nil
}

// DeleteDocument 删除文档
func (kb *PostgresKnowledgeBase) DeleteDocument(ctx context.Context, id string) error {
	_, err := kb.db.ExecContext(ctx, `DELETE FROM kb_documents WHERE id=$1`, id)
	return err
}

// ListDocuments 列出文档
func (kb *PostgresKnowledgeBase) ListDocuments(ctx context.Context, limit, offset int) ([]*Document, error) {
	if limit <= 0 {
		limit = 50
	}
	rows, err := kb.db.QueryContext(ctx, `SELECT id,title,content,type,source,language,tags,metadata,created_at,updated_at,version,hash FROM kb_documents ORDER BY created_at DESC LIMIT $1 OFFSET $2`, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []*Document
	for rows.Next() {
		var d Document
		var tags, md sql.NullString
		var typ string
		if err := rows.Scan(&d.ID, &d.Title, &d.Content, &typ, &d.Source, &d.Language, &tags, &md, &d.CreatedAt, &d.UpdatedAt, &d.Version, &d.Hash); err != nil {
			return nil, err
		}
		d.Type = DocumentType(typ)
		d.Tags = parsePGArray(tags.String)
		if md.Valid && md.String != "" {
			_ = json.Unmarshal([]byte(md.String), &d.Metadata)
		}
		out = append(out, &d)
	}
	return out, nil
}

// SearchDocuments 简单文本检索（title/content ILIKE）
func (kb *PostgresKnowledgeBase) SearchDocuments(ctx context.Context, query *SearchQuery) ([]*SearchResult, error) {
	if query == nil {
		query = &SearchQuery{}
	}
	q := `SELECT id,title,content,type,source,language,tags,metadata,created_at,updated_at,version,hash FROM kb_documents WHERE 1=1`
	args := []any{}
	if query.Query != "" {
		q += ` AND (title ILIKE $1 OR content ILIKE $1)`
		args = append(args, "%"+query.Query+"%")
	}
	if query.Type != nil {
		q += fmt.Sprintf(" AND type = $%d", len(args)+1)
		args = append(args, string(*query.Type))
	}
	if len(query.Tags) > 0 {
		q += fmt.Sprintf(" AND tags && $%d::text[]", len(args)+1)
		args = append(args, toPGArray(query.Tags))
	}
	q += " ORDER BY updated_at DESC"
	limit := query.Limit
	if limit <= 0 {
		limit = 50
	}
	offset := query.Offset
	q += fmt.Sprintf(" LIMIT $%d OFFSET $%d", len(args)+1, len(args)+2)
	args = append(args, limit, offset)

	rows, err := kb.db.QueryContext(ctx, q, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*SearchResult
	for rows.Next() {
		var d Document
		var tags, md sql.NullString
		var typ string
		if err := rows.Scan(&d.ID, &d.Title, &d.Content, &typ, &d.Source, &d.Language, &tags, &md, &d.CreatedAt, &d.UpdatedAt, &d.Version, &d.Hash); err != nil {
			return nil, err
		}
		d.Type = DocumentType(typ)
		d.Tags = parsePGArray(tags.String)
		if md.Valid && md.String != "" {
			_ = json.Unmarshal([]byte(md.String), &d.Metadata)
		}
		results = append(results, &SearchResult{Document: &d, Score: 1.0})
	}
	return results, nil
}

// SearchSimilar 基于内容相似度搜索（使用TF-IDF和余弦相似度）
func (kb *PostgresKnowledgeBase) SearchSimilar(ctx context.Context, docID string, limit int) ([]*SearchResult, error) {
	d, err := kb.GetDocument(ctx, docID)
	if err != nil {
		return nil, err
	}

	if limit <= 0 {
		limit = 10
	}

	// 获取所有其他文档
	rows, err := kb.db.QueryContext(ctx, `
		SELECT id, title, content, type, source, language, tags, metadata, created_at, updated_at, version, hash
		FROM kb_documents
		WHERE id <> $1`, d.ID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var candidates []*Document
	for rows.Next() {
		var nd Document
		var tags, md sql.NullString
		var typ string
		if err := rows.Scan(&nd.ID, &nd.Title, &nd.Content, &typ, &nd.Source, &nd.Language, &tags, &md, &nd.CreatedAt, &nd.UpdatedAt, &nd.Version, &nd.Hash); err != nil {
			return nil, err
		}
		nd.Type = DocumentType(typ)
		nd.Tags = parsePGArray(tags.String)
		if md.Valid && md.String != "" {
			_ = json.Unmarshal([]byte(md.String), &nd.Metadata)
		}
		candidates = append(candidates, &nd)
	}

	// 计算相似度
	var results []*SearchResult
	sourceText := d.Title + " " + d.Content
	sourceTerms := extractTerms(sourceText)

	for _, candidate := range candidates {
		candidateText := candidate.Title + " " + candidate.Content
		candidateTerms := extractTerms(candidateText)

		// 计算余弦相似度
		similarity := calculateTextSimilarity(sourceTerms, candidateTerms)
		if similarity > 0.1 { // 设置最小相似度阈值
			results = append(results, &SearchResult{
				Document: candidate,
				Score:    similarity,
			})
		}
	}

	// 按相似度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 限制结果数量
	if len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

// extractTerms 提取文本中的词条
func extractTerms(text string) map[string]int {
	terms := make(map[string]int)
	words := strings.Fields(strings.ToLower(text))

	for _, word := range words {
		// 简单的词条清理
		word = strings.Trim(word, ".,!?;:\"'()[]{}")
		if len(word) > 2 { // 忽略太短的词
			terms[word]++
		}
	}

	return terms
}

// calculateTextSimilarity 计算两个文本的余弦相似度
func calculateTextSimilarity(terms1, terms2 map[string]int) float64 {
	// 获取所有唯一词条
	allTerms := make(map[string]bool)
	for term := range terms1 {
		allTerms[term] = true
	}
	for term := range terms2 {
		allTerms[term] = true
	}

	// 构建向量
	var vector1, vector2 []float64
	for term := range allTerms {
		vector1 = append(vector1, float64(terms1[term]))
		vector2 = append(vector2, float64(terms2[term]))
	}

	// 计算余弦相似度
	var dotProduct, norm1, norm2 float64
	for i := 0; i < len(vector1); i++ {
		dotProduct += vector1[i] * vector2[i]
		norm1 += vector1[i] * vector1[i]
		norm2 += vector2[i] * vector2[i]
	}

	if norm1 == 0 || norm2 == 0 {
		return 0
	}

	return dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2))
}

// GetStats 统计信息
func (kb *PostgresKnowledgeBase) GetStats(ctx context.Context) (*KnowledgeBaseStats, error) {
	stats := &KnowledgeBaseStats{}
	row := kb.db.QueryRowContext(ctx, `SELECT COUNT(*) FROM kb_documents`)
	_ = row.Scan(&stats.DocumentCount)
	row = kb.db.QueryRowContext(ctx, `SELECT COUNT(*) FROM kb_chunks`)
	_ = row.Scan(&stats.ChunkCount)
	row = kb.db.QueryRowContext(ctx, `SELECT COUNT(*) FROM kb_entities`)
	_ = row.Scan(&stats.EntityCount)
	row = kb.db.QueryRowContext(ctx, `SELECT COUNT(*) FROM kb_relations`)
	_ = row.Scan(&stats.RelationCount)
	stats.LastUpdated = time.Now()
	return stats, nil
}

// RebuildIndex 创建必要索引
func (kb *PostgresKnowledgeBase) RebuildIndex(ctx context.Context) error {
	_, err := kb.db.ExecContext(ctx, `CREATE INDEX IF NOT EXISTS idx_kb_documents_title ON kb_documents USING BTREE (title)`)
	return err
}

// OptimizeIndex 优化索引（简单执行ANALYZE）
func (kb *PostgresKnowledgeBase) OptimizeIndex(ctx context.Context) error {
	_, err := kb.db.ExecContext(ctx, `ANALYZE`)
	return err
}

// Close 关闭连接
func (kb *PostgresKnowledgeBase) Close() error {
	if kb.db != nil {
		return kb.db.Close()
	}
	return nil
}

// --- 工具函数 ---
func toPGArray(v []string) any {
	if len(v) == 0 {
		return nil
	}
	escaped := make([]string, 0, len(v))
	for _, s := range v {
		escaped = append(escaped, strings.ReplaceAll(s, "\"", "\\\""))
	}
	return fmt.Sprintf("{%s}", strings.Join(escaped, ","))
}

func jsonRawOrNull(b []byte) any {
	if len(b) == 0 {
		return nil
	}
	return string(b)
}

func parsePGArray(s string) []string {
	if s == "" || s == "{}" {
		return nil
	}
	if strings.HasPrefix(s, "{") && strings.HasSuffix(s, "}") {
		s = s[1 : len(s)-1]
	}
	if s == "" {
		return nil
	}
	parts := strings.Split(s, ",")
	out := make([]string, 0, len(parts))
	for _, p := range parts {
		out = append(out, strings.Trim(p, " \""))
	}
	return out
}
