package audit

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	_ "modernc.org/sqlite"
)

// SQLiteStore SQLite存储实现
type SQLiteStore struct {
	db     *sql.DB
	config *Config
	mu     sync.RWMutex
	closed bool
}

// NewSQLiteStore 创建SQLite存储
func NewSQLiteStore(dsn string, config *Config) (*SQLiteStore, error) {
	if config == nil {
		config = DefaultConfig()
	}

	db, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, NewStoreInitError("打开SQLite数据库失败", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// 启用WAL模式和外键约束
	if _, err := db.Exec("PRAGMA journal_mode=WAL"); err != nil {
		db.Close()
		return nil, NewStoreInitError("启用WAL模式失败", err)
	}

	if _, err := db.Exec("PRAGMA foreign_keys=ON"); err != nil {
		db.Close()
		return nil, NewStoreInitError("启用外键约束失败", err)
	}

	store := &SQLiteStore{
		db:     db,
		config: config,
	}

	// 执行数据库迁移
	if err := store.migrate(context.Background()); err != nil {
		db.Close()
		return nil, err
	}

	return store, nil
}

// migrate 执行数据库迁移
func (s *SQLiteStore) migrate(ctx context.Context) error {
	migrator := NewMigrator(s.db, GetSQLiteMigrations())
	return migrator.MigrateToLatest(ctx)
}

// SaveMessage 保存消息记录
func (s *SQLiteStore) SaveMessage(ctx context.Context, r *Record) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if err := ValidateRecord(r); err != nil {
		return err
	}

	query := `
		INSERT INTO audit_messages (
			id, session_id, user_id, agent_id, role, msg_type, 
			content, content_hash, event_type, tool_name, error_code, 
			trace_id, span_id, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := s.db.ExecContext(ctx, query,
		r.ID, r.SessionID, r.UserID, r.AgentID, r.Role, r.MsgType,
		r.Content, r.ContentHash, r.EventType, r.ToolName, r.ErrorCode,
		r.TraceID, r.SpanID, r.CreatedAt,
	)

	if err != nil {
		return NewStoreOperationError("保存消息记录失败", err)
	}

	return nil
}

// SaveSession 保存会话信息
func (s *SQLiteStore) SaveSession(ctx context.Context, sessionID, userID string, meta map[string]any) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if sessionID == "" || userID == "" {
		return NewInvalidRecordError("会话ID和用户ID不能为空", nil)
	}

	var metaJSON string
	if meta != nil {
		metaBytes, err := json.Marshal(meta)
		if err != nil {
			return NewStoreOperationError("序列化会话元数据失败", err)
		}
		metaJSON = string(metaBytes)
	}

	now := time.Now()
	query := `
		INSERT OR REPLACE INTO audit_sessions (session_id, user_id, created_at, last_active_at, metadata)
		VALUES (?, ?, COALESCE((SELECT created_at FROM audit_sessions WHERE session_id = ?), ?), ?, ?)`

	_, err := s.db.ExecContext(ctx, query, sessionID, userID, sessionID, now, now, metaJSON)
	if err != nil {
		return NewStoreOperationError("保存会话信息失败", err)
	}

	return nil
}

// TouchSession 更新会话最后活跃时间
func (s *SQLiteStore) TouchSession(ctx context.Context, sessionID string, at time.Time) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if sessionID == "" {
		return NewInvalidRecordError("会话ID不能为空", nil)
	}

	query := `UPDATE audit_sessions SET last_active_at = ? WHERE session_id = ?`
	_, err := s.db.ExecContext(ctx, query, at, sessionID)
	if err != nil {
		return NewStoreOperationError("更新会话活跃时间失败", err)
	}

	return nil
}

// QueryMessages 查询消息记录
func (s *SQLiteStore) QueryMessages(ctx context.Context, q Query) (*QueryResult, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return nil, NewStoreOperationError("存储已关闭", nil)
	}

	if err := ValidateQuery(q); err != nil {
		return nil, err
	}

	NormalizeQuery(&q)

	// 构建查询条件
	var conditions []string
	var args []interface{}

	if q.SessionID != nil {
		conditions = append(conditions, "session_id = ?")
		args = append(args, *q.SessionID)
	}

	if q.UserID != nil {
		conditions = append(conditions, "user_id = ?")
		args = append(args, *q.UserID)
	}

	if q.AgentID != nil {
		conditions = append(conditions, "agent_id = ?")
		args = append(args, *q.AgentID)
	}

	if len(q.Types) > 0 {
		placeholders := make([]string, len(q.Types))
		for i, t := range q.Types {
			placeholders[i] = "?"
			args = append(args, t)
		}
		conditions = append(conditions, fmt.Sprintf("msg_type IN (%s)", strings.Join(placeholders, ",")))
	}

	if q.Since != nil {
		conditions = append(conditions, "created_at >= ?")
		args = append(args, *q.Since)
	}

	if q.Until != nil {
		conditions = append(conditions, "created_at <= ?")
		args = append(args, *q.Until)
	}

	if q.Keyword != "" {
		conditions = append(conditions, "content LIKE ?")
		args = append(args, "%"+q.Keyword+"%")
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM audit_messages %s", whereClause)
	var total int
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, NewStoreOperationError("查询消息总数失败", err)
	}

	// 查询记录
	dataQuery := fmt.Sprintf(`
		SELECT id, session_id, user_id, agent_id, role, msg_type, 
			   content, content_hash, event_type, tool_name, error_code, 
			   trace_id, span_id, created_at
		FROM audit_messages %s 
		ORDER BY created_at DESC 
		LIMIT ? OFFSET ?`, whereClause)

	args = append(args, q.Limit, q.Offset)

	rows, err := s.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, NewStoreOperationError("查询消息记录失败", err)
	}
	defer rows.Close()

	var records []*Record
	for rows.Next() {
		r := &Record{}
		err := rows.Scan(
			&r.ID, &r.SessionID, &r.UserID, &r.AgentID, &r.Role, &r.MsgType,
			&r.Content, &r.ContentHash, &r.EventType, &r.ToolName, &r.ErrorCode,
			&r.TraceID, &r.SpanID, &r.CreatedAt,
		)
		if err != nil {
			return nil, NewStoreOperationError("扫描消息记录失败", err)
		}
		records = append(records, r)
	}

	if err := rows.Err(); err != nil {
		return nil, NewStoreOperationError("遍历消息记录失败", err)
	}

	hasMore := q.Offset+len(records) < total

	return &QueryResult{
		Records: records,
		Total:   total,
		HasMore: hasMore,
	}, nil
}

// QuerySessions 查询会话列表
func (s *SQLiteStore) QuerySessions(ctx context.Context, userID string, limit, offset int) ([]*Session, int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return nil, 0, NewStoreOperationError("存储已关闭", nil)
	}

	if limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000
	}
	if offset < 0 {
		offset = 0
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM audit_sessions"
	var args []interface{}
	if userID != "" {
		countQuery += " WHERE user_id = ?"
		args = append(args, userID)
	}

	var total int
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, NewStoreOperationError("查询会话总数失败", err)
	}

	// 查询会话列表
	dataQuery := "SELECT session_id, user_id, created_at, last_active_at, metadata FROM audit_sessions"
	if userID != "" {
		dataQuery += " WHERE user_id = ?"
	}
	dataQuery += " ORDER BY last_active_at DESC LIMIT ? OFFSET ?"

	args = append(args, limit, offset)

	rows, err := s.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, 0, NewStoreOperationError("查询会话列表失败", err)
	}
	defer rows.Close()

	var sessions []*Session
	for rows.Next() {
		s := &Session{}
		var metaJSON sql.NullString
		err := rows.Scan(&s.SessionID, &s.UserID, &s.CreatedAt, &s.LastActiveAt, &metaJSON)
		if err != nil {
			return nil, 0, NewStoreOperationError("扫描会话记录失败", err)
		}

		if metaJSON.Valid && metaJSON.String != "" {
			var meta map[string]any
			if err := json.Unmarshal([]byte(metaJSON.String), &meta); err == nil {
				s.Metadata = meta
			}
		}

		sessions = append(sessions, s)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, NewStoreOperationError("遍历会话记录失败", err)
	}

	return sessions, total, nil
}

// RunRetention 执行数据保留策略清理
func (s *SQLiteStore) RunRetention(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.closed {
		return NewStoreOperationError("存储已关闭", nil)
	}

	if !s.config.Retention.Enabled || s.config.Retention.MaxDays <= 0 {
		return nil // 未启用保留策略
	}

	cutoffTime := time.Now().AddDate(0, 0, -s.config.Retention.MaxDays)

	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return NewRetentionError("开始保留策略事务失败", err)
	}
	defer tx.Rollback()

	// 删除过期消息
	result, err := tx.ExecContext(ctx, "DELETE FROM audit_messages WHERE created_at < ?", cutoffTime)
	if err != nil {
		return NewRetentionError("删除过期消息失败", err)
	}

	deletedMessages, _ := result.RowsAffected()

	// 删除没有消息的会话
	result, err = tx.ExecContext(ctx, `
		DELETE FROM audit_sessions
		WHERE session_id NOT IN (SELECT DISTINCT session_id FROM audit_messages)`)
	if err != nil {
		return NewRetentionError("删除空会话失败", err)
	}

	deletedSessions, _ := result.RowsAffected()

	if err := tx.Commit(); err != nil {
		return NewRetentionError("提交保留策略事务失败", err)
	}

	// 记录清理结果（可选添加日志）
	_ = deletedMessages
	_ = deletedSessions

	return nil
}

// Close 关闭存储连接
func (s *SQLiteStore) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.closed {
		return nil
	}

	s.closed = true
	return s.db.Close()
}
