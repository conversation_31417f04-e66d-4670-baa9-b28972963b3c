package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/web"
)

func main() {
	fmt.Println("WebSocket Origin校验安全示例")
	fmt.Println("=============================")

	// 创建Web配置，启用严格的Origin校验
	webConfig := &config.WebConfig{
		Host: "localhost",
		Port: 8080,
		CORS: &config.CORSConfig{
			Enabled: true,
			AllowedOrigins: []string{
				"http://localhost:3000",  // 允许前端开发服务器
				"https://app.example.com", // 允许生产环境域名
				"*.dev.example.com",       // 允许开发环境子域名
			},
			AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
			AllowedHeaders:   []string{"Content-Type", "Authorization"},
			AllowCredentials: true,
		},
	}

	// 创建WebSocket管理器
	wsManager := web.NewWebSocketManager(nil, webConfig)

	// 创建HTTP服务器
	mux := http.NewServeMux()

	// WebSocket端点
	mux.HandleFunc("/ws", wsManager.HandleWebSocket)

	// 测试页面端点
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		html := `<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Origin校验测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        #log { height: 200px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Origin校验测试</h1>
        
        <div class="test-section">
            <h3>当前页面Origin: <span id="currentOrigin"></span></h3>
            <p>允许的Origin列表:</p>
            <ul>
                <li>http://localhost:3000</li>
                <li>https://app.example.com</li>
                <li>*.dev.example.com</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>连接测试</h3>
            <button onclick="testConnection()">测试WebSocket连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>连接日志</h3>
            <div id="log"></div>
        </div>

        <div class="test-section">
            <h3>安全说明</h3>
            <p>此示例演示了WebSocket Origin校验的安全机制：</p>
            <ul>
                <li><strong>严格校验</strong>: 只有配置的Origin才能建立WebSocket连接</li>
                <li><strong>防止CSRF</strong>: 恶意网站无法建立跨域WebSocket连接</li>
                <li><strong>子域名支持</strong>: 支持通配符匹配子域名</li>
                <li><strong>协议匹配</strong>: HTTP和HTTPS协议必须精确匹配</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示当前Origin
        document.getElementById('currentOrigin').textContent = window.location.origin;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logDiv.innerHTML += '<div class="' + className + '">[' + timestamp + '] ' + message + '</div>';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testConnection() {
            log('尝试建立WebSocket连接...');
            
            const wsUrl = 'ws://' + window.location.host + '/ws';
            log('连接URL: ' + wsUrl);
            log('当前Origin: ' + window.location.origin);

            const ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                log('✅ WebSocket连接成功建立！', 'success');
                log('连接状态: ' + ws.readyState);
                
                // 发送测试消息
                ws.send(JSON.stringify({
                    type: 'test',
                    message: 'Hello from ' + window.location.origin
                }));
            };

            ws.onmessage = function(event) {
                log('📨 收到消息: ' + event.data, 'success');
            };

            ws.onerror = function(error) {
                log('❌ WebSocket连接错误: ' + error, 'error');
            };

            ws.onclose = function(event) {
                if (event.wasClean) {
                    log('🔒 WebSocket连接正常关闭 (code=' + event.code + ')', 'info');
                } else {
                    log('❌ WebSocket连接异常关闭 (code=' + event.code + ')', 'error');
                    if (event.code === 1006) {
                        log('可能原因: Origin校验失败，连接被服务器拒绝', 'error');
                    }
                }
            };

            // 5秒后关闭连接
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                    log('主动关闭WebSocket连接');
                }
            }, 5000);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            log('页面加载完成');
            log('当前Origin: ' + window.location.origin);
            
            if (window.location.origin === 'http://localhost:8080') {
                log('⚠️  当前Origin不在允许列表中，WebSocket连接将被拒绝', 'error');
            } else {
                log('✅ 当前Origin可能被允许，请测试连接', 'success');
            }
        };
    </script>
</body>
</html>`
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(html))
	})

	// 启动WebSocket管理器
	wsManager.Start()

	// 启动HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", webConfig.Host, webConfig.Port),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	fmt.Printf("服务器启动在 http://%s:%d\n", webConfig.Host, webConfig.Port)
	fmt.Println("请在浏览器中访问以下URL进行测试:")
	fmt.Printf("- http://localhost:%d (应该被拒绝)\n", webConfig.Port)
	fmt.Printf("- http://localhost:3000 (如果有前端服务器，应该被允许)\n")
	fmt.Println("\n按 Ctrl+C 停止服务器")

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
