package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// ToolExecutor 工具执行器
type ToolExecutor struct {
	registry Registry
	logger   logger.Logger
	timeout  time.Duration
}

// NewToolExecutor 创建工具执行器
func NewToolExecutor(registry Registry) *ToolExecutor {
	return &ToolExecutor{
		registry: registry,
		logger:   logger.GetGlobalLogger(),
		timeout:  30 * time.Second,
	}
}

// SetTimeout 设置超时时间
func (e *ToolExecutor) SetTimeout(timeout time.Duration) {
	e.timeout = timeout
}

// ExecuteToolCall 执行工具调用
func (e *ToolExecutor) ExecuteToolCall(ctx context.Context, toolCall *ToolCall) (*ToolResult, error) {
	if toolCall == nil {
		return nil, errors.NewValidationError("invalid_tool_call", "tool call cannot be nil")
	}

	// 获取工具
	tool, exists := e.registry.Get(toolCall.Name)
	if !exists {
		return &ToolResult{
			Success:   false,
			Error:     fmt.Sprintf("tool not found: %s", toolCall.Name),
			Timestamp: time.Now(),
		}, nil
	}

	// 创建带超时的上下文
	execCtx, cancel := context.WithTimeout(ctx, e.timeout)
	defer cancel()

	// 执行工具
	e.logger.Debug("Executing tool: %s with parameters: %v", toolCall.Name, toolCall.Parameters)

	start := time.Now()
	result, err := tool.Execute(execCtx, toolCall.Parameters)
	duration := time.Since(start)

	if err != nil {
		return &ToolResult{
			Success:   false,
			Error:     err.Error(),
			Duration:  duration,
			Timestamp: time.Now(),
		}, nil
	}

	e.logger.Debug("Tool execution completed: %s", toolCall.Name)
	return &ToolResult{
		Success:   true,
		Data:      result,
		Duration:  duration,
		Timestamp: time.Now(),
	}, nil
}

// ExecuteToolCalls 批量执行工具调用
func (e *ToolExecutor) ExecuteToolCalls(ctx context.Context, toolCalls []*ToolCall) ([]*ToolResult, error) {
	if len(toolCalls) == 0 {
		return []*ToolResult{}, nil
	}

	results := make([]*ToolResult, len(toolCalls))

	for i, toolCall := range toolCalls {
		result, err := e.ExecuteToolCall(ctx, toolCall)
		if err != nil {
			return nil, err
		}
		results[i] = result
	}

	return results, nil
}

// ValidateToolCall 验证工具调用
func (e *ToolExecutor) ValidateToolCall(toolCall *ToolCall) error {
	if toolCall == nil {
		return errors.NewValidationError("invalid_tool_call", "tool call cannot be nil")
	}

	if toolCall.Name == "" {
		return errors.NewValidationError("invalid_tool_name", "tool name cannot be empty")
	}

	// 检查工具是否存在
	tool, exists := e.registry.Get(toolCall.Name)
	if !exists {
		return fmt.Errorf("tool not found: %s", toolCall.Name)
	}

	// 验证参数
	schema := tool.Schema()
	if schema != nil {
		// 将参数转换为JSON字符串进行验证
		paramsJSON, err := json.Marshal(toolCall.Parameters)
		if err != nil {
			return fmt.Errorf("failed to marshal parameters: %w", err)
		}
		return ValidateInput(schema, string(paramsJSON))
	}

	return nil
}

// GetAvailableTools 获取可用工具列表
func (e *ToolExecutor) GetAvailableTools() []ToolInfo {
	toolNames := e.registry.List()
	infos := make([]ToolInfo, len(toolNames))

	for i, name := range toolNames {
		tool, exists := e.registry.Get(name)
		if exists {
			infos[i] = ToolInfo{
				Name:        tool.Name(),
				Description: tool.Description(),
				Schema:      tool.Schema(),
			}
		}
	}

	return infos
}

// ToolInfo 工具信息
type ToolInfo struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Schema      *JSONSchema `json:"schema"`
}

// ConvertToLLMToolDefinitions 转换为LLM工具定义格式
func (e *ToolExecutor) ConvertToLLMToolDefinitions() ([]map[string]interface{}, error) {
	tools := e.registry.List()
	definitions := make([]map[string]interface{}, len(tools))

	for i, name := range tools {
		tool, exists := e.registry.Get(name)
		if !exists {
			return nil, fmt.Errorf("tool not found: %s", name)
		}

		schema := tool.Schema()
		schemaJSON, err := json.Marshal(schema)
		if err != nil {
			return nil, err
		}

		var schemaMap map[string]interface{}
		if err := json.Unmarshal(schemaJSON, &schemaMap); err != nil {
			return nil, err
		}

		definitions[i] = map[string]interface{}{
			"type": "function",
			"function": map[string]interface{}{
				"name":        tool.Name(),
				"description": tool.Description(),
				"parameters":  schemaMap,
			},
		}
	}

	return definitions, nil
}
