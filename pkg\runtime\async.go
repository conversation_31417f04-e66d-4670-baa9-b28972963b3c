package runtime

import (
	"context"
	"fmt"
	"sync"
)

// AsyncIterator 表示异步事件迭代器，用于消费事件流
type AsyncIterator[T any] struct {
	ch     <-chan T
	ctx    context.Context
	cancel context.CancelFunc
	closed bool
	mu     sync.RWMutex
}

// Generator 表示事件生成器，用于产生事件流
type Generator[T any] struct {
	ch     chan T
	ctx    context.Context
	cancel context.CancelFunc
	closed bool
	mu     sync.RWMutex
}

// GeneratorPair 表示生成器和迭代器的配对
type GeneratorPair[T any] struct {
	Generator *Generator[T]
	Iterator  *AsyncIterator[T]
}

// BufferSizeConfig 缓冲区大小配置
type BufferSizeConfig struct {
	// 场景类型
	ScenarioType string
	// 预期事件频率（每秒事件数）
	EventsPerSecond float64
	// 预期处理延迟（毫秒）
	ProcessingLatencyMs int
	// 是否为流式输出
	IsStreaming bool
	// 最大缓冲区大小限制
	MaxBufferSize int
	// 最小缓冲区大小限制
	MinBufferSize int
}

// 预定义的场景配置
var (
	// LLMStreamingConfig LLM流式输出场景配置
	LLMStreamingConfig = &BufferSizeConfig{
		ScenarioType:        "llm_streaming",
		EventsPerSecond:     50.0, // 流式输出通常每秒50个token
		ProcessingLatencyMs: 20,   // 20ms处理延迟
		IsStreaming:         true,
		MaxBufferSize:       200,
		MinBufferSize:       10,
	}

	// LLMBatchConfig LLM批量输出场景配置
	LLMBatchConfig = &BufferSizeConfig{
		ScenarioType:        "llm_batch",
		EventsPerSecond:     5.0, // 批量输出较少事件
		ProcessingLatencyMs: 100, // 100ms处理延迟
		IsStreaming:         false,
		MaxBufferSize:       50,
		MinBufferSize:       5,
	}

	// ToolExecutionConfig 工具执行场景配置
	ToolExecutionConfig = &BufferSizeConfig{
		ScenarioType:        "tool_execution",
		EventsPerSecond:     10.0, // 工具执行中等频率
		ProcessingLatencyMs: 50,   // 50ms处理延迟
		IsStreaming:         false,
		MaxBufferSize:       30,
		MinBufferSize:       3,
	}

	// DefaultConfig 默认配置
	DefaultConfig = &BufferSizeConfig{
		ScenarioType:        "default",
		EventsPerSecond:     10.0,
		ProcessingLatencyMs: 50,
		IsStreaming:         false,
		MaxBufferSize:       50,
		MinBufferSize:       5,
	}
)

// CalculateOptimalBufferSize 根据场景配置计算最优缓冲区大小
func CalculateOptimalBufferSize(config *BufferSizeConfig) int {
	if config == nil {
		config = DefaultConfig
	}

	// 基础计算：事件频率 × 处理延迟 = 需要缓冲的事件数
	// 公式：bufferSize = (eventsPerSecond * processingLatencyMs / 1000) * safetyFactor
	baseSize := int(config.EventsPerSecond * float64(config.ProcessingLatencyMs) / 1000.0)

	// 安全系数：为突发流量预留空间
	safetyFactor := 2.0
	if config.IsStreaming {
		// 流式输出需要更大的安全系数，因为token生成速度可能不均匀
		safetyFactor = 3.0
	}

	optimalSize := int(float64(baseSize) * safetyFactor)

	// 应用最小和最大限制
	if optimalSize < config.MinBufferSize {
		optimalSize = config.MinBufferSize
	}
	if optimalSize > config.MaxBufferSize {
		optimalSize = config.MaxBufferSize
	}

	return optimalSize
}

// NewAsyncIteratorWithConfig 根据场景配置创建优化的异步迭代器
func NewAsyncIteratorWithConfig[T any](ctx context.Context, config *BufferSizeConfig) *GeneratorPair[T] {
	bufferSize := CalculateOptimalBufferSize(config)
	return NewAsyncIterator[T](ctx, bufferSize)
}

// NewAsyncIteratorForLLMStreaming 为LLM流式输出创建优化的迭代器
func NewAsyncIteratorForLLMStreaming[T any](ctx context.Context) *GeneratorPair[T] {
	return NewAsyncIteratorWithConfig[T](ctx, LLMStreamingConfig)
}

// NewAsyncIteratorForLLMBatch 为LLM批量输出创建优化的迭代器
func NewAsyncIteratorForLLMBatch[T any](ctx context.Context) *GeneratorPair[T] {
	return NewAsyncIteratorWithConfig[T](ctx, LLMBatchConfig)
}

// NewAsyncIteratorForToolExecution 为工具执行创建优化的迭代器
func NewAsyncIteratorForToolExecution[T any](ctx context.Context) *GeneratorPair[T] {
	return NewAsyncIteratorWithConfig[T](ctx, ToolExecutionConfig)
}

// NewAsyncIterator 创建新的异步迭代器和生成器配对
func NewAsyncIterator[T any](ctx context.Context, bufferSize int) *GeneratorPair[T] {
	if bufferSize <= 0 {
		bufferSize = 1 // 默认缓冲大小
	}

	// 创建可取消的上下文
	ctxWithCancel, cancel := context.WithCancel(ctx)

	// 创建带缓冲的通道
	ch := make(chan T, bufferSize)

	generator := &Generator[T]{
		ch:     ch,
		ctx:    ctxWithCancel,
		cancel: cancel,
	}

	iterator := &AsyncIterator[T]{
		ch:     ch,
		ctx:    ctxWithCancel,
		cancel: cancel,
	}

	return &GeneratorPair[T]{
		Generator: generator,
		Iterator:  iterator,
	}
}

// Next 获取下一个事件，返回事件和是否还有更多事件的标志
func (it *AsyncIterator[T]) Next() (T, bool) {
	it.mu.RLock()
	if it.closed {
		it.mu.RUnlock()
		var zero T
		return zero, false
	}
	it.mu.RUnlock()

	select {
	case value, ok := <-it.ch:
		if !ok {
			// 通道关闭，标记迭代器为关闭状态
			it.mu.Lock()
			it.closed = true
			it.mu.Unlock()
			var zero T
			return zero, false
		}
		return value, true
	case <-it.ctx.Done():
		it.mu.Lock()
		it.closed = true
		it.mu.Unlock()
		var zero T
		return zero, false
	}
}

// IsClosed 检查迭代器是否已关闭
func (it *AsyncIterator[T]) IsClosed() bool {
	it.mu.RLock()
	defer it.mu.RUnlock()
	return it.closed
}

// Close 关闭迭代器
func (it *AsyncIterator[T]) Close() {
	it.mu.Lock()
	defer it.mu.Unlock()

	if !it.closed {
		it.closed = true
		it.cancel()
	}
}

// Context 返回迭代器的上下文
func (it *AsyncIterator[T]) Context() context.Context {
	return it.ctx
}

// Send 发送事件到迭代器
func (g *Generator[T]) Send(value T) error {
	g.mu.RLock()
	if g.closed {
		g.mu.RUnlock()
		return fmt.Errorf("生成器已关闭")
	}
	g.mu.RUnlock()

	select {
	case g.ch <- value:
		return nil
	case <-g.ctx.Done():
		return fmt.Errorf("上下文已取消: %w", g.ctx.Err())
	}
}

// TrySend 尝试非阻塞发送事件，如果通道满了则返回 false
func (g *Generator[T]) TrySend(value T) bool {
	g.mu.RLock()
	if g.closed {
		g.mu.RUnlock()
		return false
	}
	g.mu.RUnlock()

	select {
	case g.ch <- value:
		return true
	case <-g.ctx.Done():
		return false
	default:
		return false // 通道满了
	}
}

// Close 关闭生成器
func (g *Generator[T]) Close() {
	g.mu.Lock()
	defer g.mu.Unlock()

	if !g.closed {
		g.closed = true
		close(g.ch)
		// 不要立即取消上下文，让迭代器能够读取通道中的剩余数据
	}
}

// IsClosed 检查生成器是否已关闭
func (g *Generator[T]) IsClosed() bool {
	g.mu.RLock()
	defer g.mu.RUnlock()
	return g.closed
}

// Context 返回生成器的上下文
func (g *Generator[T]) Context() context.Context {
	return g.ctx
}

// SendWithRecovery 发送事件并捕获 panic，将 panic 转换为错误返回
func (g *Generator[T]) SendWithRecovery(value T) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("发送事件时发生 panic: %v", r)
		}
	}()

	return g.Send(value)
}

// Collect 收集迭代器中的所有事件到切片中
func (it *AsyncIterator[T]) Collect() []T {
	var results []T
	for value, ok := it.Next(); ok; value, ok = it.Next() {
		results = append(results, value)
	}
	return results
}

// CollectWithTimeout 在指定超时时间内收集事件
func (it *AsyncIterator[T]) CollectWithTimeout(ctx context.Context) []T {
	var results []T

	for {
		select {
		case <-ctx.Done():
			return results
		case value, ok := <-it.ch:
			if !ok {
				return results
			}
			results = append(results, value)
		case <-it.ctx.Done():
			return results
		}
	}
}

// ForEach 对迭代器中的每个事件执行指定函数
func (it *AsyncIterator[T]) ForEach(fn func(T) bool) {
	for value, ok := it.Next(); ok; value, ok = it.Next() {
		if !fn(value) {
			break
		}
	}
}

// Filter 创建一个新的迭代器，只包含满足条件的事件
func (it *AsyncIterator[T]) Filter(ctx context.Context, predicate func(T) bool, bufferSize int) *AsyncIterator[T] {
	pair := NewAsyncIterator[T](ctx, bufferSize)

	go func() {
		defer pair.Generator.Close()

		for value, ok := it.Next(); ok; value, ok = it.Next() {
			if predicate(value) {
				if err := pair.Generator.Send(value); err != nil {
					break
				}
			}
		}
	}()

	return pair.Iterator
}

// Map 创建一个新的迭代器，对每个事件应用转换函数
func Map[T, U any](it *AsyncIterator[T], ctx context.Context, mapper func(T) U, bufferSize int) *AsyncIterator[U] {
	pair := NewAsyncIterator[U](ctx, bufferSize)

	go func() {
		defer pair.Generator.Close()

		for value, ok := it.Next(); ok; value, ok = it.Next() {
			mapped := mapper(value)
			if err := pair.Generator.Send(mapped); err != nil {
				break
			}
		}
	}()

	return pair.Iterator
}

// Merge 合并多个迭代器的事件流
func Merge[T any](ctx context.Context, iterators []*AsyncIterator[T], bufferSize int) *AsyncIterator[T] {
	if len(iterators) == 0 {
		pair := NewAsyncIterator[T](ctx, 1)
		pair.Generator.Close()
		return pair.Iterator
	}

	if len(iterators) == 1 {
		return iterators[0]
	}

	pair := NewAsyncIterator[T](ctx, bufferSize)

	var wg sync.WaitGroup

	for _, it := range iterators {
		wg.Add(1)
		go func(iterator *AsyncIterator[T]) {
			defer wg.Done()

			for value, ok := iterator.Next(); ok; value, ok = iterator.Next() {
				if err := pair.Generator.Send(value); err != nil {
					break
				}
			}
		}(it)
	}

	go func() {
		wg.Wait()
		pair.Generator.Close()
	}()

	return pair.Iterator
}
