package memory

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/message"
	_ "github.com/lib/pq"
)

// PostgresStore Postgres实现的记忆存储
type PostgresStore struct {
	db *sql.DB
}

// PostgresConfig Postgres存储配置
type PostgresConfig struct {
	Host     string // 主机地址
	Port     int    // 端口
	Database string // 数据库名
	Username string // 用户名
	Password string // 密码
	SSLMode  string // SSL模式
}

// NewPostgresStore 创建Postgres记忆存储
func NewPostgresStore(config *PostgresConfig) (*PostgresStore, error) {
	if config == nil {
		// 从环境变量读取配置
		config = &PostgresConfig{
			Host:     getEnvOrDefault("POSTGRES_HOST", "localhost"),
			Port:     5432,
			Database: getEnvOrDefault("POSTGRES_DB", "agentscope"),
			Username: getEnvOrDefault("POSTGRES_USER", "postgres"),
			Password: getEnvOrDefault("POSTGRES_PASSWORD", ""),
			SSLMode:  getEnvOrDefault("POSTGRES_SSLMODE", "disable"),
		}
	}

	// 构建连接字符串
	connStr := fmt.Sprintf("host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		config.Host, config.Port, config.Database, config.Username, config.Password, config.SSLMode)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("打开Postgres数据库失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("连接Postgres数据库失败: %w", err)
	}

	store := &PostgresStore{
		db: db,
	}

	if err := store.initTables(); err != nil {
		db.Close()
		return nil, fmt.Errorf("初始化数据库表失败: %w", err)
	}

	return store, nil
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// initTables 初始化数据库表
func (p *PostgresStore) initTables() error {
	// 创建消息表
	createMessagesTable := `
	CREATE TABLE IF NOT EXISTS messages (
		id TEXT PRIMARY KEY,
		session_id TEXT NOT NULL,
		role TEXT NOT NULL,
		content TEXT NOT NULL,
		metadata JSONB,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
	);
	CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);
	CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
	CREATE INDEX IF NOT EXISTS idx_messages_metadata ON messages USING GIN(metadata);`

	// 创建向量表（使用pgvector扩展）
	createVectorsTable := `
	CREATE EXTENSION IF NOT EXISTS vector;
	CREATE TABLE IF NOT EXISTS vectors (
		id SERIAL PRIMARY KEY,
		session_id TEXT NOT NULL,
		message_id TEXT NOT NULL,
		vector vector(1536), -- 假设使用1536维向量（OpenAI embedding）
		metadata JSONB,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(session_id, message_id)
	);
	CREATE INDEX IF NOT EXISTS idx_vectors_session_id ON vectors(session_id);
	CREATE INDEX IF NOT EXISTS idx_vectors_message_id ON vectors(message_id);
	CREATE INDEX IF NOT EXISTS idx_vectors_vector ON vectors USING ivfflat (vector vector_cosine_ops);`

	if _, err := p.db.Exec(createMessagesTable); err != nil {
		return fmt.Errorf("创建消息表失败: %w", err)
	}

	// 尝试创建向量表，如果pgvector扩展不存在则跳过
	if _, err := p.db.Exec(createVectorsTable); err != nil {
		// 创建简化的向量表（不使用pgvector）
		fallbackVectorsTable := `
		CREATE TABLE IF NOT EXISTS vectors (
			id SERIAL PRIMARY KEY,
			session_id TEXT NOT NULL,
			message_id TEXT NOT NULL,
			vector_data BYTEA,
			metadata JSONB,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(session_id, message_id)
		);
		CREATE INDEX IF NOT EXISTS idx_vectors_session_id ON vectors(session_id);
		CREATE INDEX IF NOT EXISTS idx_vectors_message_id ON vectors(message_id);`

		if _, err := p.db.Exec(fallbackVectorsTable); err != nil {
			return fmt.Errorf("创建向量表失败: %w", err)
		}
	}

	return nil
}

// Save 保存消息到记忆
func (p *PostgresStore) Save(ctx context.Context, sessionID string, msg *message.Message) error {
	if msg == nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息不能为空",
		}
	}

	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if err := msg.Validate(); err != nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息验证失败: " + err.Error(),
		}
	}

	// 序列化元数据为JSONB
	var metadataJSON []byte
	if msg.Metadata != nil {
		var err error
		metadataJSON, err = json.Marshal(msg.Metadata)
		if err != nil {
			return fmt.Errorf("序列化消息元数据失败: %w", err)
		}
	}

	// 插入消息
	query := `
	INSERT INTO messages (id, session_id, role, content, metadata)
	VALUES ($1, $2, $3, $4, $5)`

	_, err := p.db.ExecContext(ctx, query, msg.ID, sessionID, msg.Role, msg.GetContentString(), metadataJSON)
	if err != nil {
		return fmt.Errorf("保存消息到数据库失败: %w", err)
	}

	return nil
}

// Load 加载指定会话的消息历史
func (p *PostgresStore) Load(ctx context.Context, sessionID string, limit int) ([]*message.Message, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	var query string
	var args []any

	if limit <= 0 {
		query = `
		SELECT id, role, content, metadata, created_at
		FROM messages
		WHERE session_id = $1
		ORDER BY created_at ASC`
		args = []any{sessionID}
	} else {
		query = `
		SELECT id, role, content, metadata, created_at
		FROM (
			SELECT id, role, content, metadata, created_at
			FROM messages
			WHERE session_id = $1
			ORDER BY created_at DESC
			LIMIT $2
		) AS recent_messages
		ORDER BY created_at ASC`
		args = []any{sessionID, limit}
	}

	rows, err := p.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询消息失败: %w", err)
	}
	defer rows.Close()

	var messages []*message.Message
	for rows.Next() {
		var id, role, content string
		var metadataJSON []byte
		var createdAt time.Time

		if err := rows.Scan(&id, &role, &content, &metadataJSON, &createdAt); err != nil {
			return nil, fmt.Errorf("扫描消息行失败: %w", err)
		}

		// 反序列化元数据
		var metadata map[string]any
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &metadata); err != nil {
				return nil, fmt.Errorf("反序列化消息元数据失败: %w", err)
			}
		}

		// 创建消息对象
		msg := &message.Message{
			ID:       id,
			Role:     role,
			Content:  content,
			Metadata: metadata,
		}

		messages = append(messages, msg)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历消息行失败: %w", err)
	}

	return messages, nil
}

// LoadAll 加载指定会话的所有消息
func (p *PostgresStore) LoadAll(ctx context.Context, sessionID string) ([]*message.Message, error) {
	return p.Load(ctx, sessionID, 0)
}

// Delete 删除指定会话的消息
func (p *PostgresStore) Delete(ctx context.Context, sessionID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	// 开始事务
	tx, err := p.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 删除消息
	_, err = tx.ExecContext(ctx, "DELETE FROM messages WHERE session_id = $1", sessionID)
	if err != nil {
		return fmt.Errorf("删除会话消息失败: %w", err)
	}

	// 删除向量
	_, err = tx.ExecContext(ctx, "DELETE FROM vectors WHERE session_id = $1", sessionID)
	if err != nil {
		return fmt.Errorf("删除会话向量失败: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// DeleteMessage 删除指定消息
func (p *PostgresStore) DeleteMessage(ctx context.Context, sessionID, messageID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if messageID == "" {
		return &ValidationError{
			Field:   "messageID",
			Message: "消息ID不能为空",
		}
	}

	// 开始事务
	tx, err := p.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 删除消息
	_, err = tx.ExecContext(ctx, "DELETE FROM messages WHERE session_id = $1 AND id = $2", sessionID, messageID)
	if err != nil {
		return fmt.Errorf("删除消息失败: %w", err)
	}

	// 删除对应的向量
	_, err = tx.ExecContext(ctx, "DELETE FROM vectors WHERE session_id = $1 AND message_id = $2", sessionID, messageID)
	if err != nil {
		return fmt.Errorf("删除消息向量失败: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// Search 搜索消息（基于内容）
func (p *PostgresStore) Search(ctx context.Context, sessionID, query string, limit int) ([]*message.Message, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if query == "" {
		return []*message.Message{}, nil
	}

	sqlQuery := `
	SELECT id, role, content, metadata, created_at
	FROM messages
	WHERE session_id = $1 AND content ILIKE $2
	ORDER BY created_at ASC`

	var args []any
	args = append(args, sessionID, "%"+query+"%")

	if limit > 0 {
		sqlQuery += " LIMIT $3"
		args = append(args, limit)
	}

	rows, err := p.db.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("搜索消息失败: %w", err)
	}
	defer rows.Close()

	var messages []*message.Message
	for rows.Next() {
		var id, role, content string
		var metadataJSON []byte
		var createdAt time.Time

		if err := rows.Scan(&id, &role, &content, &metadataJSON, &createdAt); err != nil {
			return nil, fmt.Errorf("扫描搜索结果失败: %w", err)
		}

		// 反序列化元数据
		var metadata map[string]any
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &metadata); err != nil {
				return nil, fmt.Errorf("反序列化搜索结果元数据失败: %w", err)
			}
		}

		// 创建消息对象
		msg := &message.Message{
			ID:       id,
			Role:     role,
			Content:  content,
			Metadata: metadata,
		}

		messages = append(messages, msg)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历搜索结果失败: %w", err)
	}

	return messages, nil
}

// GetSessions 获取所有会话ID
func (p *PostgresStore) GetSessions(ctx context.Context) ([]string, error) {
	query := "SELECT DISTINCT session_id FROM messages ORDER BY session_id"

	rows, err := p.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询会话列表失败: %w", err)
	}
	defer rows.Close()

	var sessions []string
	for rows.Next() {
		var sessionID string
		if err := rows.Scan(&sessionID); err != nil {
			return nil, fmt.Errorf("扫描会话ID失败: %w", err)
		}
		sessions = append(sessions, sessionID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历会话列表失败: %w", err)
	}

	return sessions, nil
}

// Close 关闭存储
func (p *PostgresStore) Close() error {
	if p.db != nil {
		return p.db.Close()
	}
	return nil
}

// Validate 验证Postgres配置
func (c *PostgresConfig) Validate() error {
	if c.Host == "" {
		return fmt.Errorf("host cannot be empty")
	}
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("port must be between 1 and 65535")
	}
	if c.Database == "" {
		return fmt.Errorf("database cannot be empty")
	}
	if c.Username == "" {
		return fmt.Errorf("username cannot be empty")
	}
	return nil
}

// ConnectionString 生成Postgres连接字符串
func (c *PostgresConfig) ConnectionString() string {
	return fmt.Sprintf("host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		c.Host, c.Port, c.Database, c.Username, c.Password, c.SSLMode)
}
