package audit

import (
	"context"
	"database/sql"
	"fmt"
)

// SQLiteSchema SQLite数据库架构
const SQLiteSchema = `
-- 会话表
CREATE TABLE IF NOT EXISTS audit_sessions (
  session_id TEXT PRIMARY KEY,
  user_id    TEXT NOT NULL,
  created_at DATETIME NOT NULL,
  last_active_at DATETIME NOT NULL,
  metadata   TEXT
);

-- 会话表索引
CREATE INDEX IF NOT EXISTS idx_audit_sessions_user ON audit_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_sessions_last_active ON audit_sessions(last_active_at);

-- 消息表
CREATE TABLE IF NOT EXISTS audit_messages (
  id          TEXT PRIMARY KEY,
  session_id  TEXT NOT NULL,
  user_id     TEXT,
  agent_id    TEXT,
  role        TEXT NOT NULL,      -- user/assistant/system/tool
  msg_type    TEXT NOT NULL,      -- text/tool_result/error/token/final
  content     TEXT,               -- 可选加密/脱敏后的内容
  content_hash TEXT,              -- 可用于去重/敏感场景不存明文
  event_type  TEXT,               -- 对应 event.Type（token/final/error/...）
  tool_name   TEXT,
  error_code  TEXT,
  trace_id    TEXT,
  span_id     TEXT,
  created_at  DATETIME NOT NULL
);

-- 消息表索引
CREATE INDEX IF NOT EXISTS idx_audit_messages_session ON audit_messages(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_messages_user ON audit_messages(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_messages_agent ON audit_messages(agent_id, created_at);
CREATE INDEX IF NOT EXISTS idx_audit_messages_type ON audit_messages(msg_type, event_type);
CREATE INDEX IF NOT EXISTS idx_audit_messages_created_at ON audit_messages(created_at);

-- 版本表
CREATE TABLE IF NOT EXISTS audit_schema_version (
  version INTEGER PRIMARY KEY,
  applied_at DATETIME NOT NULL
);
`

// PostgreSQLSchema PostgreSQL数据库架构
const PostgreSQLSchema = `
-- 会话表
CREATE TABLE IF NOT EXISTS audit_sessions (
  session_id TEXT PRIMARY KEY,
  user_id    TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL,
  last_active_at TIMESTAMPTZ NOT NULL,
  metadata   JSONB
);

-- 会话表索引
CREATE INDEX IF NOT EXISTS idx_audit_sessions_user ON audit_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_sessions_last_active ON audit_sessions(last_active_at);

-- 消息表
CREATE TABLE IF NOT EXISTS audit_messages (
  id          TEXT PRIMARY KEY,
  session_id  TEXT NOT NULL REFERENCES audit_sessions(session_id) ON DELETE CASCADE,
  user_id     TEXT,
  agent_id    TEXT,
  role        TEXT NOT NULL,
  msg_type    TEXT NOT NULL,
  content     TEXT,
  content_hash TEXT,
  event_type  TEXT,
  tool_name   TEXT,
  error_code  TEXT,
  trace_id    TEXT,
  span_id     TEXT,
  created_at  TIMESTAMPTZ NOT NULL
);

-- 消息表索引
CREATE INDEX IF NOT EXISTS idx_audit_messages_session ON audit_messages(session_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_messages_user ON audit_messages(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_messages_agent ON audit_messages(agent_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_messages_type ON audit_messages(msg_type, event_type);
CREATE INDEX IF NOT EXISTS idx_audit_messages_created_at ON audit_messages(created_at DESC);

-- 版本表
CREATE TABLE IF NOT EXISTS audit_schema_version (
  version INTEGER PRIMARY KEY,
  applied_at TIMESTAMPTZ NOT NULL
);
`

// SchemaVersion 当前架构版本
const SchemaVersion = 1

// Migration 数据库迁移接口
type Migration interface {
	// Version 返回迁移版本号
	Version() int
	// Up 执行向上迁移
	Up(ctx context.Context, db *sql.DB) error
	// Down 执行向下迁移（可选）
	Down(ctx context.Context, db *sql.DB) error
}

// BaseMigration 基础迁移实现
type BaseMigration struct {
	version int
	upSQL   string
	downSQL string
}

// Version 返回迁移版本号
func (m *BaseMigration) Version() int {
	return m.version
}

// Up 执行向上迁移
func (m *BaseMigration) Up(ctx context.Context, db *sql.DB) error {
	if m.upSQL == "" {
		return nil
	}
	
	_, err := db.ExecContext(ctx, m.upSQL)
	if err != nil {
		return NewStoreOperationError(fmt.Sprintf("执行迁移SQL失败 (版本 %d)", m.version), err)
	}
	
	return nil
}

// Down 执行向下迁移
func (m *BaseMigration) Down(ctx context.Context, db *sql.DB) error {
	if m.downSQL == "" {
		return nil
	}
	
	_, err := db.ExecContext(ctx, m.downSQL)
	if err != nil {
		return NewStoreOperationError(fmt.Sprintf("执行回滚SQL失败 (版本 %d)", m.version), err)
	}
	
	return nil
}

// GetSQLiteMigrations 获取SQLite迁移列表
func GetSQLiteMigrations() []Migration {
	return []Migration{
		&BaseMigration{
			version: 1,
			upSQL:   SQLiteSchema,
			downSQL: `DROP TABLE IF EXISTS audit_messages; DROP TABLE IF EXISTS audit_sessions; DROP TABLE IF EXISTS audit_schema_version;`,
		},
	}
}

// GetPostgreSQLMigrations 获取PostgreSQL迁移列表
func GetPostgreSQLMigrations() []Migration {
	return []Migration{
		&BaseMigration{
			version: 1,
			upSQL:   PostgreSQLSchema,
			downSQL: `DROP TABLE IF EXISTS audit_messages; DROP TABLE IF EXISTS audit_sessions; DROP TABLE IF EXISTS audit_schema_version;`,
		},
	}
}

// Migrator 数据库迁移器
type Migrator struct {
	db         *sql.DB
	migrations []Migration
}

// NewMigrator 创建迁移器
func NewMigrator(db *sql.DB, migrations []Migration) *Migrator {
	return &Migrator{
		db:         db,
		migrations: migrations,
	}
}

// GetCurrentVersion 获取当前数据库版本
func (m *Migrator) GetCurrentVersion(ctx context.Context) (int, error) {
	var version int
	err := m.db.QueryRowContext(ctx, "SELECT COALESCE(MAX(version), 0) FROM audit_schema_version").Scan(&version)
	if err != nil {
		// 如果表不存在，返回版本0
		return 0, nil
	}
	return version, nil
}

// Migrate 执行迁移到指定版本
func (m *Migrator) Migrate(ctx context.Context, targetVersion int) error {
	currentVersion, err := m.GetCurrentVersion(ctx)
	if err != nil {
		return NewStoreOperationError("获取当前数据库版本失败", err)
	}
	
	if currentVersion == targetVersion {
		return nil // 已经是目标版本
	}
	
	if currentVersion > targetVersion {
		return m.migrateDown(ctx, currentVersion, targetVersion)
	}
	
	return m.migrateUp(ctx, currentVersion, targetVersion)
}

// MigrateToLatest 迁移到最新版本
func (m *Migrator) MigrateToLatest(ctx context.Context) error {
	if len(m.migrations) == 0 {
		return nil
	}
	
	latestVersion := 0
	for _, migration := range m.migrations {
		if migration.Version() > latestVersion {
			latestVersion = migration.Version()
		}
	}
	
	return m.Migrate(ctx, latestVersion)
}

// migrateUp 向上迁移
func (m *Migrator) migrateUp(ctx context.Context, currentVersion, targetVersion int) error {
	for _, migration := range m.migrations {
		version := migration.Version()
		if version > currentVersion && version <= targetVersion {
			tx, err := m.db.BeginTx(ctx, nil)
			if err != nil {
				return NewStoreOperationError("开始迁移事务失败", err)
			}
			
			err = migration.Up(ctx, m.db)
			if err != nil {
				tx.Rollback()
				return err
			}
			
			// 记录迁移版本
			_, err = tx.ExecContext(ctx, "INSERT INTO audit_schema_version (version, applied_at) VALUES (?, ?)", 
				version, "datetime('now')")
			if err != nil {
				tx.Rollback()
				return NewStoreOperationError("记录迁移版本失败", err)
			}
			
			err = tx.Commit()
			if err != nil {
				return NewStoreOperationError("提交迁移事务失败", err)
			}
		}
	}
	
	return nil
}

// migrateDown 向下迁移
func (m *Migrator) migrateDown(ctx context.Context, currentVersion, targetVersion int) error {
	for i := len(m.migrations) - 1; i >= 0; i-- {
		migration := m.migrations[i]
		version := migration.Version()
		if version <= currentVersion && version > targetVersion {
			tx, err := m.db.BeginTx(ctx, nil)
			if err != nil {
				return NewStoreOperationError("开始回滚事务失败", err)
			}
			
			err = migration.Down(ctx, m.db)
			if err != nil {
				tx.Rollback()
				return err
			}
			
			// 删除迁移版本记录
			_, err = tx.ExecContext(ctx, "DELETE FROM audit_schema_version WHERE version = ?", version)
			if err != nil {
				tx.Rollback()
				return NewStoreOperationError("删除迁移版本记录失败", err)
			}
			
			err = tx.Commit()
			if err != nil {
				return NewStoreOperationError("提交回滚事务失败", err)
			}
		}
	}
	
	return nil
}
