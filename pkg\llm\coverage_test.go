package llm

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestConfig 测试Config结构和验证
func TestConfig(t *testing.T) {
	// 测试有效配置
	validConfig := &Config{
		Provider:   "deepseek",
		APIKey:     "test-api-key",
		BaseURL:    "https://api.deepseek.com/v1",
		Model:      "deepseek-chat",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		Parameters: map[string]interface{}{
			"temperature": 0.7,
			"max_tokens":  1000,
		},
	}

	assert.Equal(t, "deepseek", validConfig.Provider)
	assert.Equal(t, "test-api-key", validConfig.APIKey)
	assert.Equal(t, "https://api.deepseek.com/v1", validConfig.BaseURL)
	assert.Equal(t, "deepseek-chat", validConfig.Model)
	assert.Equal(t, 30*time.Second, validConfig.Timeout)
	assert.Equal(t, 3, validConfig.MaxRetries)
	assert.NotNil(t, validConfig.Parameters)

	// 测试默认配置
	defaultConfig := DefaultConfig()
	assert.NotNil(t, defaultConfig)
	assert.Equal(t, "deepseek", defaultConfig.Provider)
	assert.Equal(t, "deepseek-chat", defaultConfig.Model)
	assert.Equal(t, 30*time.Second, defaultConfig.Timeout)
	assert.Equal(t, 3, defaultConfig.MaxRetries)
}

// TestErrorInfo 测试错误信息结构
func TestErrorInfo(t *testing.T) {
	errorInfo := &ErrorInfo{
		Message: "API rate limit exceeded",
		Type:    "rate_limit_error",
		Code:    "rate_limit_exceeded",
	}

	assert.Equal(t, "API rate limit exceeded", errorInfo.Message)
	assert.Equal(t, "rate_limit_error", errorInfo.Type)
	assert.Equal(t, "rate_limit_exceeded", errorInfo.Code)
}

// TestToolCallFunction 测试工具调用函数结构
func TestToolCallFunction(t *testing.T) {
	toolCallFunc := &ToolCallFunction{
		Name:      "get_weather",
		Arguments: `{"location": "San Francisco"}`,
	}

	assert.Equal(t, "get_weather", toolCallFunc.Name)
	assert.Equal(t, `{"location": "San Francisco"}`, toolCallFunc.Arguments)
}

// TestToolCall 测试工具调用结构
func TestToolCall(t *testing.T) {
	toolCall := &ToolCall{
		ID:   "call-123",
		Type: "function",
		Function: &ToolCallFunction{
			Name:      "get_weather",
			Arguments: `{"location": "San Francisco"}`,
		},
	}

	assert.Equal(t, "call-123", toolCall.ID)
	assert.Equal(t, "function", toolCall.Type)
	assert.NotNil(t, toolCall.Function)
	assert.Equal(t, "get_weather", toolCall.Function.Name)
	assert.Equal(t, `{"location": "San Francisco"}`, toolCall.Function.Arguments)
}

// TestToolFunctionDefinition 测试工具函数定义结构
func TestToolFunctionDefinition(t *testing.T) {
	funcDef := &ToolFunctionDefinition{
		Name:        "calculate",
		Description: "Perform calculation",
		Parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"expression": map[string]interface{}{
					"type":        "string",
					"description": "Mathematical expression",
				},
			},
		},
	}

	assert.Equal(t, "calculate", funcDef.Name)
	assert.Equal(t, "Perform calculation", funcDef.Description)
	assert.NotNil(t, funcDef.Parameters)

	// 验证参数结构
	params, ok := funcDef.Parameters["properties"]
	assert.True(t, ok)
	assert.NotNil(t, params)
}

// TestToolDefinition 测试工具定义结构
func TestToolDefinitionStruct(t *testing.T) {
	tool := &ToolDefinition{
		Type: "function",
		Function: &ToolFunctionDefinition{
			Name:        "get_weather",
			Description: "Get current weather",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"location": map[string]interface{}{
						"type":        "string",
						"description": "The city name",
					},
				},
				"required": []string{"location"},
			},
		},
	}

	assert.Equal(t, "function", tool.Type)
	assert.NotNil(t, tool.Function)
	assert.Equal(t, "get_weather", tool.Function.Name)
	assert.Equal(t, "Get current weather", tool.Function.Description)
	assert.NotNil(t, tool.Function.Parameters)
}

// TestModelInfo 测试模型信息结构
func TestModelInfo(t *testing.T) {
	info := &ModelInfo{
		ID:          "test-model",
		Object:      "model",
		Created:     time.Now().Unix(),
		OwnedBy:     "test-provider",
		MaxTokens:   4096,
		Description: "Test model for unit testing",
	}

	assert.Equal(t, "test-model", info.ID)
	assert.Equal(t, "model", info.Object)
	assert.Equal(t, "test-provider", info.OwnedBy)
	assert.Equal(t, 4096, info.MaxTokens)
	assert.Equal(t, "Test model for unit testing", info.Description)
}

// TestMultiModalContent 测试多模态内容结构
func TestMultiModalContent(t *testing.T) {
	// 测试文本内容
	textContent := &MultiModalContent{
		Type: "text",
		Text: "Describe this image",
	}
	assert.Equal(t, "text", textContent.Type)
	assert.Equal(t, "Describe this image", textContent.Text)

	// 测试图片内容
	imageContent := &MultiModalContent{
		Type: "image_url",
		ImageURL: &ImageURL{
			URL:    "https://example.com/image.jpg",
			Detail: "high",
		},
	}
	assert.Equal(t, "image_url", imageContent.Type)
	assert.NotNil(t, imageContent.ImageURL)
	assert.Equal(t, "https://example.com/image.jpg", imageContent.ImageURL.URL)
	assert.Equal(t, "high", imageContent.ImageURL.Detail)

	// 测试音频内容
	audioContent := &MultiModalContent{
		Type: "audio_url",
		AudioURL: &AudioURL{
			URL:    "https://example.com/audio.mp3",
			Format: "mp3",
		},
	}
	assert.Equal(t, "audio_url", audioContent.Type)
	assert.NotNil(t, audioContent.AudioURL)
	assert.Equal(t, "https://example.com/audio.mp3", audioContent.AudioURL.URL)
	assert.Equal(t, "mp3", audioContent.AudioURL.Format)

	// 测试视频内容
	videoContent := &MultiModalContent{
		Type: "video_url",
		VideoURL: &VideoURL{
			URL:    "https://example.com/video.mp4",
			Format: "mp4",
		},
	}
	assert.Equal(t, "video_url", videoContent.Type)
	assert.NotNil(t, videoContent.VideoURL)
	assert.Equal(t, "https://example.com/video.mp4", videoContent.VideoURL.URL)
	assert.Equal(t, "mp4", videoContent.VideoURL.Format)

	// 测试文件内容
	fileContent := &MultiModalContent{
		Type: "file_url",
		FileURL: &FileURL{
			URL:      "https://example.com/document.pdf",
			FileName: "document.pdf",
			MimeType: "application/pdf",
		},
	}
	assert.Equal(t, "file_url", fileContent.Type)
	assert.NotNil(t, fileContent.FileURL)
	assert.Equal(t, "https://example.com/document.pdf", fileContent.FileURL.URL)
	assert.Equal(t, "document.pdf", fileContent.FileURL.FileName)
	assert.Equal(t, "application/pdf", fileContent.FileURL.MimeType)
}

// TestMultiModalMessage 测试多模态消息结构
func TestMultiModalMessage(t *testing.T) {
	message := &MultiModalMessage{
		Role: "user",
		Content: []MultiModalContent{
			{
				Type: "text",
				Text: "What's in this image?",
			},
			{
				Type: "image_url",
				ImageURL: &ImageURL{
					URL: "https://example.com/image.jpg",
				},
			},
		},
	}

	assert.Equal(t, "user", message.Role)
	assert.Len(t, message.Content, 2)

	// 验证文本内容
	textContent := message.Content[0]
	assert.Equal(t, "text", textContent.Type)
	assert.Equal(t, "What's in this image?", textContent.Text)

	// 验证图片内容
	imageContent := message.Content[1]
	assert.Equal(t, "image_url", imageContent.Type)
	assert.NotNil(t, imageContent.ImageURL)
	assert.Equal(t, "https://example.com/image.jpg", imageContent.ImageURL.URL)
}

// TestChoice 测试选择结构
func TestChoice(t *testing.T) {
	choice := &Choice{
		Index: 0,
		Message: &ChatMessage{
			Role:    "assistant",
			Content: "Hello, world!",
		},
		FinishReason: "stop",
		Logprobs:     nil,
	}

	assert.Equal(t, 0, choice.Index)
	assert.NotNil(t, choice.Message)
	assert.Equal(t, "assistant", choice.Message.Role)
	assert.Equal(t, "Hello, world!", choice.Message.Content)
	assert.Equal(t, "stop", choice.FinishReason)
	assert.Nil(t, choice.Logprobs)
}

// TestStreamChoice 测试流式选择结构
func TestStreamChoice(t *testing.T) {
	streamChoice := &StreamChoice{
		Index: 0,
		Delta: &ChatMessage{
			Role:    "assistant",
			Content: "Hello",
		},
		FinishReason: "",
		Logprobs:     nil,
	}

	assert.Equal(t, 0, streamChoice.Index)
	assert.NotNil(t, streamChoice.Delta)
	assert.Equal(t, "assistant", streamChoice.Delta.Role)
	assert.Equal(t, "Hello", streamChoice.Delta.Content)
	assert.Equal(t, "", streamChoice.FinishReason)
	assert.Nil(t, streamChoice.Logprobs)
}
