package audit

import (
	"context"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// AuditInterceptor 审计拦截器
type AuditInterceptor struct {
	writer    *AuditWriter
	config    *Config
	mu        sync.RWMutex
	enabled   bool
	sessionID string
}

// NewAuditInterceptor 创建审计拦截器
func NewAuditInterceptor(writer *AuditWriter, config *Config) *AuditInterceptor {
	return &AuditInterceptor{
		writer:  writer,
		config:  config,
		enabled: config != nil && config.Enabled,
	}
}

// SetSessionID 设置会话ID
func (i *AuditInterceptor) SetSessionID(sessionID string) {
	i.mu.Lock()
	defer i.mu.Unlock()
	i.sessionID = sessionID
}

// GetSessionID 获取会话ID
func (i *AuditInterceptor) GetSessionID() string {
	i.mu.RLock()
	defer i.mu.RUnlock()
	return i.sessionID
}

// Enable 启用拦截器
func (i *AuditInterceptor) Enable() {
	i.mu.Lock()
	defer i.mu.Unlock()
	i.enabled = true
}

// Disable 禁用拦截器
func (i *AuditInterceptor) Disable() {
	i.mu.Lock()
	defer i.mu.Unlock()
	i.enabled = false
}

// IsEnabled 检查是否启用
func (i *AuditInterceptor) IsEnabled() bool {
	i.mu.RLock()
	defer i.mu.RUnlock()
	return i.enabled
}

// Intercept 拦截器函数
func (i *AuditInterceptor) Intercept(
	ctx context.Context,
	in *runtime.Input,
	next func(context.Context, *runtime.Input) *runtime.AsyncIterator[*event.Event],
) *runtime.AsyncIterator[*event.Event] {
	if !i.IsEnabled() || i.writer == nil {
		return next(ctx, in)
	}

	// 获取会话ID
	sessionID := i.GetSessionID()
	if sessionID == "" {
		sessionID = GenerateSessionID()
		i.SetSessionID(sessionID)
	}

	// 记录输入消息
	i.recordInputMessages(ctx, sessionID, in)

	// 执行下一个拦截器或Agent
	iterator := next(ctx, in)

	// 包装迭代器以监听事件
	return i.wrapIterator(ctx, sessionID, iterator)
}

// recordInputMessages 记录输入消息
func (i *AuditInterceptor) recordInputMessages(ctx context.Context, sessionID string, in *runtime.Input) {
	if in == nil || len(in.Messages) == 0 {
		return
	}

	for _, msg := range in.Messages {
		if msg == nil {
			continue
		}

		// 构建审计记录
		record := &Record{
			ID:        GenerateID(),
			SessionID: sessionID,
			UserID:    i.extractUserID(msg),
			Role:      "user",
			MsgType:   "text",
			Content:   i.extractMessageContent(msg),
			CreatedAt: time.Now(),
		}

		// 写入记录
		if err := i.writer.WriteRecord(ctx, record); err != nil {
			// 记录错误，但不影响主流程
			_ = err
		}
	}
}

// wrapIterator 包装迭代器以监听事件
func (i *AuditInterceptor) wrapIterator(
	ctx context.Context,
	sessionID string,
	iterator *runtime.AsyncIterator[*event.Event],
) *runtime.AsyncIterator[*event.Event] {
	// 创建新的通道
	ch := make(chan *event.Event, 100)

	// 启动协程处理事件
	go func() {
		defer close(ch)

		for {
			ev, ok := iterator.Next()
			if !ok {
				break
			}

			// 记录事件
			i.recordEvent(ctx, sessionID, ev)

			// 转发事件
			select {
			case ch <- ev:
			case <-ctx.Done():
				return
			}
		}
	}()

	pair := runtime.NewAsyncIterator[*event.Event](ctx, 100)

	// 将现有通道的数据转发到新的生成器
	go func() {
		defer pair.Generator.Close()
		for ev := range ch {
			if err := pair.Generator.Send(ev); err != nil {
				break
			}
		}
	}()

	return pair.Iterator
}

// recordEvent 记录事件
func (i *AuditInterceptor) recordEvent(ctx context.Context, sessionID string, ev *event.Event) {
	if ev == nil {
		return
	}

	var record *Record

	switch ev.Type {
	case event.EventToken:
		if !i.shouldRecordTokens() {
			return // 跳过token事件
		}
		record = i.buildTokenRecord(sessionID, ev)

	case event.EventFinal:
		record = i.buildFinalRecord(sessionID, ev)

	case event.EventError:
		record = i.buildErrorRecord(sessionID, ev)

	case event.EventToolCall:
		record = i.buildToolCallRecord(sessionID, ev)

	case event.EventToolResult:
		record = i.buildToolResultRecord(sessionID, ev)

	default:
		// 其他事件类型暂不记录
		return
	}

	if record != nil {
		// 写入记录
		if err := i.writer.WriteRecord(ctx, record); err != nil {
			// 记录错误，但不影响主流程
			_ = err
		}
	}
}

// shouldRecordTokens 检查是否应该记录token事件
func (i *AuditInterceptor) shouldRecordTokens() bool {
	// 默认不记录token事件，只记录final事件
	// 可以通过配置控制
	return false
}

// buildTokenRecord 构建token记录
func (i *AuditInterceptor) buildTokenRecord(sessionID string, ev *event.Event) *Record {
	content := i.extractEventContent(ev)

	return &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		AgentID:   i.extractAgentID(ev),
		Role:      "assistant",
		MsgType:   "token",
		Content:   content,
		EventType: string(ev.Type),
		CreatedAt: ev.At,
	}
}

// buildFinalRecord 构建final记录
func (i *AuditInterceptor) buildFinalRecord(sessionID string, ev *event.Event) *Record {
	content := i.extractEventContent(ev)

	return &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		AgentID:   i.extractAgentID(ev),
		Role:      "assistant",
		MsgType:   "final",
		Content:   content,
		EventType: string(ev.Type),
		CreatedAt: ev.At,
	}
}

// buildErrorRecord 构建error记录
func (i *AuditInterceptor) buildErrorRecord(sessionID string, ev *event.Event) *Record {
	content := i.extractEventContent(ev)
	errorCode := i.extractErrorCode(ev)

	return &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		AgentID:   i.extractAgentID(ev),
		Role:      "assistant",
		MsgType:   "error",
		Content:   content,
		EventType: string(ev.Type),
		ErrorCode: errorCode,
		CreatedAt: ev.At,
	}
}

// buildToolCallRecord 构建tool_call记录
func (i *AuditInterceptor) buildToolCallRecord(sessionID string, ev *event.Event) *Record {
	content := i.extractEventContent(ev)
	toolName := i.extractToolName(ev)

	return &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		AgentID:   i.extractAgentID(ev),
		Role:      "assistant",
		MsgType:   "tool_result",
		Content:   content,
		EventType: string(ev.Type),
		ToolName:  toolName,
		CreatedAt: ev.At,
	}
}

// buildToolResultRecord 构建tool_result记录
func (i *AuditInterceptor) buildToolResultRecord(sessionID string, ev *event.Event) *Record {
	content := i.extractEventContent(ev)
	toolName := i.extractToolName(ev)

	return &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		Role:      "tool",
		MsgType:   "tool_result",
		Content:   content,
		EventType: string(ev.Type),
		ToolName:  toolName,
		CreatedAt: ev.At,
	}
}

// extractEventContent 提取事件内容
func (i *AuditInterceptor) extractEventContent(ev *event.Event) string {
	if ev.Data == nil {
		return ""
	}

	// 尝试从Data中提取内容
	if contentGetter, ok := ev.Data.(interface{ GetContent() string }); ok {
		return contentGetter.GetContent()
	}

	// 如果是map类型，尝试获取content字段
	if dataMap, ok := ev.Data.(map[string]interface{}); ok {
		if content, exists := dataMap["content"]; exists {
			if contentStr, ok := content.(string); ok {
				return contentStr
			}
		}
	}

	return ""
}

// extractAgentID 提取Agent ID
func (i *AuditInterceptor) extractAgentID(ev *event.Event) string {
	// 从事件元数据或上下文中提取Agent ID
	// 这里需要根据实际的事件结构来实现
	return "unknown_agent"
}

// extractUserID 提取用户ID
func (i *AuditInterceptor) extractUserID(msg interface{}) string {
	// 尝试从消息元数据中提取用户ID
	if msgObj, ok := msg.(interface{ GetMetadata(string) (any, bool) }); ok {
		if userID, exists := msgObj.GetMetadata("user_id"); exists {
			if userIDStr, ok := userID.(string); ok {
				return userIDStr
			}
		}
	}
	// 尝试从map类型中提取
	if msgMap, ok := msg.(map[string]interface{}); ok {
		if userID, exists := msgMap["user_id"]; exists {
			if userIDStr, ok := userID.(string); ok {
				return userIDStr
			}
		}
		if metadata, exists := msgMap["metadata"]; exists {
			if metaMap, ok := metadata.(map[string]interface{}); ok {
				if userID, exists := metaMap["user_id"]; exists {
					if userIDStr, ok := userID.(string); ok {
						return userIDStr
					}
				}
			}
		}
	}
	// 默认返回匿名用户
	return "anonymous"
}

// extractErrorCode 提取错误代码
func (i *AuditInterceptor) extractErrorCode(ev *event.Event) string {
	if ev.Err != nil {
		return ev.Err.Error()
	}
	return ""
}

// extractToolName 提取工具名称
func (i *AuditInterceptor) extractToolName(ev *event.Event) string {
	if ev.Data == nil {
		return ""
	}

	// 尝试从Data中提取工具名称
	if dataMap, ok := ev.Data.(map[string]interface{}); ok {
		if toolName, exists := dataMap["tool_name"]; exists {
			if toolNameStr, ok := toolName.(string); ok {
				return toolNameStr
			}
		}
		if name, exists := dataMap["name"]; exists {
			if nameStr, ok := name.(string); ok {
				return nameStr
			}
		}
	}

	return ""
}

// extractMessageContent 从消息中提取内容
func (i *AuditInterceptor) extractMessageContent(msg interface{}) string {
	// 首先尝试message.Message类型
	if msgObj, ok := msg.(interface{ GetContentString() string }); ok {
		return msgObj.GetContentString()
	}
	// 兼容旧的GetContent接口
	if msgObj, ok := msg.(interface{ GetContent() string }); ok {
		return msgObj.GetContent()
	}
	// 尝试map类型
	if msgMap, ok := msg.(map[string]interface{}); ok {
		if content, exists := msgMap["content"]; exists {
			if contentStr, ok := content.(string); ok {
				return contentStr
			}
		}
	}
	// 尝试直接字符串类型
	if str, ok := msg.(string); ok {
		return str
	}
	return ""
}

// RuntimeInterceptorAdapter 将AuditInterceptor适配为runtime.Interceptor接口
type RuntimeInterceptorAdapter struct {
	auditInterceptor *AuditInterceptor
	name             string
}

// NewRuntimeInterceptorAdapter 创建运行时拦截器适配器
func NewRuntimeInterceptorAdapter(auditInterceptor *AuditInterceptor, name string) *RuntimeInterceptorAdapter {
	if name == "" {
		name = "audit-interceptor"
	}
	return &RuntimeInterceptorAdapter{
		auditInterceptor: auditInterceptor,
		name:             name,
	}
}

// Name 返回拦截器名称
func (a *RuntimeInterceptorAdapter) Name() string {
	return a.name
}

// BeforeRun 在Agent运行前调用
func (a *RuntimeInterceptorAdapter) BeforeRun(ctx context.Context, agentName string, input any) error {
	// 将input转换为*runtime.Input类型
	if runtimeInput, ok := input.(*runtime.Input); ok {
		// 记录输入消息
		sessionID := a.auditInterceptor.GetSessionID()
		if sessionID == "" {
			sessionID = GenerateSessionID()
			a.auditInterceptor.SetSessionID(sessionID)
		}
		a.auditInterceptor.recordInputMessages(ctx, sessionID, runtimeInput)
	}
	return nil
}

// AfterRun 在Agent运行后调用
func (a *RuntimeInterceptorAdapter) AfterRun(ctx context.Context, agentName string, input any, err error) error {
	// 可以在这里记录运行结果或错误
	if err != nil {
		return a.OnError(ctx, agentName, err)
	}
	return nil
}

// OnEvent 在事件产生时调用
func (a *RuntimeInterceptorAdapter) OnEvent(ctx context.Context, agentName string, ev *event.Event) error {
	if !a.auditInterceptor.IsEnabled() || a.auditInterceptor.writer == nil {
		return nil
	}

	sessionID := a.auditInterceptor.GetSessionID()
	if sessionID == "" {
		sessionID = GenerateSessionID()
		a.auditInterceptor.SetSessionID(sessionID)
	}

	// 记录事件
	a.auditInterceptor.recordEvent(ctx, sessionID, ev)
	return nil
}

// OnError 在发生错误时调用
func (a *RuntimeInterceptorAdapter) OnError(ctx context.Context, agentName string, err error) error {
	if !a.auditInterceptor.IsEnabled() || a.auditInterceptor.writer == nil {
		return nil
	}

	sessionID := a.auditInterceptor.GetSessionID()
	if sessionID == "" {
		sessionID = GenerateSessionID()
		a.auditInterceptor.SetSessionID(sessionID)
	}

	// 构建错误记录
	record := &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		AgentID:   agentName,
		Role:      "system",
		MsgType:   "error",
		Content:   err.Error(),
		EventType: "error",
		ErrorCode: err.Error(),
		CreatedAt: time.Now(),
	}

	// 写入记录
	if writeErr := a.auditInterceptor.writer.WriteRecord(ctx, record); writeErr != nil {
		// 记录错误，但不影响主流程
		_ = writeErr
	}

	return nil
}
