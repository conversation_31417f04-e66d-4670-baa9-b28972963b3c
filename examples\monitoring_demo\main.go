package main

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/monitoring"
	agentruntime "github.com/agentscope/agentscope-golang/pkg/runtime"
)

// MonitoringInterceptor 监控拦截器
type MonitoringInterceptor struct {
	collector          *monitoring.MetricsCollector
	executionStarted   *monitoring.Counter
	executionCompleted *monitoring.Counter
	executionFailed    *monitoring.Counter
	eventsCounter      *monitoring.Counter
	errorsCounter      *monitoring.Counter
}

// NewMonitoringInterceptor 创建监控拦截器
func NewMonitoringInterceptor() *MonitoringInterceptor {
	collector := monitoring.NewMetricsCollector(logger.GetGlobalLogger())

	// 注册计数器
	executionStarted := collector.RegisterCounter("agent_execution_started", "Agent execution started count", nil)
	executionCompleted := collector.RegisterCounter("agent_execution_completed", "Agent execution completed count", nil)
	executionFailed := collector.RegisterCounter("agent_execution_failed", "Agent execution failed count", nil)
	eventsCounter := collector.RegisterCounter("agent_events", "Agent events count", nil)
	errorsCounter := collector.RegisterCounter("agent_errors", "Agent errors count", nil)

	return &MonitoringInterceptor{
		collector:          collector,
		executionStarted:   executionStarted,
		executionCompleted: executionCompleted,
		executionFailed:    executionFailed,
		eventsCounter:      eventsCounter,
		errorsCounter:      errorsCounter,
	}
}

// Name 返回拦截器名称
func (m *MonitoringInterceptor) Name() string {
	return "monitoring"
}

// BeforeRun 在Agent运行前调用
func (m *MonitoringInterceptor) BeforeRun(ctx context.Context, agentName string, input any) error {
	fmt.Printf("[监控] Agent %s 开始执行\n", agentName)

	// 记录执行开始指标
	m.executionStarted.Inc()

	return nil
}

// AfterRun 在Agent运行后调用
func (m *MonitoringInterceptor) AfterRun(ctx context.Context, agentName string, input any, err error) error {
	if err != nil {
		fmt.Printf("[监控] Agent %s 执行失败: %v\n", agentName, err)
		m.executionFailed.Inc()
	} else {
		fmt.Printf("[监控] Agent %s 执行完成\n", agentName)
		m.executionCompleted.Inc()
	}

	return nil
}

// OnEvent 在事件产生时调用
func (m *MonitoringInterceptor) OnEvent(ctx context.Context, agentName string, ev *event.Event) error {
	fmt.Printf("[监控] Agent %s 产生事件: %s\n", agentName, ev.Type)

	// 记录事件指标
	m.eventsCounter.Inc()

	return nil
}

// OnError 在发生错误时调用
func (m *MonitoringInterceptor) OnError(ctx context.Context, agentName string, err error) error {
	fmt.Printf("[监控] Agent %s 发生错误: %v\n", agentName, err)

	// 记录错误指标
	m.errorsCounter.Inc()

	return nil
}

// GetMetrics 获取收集的指标
func (m *MonitoringInterceptor) GetMetrics() map[string]*monitoring.Metric {
	return m.collector.GetMetrics()
}

// MockAgent 模拟智能体用于演示
type MockAgent struct {
	name        string
	description string
	response    string
	shouldError bool
}

// NewMockAgent 创建模拟智能体
func NewMockAgent(name, description, response string, shouldError bool) *MockAgent {
	return &MockAgent{
		name:        name,
		description: description,
		response:    response,
		shouldError: shouldError,
	}
}

// Name 返回智能体名称
func (m *MockAgent) Name(ctx context.Context) string {
	return m.name
}

// Description 返回智能体描述
func (m *MockAgent) Description(ctx context.Context) string {
	return m.description
}

// Run 运行智能体
func (m *MockAgent) Run(ctx context.Context, input *agent.Input) *agentruntime.AsyncIterator[*event.Event] {
	pair := agentruntime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 模拟处理时间
		time.Sleep(100 * time.Millisecond)

		if m.shouldError {
			// 发送错误事件
			errorEvent := &event.Event{
				Type: event.EventError,
				Data: &event.ErrorData{
					Message: "模拟错误",
				},
			}
			pair.Generator.Send(errorEvent)
		} else {
			// 发送思考事件
			thoughtEvent := &event.Event{
				Type: event.EventThought,
				Data: &event.ThoughtData{
					Content: fmt.Sprintf("智能体 %s 正在思考", m.name),
				},
			}
			pair.Generator.Send(thoughtEvent)

			// 发送最终结果事件
			finalEvent := &event.Event{
				Type: event.EventFinal,
				Data: &event.FinalData{
					Content: fmt.Sprintf("[%s] %s", m.name, m.response),
				},
			}
			pair.Generator.Send(finalEvent)
		}
	}()

	return pair.Iterator
}

func main() {
	fmt.Println("=== AgentScope-Golang 监控演示 ===")
	fmt.Println("这个示例展示了如何使用拦截器进行监控和指标收集")
	fmt.Println()

	ctx := context.Background()

	// 1. 创建监控拦截器
	fmt.Println("1. 创建监控拦截器...")
	monitoringInterceptor := NewMonitoringInterceptor()

	// 2. 创建拦截器链
	fmt.Println("2. 创建拦截器链...")
	interceptorChain := agentruntime.NewInterceptorChain()
	interceptorChain.Add(monitoringInterceptor)

	// 3. 创建模拟智能体
	fmt.Println("3. 创建模拟智能体...")
	agents := []*MockAgent{
		NewMockAgent("分析师", "数据分析专家", "分析完成", false),
		NewMockAgent("设计师", "界面设计专家", "设计完成", false),
		NewMockAgent("开发者", "代码实现专家", "开发完成", false),
		NewMockAgent("测试员", "质量测试专家", "测试失败", true), // 模拟错误
	}

	// 4. 创建记忆存储（暂时不使用）
	// memoryStore := memory.NewMemoryStore()

	fmt.Println("4. 开始执行智能体并收集监控数据...")
	fmt.Println()

	// 执行每个智能体
	for i, ag := range agents {
		fmt.Printf("=== 执行智能体 %d: %s ===\n", i+1, ag.Name(ctx))

		// 创建输入
		input := agentruntime.NewInput().AddMessage(message.NewUserMessage(fmt.Sprintf("请执行任务 %d", i+1)))

		// 执行前拦截
		err := interceptorChain.BeforeRun(ctx, ag.Name(ctx), input)
		if err != nil {
			fmt.Printf("BeforeRun拦截器错误: %v\n", err)
			continue
		}

		// 运行智能体
		iterator := ag.Run(ctx, input)
		var executionError error

		// 处理事件
		for {
			event, ok := iterator.Next()
			if !ok {
				break
			}

			// 事件拦截
			interceptorChain.OnEvent(ctx, ag.Name(ctx), event)

			// 处理事件
			switch event.Type {
			case "thought":
				fmt.Printf("  💭 思考事件\n")
			case "final":
				fmt.Printf("  ✅ 完成事件\n")
			case "error":
				fmt.Printf("  ❌ 错误事件\n")
				executionError = fmt.Errorf("agent execution error")
			default:
				fmt.Printf("  📝 事件: %s\n", event.Type)
			}
		}

		// 执行后拦截
		err = interceptorChain.AfterRun(ctx, ag.Name(ctx), input, executionError)
		if err != nil {
			fmt.Printf("AfterRun拦截器错误: %v\n", err)
		}

		// 错误拦截
		if executionError != nil {
			interceptorChain.OnError(ctx, ag.Name(ctx), executionError)
		}

		fmt.Println()
		time.Sleep(200 * time.Millisecond) // 模拟间隔
	}

	// 5. 显示监控指标
	fmt.Println("=== 监控指标报告 ===")
	metrics := monitoringInterceptor.GetMetrics()

	if len(metrics) == 0 {
		fmt.Println("没有收集到指标数据")
	} else {
		for name, metric := range metrics {
			fmt.Printf("指标: %s\n", name)
			fmt.Printf("  类型: %s\n", metric.Type)
			fmt.Printf("  值: %.2f\n", metric.Value)
			fmt.Printf("  标签: %v\n", metric.Labels)
			fmt.Printf("  时间: %s\n", metric.Timestamp.Format("2006-01-02 15:04:05"))
			fmt.Println()
		}
	}

	// 6. 系统指标演示
	fmt.Println("=== 系统指标演示 ===")

	// 创建系统指标收集器
	systemCollector := monitoring.NewMetricsCollector(logger.GetGlobalLogger())

	// 收集系统指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 注册并设置系统指标
	memoryGauge := systemCollector.RegisterGauge("memory_alloc_bytes", "Memory allocation in bytes", map[string]string{"type": "heap"})
	memoryGauge.Set(float64(m.Alloc))

	sysGauge := systemCollector.RegisterGauge("memory_sys_bytes", "System memory in bytes", map[string]string{"type": "system"})
	sysGauge.Set(float64(m.Sys))

	goroutinesGauge := systemCollector.RegisterGauge("goroutines_count", "Number of goroutines", nil)
	goroutinesGauge.Set(float64(runtime.NumGoroutine()))

	// 显示系统指标
	systemMetrics := systemCollector.GetMetrics()
	for name, metric := range systemMetrics {
		fmt.Printf("系统指标: %s = %.2f %s\n", name, metric.Value, metric.Unit)
	}

	fmt.Println()
	fmt.Println("=== 监控演示完成 ===")
	fmt.Println("✅ 拦截器链: 成功拦截Agent执行过程")
	fmt.Println("✅ 指标收集: 成功收集执行指标和事件指标")
	fmt.Println("✅ 错误监控: 成功捕获和记录错误")
	fmt.Println("✅ 系统监控: 成功收集系统资源指标")
}
