# 简单聊天示例 (ADK架构)

这个示例展示了如何使用新的AgentScope-Golang ADK架构创建一个具有工具和记忆功能的聊天智能体。

## 功能特性

- **事件驱动架构**: 使用新的事件驱动模式处理智能体输出
- **工具集成**: 集成HTTP请求工具，可以获取网络信息
- **记忆管理**: 自动保存和加载对话历史
- **流式输出**: 支持实时流式响应
- **错误处理**: 完整的错误处理和重试机制

## 前置要求

1. **Go 1.21+**: 确保安装了Go 1.21或更高版本
2. **DeepSeek API密钥**: 需要有效的DeepSeek API密钥

## 环境设置

设置DeepSeek API密钥：

```bash
export DEEPSEEK_API_KEY="your_deepseek_api_key_here"
```

## 运行示例

```bash
# 进入示例目录
cd examples/simple_chat_adk

# 运行示例
go run main.go
```

## 使用说明

### 基本对话

启动程序后，您可以直接与智能体对话：

```
用户: 你好，请介绍一下自己
助手: 你好！我是一个友好的AI助手，可以帮助您回答问题和执行任务...

用户: 今天天气怎么样？
助手: 我可以帮您查询天气信息，但我需要知道您所在的城市...
```

### 工具使用

智能体可以使用HTTP请求工具获取网络信息：

```
用户: 请帮我查询一下百度首页的内容
助手: [正在使用工具: http_request]
我来为您查询百度首页的内容...
```

### 特殊命令

- `/help` - 显示帮助信息
- `/memory` - 显示当前对话记忆
- `/clear` - 清空对话记忆
- `quit` 或 `exit` - 退出程序

### 记忆管理

智能体会自动记住对话历史：

```
用户: /memory
当前对话记忆 (共4条消息):
--------------------------------------------------
1. [user] 你好，请介绍一下自己
2. [assistant] 你好！我是一个友好的AI助手...
3. [user] 我的名字是张三
4. [assistant] 很高兴认识您，张三！...
--------------------------------------------------

用户: 你还记得我的名字吗？
助手: 当然记得！您的名字是张三。
```

## 架构说明

### 核心组件

1. **ChatModelAgent**: 基于大语言模型的智能体实现
2. **DeepSeekChatModel**: DeepSeek API的聊天模型适配器
3. **MemoryStore**: 内存中的对话记忆存储
4. **HTTPRequestTool**: HTTP请求工具实现

### 事件流处理

智能体使用事件驱动架构，支持以下事件类型：

- `stream`: 流式输出内容
- `final`: 最终响应结果
- `error`: 错误信息
- `tool_call`: 工具调用通知

### 工具集成

示例集成了HTTP请求工具，智能体可以：

- 发送GET/POST请求
- 获取网页内容
- 处理API响应
- 解析JSON数据

## 扩展示例

### 添加自定义工具

```go
// 创建自定义工具
type CustomTool struct{}

func (t *CustomTool) Name() string {
    return "custom_tool"
}

func (t *CustomTool) Description() string {
    return "自定义工具示例"
}

func (t *CustomTool) Schema() *newtool.JSONSchema {
    return &newtool.JSONSchema{
        Type: "object",
        Properties: map[string]*newtool.JSONSchema{
            "input": {
                Type:        "string",
                Description: "输入参数",
            },
        },
        Required: []string{"input"},
    }
}

func (t *CustomTool) Execute(ctx context.Context, input string) (string, error) {
    // 实现工具逻辑
    return "工具执行结果", nil
}

// 注册工具
tools := []newtool.Tool{
    httprequest.NewHTTPRequestTool(),
    &CustomTool{},
}
```

### 使用不同的记忆存储

```go
// 使用SQLite存储（需要在非Windows环境）
sqliteStore, err := memory.NewSQLiteStore(&memory.SQLiteConfig{
    DatabasePath: "./chat_memory.db",
})
if err != nil {
    log.Fatal(err)
}

// 使用Postgres存储
postgresStore, err := memory.NewPostgresStore(&memory.PostgresConfig{
    Host:     "localhost",
    Port:     5432,
    Database: "agentscope",
    Username: "postgres",
    Password: "password",
})
if err != nil {
    log.Fatal(err)
}
```

## 故障排查

### 常见问题

1. **API密钥错误**
   ```
   错误: 请设置 DEEPSEEK_API_KEY 环境变量
   ```
   解决方案: 确保正确设置了DeepSeek API密钥

2. **网络连接问题**
   ```
   错误: 连接DeepSeek API失败
   ```
   解决方案: 检查网络连接和API服务状态

3. **工具执行失败**
   ```
   错误: HTTP请求工具执行失败
   ```
   解决方案: 检查目标URL是否可访问

### 调试模式

可以通过环境变量启用调试模式：

```bash
export LOG_LEVEL=DEBUG
go run main.go
```

## 相关文档

- [架构设计文档](../../docs/architecture-design.md)
- [工具开发指南](../../docs/tool-development.md)
- [记忆管理文档](../../docs/memory-management.md)

## 许可证

本示例遵循项目的开源许可证。
