package llm

import (
	"context"
	"math"
	"math/rand"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// RetryConfig holds retry configuration
type RetryConfig struct {
	MaxRetries      int           `yaml:"max_retries" json:"max_retries"`
	InitialDelay    time.Duration `yaml:"initial_delay" json:"initial_delay"`
	MaxDelay        time.Duration `yaml:"max_delay" json:"max_delay"`
	BackoffFactor   float64       `yaml:"backoff_factor" json:"backoff_factor"`
	RetryableErrors []string      `yaml:"retryable_errors" json:"retryable_errors"`
}

// DefaultRetryConfig returns a default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		RetryableErrors: []string{
			CodeRequestTimeout,
			CodeConnectionFailed,
			CodeServiceUnavailable,
			CodeLLMQuotaExceeded,
			CodeRequestFailed,
		},
	}
}

// Validate validates the retry configuration
func (rc *RetryConfig) Validate() error {
	if rc.MaxRetries < 0 {
		rc.MaxRetries = 3
	}
	if rc.InitialDelay <= 0 {
		rc.InitialDelay = 1 * time.Second
	}
	if rc.MaxDelay <= 0 {
		rc.MaxDelay = 30 * time.Second
	}
	if rc.BackoffFactor <= 0 {
		rc.BackoffFactor = 2.0
	}
	if rc.RetryableErrors == nil {
		rc.RetryableErrors = DefaultRetryConfig().RetryableErrors
	}
	return nil
}

// RetryableClient wraps an LLMClient with retry functionality
type RetryableClient struct {
	client      LLMClient
	retryConfig *RetryConfig
	logger      logger.Logger
}

// NewRetryableClient creates a new retryable client
func NewRetryableClient(client LLMClient, retryConfig *RetryConfig) *RetryableClient {
	if retryConfig == nil {
		retryConfig = DefaultRetryConfig()
	}

	if err := retryConfig.Validate(); err != nil {
		// Use default config if validation fails
		retryConfig = DefaultRetryConfig()
	}

	return &RetryableClient{
		client:      client,
		retryConfig: retryConfig,
		logger:      logger.GetGlobalLogger(),
	}
}

// Generate generates a response with retry logic
func (rc *RetryableClient) Generate(ctx context.Context, request *GenerateRequest) (*GenerateResponse, error) {
	return WithRetry(ctx, rc.retryConfig, rc.logger, func() (*GenerateResponse, error) {
		return rc.client.Generate(ctx, request)
	})
}

// GenerateStream generates a streaming response with retry logic
func (rc *RetryableClient) GenerateStream(ctx context.Context, request *GenerateRequest) (<-chan *StreamResponse, error) {
	// Note: Streaming requests are generally not retried as they are stateful
	// If retry is needed for streaming, it should be implemented at a higher level
	return rc.client.GenerateStream(ctx, request)
}

// GenerateWithTools generates a response with tool calling support and retry logic
func (rc *RetryableClient) GenerateWithTools(ctx context.Context, request *GenerateRequest, tools []ToolDefinition) (*GenerateResponse, error) {
	return WithRetry(ctx, rc.retryConfig, rc.logger, func() (*GenerateResponse, error) {
		return rc.client.GenerateWithTools(ctx, request, tools)
	})
}

// GenerateMultiModal generates a response for multi-modal input with retry logic
func (rc *RetryableClient) GenerateMultiModal(ctx context.Context, request *MultiModalGenerateRequest) (*GenerateResponse, error) {
	return WithRetry(ctx, rc.retryConfig, rc.logger, func() (*GenerateResponse, error) {
		return rc.client.GenerateMultiModal(ctx, request)
	})
}

// GetModelInfo returns model information from the underlying client
func (rc *RetryableClient) GetModelInfo() *ModelInfo {
	return rc.client.GetModelInfo()
}

// Close closes the underlying client
func (rc *RetryableClient) Close() error {
	return rc.client.Close()
}

// WithRetry executes a function with retry logic
func WithRetry[T any](ctx context.Context, config *RetryConfig, log logger.Logger, fn func() (T, error)) (T, error) {
	var zero T

	if config == nil {
		config = DefaultRetryConfig()
	}

	if log == nil {
		log = logger.GetGlobalLogger()
	}

	var lastErr error

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		// Check if context is cancelled
		select {
		case <-ctx.Done():
			return zero, ctx.Err()
		default:
		}

		// Execute the function
		result, err := fn()
		if err == nil {
			if attempt > 0 {
				log.WithField("component", "retry").
					WithField("attempt", attempt).
					Info("Operation succeeded after retry")
			}
			return result, nil
		}

		lastErr = err

		// Check if this is the last attempt
		if attempt == config.MaxRetries {
			break
		}

		// Check if the error is retryable
		if !isRetryableError(err, config.RetryableErrors) {
			log.WithField("component", "retry").
				WithField("attempt", attempt).
				WithError(err).
				Debug("Error is not retryable, stopping")
			break
		}

		// Calculate delay for next attempt
		delay := calculateDelay(attempt, config)

		log.WithField("component", "retry").
			WithField("attempt", attempt).
			WithField("delay", delay).
			WithError(err).
			Warn("Operation failed, retrying")

		// Wait before retry
		select {
		case <-time.After(delay):
		case <-ctx.Done():
			return zero, ctx.Err()
		}
	}

	// All retries exhausted
	log.WithField("component", "retry").
		WithField("max_retries", config.MaxRetries).
		WithError(lastErr).
		Error("All retry attempts exhausted")

	return zero, errors.Wrap(lastErr, errors.ErrorTypeInternal, CodeResourceExhausted,
		"operation failed after all retry attempts")
}

// isRetryableError checks if an error is retryable
func isRetryableError(err error, retryableErrors []string) bool {
	if err == nil {
		return false
	}

	// Check if it's an AgentScopeError with a retryable code
	if agentErr, ok := err.(*errors.AgentScopeError); ok {
		for _, retryableCode := range retryableErrors {
			if agentErr.Code == retryableCode {
				return true
			}
		}

		// Also check error type for some common retryable types
		switch agentErr.Type {
		case errors.ErrorTypeTimeout, errors.ErrorTypeNetwork:
			return true
		}
	}

	// Check for common Go errors that are typically retryable
	errStr := err.Error()
	retryablePatterns := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"temporary failure",
		"service unavailable",
		"too many requests",
		"rate limit",
		"quota exceeded",
	}

	for _, pattern := range retryablePatterns {
		if contains(errStr, pattern) {
			return true
		}
	}

	return false
}

// calculateDelay calculates the delay for the next retry attempt
func calculateDelay(attempt int, config *RetryConfig) time.Duration {
	// Exponential backoff with jitter
	delay := float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt))

	// Add jitter (±25% of the delay)
	jitter := delay * 0.25 * (2*rand.Float64() - 1)
	delay += jitter

	// Ensure delay is within bounds
	if delay < 0 {
		delay = float64(config.InitialDelay)
	}
	if delay > float64(config.MaxDelay) {
		delay = float64(config.MaxDelay)
	}

	return time.Duration(delay)
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexOf(s, substr) >= 0))
}

// indexOf returns the index of substr in s, or -1 if not found
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// RetryFunc is a function type that can be retried
type RetryFunc[T any] func() (T, error)

// RetryOperation retries an operation with the given configuration
func RetryOperation[T any](ctx context.Context, config *RetryConfig, operation RetryFunc[T]) (T, error) {
	return WithRetry(ctx, config, nil, operation)
}

// SimpleRetry provides a simple retry mechanism with default configuration
func SimpleRetry[T any](ctx context.Context, maxRetries int, operation RetryFunc[T]) (T, error) {
	config := DefaultRetryConfig()
	config.MaxRetries = maxRetries
	return WithRetry(ctx, config, nil, operation)
}

// RetryWithBackoff retries an operation with custom backoff settings
func RetryWithBackoff[T any](ctx context.Context, maxRetries int, initialDelay, maxDelay time.Duration, backoffFactor float64, operation RetryFunc[T]) (T, error) {
	config := &RetryConfig{
		MaxRetries:      maxRetries,
		InitialDelay:    initialDelay,
		MaxDelay:        maxDelay,
		BackoffFactor:   backoffFactor,
		RetryableErrors: DefaultRetryConfig().RetryableErrors,
	}
	return WithRetry(ctx, config, nil, operation)
}

// IsRetryableError checks if an error should trigger a retry
func IsRetryableError(err error) bool {
	return isRetryableError(err, DefaultRetryConfig().RetryableErrors)
}

// GetRetryDelay calculates the delay for a given attempt
func GetRetryDelay(attempt int, config *RetryConfig) time.Duration {
	if config == nil {
		config = DefaultRetryConfig()
	}
	return calculateDelay(attempt, config)
}
