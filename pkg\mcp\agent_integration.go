package mcp

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// MCPAgentExtension MCP Agent扩展
type MCPAgentExtension struct {
	toolManager     *MCPToolManager
	resourceManager *MCPResourceManager
	config          *MCPAgentConfig
	logger          logger.Logger
	mu              sync.RWMutex
}

// MCPAgentConfig MCP Agent配置
type MCPAgentConfig struct {
	Servers           map[string]*MCPConfig `json:"servers"`
	AutoRefreshTools  bool                  `json:"auto_refresh_tools"`
	ResourceCacheSize int                   `json:"resource_cache_size"`
	EnabledServers    []string              `json:"enabled_servers"`
}

// NewMCPAgentExtension 创建MCP Agent扩展
func NewMCPAgentExtension(config *MCPAgentConfig) *MCPAgentExtension {
	if config == nil {
		config = &MCPAgentConfig{
			Servers:           make(map[string]*MCPConfig),
			AutoRefreshTools:  true,
			ResourceCacheSize: 1000,
			EnabledServers:    []string{},
		}
	}

	return &MCPAgentExtension{
		toolManager: NewMCPToolManager(),
		config:      config,
		logger:      logger.GetGlobalLogger(),
	}
}

// Initialize 初始化MCP扩展
func (ext *MCPAgentExtension) Initialize(ctx context.Context) error {
	ext.mu.Lock()
	defer ext.mu.Unlock()

	// 连接到配置的MCP服务器
	for name, serverConfig := range ext.config.Servers {
		if ext.isServerEnabled(name) {
			if err := ext.toolManager.AddMCPServer(ctx, name, serverConfig); err != nil {
				ext.logger.Warnf("Failed to connect to MCP server %s: %v", name, err)
				continue
			}

			// 创建资源管理器
			if _, err := ext.toolManager.GetServerRegistry(name); err == nil {
				client := ext.toolManager.clients[name]
				cacheConfig := &ResourceCacheConfig{
					MaxSize:    ext.config.ResourceCacheSize,
					DefaultTTL: 30 * time.Minute,
				}
				ext.resourceManager = NewMCPResourceManager(client, cacheConfig)
			}

			ext.logger.Infof("Connected to MCP server: %s", name)
		}
	}

	// 注册工具到全局注册表
	if err := ext.toolManager.RegisterAllToGlobalRegistry(); err != nil {
		return fmt.Errorf("failed to register MCP tools: %w", err)
	}

	ext.logger.Info("MCP agent extension initialized")
	return nil
}

// isServerEnabled 检查服务器是否启用
func (ext *MCPAgentExtension) isServerEnabled(name string) bool {
	if len(ext.config.EnabledServers) == 0 {
		return true // 如果没有指定，默认启用所有服务器
	}

	for _, enabled := range ext.config.EnabledServers {
		if enabled == name {
			return true
		}
	}
	return false
}

// GetAvailableTools 获取可用的MCP工具
func (ext *MCPAgentExtension) GetAvailableTools(ctx context.Context) ([]ToolInfo, error) {
	ext.mu.RLock()
	defer ext.mu.RUnlock()

	var allTools []ToolInfo

	for serverName, registry := range ext.toolManager.registries {
		adapters := registry.ListTools()
		for _, adapter := range adapters {
			toolInfo := ToolInfo{
				Name:        adapter.Name(),
				Description: adapter.Description(),
				Server:      serverName,
				Schema:      adapter.Schema(),
			}
			allTools = append(allTools, toolInfo)
		}
	}

	return allTools, nil
}

// CallMCPTool 调用MCP工具
func (ext *MCPAgentExtension) CallMCPTool(ctx context.Context, toolName string, params map[string]interface{}) (*MCPToolCallResult, error) {
	ext.mu.RLock()
	defer ext.mu.RUnlock()

	// 查找工具
	var adapter *MCPToolAdapter
	var serverName string

	for name, registry := range ext.toolManager.registries {
		if registry.HasTool(toolName) {
			var err error
			adapter, err = registry.GetTool(toolName)
			if err != nil {
				return nil, err
			}
			serverName = name
			break
		}
	}

	if adapter == nil {
		return nil, errors.NewNotFoundError("tool_not_found", fmt.Sprintf("MCP tool %s not found", toolName))
	}

	// 执行工具
	result, err := adapter.Execute(ctx, params)
	if err != nil {
		return &MCPToolCallResult{
			ToolName:   toolName,
			ServerName: serverName,
			Success:    false,
			Error:      err.Error(),
		}, nil
	}

	return &MCPToolCallResult{
		ToolName:   toolName,
		ServerName: serverName,
		Success:    true,
		Data:       result,
	}, nil
}

// GetMCPResources 获取MCP资源
func (ext *MCPAgentExtension) GetMCPResources(ctx context.Context, serverName string) ([]*MCPResource, error) {
	ext.mu.RLock()
	defer ext.mu.RUnlock()

	if ext.resourceManager == nil {
		return nil, errors.NewValidationError("no_resource_manager", "no resource manager available")
	}

	return ext.resourceManager.ListResources(ctx)
}

// ReadMCPResource 读取MCP资源
func (ext *MCPAgentExtension) ReadMCPResource(ctx context.Context, uri string) (*MCPResourceContent, error) {
	ext.mu.RLock()
	defer ext.mu.RUnlock()

	if ext.resourceManager == nil {
		return nil, errors.NewValidationError("no_resource_manager", "no resource manager available")
	}

	return ext.resourceManager.ReadResource(ctx, uri)
}

// RefreshTools 刷新工具列表
func (ext *MCPAgentExtension) RefreshTools(ctx context.Context) error {
	ext.mu.Lock()
	defer ext.mu.Unlock()

	return ext.toolManager.RefreshAllTools(ctx)
}

// Shutdown 关闭MCP扩展
func (ext *MCPAgentExtension) Shutdown() error {
	ext.mu.Lock()
	defer ext.mu.Unlock()

	if ext.resourceManager != nil {
		ext.resourceManager.Shutdown()
	}

	if ext.toolManager != nil {
		ext.toolManager.Shutdown()
	}

	ext.logger.Info("MCP agent extension shutdown completed")
	return nil
}

// ToolInfo 工具信息
type ToolInfo struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Server      string      `json:"server"`
	Schema      interface{} `json:"schema"`
}

// MCPToolCallResult MCP工具调用结果
type MCPToolCallResult struct {
	ToolName   string                 `json:"tool_name"`
	ServerName string                 `json:"server_name"`
	Success    bool                   `json:"success"`
	Data       interface{}            `json:"data,omitempty"`
	Error      string                 `json:"error,omitempty"`
	Duration   interface{}            `json:"duration"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// MCPEnabledAgent MCP增强的Agent
type MCPEnabledAgent struct {
	agent.Agent
	mcpExtension *MCPAgentExtension
	logger       logger.Logger
}

// NewMCPEnabledAgent 创建MCP增强的Agent
func NewMCPEnabledAgent(baseAgent agent.Agent, mcpConfig *MCPAgentConfig) (*MCPEnabledAgent, error) {
	extension := NewMCPAgentExtension(mcpConfig)

	return &MCPEnabledAgent{
		Agent:        baseAgent,
		mcpExtension: extension,
		logger:       logger.GetGlobalLogger(),
	}, nil
}

// InitializeMCP 初始化MCP扩展（新接口不需要Initialize方法）
func (agent *MCPEnabledAgent) InitializeMCP(ctx context.Context) error {
	// 初始化MCP扩展
	if err := agent.mcpExtension.Initialize(ctx); err != nil {
		return fmt.Errorf("failed to initialize MCP extension: %w", err)
	}

	agent.logger.Infof("MCP-enabled agent initialized: %s", agent.Name(ctx))
	return nil
}

// Run 运行MCP增强的Agent（实现新的Agent接口）
func (agent *MCPEnabledAgent) Run(ctx context.Context, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	// 检查输入消息是否包含MCP工具调用或资源请求
	for _, msg := range in.Messages {
		if agent.containsMCPToolCall(msg) {
			return agent.handleMCPToolCallAsync(ctx, msg, in)
		}
		if agent.containsMCPResourceRequest(msg) {
			return agent.handleMCPResourceRequestAsync(ctx, msg, in)
		}
	}

	// 使用基础Agent处理
	return agent.Agent.Run(ctx, in)
}

// containsMCPToolCall 检查消息是否包含MCP工具调用
func (agent *MCPEnabledAgent) containsMCPToolCall(msg *message.Message) bool {
	// 检查消息内容或元数据中是否包含MCP工具调用标识
	if msg.Metadata != nil {
		if _, exists := msg.Metadata["mcp_tool_call"]; exists {
			return true
		}
	}

	// 检查消息内容中是否包含工具调用模式
	content := fmt.Sprintf("%v", msg.Content)
	return contains(content, "mcp:") || contains(content, "tool:")
}

// containsMCPResourceRequest 检查消息是否包含MCP资源请求
func (agent *MCPEnabledAgent) containsMCPResourceRequest(msg *message.Message) bool {
	if msg.Metadata != nil {
		if _, exists := msg.Metadata["mcp_resource_request"]; exists {
			return true
		}
	}

	content := fmt.Sprintf("%v", msg.Content)
	return contains(content, "resource:") || contains(content, "mcp://")
}

// handleMCPToolCall 处理MCP工具调用
func (agent *MCPEnabledAgent) handleMCPToolCall(ctx context.Context, msg *message.Message) (*message.Message, error) {
	// 解析工具调用参数
	toolName, params, err := agent.parseMCPToolCall(msg)
	if err != nil {
		return agent.createErrorResponse(msg, fmt.Sprintf("Failed to parse MCP tool call: %v", err))
	}

	// 调用MCP工具
	result, err := agent.mcpExtension.CallMCPTool(ctx, toolName, params)
	if err != nil {
		return agent.createErrorResponse(msg, fmt.Sprintf("Failed to call MCP tool %s: %v", toolName, err))
	}

	// 创建响应消息
	return agent.createMCPToolResponse(msg, result)
}

// handleMCPResourceRequest 处理MCP资源请求
func (agent *MCPEnabledAgent) handleMCPResourceRequest(ctx context.Context, msg *message.Message) (*message.Message, error) {
	// 解析资源URI
	uri, err := agent.parseMCPResourceURI(msg)
	if err != nil {
		return agent.createErrorResponse(msg, fmt.Sprintf("Failed to parse MCP resource URI: %v", err))
	}

	// 读取MCP资源
	content, err := agent.mcpExtension.ReadMCPResource(ctx, uri)
	if err != nil {
		return agent.createErrorResponse(msg, fmt.Sprintf("Failed to read MCP resource %s: %v", uri, err))
	}

	// 创建响应消息
	return agent.createMCPResourceResponse(msg, content)
}

// parseMCPToolCall 解析MCP工具调用
func (agent *MCPEnabledAgent) parseMCPToolCall(msg *message.Message) (string, map[string]interface{}, error) {
	// 简化实现：从消息元数据中获取工具名称和参数
	if msg.Metadata != nil {
		if toolCall, exists := msg.Metadata["mcp_tool_call"]; exists {
			if callMap, ok := toolCall.(map[string]interface{}); ok {
				toolName, _ := callMap["tool_name"].(string)
				params, _ := callMap["parameters"].(map[string]interface{})
				return toolName, params, nil
			}
		}
	}

	return "", nil, errors.NewValidationError("invalid_tool_call", "invalid MCP tool call format")
}

// parseMCPResourceURI 解析MCP资源URI
func (agent *MCPEnabledAgent) parseMCPResourceURI(msg *message.Message) (string, error) {
	if msg.Metadata != nil {
		if resourceReq, exists := msg.Metadata["mcp_resource_request"]; exists {
			if reqMap, ok := resourceReq.(map[string]interface{}); ok {
				if uri, ok := reqMap["uri"].(string); ok {
					return uri, nil
				}
			}
		}
	}

	return "", errors.NewValidationError("invalid_resource_request", "invalid MCP resource request format")
}

// createErrorResponse 创建错误响应
func (agent *MCPEnabledAgent) createErrorResponse(originalMsg *message.Message, errorMsg string) (*message.Message, error) {
	response := message.NewMessage("assistant", errorMsg)
	response.Name = agent.Name(context.Background())
	response.Metadata["error"] = true
	response.Metadata["original_message_id"] = originalMsg.ID
	return response, nil
}

// createMCPToolResponse 创建MCP工具响应
func (agent *MCPEnabledAgent) createMCPToolResponse(originalMsg *message.Message, result *MCPToolCallResult) (*message.Message, error) {
	var content string
	if result.Success {
		content = fmt.Sprintf("MCP tool %s executed successfully. Result: %v", result.ToolName, result.Data)
	} else {
		content = fmt.Sprintf("MCP tool %s failed: %s", result.ToolName, result.Error)
	}

	response := message.NewMessage("assistant", content)
	response.Name = agent.Name(context.Background())
	response.Metadata["mcp_tool_result"] = result
	response.Metadata["original_message_id"] = originalMsg.ID
	return response, nil
}

// createMCPResourceResponse 创建MCP资源响应
func (agent *MCPEnabledAgent) createMCPResourceResponse(originalMsg *message.Message, content *MCPResourceContent) (*message.Message, error) {
	responseText := fmt.Sprintf("MCP resource %s content:\n", content.URI)

	for _, mcpContent := range content.Contents {
		if mcpContent.Type == "text" {
			responseText += mcpContent.Text + "\n"
		} else {
			responseText += fmt.Sprintf("[%s content]\n", mcpContent.Type)
		}
	}

	response := message.NewMessage("assistant", responseText)
	response.Name = agent.Name(context.Background())
	response.Metadata["mcp_resource_content"] = content
	response.Metadata["original_message_id"] = originalMsg.ID
	return response, nil
}

// Shutdown 关闭MCP增强Agent
func (agent *MCPEnabledAgent) Shutdown() {
	// 新的Agent接口没有Shutdown方法，只关闭MCP扩展
	agent.mcpExtension.Shutdown()
	agent.logger.Infof("MCP-enabled agent shutdown: %s", agent.Name(context.Background()))
}

// LoadMCPConfigFromGlobal 从全局配置加载MCP配置
func LoadMCPConfigFromGlobal(globalConfig *config.Config) *MCPAgentConfig {
	mcpConfig := &MCPAgentConfig{
		Servers:           make(map[string]*MCPConfig),
		AutoRefreshTools:  true,
		ResourceCacheSize: 1000,
		EnabledServers:    []string{},
	}

	// 从全局配置中提取MCP相关配置
	if globalConfig != nil && globalConfig.LLM != nil && globalConfig.LLM.Parameters != nil {
		if mcpData, exists := globalConfig.LLM.Parameters["mcp"]; exists {
			if mcpMap, ok := mcpData.(map[string]interface{}); ok {
				// 解析服务器配置
				if servers, exists := mcpMap["servers"]; exists {
					if serversMap, ok := servers.(map[string]interface{}); ok {
						for name, serverData := range serversMap {
							if serverMap, ok := serverData.(map[string]interface{}); ok {
								serverConfig := &MCPConfig{}

								if cmd, ok := serverMap["command"].([]interface{}); ok {
									for _, c := range cmd {
										if cmdStr, ok := c.(string); ok {
											serverConfig.ServerCommand = append(serverConfig.ServerCommand, cmdStr)
										}
									}
								}

								if args, ok := serverMap["args"].([]interface{}); ok {
									for _, a := range args {
										if argStr, ok := a.(string); ok {
											serverConfig.ServerArgs = append(serverConfig.ServerArgs, argStr)
										}
									}
								}

								if env, ok := serverMap["environment"].(map[string]interface{}); ok {
									serverConfig.Environment = make(map[string]string)
									for k, v := range env {
										if vStr, ok := v.(string); ok {
											serverConfig.Environment[k] = vStr
										}
									}
								}

								mcpConfig.Servers[name] = serverConfig
							}
						}
					}
				}

				// 解析其他配置
				if autoRefresh, ok := mcpMap["auto_refresh_tools"].(bool); ok {
					mcpConfig.AutoRefreshTools = autoRefresh
				}

				if cacheSize, ok := mcpMap["resource_cache_size"].(int); ok {
					mcpConfig.ResourceCacheSize = cacheSize
				}

				if enabled, ok := mcpMap["enabled_servers"].([]interface{}); ok {
					for _, e := range enabled {
						if eStr, ok := e.(string); ok {
							mcpConfig.EnabledServers = append(mcpConfig.EnabledServers, eStr)
						}
					}
				}
			}
		}
	}

	return mcpConfig
}

// handleMCPToolCallAsync 异步处理MCP工具调用
func (agent *MCPEnabledAgent) handleMCPToolCallAsync(ctx context.Context, msg *message.Message, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()
		emit := func(ev *event.Event) {
			if err := pair.Generator.Send(ev); err != nil {
				agent.logger.Errorf("发送事件失败: %v", err)
			}
		}

		// 解析工具调用参数
		toolName, params, err := agent.parseMCPToolCall(msg)
		if err != nil {
			emit(event.NewErrorEvent(fmt.Errorf("Failed to parse MCP tool call: %w", err), "MCP_PARSE_ERROR", nil))
			return
		}

		// 发送工具调用开始事件
		emit(event.NewToolCallEvent(fmt.Sprintf("mcp_%s", toolName), toolName, params))

		// 调用MCP工具
		result, err := agent.mcpExtension.CallMCPTool(ctx, toolName, params)
		if err != nil {
			emit(event.NewErrorEvent(fmt.Errorf("Failed to call MCP tool %s: %w", toolName, err), "MCP_TOOL_ERROR", nil))
			return
		}

		// 发送工具调用结果事件
		resultData := map[string]any{
			"tool_name": toolName,
			"result":    result,
		}
		emit(event.NewToolResultEvent(fmt.Sprintf("mcp_%s", toolName), toolName, resultData, nil))

		// 发送最终完成事件
		emit(event.NewFinalEvent(fmt.Sprintf("MCP tool %s executed successfully", toolName), "", nil))
	}()

	return pair.Iterator
}

// handleMCPResourceRequestAsync 异步处理MCP资源请求
func (agent *MCPEnabledAgent) handleMCPResourceRequestAsync(ctx context.Context, msg *message.Message, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()
		emit := func(ev *event.Event) {
			if err := pair.Generator.Send(ev); err != nil {
				agent.logger.Errorf("发送事件失败: %v", err)
			}
		}

		// 解析资源URI
		uri, err := agent.parseMCPResourceURI(msg)
		if err != nil {
			emit(event.NewErrorEvent(fmt.Errorf("Failed to parse MCP resource URI: %w", err), "MCP_PARSE_ERROR", nil))
			return
		}

		// 发送资源读取开始事件
		emit(event.NewThoughtEvent(fmt.Sprintf("Reading MCP resource: %s", uri), ""))

		// 读取MCP资源
		content, err := agent.mcpExtension.ReadMCPResource(ctx, uri)
		if err != nil {
			emit(event.NewErrorEvent(fmt.Errorf("Failed to read MCP resource %s: %w", uri, err), "MCP_RESOURCE_ERROR", nil))
			return
		}

		// 发送最终完成事件
		emit(event.NewFinalEvent(fmt.Sprintf("MCP resource %s read successfully", uri), "", map[string]any{
			"uri":     uri,
			"content": content,
		}))
	}()

	return pair.Iterator
}
