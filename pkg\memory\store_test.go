package memory

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/stretchr/testify/assert"
)

func TestMemoryStore(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 测试保存消息
	msg := message.NewUserMessage("测试消息")
	err := store.Save(ctx, sessionID, msg)
	if err != nil {
		t.Errorf("保存消息失败: %v", err)
	}

	// 测试加载消息
	messages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}

	if len(messages) != 1 {
		t.<PERSON>rrorf("消息数量不匹配: got %d, want 1", len(messages))
	}

	if messages[0].GetContentString() != "测试消息" {
		t.<PERSON><PERSON><PERSON>("消息内容不匹配: got %s, want 测试消息", messages[0].GetContentString())
	}

	// 测试搜索
	results, err := store.Search(ctx, sessionID, "测试", 0)
	if err != nil {
		t.Errorf("搜索失败: %v", err)
	}

	if len(results) != 1 {
		t.Errorf("搜索结果数量不匹配: got %d, want 1", len(results))
	}

	// 测试删除
	err = store.Delete(ctx, sessionID)
	if err != nil {
		t.Errorf("删除会话失败: %v", err)
	}

	messages, err = store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}

	if len(messages) != 0 {
		t.Errorf("删除后应该没有消息: got %d", len(messages))
	}
}

// TestPostgresConfigValidation 测试PostgresConfig验证
func TestPostgresConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *PostgresConfig
		expectError bool
	}{
		{
			name: "valid config",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     5432,
				Database: "testdb",
				Username: "testuser",
				Password: "testpass",
			},
			expectError: false,
		},
		{
			name: "missing host",
			config: &PostgresConfig{
				Port:     5432,
				Database: "testdb",
				Username: "testuser",
				Password: "testpass",
			},
			expectError: true,
		},
		{
			name: "invalid port",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     0,
				Database: "testdb",
				Username: "testuser",
				Password: "testpass",
			},
			expectError: true,
		},
		{
			name: "missing database",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     5432,
				Username: "testuser",
				Password: "testpass",
			},
			expectError: true,
		},
		{
			name: "missing username",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     5432,
				Database: "testdb",
				Password: "testpass",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError && err == nil {
				t.Error("Expected validation error, got nil")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no validation error, got: %v", err)
			}
		})
	}
}

// TestPostgresConnectionString 测试PostgresConfig连接字符串生成
func TestPostgresConnectionString(t *testing.T) {
	config := &PostgresConfig{
		Host:     "localhost",
		Port:     5432,
		Database: "testdb",
		Username: "testuser",
		Password: "testpass",
		SSLMode:  "disable",
	}

	connStr := config.ConnectionString()
	expected := "host=localhost port=5432 dbname=testdb user=testuser password=testpass sslmode=disable"

	if connStr != expected {
		t.Errorf("Expected connection string '%s', got '%s'", expected, connStr)
	}
}

// TestSQLiteConfigValidation 测试SQLiteConfig验证
func TestSQLiteConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *SQLiteConfig
		expectError bool
	}{
		{
			name: "valid config",
			config: &SQLiteConfig{
				DatabasePath: ":memory:",
			},
			expectError: false,
		},
		{
			name: "missing path",
			config: &SQLiteConfig{
				DatabasePath: "",
			},
			expectError: false, // 空路径会使用默认的:memory:
		},
		{
			name: "file database",
			config: &SQLiteConfig{
				DatabasePath: "test.db",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError && err == nil {
				t.Error("Expected validation error, got nil")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no validation error, got: %v", err)
			}
		})
	}
}

func TestMemoryStoreValidation(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 测试空消息
	err := store.Save(ctx, "session", nil)
	if err == nil {
		t.Error("保存空消息应该返回错误")
	}

	// 测试空会话ID
	msg := message.NewUserMessage("测试")
	err = store.Save(ctx, "", msg)
	if err == nil {
		t.Error("空会话ID应该返回错误")
	}

	// 测试加载空会话ID
	_, err = store.LoadAll(ctx, "")
	if err == nil {
		t.Error("空会话ID应该返回错误")
	}

	// 测试删除空会话ID
	err = store.Delete(ctx, "")
	if err == nil {
		t.Error("空会话ID应该返回错误")
	}

	// 测试搜索空会话ID
	_, err = store.Search(ctx, "", "query", 0)
	if err == nil {
		t.Error("空会话ID应该返回错误")
	}
}

func TestMemoryStoreMultipleSessions(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 创建多个会话
	sessions := []string{"session1", "session2", "session3"}
	for i, sessionID := range sessions {
		msg := message.NewUserMessage(fmt.Sprintf("消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		if err != nil {
			t.Errorf("保存消息失败: %v", err)
		}
	}

	// 获取会话列表
	sessionList, err := store.GetSessions(ctx)
	if err != nil {
		t.Errorf("获取会话列表失败: %v", err)
	}

	if len(sessionList) != 3 {
		t.Errorf("会话数量不匹配: got %d, want 3", len(sessionList))
	}

	// 验证每个会话的消息
	for i, sessionID := range sessions {
		messages, err := store.LoadAll(ctx, sessionID)
		if err != nil {
			t.Errorf("加载会话%s消息失败: %v", sessionID, err)
		}

		if len(messages) != 1 {
			t.Errorf("会话%s消息数量不匹配: got %d, want 1", sessionID, len(messages))
		}

		expectedContent := fmt.Sprintf("消息%d", i+1)
		if messages[0].GetContentString() != expectedContent {
			t.Errorf("会话%s消息内容不匹配: got %s, want %s", sessionID, messages[0].GetContentString(), expectedContent)
		}
	}
}

func TestMemoryStoreLimit(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 保存多条消息
	for i := 0; i < 5; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		if err != nil {
			t.Errorf("保存消息%d失败: %v", i+1, err)
		}
	}

	// 测试限制加载
	messages, err := store.Load(ctx, sessionID, 3)
	if err != nil {
		t.Errorf("限制加载失败: %v", err)
	}

	if len(messages) != 3 {
		t.Errorf("限制加载消息数量不匹配: got %d, want 3", len(messages))
	}

	// 验证是最后3条消息
	expectedContents := []string{"消息3", "消息4", "消息5"}
	for i, msg := range messages {
		if msg.GetContentString() != expectedContents[i] {
			t.Errorf("限制加载消息%d内容不匹配: got %s, want %s", i, msg.GetContentString(), expectedContents[i])
		}
	}

	// 测试加载所有消息
	allMessages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载所有消息失败: %v", err)
	}

	if len(allMessages) != 5 {
		t.Errorf("所有消息数量不匹配: got %d, want 5", len(allMessages))
	}
}

func TestMemoryStoreDeleteMessage(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 保存多条消息
	var messageIDs []string
	for i := 0; i < 3; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		if err != nil {
			t.Errorf("保存消息%d失败: %v", i+1, err)
		}
		messageIDs = append(messageIDs, msg.ID)
	}

	// 删除中间的消息（消息2）
	err := store.DeleteMessage(ctx, sessionID, messageIDs[1])
	if err != nil {
		t.Errorf("删除消息失败: %v", err)
	}

	// 验证消息已删除
	messages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}

	if len(messages) != 2 {
		t.Errorf("删除后消息数量不匹配: got %d, want 2", len(messages))
	}

	// 验证剩余消息
	expectedContents := []string{"消息1", "消息3"}
	for i, msg := range messages {
		if msg.GetContentString() != expectedContents[i] {
			t.Errorf("剩余消息%d内容不匹配: got %s, want %s", i, msg.GetContentString(), expectedContents[i])
		}
	}

	// 测试删除不存在的消息
	err = store.DeleteMessage(ctx, sessionID, "non-existent")
	if err != nil {
		t.Errorf("删除不存在的消息不应该返回错误: %v", err)
	}

	// 测试空参数
	err = store.DeleteMessage(ctx, "", messageIDs[0])
	if err == nil {
		t.Error("空会话ID应该返回错误")
	}

	err = store.DeleteMessage(ctx, sessionID, "")
	if err == nil {
		t.Error("空消息ID应该返回错误")
	}
}

func TestMemoryStoreGetSessions(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 测试空会话列表
	sessions, err := store.GetSessions(ctx)
	if err != nil {
		t.Errorf("获取会话列表失败: %v", err)
	}
	if len(sessions) != 0 {
		t.Errorf("空存储应该返回0个会话，实际返回: %d", len(sessions))
	}

	// 添加一些会话
	msg1 := message.NewUserMessage("消息1")
	msg2 := message.NewUserMessage("消息2")

	err = store.Save(ctx, "session1", msg1)
	if err != nil {
		t.Errorf("保存消息失败: %v", err)
	}

	err = store.Save(ctx, "session2", msg2)
	if err != nil {
		t.Errorf("保存消息失败: %v", err)
	}

	// 测试获取会话列表
	sessions, err = store.GetSessions(ctx)
	if err != nil {
		t.Errorf("获取会话列表失败: %v", err)
	}
	if len(sessions) != 2 {
		t.Errorf("期望2个会话，实际返回: %d", len(sessions))
	}

	// 验证会话ID
	sessionMap := make(map[string]bool)
	for _, session := range sessions {
		sessionMap[session] = true
	}
	if !sessionMap["session1"] || !sessionMap["session2"] {
		t.Errorf("会话ID不匹配，期望包含session1和session2，实际: %v", sessions)
	}
}

func TestMemoryStoreLoadWithLimit(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 添加多条消息
	for i := 0; i < 5; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("消息%d", i))
		err := store.Save(ctx, sessionID, msg)
		if err != nil {
			t.Errorf("保存消息失败: %v", err)
		}
	}

	// 测试限制加载
	messages, err := store.Load(ctx, sessionID, 3)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}
	if len(messages) != 3 {
		t.Errorf("期望3条消息，实际返回: %d", len(messages))
	}

	// 测试加载所有消息
	allMessages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载所有消息失败: %v", err)
	}
	if len(allMessages) != 5 {
		t.Errorf("期望5条消息，实际返回: %d", len(allMessages))
	}

	// 测试limit为0的情况
	messages, err = store.Load(ctx, sessionID, 0)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}
	if len(messages) != 5 {
		t.Errorf("limit为0时期望返回所有消息，实际返回: %d", len(messages))
	}
}

// TestPostgresConfig 测试Postgres配置
func TestPostgresConfig(t *testing.T) {
	tests := []struct {
		name   string
		config *PostgresConfig
		valid  bool
	}{
		{
			name: "有效配置",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     5432,
				Database: "test",
				Username: "user",
				Password: "pass",
				SSLMode:  "disable",
			},
			valid: true,
		},
		{
			name: "空主机",
			config: &PostgresConfig{
				Host:     "",
				Port:     5432,
				Database: "test",
				Username: "user",
				Password: "pass",
				SSLMode:  "disable",
			},
			valid: false,
		},
		{
			name: "无效端口",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     0,
				Database: "test",
				Username: "user",
				Password: "pass",
				SSLMode:  "disable",
			},
			valid: false,
		},
		{
			name: "空数据库名",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     5432,
				Database: "",
				Username: "user",
				Password: "pass",
				SSLMode:  "disable",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.valid {
				assert.NoError(t, err, "配置应该有效")
			} else {
				assert.Error(t, err, "配置应该无效")
			}
		})
	}
}

// TestSQLiteConfig 测试SQLite配置
func TestSQLiteConfig(t *testing.T) {
	tests := []struct {
		name   string
		config *SQLiteConfig
		valid  bool
	}{
		{
			name: "内存数据库",
			config: &SQLiteConfig{
				DatabasePath: ":memory:",
			},
			valid: true,
		},
		{
			name: "文件数据库",
			config: &SQLiteConfig{
				DatabasePath: "test.db",
			},
			valid: true,
		},
		{
			name: "空路径",
			config: &SQLiteConfig{
				DatabasePath: "",
			},
			valid: true, // 空路径会使用默认的:memory:
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.valid {
				assert.NoError(t, err, "配置应该有效")
			} else {
				assert.Error(t, err, "配置应该无效")
			}
		})
	}
}

// TestPostgresConfigDefaults 测试PostgresConfig默认值
func TestPostgresConfigDefaults(t *testing.T) {
	config := &PostgresConfig{
		Host:     "localhost",
		Database: "testdb",
		Username: "testuser",
		Password: "testpass",
	}

	// 测试默认端口
	if config.Port == 0 {
		config.Port = 5432 // 模拟设置默认值
	}
	assert.Equal(t, 5432, config.Port)

	// 测试默认SSL模式
	if config.SSLMode == "" {
		config.SSLMode = "disable" // 模拟设置默认值
	}
	assert.Equal(t, "disable", config.SSLMode)
}

// TestPostgresConfigEdgeCases 测试PostgresConfig边界情况
func TestPostgresConfigEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		config      *PostgresConfig
		expectError bool
	}{
		{
			name: "port at minimum valid value",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     1,
				Database: "testdb",
				Username: "testuser",
				Password: "testpass",
			},
			expectError: false,
		},
		{
			name: "port at maximum valid value",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     65535,
				Database: "testdb",
				Username: "testuser",
				Password: "testpass",
			},
			expectError: false,
		},
		{
			name: "port too high",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     65536,
				Database: "testdb",
				Username: "testuser",
				Password: "testpass",
			},
			expectError: true,
		},
		{
			name: "empty password allowed",
			config: &PostgresConfig{
				Host:     "localhost",
				Port:     5432,
				Database: "testdb",
				Username: "testuser",
				Password: "",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError && err == nil {
				t.Error("Expected validation error, got nil")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no validation error, got: %v", err)
			}
		})
	}
}

// TestMemoryStoreEdgeCases 测试MemoryStore边界情况
func TestMemoryStoreEdgeCases(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 测试空会话ID
	messages, err := store.Load(ctx, "", 0)
	assert.Error(t, err) // 应该返回错误
	assert.Empty(t, messages)

	// 测试非常长的会话ID
	longSessionID := ""
	for i := 0; i < 1000; i++ {
		longSessionID += "a"
	}

	msg := &message.Message{
		ID:      "test-msg-1",
		Role:    "user",
		Content: "Test message",
		Name:    "test-user",
	}

	err = store.Save(ctx, longSessionID, msg)
	assert.NoError(t, err)

	messages, err = store.Load(ctx, longSessionID, 0)
	assert.NoError(t, err)
	assert.Len(t, messages, 1)
}

// TestMemoryStoreConcurrency 测试MemoryStore并发安全
func TestMemoryStoreConcurrency(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "concurrent-session"

	// 并发写入
	const numGoroutines = 10
	const messagesPerGoroutine = 10

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer func() { done <- true }()

			for j := 0; j < messagesPerGoroutine; j++ {
				msg := &message.Message{
					ID:      fmt.Sprintf("msg-%d-%d", goroutineID, j),
					Role:    "user",
					Content: fmt.Sprintf("Message from goroutine %d, message %d", goroutineID, j),
					Name:    "test-user",
				}

				err := store.Save(ctx, sessionID, msg)
				assert.NoError(t, err)
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// 验证所有消息都被保存
	messages, err := store.Load(ctx, sessionID, 0)
	assert.NoError(t, err)
	assert.Len(t, messages, numGoroutines*messagesPerGoroutine)
}

// TestSQLiteStore 测试SQLite存储功能
func TestSQLiteStore(t *testing.T) {
	config := &SQLiteConfig{
		DatabasePath: ":memory:",
	}

	store, err := NewSQLiteStore(config)
	assert.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 测试保存消息
	msg := message.NewUserMessage("测试SQLite消息")
	err = store.Save(ctx, sessionID, msg)
	assert.NoError(t, err)

	// 测试加载消息
	messages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, messages, 1)
	assert.Equal(t, "测试SQLite消息", messages[0].GetContentString())

	// 测试搜索
	results, err := store.Search(ctx, sessionID, "SQLite", 0)
	assert.NoError(t, err)
	assert.Len(t, results, 1)

	// 测试删除
	err = store.Delete(ctx, sessionID)
	assert.NoError(t, err)

	messages, err = store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Empty(t, messages)
}

// TestSQLiteStoreMultipleSessions 测试SQLite多会话功能
func TestSQLiteStoreMultipleSessions(t *testing.T) {
	config := &SQLiteConfig{
		DatabasePath: ":memory:",
	}

	store, err := NewSQLiteStore(config)
	assert.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// 创建多个会话
	sessions := []string{"session1", "session2", "session3"}
	for i, sessionID := range sessions {
		msg := message.NewUserMessage(fmt.Sprintf("SQLite消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		assert.NoError(t, err)
	}

	// 获取会话列表
	sessionList, err := store.GetSessions(ctx)
	assert.NoError(t, err)
	assert.Len(t, sessionList, 3)

	// 验证每个会话的消息
	for i, sessionID := range sessions {
		messages, err := store.LoadAll(ctx, sessionID)
		assert.NoError(t, err)
		assert.Len(t, messages, 1)

		expectedContent := fmt.Sprintf("SQLite消息%d", i+1)
		assert.Equal(t, expectedContent, messages[0].GetContentString())
	}
}

// TestSQLiteStoreLimit 测试SQLite限制加载功能
func TestSQLiteStoreLimit(t *testing.T) {
	config := &SQLiteConfig{
		DatabasePath: ":memory:",
	}

	store, err := NewSQLiteStore(config)
	assert.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 保存多条消息，添加小延迟确保时间戳不同
	for i := 0; i < 5; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("SQLite消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		assert.NoError(t, err)
		// 添加1毫秒延迟确保时间戳不同
		time.Sleep(1 * time.Millisecond)
	}

	// 测试限制加载
	messages, err := store.Load(ctx, sessionID, 3)
	assert.NoError(t, err)
	assert.Len(t, messages, 3)

	// 验证是最后3条消息（按时间顺序）
	expectedContents := []string{"SQLite消息3", "SQLite消息4", "SQLite消息5"}
	for i, msg := range messages {
		assert.Equal(t, expectedContents[i], msg.GetContentString())
	}

	// 测试加载所有消息
	allMessages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, allMessages, 5)
}

// TestSQLiteStoreDeleteMessage 测试SQLite删除单条消息功能
func TestSQLiteStoreDeleteMessage(t *testing.T) {
	config := &SQLiteConfig{
		DatabasePath: ":memory:",
	}

	store, err := NewSQLiteStore(config)
	assert.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 保存多条消息
	var messageIDs []string
	for i := 0; i < 3; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("SQLite消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		assert.NoError(t, err)
		messageIDs = append(messageIDs, msg.ID)
	}

	// 删除中间的消息（消息2）
	err = store.DeleteMessage(ctx, sessionID, messageIDs[1])
	assert.NoError(t, err)

	// 验证消息已删除
	messages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, messages, 2)

	// 验证剩余消息
	expectedContents := []string{"SQLite消息1", "SQLite消息3"}
	for i, msg := range messages {
		assert.Equal(t, expectedContents[i], msg.GetContentString())
	}
}

// TestSQLiteStoreValidation 测试SQLite存储验证
func TestSQLiteStoreValidation(t *testing.T) {
	config := &SQLiteConfig{
		DatabasePath: ":memory:",
	}

	store, err := NewSQLiteStore(config)
	assert.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// 测试空消息
	err = store.Save(ctx, "session", nil)
	assert.Error(t, err)

	// 测试空会话ID
	msg := message.NewUserMessage("测试")
	err = store.Save(ctx, "", msg)
	assert.Error(t, err)

	// 测试加载空会话ID
	_, err = store.LoadAll(ctx, "")
	assert.Error(t, err)

	// 测试删除空会话ID
	err = store.Delete(ctx, "")
	assert.Error(t, err)

	// 测试搜索空会话ID
	_, err = store.Search(ctx, "", "query", 0)
	assert.Error(t, err)
}

// TestPostgresStoreValidationMethods 测试PostgresStore验证方法
func TestPostgresStoreValidationMethods(t *testing.T) {
	// 创建一个模拟的PostgresStore用于测试验证逻辑
	store := &PostgresStore{}

	// 测试Save方法的验证
	t.Run("Save validation", func(t *testing.T) {
		// 测试nil消息
		err := store.validateSaveInput("session1", nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "消息不能为空")

		// 测试空会话ID
		msg := message.NewUserMessage("test")
		err = store.validateSaveInput("", msg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "会话ID不能为空")

		// 测试有效输入
		err = store.validateSaveInput("session1", msg)
		assert.NoError(t, err)
	})

	// 测试Load方法的验证
	t.Run("Load validation", func(t *testing.T) {
		// 测试空会话ID
		err := store.validateLoadInput("", 10)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "会话ID不能为空")

		// 测试负数限制
		err = store.validateLoadInput("session1", -1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "限制数量不能为负数")

		// 测试有效输入
		err = store.validateLoadInput("session1", 10)
		assert.NoError(t, err)

		// 测试零限制（应该允许）
		err = store.validateLoadInput("session1", 0)
		assert.NoError(t, err)
	})

	// 测试Delete方法的验证
	t.Run("Delete validation", func(t *testing.T) {
		// 测试空会话ID
		err := store.validateSessionID("")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "会话ID不能为空")

		// 测试有效会话ID
		err = store.validateSessionID("session1")
		assert.NoError(t, err)
	})

	// 测试DeleteMessage方法的验证
	t.Run("DeleteMessage validation", func(t *testing.T) {
		// 测试空会话ID
		err := store.validateDeleteMessageInput("", "msg1")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "会话ID不能为空")

		// 测试空消息ID
		err = store.validateDeleteMessageInput("session1", "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "消息ID不能为空")

		// 测试有效输入
		err = store.validateDeleteMessageInput("session1", "msg1")
		assert.NoError(t, err)
	})

	// 测试Search方法的验证
	t.Run("Search validation", func(t *testing.T) {
		// 测试空会话ID
		err := store.validateSearchInput("", "query", 10)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "会话ID不能为空")

		// 测试空查询
		err = store.validateSearchInput("session1", "", 10)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "搜索查询不能为空")

		// 测试负数限制
		err = store.validateSearchInput("session1", "query", -1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "限制数量不能为负数")

		// 测试有效输入
		err = store.validateSearchInput("session1", "query", 10)
		assert.NoError(t, err)
	})
}

// TestMemoryStoreSearchFunctionality 测试MemoryStore搜索功能
func TestMemoryStoreSearchFunctionality(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "search-session"

	// 添加多条不同内容的消息
	messages := []string{
		"这是一条关于Go语言的消息",
		"这是一条关于Python编程的消息",
		"这是一条关于数据库设计的消息",
		"这是一条关于机器学习的消息",
		"这是一条关于Web开发的消息",
	}

	for _, content := range messages {
		msg := message.NewUserMessage(content)
		err := store.Save(ctx, sessionID, msg)
		assert.NoError(t, err)
	}

	// 测试精确搜索
	results, err := store.Search(ctx, sessionID, "Go语言", 0)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Contains(t, results[0].GetContentString(), "Go语言")

	// 测试部分匹配搜索
	results, err = store.Search(ctx, sessionID, "编程", 0)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Contains(t, results[0].GetContentString(), "Python编程")

	// 测试通用词搜索
	results, err = store.Search(ctx, sessionID, "关于", 0)
	assert.NoError(t, err)
	assert.Len(t, results, 5) // 所有消息都包含"关于"

	// 测试限制结果数量
	results, err = store.Search(ctx, sessionID, "关于", 3)
	assert.NoError(t, err)
	assert.Len(t, results, 3)

	// 测试不存在的内容
	results, err = store.Search(ctx, sessionID, "不存在的内容", 0)
	assert.NoError(t, err)
	assert.Empty(t, results)

	// 测试空查询
	results, err = store.Search(ctx, sessionID, "", 0)
	assert.NoError(t, err) // MemoryStore允许空查询，返回空结果
	assert.Empty(t, results)
}

// TestMemoryStoreMessageOrdering 测试MemoryStore消息排序
func TestMemoryStoreMessageOrdering(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "ordering-session"

	// 添加消息，确保有明确的时间顺序
	for i := 0; i < 5; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		assert.NoError(t, err)
		// 添加小延迟确保时间戳不同
		time.Sleep(1 * time.Millisecond)
	}

	// 测试加载所有消息的顺序
	messages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, messages, 5)

	// 验证消息按时间顺序排列
	for i, msg := range messages {
		expectedContent := fmt.Sprintf("消息%d", i+1)
		assert.Equal(t, expectedContent, msg.GetContentString())
	}

	// 测试限制加载的顺序
	limitedMessages, err := store.Load(ctx, sessionID, 3)
	assert.NoError(t, err)
	assert.Len(t, limitedMessages, 3)

	// 验证是最后3条消息
	expectedContents := []string{"消息3", "消息4", "消息5"}
	for i, msg := range limitedMessages {
		assert.Equal(t, expectedContents[i], msg.GetContentString())
	}
}

// 为PostgresStore添加验证方法（这些方法应该在实际的postgres.go中实现）
func (p *PostgresStore) validateSaveInput(sessionID string, msg *message.Message) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if msg == nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息不能为空",
		}
	}
	return nil
}

func (p *PostgresStore) validateLoadInput(sessionID string, limit int) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if limit < 0 {
		return &ValidationError{
			Field:   "limit",
			Message: "限制数量不能为负数",
		}
	}
	return nil
}

func (p *PostgresStore) validateSessionID(sessionID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	return nil
}

func (p *PostgresStore) validateDeleteMessageInput(sessionID, messageID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if messageID == "" {
		return &ValidationError{
			Field:   "messageID",
			Message: "消息ID不能为空",
		}
	}
	return nil
}

func (p *PostgresStore) validateSearchInput(sessionID, query string, limit int) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if query == "" {
		return &ValidationError{
			Field:   "query",
			Message: "搜索查询不能为空",
		}
	}
	if limit < 0 {
		return &ValidationError{
			Field:   "limit",
			Message: "限制数量不能为负数",
		}
	}
	return nil
}

// TestMemoryStoreAdvanced 测试内存存储高级功能
func TestMemoryStoreAdvanced(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "advanced-session"

	// 测试复杂消息类型
	assistantMsg := message.NewAssistantMessage("I can help you with that.")
	systemMsg := message.NewSystemMessage("You are a helpful assistant.")
	userMsg := message.NewUserMessage("What can you do?")

	// 保存不同类型的消息
	err := store.Save(ctx, sessionID, systemMsg)
	assert.NoError(t, err)
	err = store.Save(ctx, sessionID, userMsg)
	assert.NoError(t, err)
	err = store.Save(ctx, sessionID, assistantMsg)
	assert.NoError(t, err)

	// 验证消息类型和顺序
	messages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, messages, 3)

	assert.Equal(t, "system", messages[0].Role)
	assert.Equal(t, "user", messages[1].Role)
	assert.Equal(t, "assistant", messages[2].Role)

	// 测试消息元数据
	msgWithMetadata := message.NewUserMessage("Test with metadata")
	msgWithMetadata.Metadata = map[string]interface{}{
		"source":    "test",
		"timestamp": time.Now().Unix(),
		"priority":  "high",
	}

	err = store.Save(ctx, sessionID, msgWithMetadata)
	assert.NoError(t, err)

	// 验证元数据保存
	allMessages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, allMessages, 4)

	lastMsg := allMessages[3]
	assert.NotNil(t, lastMsg.Metadata)
	assert.Equal(t, "test", lastMsg.Metadata["source"])
	assert.Equal(t, "high", lastMsg.Metadata["priority"])
}

// TestMemoryStoreCloneMessages 测试消息克隆功能
func TestMemoryStoreCloneMessages(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "clone-session"

	// 添加一些消息
	msg1 := message.NewUserMessage("消息1")
	msg2 := message.NewAssistantMessage("消息2")

	err := store.Save(ctx, sessionID, msg1)
	assert.NoError(t, err)

	err = store.Save(ctx, sessionID, msg2)
	assert.NoError(t, err)

	// 加载消息
	messages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, messages, 2)

	// 验证消息是克隆的，不是原始引用
	assert.NotSame(t, msg1, messages[0])
	assert.NotSame(t, msg2, messages[1])

	// 但内容应该相同
	assert.Equal(t, msg1.GetContentString(), messages[0].GetContentString())
	assert.Equal(t, msg2.GetContentString(), messages[1].GetContentString())
}

// TestMemoryStoreGetSessionsEmpty 测试空会话列表
func TestMemoryStoreGetSessionsEmpty(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 初始状态应该没有会话
	sessions, err := store.GetSessions(ctx)
	assert.NoError(t, err)
	assert.Len(t, sessions, 0)
}

// TestMemoryStoreDeleteNonexistentSession 测试删除不存在的会话
func TestMemoryStoreDeleteNonexistentSession(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 删除不存在的会话应该成功（幂等操作）
	err := store.Delete(ctx, "nonexistent-session")
	assert.NoError(t, err)
}

// TestMemoryStoreSearchEmptySession 测试在空会话中搜索
func TestMemoryStoreSearchEmptySession(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()

	// 在不存在的会话中搜索应该返回空结果
	results, err := store.Search(ctx, "nonexistent-session", "query", 10)
	assert.NoError(t, err)
	assert.Len(t, results, 0)
}

// TestMemoryStoreDeleteMessageNonexistent 测试删除不存在的消息
func TestMemoryStoreDeleteMessageNonexistent(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 添加一条消息
	msg := message.NewUserMessage("测试消息")
	err := store.Save(ctx, sessionID, msg)
	assert.NoError(t, err)

	// 删除不存在的消息应该成功（幂等操作）
	err = store.DeleteMessage(ctx, sessionID, "nonexistent-message-id")
	assert.NoError(t, err)

	// 验证原消息仍然存在
	messages, err := store.LoadAll(ctx, sessionID)
	assert.NoError(t, err)
	assert.Len(t, messages, 1)
}

// TestMemoryStoreLoadWithZeroLimit 测试使用0限制加载
func TestMemoryStoreLoadWithZeroLimit(t *testing.T) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"

	// 添加多条消息
	for i := 0; i < 5; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("消息%d", i+1))
		err := store.Save(ctx, sessionID, msg)
		assert.NoError(t, err)
	}

	// limit为0应该返回所有消息
	messages, err := store.Load(ctx, sessionID, 0)
	assert.NoError(t, err)
	assert.Len(t, messages, 5)

	// 验证消息顺序
	for i, msg := range messages {
		expected := fmt.Sprintf("消息%d", i+1)
		assert.Equal(t, expected, msg.GetContentString())
	}
}
