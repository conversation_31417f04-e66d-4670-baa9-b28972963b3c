package monitoring

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// TraceManager 链路追踪管理器
type TraceManager struct {
	traces    map[string]*Trace
	spans     map[string]*Span
	mu        sync.RWMutex
	logger    logger.Logger
	maxTraces int
	maxAge    time.Duration
}

// Trace 追踪信息
type Trace struct {
	TraceID     string            `json:"trace_id"`
	SpanID      string            `json:"span_id"`
	ParentID    string            `json:"parent_id,omitempty"`
	Operation   string            `json:"operation"`
	StartTime   time.Time         `json:"start_time"`
	EndTime     time.Time         `json:"end_time"`
	Duration    time.Duration     `json:"duration"`
	Tags        map[string]string `json:"tags"`
	Logs        []TraceLog        `json:"logs"`
	Status      TraceStatus       `json:"status"`
	ServiceName string            `json:"service_name"`
	AgentID     string            `json:"agent_id,omitempty"`
	Error       string            `json:"error,omitempty"`
}

// Span 跨度信息
type Span struct {
	SpanID      string            `json:"span_id"`
	TraceID     string            `json:"trace_id"`
	ParentID    string            `json:"parent_id,omitempty"`
	Operation   string            `json:"operation"`
	StartTime   time.Time         `json:"start_time"`
	EndTime     time.Time         `json:"end_time"`
	Duration    time.Duration     `json:"duration"`
	Tags        map[string]string `json:"tags"`
	Logs        []TraceLog        `json:"logs"`
	Status      TraceStatus       `json:"status"`
	ServiceName string            `json:"service_name"`
	ChildSpans  []*Span           `json:"child_spans"`
}

// TraceLog 追踪日志
type TraceLog struct {
	Timestamp time.Time         `json:"timestamp"`
	Level     string            `json:"level"`
	Message   string            `json:"message"`
	Fields    map[string]string `json:"fields"`
}

// TraceStatus 追踪状态
type TraceStatus string

const (
	TraceStatusOK    TraceStatus = "ok"
	TraceStatusError TraceStatus = "error"
)

// TraceContext 追踪上下文
type TraceContext struct {
	TraceID  string
	SpanID   string
	ParentID string
	Baggage  map[string]string
}

// NewTraceManager 创建链路追踪管理器
func NewTraceManager(maxTraces int, maxAge time.Duration) *TraceManager {
	return &TraceManager{
		traces:    make(map[string]*Trace),
		spans:     make(map[string]*Span),
		logger:    logger.GetGlobalLogger(),
		maxTraces: maxTraces,
		maxAge:    maxAge,
	}
}

// Start 启动追踪管理器
func (tm *TraceManager) Start() {
	go tm.cleanupLoop()
	tm.logger.Info("Trace manager started")
}

// StartTrace 开始新的追踪
func (tm *TraceManager) StartTrace(ctx context.Context, operation, serviceName string) (context.Context, *Trace) {
	traceID := tm.generateID()
	spanID := tm.generateID()

	trace := &Trace{
		TraceID:     traceID,
		SpanID:      spanID,
		Operation:   operation,
		StartTime:   time.Now(),
		Tags:        make(map[string]string),
		Logs:        make([]TraceLog, 0),
		Status:      TraceStatusOK,
		ServiceName: serviceName,
	}

	tm.mu.Lock()
	tm.traces[traceID] = trace
	tm.mu.Unlock()

	// 创建新的上下文
	traceCtx := &TraceContext{
		TraceID: traceID,
		SpanID:  spanID,
		Baggage: make(map[string]string),
	}

	newCtx := context.WithValue(ctx, "trace_context", traceCtx)
	tm.logger.Debug("Started trace: %s for operation: %s", traceID, operation)

	return newCtx, trace
}

// StartSpan 开始新的跨度
func (tm *TraceManager) StartSpan(ctx context.Context, operation, serviceName string) (context.Context, *Span) {
	traceCtx := tm.getTraceContext(ctx)
	if traceCtx == nil {
		// 如果没有追踪上下文，创建新的追踪
		newCtx, trace := tm.StartTrace(ctx, operation, serviceName)
		span := &Span{
			SpanID:      trace.SpanID,
			TraceID:     trace.TraceID,
			Operation:   operation,
			StartTime:   trace.StartTime,
			Tags:        make(map[string]string),
			Logs:        make([]TraceLog, 0),
			Status:      TraceStatusOK,
			ServiceName: serviceName,
			ChildSpans:  make([]*Span, 0),
		}
		return newCtx, span
	}

	spanID := tm.generateID()
	span := &Span{
		SpanID:      spanID,
		TraceID:     traceCtx.TraceID,
		ParentID:    traceCtx.SpanID,
		Operation:   operation,
		StartTime:   time.Now(),
		Tags:        make(map[string]string),
		Logs:        make([]TraceLog, 0),
		Status:      TraceStatusOK,
		ServiceName: serviceName,
		ChildSpans:  make([]*Span, 0),
	}

	tm.mu.Lock()
	tm.spans[spanID] = span
	tm.mu.Unlock()

	// 更新上下文
	newTraceCtx := &TraceContext{
		TraceID:  traceCtx.TraceID,
		SpanID:   spanID,
		ParentID: traceCtx.SpanID,
		Baggage:  traceCtx.Baggage,
	}

	newCtx := context.WithValue(ctx, "trace_context", newTraceCtx)
	tm.logger.Debug("Started span: %s for operation: %s", spanID, operation)

	return newCtx, span
}

// FinishTrace 完成追踪
func (tm *TraceManager) FinishTrace(trace *Trace) {
	trace.EndTime = time.Now()
	trace.Duration = trace.EndTime.Sub(trace.StartTime)

	tm.logger.Debug("Finished trace: %s, duration: %v", trace.TraceID, trace.Duration)
}

// FinishSpan 完成跨度
func (tm *TraceManager) FinishSpan(span *Span) {
	span.EndTime = time.Now()
	span.Duration = span.EndTime.Sub(span.StartTime)

	// 将span添加到父span的子span列表中
	if span.ParentID != "" {
		tm.mu.Lock()
		if parentSpan, exists := tm.spans[span.ParentID]; exists {
			parentSpan.ChildSpans = append(parentSpan.ChildSpans, span)
		}
		tm.mu.Unlock()
	}

	tm.logger.Debug("Finished span: %s, duration: %v", span.SpanID, span.Duration)
}

// AddTag 添加标签
func (tm *TraceManager) AddTag(ctx context.Context, key, value string) {
	traceCtx := tm.getTraceContext(ctx)
	if traceCtx == nil {
		return
	}

	tm.mu.Lock()
	defer tm.mu.Unlock()

	if trace, exists := tm.traces[traceCtx.TraceID]; exists {
		trace.Tags[key] = value
	}

	if span, exists := tm.spans[traceCtx.SpanID]; exists {
		span.Tags[key] = value
	}
}

// LogEvent 记录事件
func (tm *TraceManager) LogEvent(ctx context.Context, level, message string, fields map[string]string) {
	traceCtx := tm.getTraceContext(ctx)
	if traceCtx == nil {
		return
	}

	log := TraceLog{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}

	tm.mu.Lock()
	defer tm.mu.Unlock()

	if trace, exists := tm.traces[traceCtx.TraceID]; exists {
		trace.Logs = append(trace.Logs, log)
	}

	if span, exists := tm.spans[traceCtx.SpanID]; exists {
		span.Logs = append(span.Logs, log)
	}
}

// SetError 设置错误
func (tm *TraceManager) SetError(ctx context.Context, err error) {
	traceCtx := tm.getTraceContext(ctx)
	if traceCtx == nil {
		return
	}

	tm.mu.Lock()
	defer tm.mu.Unlock()

	if trace, exists := tm.traces[traceCtx.TraceID]; exists {
		trace.Status = TraceStatusError
		trace.Error = err.Error()
	}

	if span, exists := tm.spans[traceCtx.SpanID]; exists {
		span.Status = TraceStatusError
	}
}

// GetTrace 获取追踪
func (tm *TraceManager) GetTrace(traceID string) (*Trace, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	trace, exists := tm.traces[traceID]
	return trace, exists
}

// GetSpan 获取跨度
func (tm *TraceManager) GetSpan(spanID string) (*Span, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	span, exists := tm.spans[spanID]
	return span, exists
}

// ListTraces 列出所有追踪
func (tm *TraceManager) ListTraces() []*Trace {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	traces := make([]*Trace, 0, len(tm.traces))
	for _, trace := range tm.traces {
		traces = append(traces, trace)
	}

	return traces
}

// SearchTraces 搜索追踪
func (tm *TraceManager) SearchTraces(serviceName, operation string, startTime, endTime time.Time) []*Trace {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	results := make([]*Trace, 0)
	for _, trace := range tm.traces {
		if serviceName != "" && trace.ServiceName != serviceName {
			continue
		}

		if operation != "" && trace.Operation != operation {
			continue
		}

		if !startTime.IsZero() && trace.StartTime.Before(startTime) {
			continue
		}

		if !endTime.IsZero() && trace.StartTime.After(endTime) {
			continue
		}

		results = append(results, trace)
	}

	return results
}

// getTraceContext 获取追踪上下文
func (tm *TraceManager) getTraceContext(ctx context.Context) *TraceContext {
	if ctx == nil {
		return nil
	}

	traceCtx, ok := ctx.Value("trace_context").(*TraceContext)
	if !ok {
		return nil
	}

	return traceCtx
}

// generateID 生成ID
func (tm *TraceManager) generateID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// cleanupLoop 清理循环
func (tm *TraceManager) cleanupLoop() {
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			tm.cleanup()
		}
	}
}

// cleanup 清理过期追踪
func (tm *TraceManager) cleanup() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	now := time.Now()
	expiredTraces := make([]string, 0)
	expiredSpans := make([]string, 0)

	// 清理过期追踪
	for traceID, trace := range tm.traces {
		if now.Sub(trace.StartTime) > tm.maxAge {
			expiredTraces = append(expiredTraces, traceID)
		}
	}

	// 清理过期跨度
	for spanID, span := range tm.spans {
		if now.Sub(span.StartTime) > tm.maxAge {
			expiredSpans = append(expiredSpans, spanID)
		}
	}

	// 删除过期项
	for _, traceID := range expiredTraces {
		delete(tm.traces, traceID)
	}

	for _, spanID := range expiredSpans {
		delete(tm.spans, spanID)
	}

	// 如果追踪数量超过限制，删除最旧的
	if len(tm.traces) > tm.maxTraces {
		oldestTraces := make([]*Trace, 0, len(tm.traces))
		for _, trace := range tm.traces {
			oldestTraces = append(oldestTraces, trace)
		}

		// 按时间排序
		for i := 0; i < len(oldestTraces)-1; i++ {
			for j := i + 1; j < len(oldestTraces); j++ {
				if oldestTraces[i].StartTime.After(oldestTraces[j].StartTime) {
					oldestTraces[i], oldestTraces[j] = oldestTraces[j], oldestTraces[i]
				}
			}
		}

		// 删除最旧的追踪
		toDelete := len(oldestTraces) - tm.maxTraces
		for i := 0; i < toDelete; i++ {
			delete(tm.traces, oldestTraces[i].TraceID)
		}
	}

	if len(expiredTraces) > 0 || len(expiredSpans) > 0 {
		tm.logger.Info("Cleaned up %d expired traces and %d expired spans", len(expiredTraces), len(expiredSpans))
	}
}

// AgentTracer Agent追踪器
type AgentTracer struct {
	traceManager *TraceManager
	serviceName  string
	logger       logger.Logger
}

// NewAgentTracer 创建Agent追踪器
func NewAgentTracer(traceManager *TraceManager, serviceName string) *AgentTracer {
	return &AgentTracer{
		traceManager: traceManager,
		serviceName:  serviceName,
		logger:       logger.GetGlobalLogger(),
	}
}

// TraceAgentExecution 追踪Agent执行
func (at *AgentTracer) TraceAgentExecution(ctx context.Context, agentID, operation string, fn func(context.Context) error) error {
	// 开始新的跨度
	spanCtx, span := at.traceManager.StartSpan(ctx, operation, at.serviceName)

	// 添加Agent相关标签
	at.traceManager.AddTag(spanCtx, "agent.id", agentID)
	at.traceManager.AddTag(spanCtx, "agent.operation", operation)
	at.traceManager.AddTag(spanCtx, "service.name", at.serviceName)

	// 记录开始事件
	at.traceManager.LogEvent(spanCtx, "info", fmt.Sprintf("Agent %s started operation %s", agentID, operation), map[string]string{
		"agent_id":  agentID,
		"operation": operation,
	})

	// 执行函数
	err := fn(spanCtx)

	// 处理错误
	if err != nil {
		at.traceManager.SetError(spanCtx, err)
		at.traceManager.LogEvent(spanCtx, "error", fmt.Sprintf("Agent %s failed: %v", agentID, err), map[string]string{
			"agent_id": agentID,
			"error":    err.Error(),
		})
	} else {
		at.traceManager.LogEvent(spanCtx, "info", fmt.Sprintf("Agent %s completed successfully", agentID), map[string]string{
			"agent_id": agentID,
		})
	}

	// 完成跨度
	at.traceManager.FinishSpan(span)

	return err
}

// TracePipelineExecution 追踪Pipeline执行
func (at *AgentTracer) TracePipelineExecution(ctx context.Context, pipelineID string, agents []string, fn func(context.Context) error) error {
	// 开始新的追踪
	traceCtx, trace := at.traceManager.StartTrace(ctx, "pipeline_execution", at.serviceName)

	// 添加Pipeline相关标签
	at.traceManager.AddTag(traceCtx, "pipeline.id", pipelineID)
	at.traceManager.AddTag(traceCtx, "pipeline.agents", fmt.Sprintf("%v", agents))
	at.traceManager.AddTag(traceCtx, "pipeline.agent_count", fmt.Sprintf("%d", len(agents)))

	// 记录开始事件
	at.traceManager.LogEvent(traceCtx, "info", fmt.Sprintf("Pipeline %s started with %d agents", pipelineID, len(agents)), map[string]string{
		"pipeline_id": pipelineID,
		"agent_count": fmt.Sprintf("%d", len(agents)),
	})

	// 执行函数
	err := fn(traceCtx)

	// 处理错误
	if err != nil {
		at.traceManager.SetError(traceCtx, err)
		at.traceManager.LogEvent(traceCtx, "error", fmt.Sprintf("Pipeline %s failed: %v", pipelineID, err), map[string]string{
			"pipeline_id": pipelineID,
			"error":       err.Error(),
		})
	} else {
		at.traceManager.LogEvent(traceCtx, "info", fmt.Sprintf("Pipeline %s completed successfully", pipelineID), map[string]string{
			"pipeline_id": pipelineID,
		})
	}

	// 完成追踪
	at.traceManager.FinishTrace(trace)

	return err
}

// TraceLLMCall 追踪LLM调用
func (at *AgentTracer) TraceLLMCall(ctx context.Context, provider, model string, fn func(context.Context) error) error {
	// 开始新的跨度
	spanCtx, span := at.traceManager.StartSpan(ctx, "llm_call", at.serviceName)

	// 添加LLM相关标签
	at.traceManager.AddTag(spanCtx, "llm.provider", provider)
	at.traceManager.AddTag(spanCtx, "llm.model", model)

	// 记录开始事件
	at.traceManager.LogEvent(spanCtx, "info", fmt.Sprintf("LLM call started: %s/%s", provider, model), map[string]string{
		"provider": provider,
		"model":    model,
	})

	// 执行函数
	err := fn(spanCtx)

	// 处理错误
	if err != nil {
		at.traceManager.SetError(spanCtx, err)
		at.traceManager.LogEvent(spanCtx, "error", fmt.Sprintf("LLM call failed: %v", err), map[string]string{
			"provider": provider,
			"model":    model,
			"error":    err.Error(),
		})
	} else {
		at.traceManager.LogEvent(spanCtx, "info", "LLM call completed successfully", map[string]string{
			"provider": provider,
			"model":    model,
		})
	}

	// 完成跨度
	at.traceManager.FinishSpan(span)

	return err
}

// TraceExporter 追踪导出器接口
type TraceExporter interface {
	Export(traces []*Trace) error
	ExportSpans(spans []*Span) error
}

// ConsoleTraceExporter 控制台追踪导出器
type ConsoleTraceExporter struct {
	logger logger.Logger
}

// NewConsoleTraceExporter 创建控制台追踪导出器
func NewConsoleTraceExporter() *ConsoleTraceExporter {
	return &ConsoleTraceExporter{
		logger: logger.GetGlobalLogger(),
	}
}

// Export 导出追踪
func (cte *ConsoleTraceExporter) Export(traces []*Trace) error {
	for _, trace := range traces {
		cte.logger.Infof("Trace: %s, Operation: %s, Duration: %v, Status: %s",
			trace.TraceID, trace.Operation, trace.Duration, trace.Status)

		for _, log := range trace.Logs {
			cte.logger.Infof("  Log: [%s] %s %s", log.Level, log.Timestamp.Format(time.RFC3339), log.Message)
		}
	}
	return nil
}

// ExportSpans 导出跨度
func (cte *ConsoleTraceExporter) ExportSpans(spans []*Span) error {
	for _, span := range spans {
		cte.logger.Infof("Span: %s, Operation: %s, Duration: %v, Status: %s",
			span.SpanID, span.Operation, span.Duration, span.Status)

		for _, childSpan := range span.ChildSpans {
			cte.logger.Infof("  Child Span: %s, Operation: %s, Duration: %v",
				childSpan.SpanID, childSpan.Operation, childSpan.Duration)
		}
	}
	return nil
}
