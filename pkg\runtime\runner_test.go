package runtime

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/session"
)

// mockAgent 用于测试的模拟智能体
type mockAgent struct {
	name        string
	description string
	events      []*event.Event
	delay       time.Duration
	shouldError bool
	errorMsg    string
}

func newMockAgent(name string) *mockAgent {
	return &mockAgent{
		name:        name,
		description: fmt.Sprintf("Mock agent: %s", name),
		events:      make([]*event.Event, 0),
	}
}

func (ma *mockAgent) Name(ctx context.Context) string {
	return ma.name
}

func (ma *mockAgent) Description(ctx context.Context) string {
	return ma.description
}

func (ma *mockAgent) Run(ctx context.Context, input *Input) *AsyncIterator[*event.Event] {
	pair := NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 如果设置了延迟，等待一段时间
		if ma.delay > 0 {
			select {
			case <-ctx.Done():
				return
			case <-time.After(ma.delay):
			}
		}

		// 如果应该出错，发送错误事件
		if ma.shouldError {
			errorEvent := event.NewErrorEvent(
				errors.New(ma.errorMsg),
				"MOCK_ERROR",
				nil,
			)
			pair.Generator.Send(errorEvent)
			return
		}

		// 发送预设的事件
		for _, ev := range ma.events {
			if err := pair.Generator.Send(ev); err != nil {
				return
			}
		}

		// 发送最终事件
		finalEvent := event.NewFinalEvent("完成", "模拟智能体执行完成", nil)
		pair.Generator.Send(finalEvent)
	}()

	return pair.Iterator
}

func (ma *mockAgent) addEvent(ev *event.Event) {
	ma.events = append(ma.events, ev)
}

func (ma *mockAgent) setDelay(delay time.Duration) {
	ma.delay = delay
}

func (ma *mockAgent) setError(shouldError bool, errorMsg string) {
	ma.shouldError = shouldError
	ma.errorMsg = errorMsg
}

func TestNewRunner(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()

	runner := NewRunner(ctx, config)

	if runner == nil {
		t.Error("NewRunner 不应该返回 nil")
	}

	if len(runner.ListInterceptors()) != 0 {
		t.Error("新创建的 Runner 不应该有拦截器")
	}

	health := runner.Health()
	if health["status"] != "healthy" {
		t.Errorf("健康状态不正确: %v", health["status"])
	}
}

func TestDefaultRunnerConfig(t *testing.T) {
	config := DefaultRunnerConfig()

	if !config.EnableStreaming {
		t.Error("默认配置应该启用流式输出")
	}

	if config.BufferSize <= 0 {
		t.Error("默认缓冲区大小应该大于 0")
	}

	if config.Timeout <= 0 {
		t.Error("默认超时时间应该大于 0")
	}

	if config.MaxRetries < 0 {
		t.Error("默认最大重试次数不应该小于 0")
	}
}

func TestRunnerInterceptors(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 添加拦截器
	interceptor1 := NewLoggingInterceptor("test-logger", nil)
	interceptor2 := NewMetricsInterceptor("test-metrics")

	runner.AddInterceptor(interceptor1)
	runner.AddInterceptor(interceptor2)

	interceptors := runner.ListInterceptors()
	if len(interceptors) != 2 {
		t.Errorf("拦截器数量不匹配: got %d, want %d", len(interceptors), 2)
	}

	// 移除拦截器
	removed := runner.RemoveInterceptor("test-logger")
	if !removed {
		t.Error("应该成功移除拦截器")
	}

	interceptors = runner.ListInterceptors()
	if len(interceptors) != 1 {
		t.Errorf("移除后拦截器数量不匹配: got %d, want %d", len(interceptors), 1)
	}

	// 清空拦截器
	runner.ClearInterceptors()
	interceptors = runner.ListInterceptors()
	if len(interceptors) != 0 {
		t.Error("清空后不应该有拦截器")
	}
}

func TestRunnerBasicExecution(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 创建模拟智能体
	agent := newMockAgent("test-agent")
	agent.addEvent(event.NewTokenEvent("Hello", false))
	agent.addEvent(event.NewThoughtEvent("思考中", "分析用户输入"))

	// 创建输入
	input := NewInput()
	input.SetOption("test", true)

	// 执行智能体
	iterator := runner.Run(ctx, agent, input)

	// 收集事件
	events := iterator.Collect()

	// 验证事件
	if len(events) < 3 { // Token + Thought + Final
		t.Errorf("事件数量不足: got %d, want at least %d", len(events), 3)
	}

	// 检查事件类型
	hasToken := false
	hasThought := false
	hasFinal := false

	for _, ev := range events {
		switch ev.Type {
		case event.EventToken:
			hasToken = true
		case event.EventThought:
			hasThought = true
		case event.EventFinal:
			hasFinal = true
		}
	}

	if !hasToken {
		t.Error("应该有 Token 事件")
	}
	if !hasThought {
		t.Error("应该有 Thought 事件")
	}
	if !hasFinal {
		t.Error("应该有 Final 事件")
	}
}

func TestRunnerWithCallback(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 创建模拟智能体
	agent := newMockAgent("test-agent")
	agent.addEvent(event.NewTokenEvent("Hello", false))
	agent.addEvent(event.NewTokenEvent("World", false))

	// 创建输入
	input := NewInput()
	input.AddMessage(message.NewUserMessage("用户消息"))

	// 收集事件
	var receivedEvents []*event.Event
	err := runner.RunWithCallback(ctx, agent, input, func(ev *event.Event) bool {
		receivedEvents = append(receivedEvents, ev)
		return true // 继续处理
	})

	if err != nil {
		t.Errorf("RunWithCallback 不应该失败: %v", err)
	}

	if len(receivedEvents) < 3 { // Token + Token + Final
		t.Errorf("接收的事件数量不足: got %d", len(receivedEvents))
	}
}

func TestRunnerErrorHandling(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	config.MaxRetries = 1 // 设置重试次数
	runner := NewRunner(ctx, config)

	// 创建会出错的模拟智能体
	agent := newMockAgent("error-agent")
	agent.setError(true, "模拟错误")

	// 创建输入
	input := NewInput()
	input.AddMessage(message.NewUserMessage("用户消息"))

	// 执行智能体
	events, err := runner.RunAndCollect(ctx, agent, input)

	if err == nil {
		t.Error("应该返回错误")
	}

	if err.Error() != "模拟错误" {
		t.Errorf("错误信息不匹配: got %v", err)
	}

	// 应该有错误事件
	hasError := false
	for _, ev := range events {
		if ev.IsError() {
			hasError = true
			break
		}
	}

	if !hasError {
		t.Error("应该有错误事件")
	}
}

func TestRunnerTimeout(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	config.Timeout = 100 * time.Millisecond // 设置短超时
	runner := NewRunner(ctx, config)

	// 创建会延迟的模拟智能体
	agent := newMockAgent("slow-agent")
	agent.setDelay(200 * time.Millisecond) // 延迟超过超时时间

	// 创建输入
	input := NewInput()
	input.AddMessage(message.NewUserMessage("用户消息"))

	// 执行智能体
	start := time.Now()
	iterator := runner.Run(ctx, agent, input)

	// 收集事件（应该因为超时而提前结束）
	events := iterator.Collect()
	duration := time.Since(start)

	// 验证超时
	if duration > 300*time.Millisecond {
		t.Errorf("执行时间过长: %v", duration)
	}

	// 可能没有事件或只有错误事件
	t.Logf("收到 %d 个事件，执行时间: %v", len(events), duration)
}

func TestRunnerWithInterceptors(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 添加指标拦截器
	metricsInterceptor := NewMetricsInterceptor("test-metrics")
	runner.AddInterceptor(metricsInterceptor)

	// 创建模拟智能体
	agent := newMockAgent("test-agent")
	agent.addEvent(event.NewTokenEvent("Hello", false))

	// 创建输入
	input := NewInput()
	input.AddMessage(message.NewUserMessage("用户消息"))

	// 执行智能体
	_, err := runner.RunAndCollect(ctx, agent, input)
	if err != nil {
		t.Errorf("执行不应该失败: %v", err)
	}

	// 检查指标
	metrics := metricsInterceptor.GetMetrics("test-agent")

	if metrics["run_count"] != int64(1) {
		t.Errorf("运行次数不匹配: got %v, want %d", metrics["run_count"], 1)
	}

	eventCount, ok := metrics["event_count"].(map[event.Type]int64)
	if !ok {
		t.Error("事件计数应该是 map 类型")
	} else if eventCount[event.EventToken] < 1 {
		t.Error("应该有 Token 事件计数")
	}
}

func TestRunnerConfigUpdate(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 获取初始配置
	initialConfig := runner.GetConfig()
	if initialConfig.BufferSize != config.BufferSize {
		t.Error("初始配置不匹配")
	}

	// 更新配置
	newConfig := config
	newConfig.BufferSize = 200
	newConfig.MaxRetries = 5

	runner.UpdateConfig(newConfig)

	// 验证配置更新
	updatedConfig := runner.GetConfig()
	if updatedConfig.BufferSize != 200 {
		t.Errorf("缓冲区大小更新失败: got %d, want %d", updatedConfig.BufferSize, 200)
	}

	if updatedConfig.MaxRetries != 5 {
		t.Errorf("最大重试次数更新失败: got %d, want %d", updatedConfig.MaxRetries, 5)
	}
}

func TestRunnerConcurrency(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 添加指标拦截器
	metricsInterceptor := NewMetricsInterceptor("test-metrics")
	runner.AddInterceptor(metricsInterceptor)

	var wg sync.WaitGroup
	const numGoroutines = 10

	// 并发执行多个智能体
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			agent := newMockAgent(fmt.Sprintf("agent-%d", id))
			agent.addEvent(event.NewTokenEvent(fmt.Sprintf("Message %d", id), false))

			input := NewInput()
			input.AddMessage(message.NewUserMessage(fmt.Sprintf("Input %d", id)))

			_, err := runner.RunAndCollect(ctx, agent, input)
			if err != nil {
				t.Errorf("并发执行失败: %v", err)
			}
		}(i)
	}

	wg.Wait()

	// 检查指标
	allMetrics := metricsInterceptor.GetAllMetrics()
	if len(allMetrics) != numGoroutines {
		t.Errorf("指标数量不匹配: got %d, want %d", len(allMetrics), numGoroutines)
	}
}

func TestRunnerSessionHandling(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 创建模拟智能体
	agent := newMockAgent("test-agent")
	agent.addEvent(event.NewTokenEvent("Hello", false))

	// 创建带会话的输入
	sessionMap := session.New()
	sessionMap.Set("user_id", "12345")

	input := NewInput()
	input.AddMessage(message.NewUserMessage("用户消息"))
	input.SetSession(sessionMap)

	// 执行智能体
	_, err := runner.RunAndCollect(ctx, agent, input)
	if err != nil {
		t.Errorf("执行不应该失败: %v", err)
	}

	// 验证会话数据
	userID, exists := sessionMap.Get("user_id")
	if !exists || userID != "12345" {
		t.Error("会话数据应该保持不变")
	}
}

func TestRunnerHealth(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 添加一些拦截器
	runner.AddInterceptor(NewLoggingInterceptor("logger", nil))
	runner.AddInterceptor(NewMetricsInterceptor("metrics"))

	health := runner.Health()

	if health["status"] != "healthy" {
		t.Errorf("健康状态不正确: %v", health["status"])
	}

	if health["interceptors"] != 2 {
		t.Errorf("拦截器数量不正确: %v", health["interceptors"])
	}

	if health["timestamp"] == nil {
		t.Error("应该有时间戳")
	}
}

// TestRunnerWithCheckpoint 测试Runner的检查点功能
func TestRunnerWithCheckpoint(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()

	// 启用检查点功能
	config.EnableCheckpoint = true
	config.CheckpointStore = NewMemoryCheckpointStore()
	config.CheckpointInterval = 2 // 每2个事件保存一次检查点

	runner := NewRunner(ctx, config)
	defer config.CheckpointStore.Close()

	// 创建模拟智能体
	agent := newMockAgent("checkpoint-agent")
	agent.addEvent(event.NewTokenEvent("Hello", false))
	agent.addEvent(event.NewTokenEvent(" World", false))
	agent.addEvent(event.NewThoughtEvent("思考中", "处理用户请求"))

	// 创建输入
	input := NewInput()
	input.AddMessage(message.NewUserMessage("用户消息"))

	// 确保有Session
	if input.Session == nil {
		input.Session = session.New()
	}
	input.Session.Set("test_key", "test_value")

	// 执行智能体
	events, err := runner.RunAndCollect(ctx, agent, input)
	if err != nil {
		t.Fatalf("执行不应该失败: %v", err)
	}

	// 验证事件数量
	if len(events) < 4 {
		t.Errorf("事件数量不足: got %d, want at least 4", len(events))
	}

	// 获取会话ID
	sessionID := runner.getSessionID(input)
	if sessionID == "" {
		t.Fatal("会话ID不应为空")
	}

	// 验证检查点是否存在
	exists, err := config.CheckpointStore.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if !exists {
		t.Error("检查点应该存在")
	}

	// 加载检查点验证数据
	checkpointData, err := config.CheckpointStore.Load(ctx, sessionID)
	if err != nil {
		t.Fatalf("加载检查点失败: %v", err)
	}

	// 验证检查点数据
	if checkpointData.AgentName != "checkpoint-agent" {
		t.Errorf("智能体名称不匹配: got %s, want %s", checkpointData.AgentName, "checkpoint-agent")
	}

	// 验证状态数据
	if checkpointData.State["test_key"] != "test_value" {
		t.Errorf("状态数据不匹配: got %v, want %s", checkpointData.State["test_key"], "test_value")
	}

	// 验证元数据
	if eventCount, exists := checkpointData.Metadata["event_count"]; !exists {
		t.Error("检查点元数据应该包含事件计数")
	} else if count, ok := eventCount.(int); !ok || count <= 0 {
		t.Errorf("事件计数应该大于0: got %v", eventCount)
	}
}

// sessionModifyingAgent 可以修改Session的测试Agent
type sessionModifyingAgent struct {
	name     string
	modifier func(session.Map)
	events   []*event.Event
}

func newSessionModifyingAgent(name string, modifier func(session.Map)) *sessionModifyingAgent {
	return &sessionModifyingAgent{
		name:     name,
		modifier: modifier,
		events:   make([]*event.Event, 0),
	}
}

func (a *sessionModifyingAgent) addEvent(ev *event.Event) {
	a.events = append(a.events, ev)
}

func (a *sessionModifyingAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *sessionModifyingAgent) Description(ctx context.Context) string {
	return fmt.Sprintf("Session修改测试Agent: %s", a.name)
}

func (a *sessionModifyingAgent) Run(ctx context.Context, input *Input) *AsyncIterator[*event.Event] {
	pair := NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 修改Session
		if a.modifier != nil && input.Session != nil {
			a.modifier(input.Session)
		}

		// 发送预设事件
		for _, ev := range a.events {
			if err := pair.Generator.Send(ev); err != nil {
				return
			}
		}

		// 发送最终事件
		finalEvent := event.NewFinalEvent(fmt.Sprintf("%s completed", a.name), "完成", nil)
		pair.Generator.Send(finalEvent)
	}()

	return pair.Iterator
}

// TestRunnerSessionSharing 测试多个Agent之间的Session共享
func TestRunnerSessionSharing(t *testing.T) {
	ctx := context.Background()
	config := DefaultRunnerConfig()
	runner := NewRunner(ctx, config)

	// 创建共享的Session
	sharedSession := session.New()
	sharedSession.Set("shared_data", "initial_value")
	sharedSession.Set("counter", 0)

	// 第一个Agent：修改Session数据
	agent1 := newSessionModifyingAgent("agent1", func(s session.Map) {
		s.Set("shared_data", "modified_by_agent1")
		if counter, exists := s.Get("counter"); exists {
			if count, ok := counter.(int); ok {
				s.Set("counter", count+1)
			}
		}
		s.Set("agent1_executed", true)
	})
	agent1.addEvent(event.NewTokenEvent("Agent1 processing", false))

	input1 := NewInput()
	input1.AddMessage(message.NewUserMessage("Agent1 任务"))
	input1.SetSession(sharedSession)

	// 执行第一个Agent
	_, err := runner.RunAndCollect(ctx, agent1, input1)
	if err != nil {
		t.Fatalf("Agent1执行失败: %v", err)
	}

	// 验证Session数据被修改
	if data, exists := sharedSession.Get("shared_data"); !exists || data != "modified_by_agent1" {
		t.Errorf("Agent1应该修改shared_data: got %v", data)
	}

	if counter, exists := sharedSession.Get("counter"); !exists || counter != 1 {
		t.Errorf("Agent1应该增加counter: got %v", counter)
	}

	// 第二个Agent：读取并继续修改Session
	agent2 := newSessionModifyingAgent("agent2", func(s session.Map) {
		// 验证能够读取Agent1设置的数据
		if data, exists := s.Get("shared_data"); exists && data == "modified_by_agent1" {
			s.Set("agent2_read_agent1_data", true)
		}

		// 继续修改数据
		if counter, exists := s.Get("counter"); exists {
			if count, ok := counter.(int); ok {
				s.Set("counter", count+1)
			}
		}
		s.Set("agent2_executed", true)
	})
	agent2.addEvent(event.NewTokenEvent("Agent2 processing", false))

	input2 := NewInput()
	input2.AddMessage(message.NewUserMessage("Agent2 任务"))
	input2.SetSession(sharedSession) // 使用相同的Session

	// 执行第二个Agent
	_, err = runner.RunAndCollect(ctx, agent2, input2)
	if err != nil {
		t.Fatalf("Agent2执行失败: %v", err)
	}

	// 验证Session数据的共享和累积
	if flag, exists := sharedSession.Get("agent2_read_agent1_data"); !exists || flag != true {
		t.Error("Agent2应该能够读取Agent1设置的数据")
	}

	if counter, exists := sharedSession.Get("counter"); !exists || counter != 2 {
		t.Errorf("counter应该被两个Agent累积修改: got %v, want 2", counter)
	}

	if flag, exists := sharedSession.Get("agent1_executed"); !exists || flag != true {
		t.Error("应该记录Agent1的执行")
	}

	if flag, exists := sharedSession.Get("agent2_executed"); !exists || flag != true {
		t.Error("应该记录Agent2的执行")
	}

	// 验证Session的并发安全性
	t.Logf("Session最终状态:")
	for _, key := range sharedSession.Keys() {
		if value, exists := sharedSession.Get(key); exists {
			t.Logf("  %s: %v", key, value)
		}
	}
}
