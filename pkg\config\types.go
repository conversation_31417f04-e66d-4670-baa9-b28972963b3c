package config

import (
	"time"
)

// Config represents the main application configuration
type Config struct {
	App *AppConfig `yaml:"app" json:"app"`
	// Agents map[string]*agent.Config `yaml:"agents,omitempty" json:"agents,omitempty"` // 暂时禁用
	// Pipelines map[string]*pipeline.Config `yaml:"pipelines,omitempty" json:"pipelines,omitempty"` // 暂时禁用
	LLM                   *LLMConfig                   `yaml:"llm,omitempty" json:"llm,omitempty"`
	Logging               *LoggingConfig               `yaml:"logging,omitempty" json:"logging,omitempty"`
	Web                   *WebConfig                   `yaml:"web,omitempty" json:"web,omitempty"`
	Audit                 *AuditConfig                 `yaml:"audit,omitempty" json:"audit,omitempty"`
	WeChatCustomerService *WeChatCustomerServiceConfig `yaml:"wechat_customer_service,omitempty" json:"wechat_customer_service,omitempty"`

	// Agents holds agent configurations
	Agents map[string]*AgentConfig `yaml:"agents,omitempty" json:"agents,omitempty"`
}

// AppConfig holds general application configuration
type AppConfig struct {
	Name        string `yaml:"name" json:"name"`
	Version     string `yaml:"version" json:"version"`
	Environment string `yaml:"environment" json:"environment"`
	Debug       bool   `yaml:"debug" json:"debug"`
}

// LLMConfig holds LLM service configuration
type LLMConfig struct {
	DefaultProvider string                  `yaml:"default_provider" json:"default_provider"`
	Providers       map[string]*LLMProvider `yaml:"providers,omitempty" json:"providers,omitempty"`
	Timeout         time.Duration           `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	MaxRetries      int                     `yaml:"max_retries,omitempty" json:"max_retries,omitempty"`
	Parameters      map[string]interface{}  `yaml:"parameters,omitempty" json:"parameters,omitempty"`
}

// LLMProvider holds configuration for a specific LLM provider
type LLMProvider struct {
	Type       string                 `yaml:"type" json:"type"`
	APIKey     string                 `yaml:"api_key,omitempty" json:"api_key,omitempty"`
	BaseURL    string                 `yaml:"base_url,omitempty" json:"base_url,omitempty"`
	Model      string                 `yaml:"model,omitempty" json:"model,omitempty"`
	Timeout    time.Duration          `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	MaxRetries int                    `yaml:"max_retries,omitempty" json:"max_retries,omitempty"`
	Parameters map[string]interface{} `yaml:"parameters,omitempty" json:"parameters,omitempty"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `yaml:"level" json:"level"`
	Format string `yaml:"format" json:"format"`
	Output string `yaml:"output" json:"output"`
}

// WebConfig holds web service configuration
type WebConfig struct {
	Enabled bool        `yaml:"enabled" json:"enabled"`
	Host    string      `yaml:"host" json:"host"`
	Port    int         `yaml:"port" json:"port"`
	TLS     *TLSConfig  `yaml:"tls,omitempty" json:"tls,omitempty"`
	CORS    *CORSConfig `yaml:"cors,omitempty" json:"cors,omitempty"`
	Auth    *AuthConfig `yaml:"auth,omitempty" json:"auth,omitempty"`
}

// TLSConfig holds TLS configuration
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" json:"enabled"`
	CertFile string `yaml:"cert_file,omitempty" json:"cert_file,omitempty"`
	KeyFile  string `yaml:"key_file,omitempty" json:"key_file,omitempty"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	Enabled          bool     `yaml:"enabled" json:"enabled"`
	AllowedOrigins   []string `yaml:"allowed_origins,omitempty" json:"allowed_origins,omitempty"`
	AllowedMethods   []string `yaml:"allowed_methods,omitempty" json:"allowed_methods,omitempty"`
	AllowedHeaders   []string `yaml:"allowed_headers,omitempty" json:"allowed_headers,omitempty"`
	AllowCredentials bool     `yaml:"allow_credentials" json:"allow_credentials"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	Enabled    bool          `yaml:"enabled" json:"enabled"`
	Type       string        `yaml:"type" json:"type"`
	SecretKey  string        `yaml:"secret_key,omitempty" json:"secret_key,omitempty"`
	Expiration time.Duration `yaml:"expiration,omitempty" json:"expiration,omitempty"`
}

// AuditConfig holds audit configuration
type AuditConfig struct {
	Enabled   bool                  `yaml:"enabled" json:"enabled"`
	Driver    string                `yaml:"driver" json:"driver"`
	DSN       string                `yaml:"dsn" json:"dsn"`
	Retention *AuditRetentionConfig `yaml:"retention,omitempty" json:"retention,omitempty"`
	Privacy   *AuditPrivacyConfig   `yaml:"privacy,omitempty" json:"privacy,omitempty"`
	Batch     *AuditBatchConfig     `yaml:"batch,omitempty" json:"batch,omitempty"`
	Web       *AuditWebConfig       `yaml:"web,omitempty" json:"web,omitempty"`
}

// AuditRetentionConfig holds audit data retention configuration
type AuditRetentionConfig struct {
	Enabled bool   `yaml:"enabled" json:"enabled"`
	MaxDays int    `yaml:"max_days" json:"max_days"`
	Cron    string `yaml:"cron" json:"cron"`
}

// AuditPrivacyConfig holds audit privacy configuration
type AuditPrivacyConfig struct {
	RedactPII     bool     `yaml:"redact_pii" json:"redact_pii"`
	PIIPatterns   []string `yaml:"pii_patterns,omitempty" json:"pii_patterns,omitempty"`
	HashContent   bool     `yaml:"hash_content" json:"hash_content"`
	EncryptAtRest bool     `yaml:"encrypt_at_rest" json:"encrypt_at_rest"`
	EncryptKeyEnv string   `yaml:"encrypt_key_env,omitempty" json:"encrypt_key_env,omitempty"`
}

// AuditBatchConfig holds audit batch processing configuration
type AuditBatchConfig struct {
	Async         bool          `yaml:"async" json:"async"`
	ChanBuffer    int           `yaml:"chan_buffer" json:"chan_buffer"`
	FlushInterval time.Duration `yaml:"flush_interval" json:"flush_interval"`
}

// AuditWebConfig holds audit web API configuration
type AuditWebConfig struct {
	AllowReadAPI   bool `yaml:"allow_read_api" json:"allow_read_api"`
	AllowDeleteAPI bool `yaml:"allow_delete_api" json:"allow_delete_api"`
}

// WeChatCustomerServiceConfig holds WeChat customer service configuration
type WeChatCustomerServiceConfig struct {
	CorpID            string                   `yaml:"corp_id,omitempty" json:"corp_id,omitempty"`
	CorpSecret        string                   `yaml:"corp_secret,omitempty" json:"corp_secret,omitempty"`
	AgentID           string                   `yaml:"agent_id,omitempty" json:"agent_id,omitempty"`
	ServiceHours      *ServiceHoursConfig      `yaml:"service_hours,omitempty" json:"service_hours,omitempty"`
	Classification    *ClassificationConfig    `yaml:"classification,omitempty" json:"classification,omitempty"`
	AutoReply         *AutoReplyConfig         `yaml:"auto_reply,omitempty" json:"auto_reply,omitempty"`
	TicketSystem      *TicketSystemConfig      `yaml:"ticket_system,omitempty" json:"ticket_system,omitempty"`
	EmotionDetection  *EmotionDetectionConfig  `yaml:"emotion_detection,omitempty" json:"emotion_detection,omitempty"`
	QualityAssessment *QualityAssessmentConfig `yaml:"quality_assessment,omitempty" json:"quality_assessment,omitempty"`
}

// ServiceHoursConfig holds service hours configuration
type ServiceHoursConfig struct {
	Start    string `yaml:"start" json:"start"`
	End      string `yaml:"end" json:"end"`
	Timezone string `yaml:"timezone" json:"timezone"`
}

// ClassificationConfig holds question classification configuration
type ClassificationConfig struct {
	Categories []*CategoryConfig `yaml:"categories,omitempty" json:"categories,omitempty"`
}

// CategoryConfig holds category configuration
type CategoryConfig struct {
	Name        string   `yaml:"name" json:"name"`
	Description string   `yaml:"description,omitempty" json:"description,omitempty"`
	Keywords    []string `yaml:"keywords,omitempty" json:"keywords,omitempty"`
	Priority    string   `yaml:"priority" json:"priority"`
}

// AutoReplyConfig holds auto reply configuration
type AutoReplyConfig struct {
	Enabled         bool                    `yaml:"enabled" json:"enabled"`
	CommonQuestions []*CommonQuestionConfig `yaml:"common_questions,omitempty" json:"common_questions,omitempty"`
}

// CommonQuestionConfig holds common question configuration
type CommonQuestionConfig struct {
	Question string `yaml:"question" json:"question"`
	Answer   string `yaml:"answer" json:"answer"`
}

// TicketSystemConfig holds ticket system configuration
type TicketSystemConfig struct {
	Enabled             bool `yaml:"enabled" json:"enabled"`
	AutoCreateThreshold int  `yaml:"auto_create_threshold" json:"auto_create_threshold"`
	EscalationTime      int  `yaml:"escalation_time" json:"escalation_time"`
}

// EmotionDetectionConfig holds emotion detection configuration
type EmotionDetectionConfig struct {
	Enabled             bool     `yaml:"enabled" json:"enabled"`
	NegativeKeywords    []string `yaml:"negative_keywords,omitempty" json:"negative_keywords,omitempty"`
	EscalationThreshold float64  `yaml:"escalation_threshold" json:"escalation_threshold"`
}

// QualityAssessmentConfig holds quality assessment configuration
type QualityAssessmentConfig struct {
	Enabled bool     `yaml:"enabled" json:"enabled"`
	Metrics []string `yaml:"metrics,omitempty" json:"metrics,omitempty"`
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		App: &AppConfig{
			Name:        "AgentScope-Golang",
			Version:     "0.1.0-MVP",
			Environment: "development",
			Debug:       true,
		},
		// Agents: make(map[string]*agent.Config), // 暂时禁用
		// Pipelines: make(map[string]*pipeline.Config), // 暂时禁用
		LLM: &LLMConfig{
			DefaultProvider: "deepseek",
			Providers: map[string]*LLMProvider{
				"deepseek": {
					Type:       "deepseek",
					BaseURL:    "https://api.deepseek.com/v1",
					Model:      "deepseek-chat",
					Timeout:    30 * time.Second,
					MaxRetries: 3,
					Parameters: map[string]interface{}{
						"temperature": 0.7,
						"max_tokens":  2048,
					},
				},
			},
			Timeout:    30 * time.Second,
			MaxRetries: 3,
			Parameters: make(map[string]interface{}),
		},
		Logging: &LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		},
		Web: &WebConfig{
			Enabled: false,
			Host:    "localhost",
			Port:    8080,
			TLS: &TLSConfig{
				Enabled: false,
			},
			CORS: &CORSConfig{
				Enabled:          true,
				AllowedOrigins:   []string{"*"},
				AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
				AllowedHeaders:   []string{"*"},
				AllowCredentials: false,
			},
			Auth: &AuthConfig{
				Enabled:    false,
				Type:       "jwt",
				Expiration: 24 * time.Hour,
			},
		},
		Audit: &AuditConfig{
			Enabled: false,
			Driver:  "sqlite",
			DSN:     "file:audit.db?_journal_mode=WAL",
			Retention: &AuditRetentionConfig{
				Enabled: true,
				MaxDays: 90,
				Cron:    "@daily",
			},
			Privacy: &AuditPrivacyConfig{
				RedactPII:     true,
				PIIPatterns:   []string{"email", "phone"},
				HashContent:   false,
				EncryptAtRest: false,
				EncryptKeyEnv: "AUDIT_AES_KEY",
			},
			Batch: &AuditBatchConfig{
				Async:         true,
				ChanBuffer:    1024,
				FlushInterval: time.Second,
			},
			Web: &AuditWebConfig{
				AllowReadAPI:   false,
				AllowDeleteAPI: false,
			},
		},
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.App == nil {
		c.App = &AppConfig{
			Name:        "AgentScope-Golang",
			Version:     "0.1.0-MVP",
			Environment: "development",
			Debug:       true,
		}
	}

	if err := c.App.Validate(); err != nil {
		return &ValidationError{Field: "app", Message: err.Error()}
	}

	// if c.Agents == nil {
	//	c.Agents = make(map[string]*agent.Config)
	// } // 暂时禁用

	// if c.Pipelines == nil {
	//	c.Pipelines = make(map[string]*pipeline.Config)
	// } // 暂时禁用

	if c.LLM != nil {
		if err := c.LLM.Validate(); err != nil {
			return &ValidationError{Field: "llm", Message: err.Error()}
		}
	}

	if c.Logging != nil {
		if err := c.Logging.Validate(); err != nil {
			return &ValidationError{Field: "logging", Message: err.Error()}
		}
	}

	if c.Web != nil {
		if err := c.Web.Validate(); err != nil {
			return &ValidationError{Field: "web", Message: err.Error()}
		}
	}

	if c.Audit != nil {
		if err := c.Audit.Validate(); err != nil {
			return &ValidationError{Field: "audit", Message: err.Error()}
		}
	}

	// Validate agent configurations
	// for name, agentConfig := range c.Agents {
	//	if err := agentConfig.Validate(); err != nil {
	//		return &ValidationError{Field: "agents." + name, Message: err.Error()}
	//	}
	// } // 暂时禁用

	// Validate pipeline configurations
	// for name, pipelineConfig := range c.Pipelines {
	//	if err := pipelineConfig.Validate(); err != nil {
	//		return &ValidationError{Field: "pipelines." + name, Message: err.Error()}
	//	}
	// } // 暂时禁用

	return nil
}

// Validate validates the app configuration
func (ac *AppConfig) Validate() error {
	if ac.Name == "" {
		return &ValidationError{Field: "name", Message: "app name cannot be empty"}
	}
	if ac.Version == "" {
		ac.Version = "0.1.0"
	}
	if ac.Environment == "" {
		ac.Environment = "development"
	}
	return nil
}

// Validate validates the LLM configuration
func (lc *LLMConfig) Validate() error {
	if lc.DefaultProvider == "" {
		return &ValidationError{Field: "default_provider", Message: "default provider cannot be empty"}
	}

	if lc.Providers == nil {
		lc.Providers = make(map[string]*LLMProvider)
	}

	if lc.Parameters == nil {
		lc.Parameters = make(map[string]interface{})
	}

	if lc.Timeout <= 0 {
		lc.Timeout = 30 * time.Second
	}

	if lc.MaxRetries < 0 {
		lc.MaxRetries = 3
	}

	// Validate that default provider exists
	if _, exists := lc.Providers[lc.DefaultProvider]; !exists {
		return &ValidationError{Field: "default_provider", Message: "default provider not found in providers"}
	}

	// Validate provider configurations
	for name, provider := range lc.Providers {
		if err := provider.Validate(); err != nil {
			return &ValidationError{Field: "providers." + name, Message: err.Error()}
		}
	}

	return nil
}

// Validate validates the LLM provider configuration
func (lp *LLMProvider) Validate() error {
	if lp.Type == "" {
		return &ValidationError{Field: "type", Message: "provider type cannot be empty"}
	}

	if lp.Parameters == nil {
		lp.Parameters = make(map[string]interface{})
	}

	if lp.Timeout <= 0 {
		lp.Timeout = 30 * time.Second
	}

	if lp.MaxRetries < 0 {
		lp.MaxRetries = 3
	}

	return nil
}

// Validate validates the logging configuration
func (lc *LoggingConfig) Validate() error {
	if lc.Level == "" {
		lc.Level = "info"
	}
	if lc.Format == "" {
		lc.Format = "text"
	}
	if lc.Output == "" {
		lc.Output = "stdout"
	}
	return nil
}

// Validate validates the web configuration
func (wc *WebConfig) Validate() error {
	if wc.Host == "" {
		wc.Host = "localhost"
	}
	if wc.Port <= 0 {
		wc.Port = 8080
	}

	if wc.TLS != nil {
		if err := wc.TLS.Validate(); err != nil {
			return &ValidationError{Field: "tls", Message: err.Error()}
		}
	}

	if wc.CORS != nil {
		if err := wc.CORS.Validate(); err != nil {
			return &ValidationError{Field: "cors", Message: err.Error()}
		}
	}

	if wc.Auth != nil {
		if err := wc.Auth.Validate(); err != nil {
			return &ValidationError{Field: "auth", Message: err.Error()}
		}
	}

	return nil
}

// Validate validates the TLS configuration
func (tc *TLSConfig) Validate() error {
	if tc.Enabled {
		if tc.CertFile == "" {
			return &ValidationError{Field: "cert_file", Message: "cert file is required when TLS is enabled"}
		}
		if tc.KeyFile == "" {
			return &ValidationError{Field: "key_file", Message: "key file is required when TLS is enabled"}
		}
	}
	return nil
}

// Validate validates the CORS configuration
func (cc *CORSConfig) Validate() error {
	if cc.Enabled {
		if len(cc.AllowedOrigins) == 0 {
			cc.AllowedOrigins = []string{"*"}
		}
		if len(cc.AllowedMethods) == 0 {
			cc.AllowedMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
		}
		if len(cc.AllowedHeaders) == 0 {
			cc.AllowedHeaders = []string{"*"}
		}
	}
	return nil
}

// Validate validates the auth configuration
func (ac *AuthConfig) Validate() error {
	if ac.Enabled {
		if ac.Type == "" {
			ac.Type = "jwt"
		}
		if ac.Expiration <= 0 {
			ac.Expiration = 24 * time.Hour
		}
	}
	return nil
}

// Validate validates the audit configuration
func (ac *AuditConfig) Validate() error {
	if ac.Driver == "" {
		ac.Driver = "sqlite"
	}

	if ac.DSN == "" {
		if ac.Driver == "sqlite" {
			ac.DSN = "file:audit.db?_journal_mode=WAL"
		}
	}

	if ac.Retention != nil {
		if err := ac.Retention.Validate(); err != nil {
			return &ValidationError{Field: "retention", Message: err.Error()}
		}
	}

	if ac.Privacy != nil {
		if err := ac.Privacy.Validate(); err != nil {
			return &ValidationError{Field: "privacy", Message: err.Error()}
		}
	}

	if ac.Batch != nil {
		if err := ac.Batch.Validate(); err != nil {
			return &ValidationError{Field: "batch", Message: err.Error()}
		}
	}

	if ac.Web != nil {
		if err := ac.Web.Validate(); err != nil {
			return &ValidationError{Field: "web", Message: err.Error()}
		}
	}

	return nil
}

// Validate validates the audit retention configuration
func (arc *AuditRetentionConfig) Validate() error {
	if arc.MaxDays <= 0 {
		arc.MaxDays = 90
	}
	if arc.Cron == "" {
		arc.Cron = "@daily"
	}
	return nil
}

// Validate validates the audit privacy configuration
func (apc *AuditPrivacyConfig) Validate() error {
	if len(apc.PIIPatterns) == 0 && apc.RedactPII {
		apc.PIIPatterns = []string{"email", "phone"}
	}
	if apc.EncryptKeyEnv == "" && apc.EncryptAtRest {
		apc.EncryptKeyEnv = "AUDIT_AES_KEY"
	}
	return nil
}

// Validate validates the audit batch configuration
func (abc *AuditBatchConfig) Validate() error {
	if abc.ChanBuffer <= 0 {
		abc.ChanBuffer = 1024
	}
	if abc.FlushInterval <= 0 {
		abc.FlushInterval = time.Second
	}
	return nil
}

// Validate validates the audit web configuration
func (awc *AuditWebConfig) Validate() error {
	// No specific validation needed for boolean fields
	return nil
}

// Clone creates a deep copy of the configuration
func (c *Config) Clone() *Config {
	// This is a simplified clone - in production, you might want to use a library like copier
	clone := *c

	if c.App != nil {
		appClone := *c.App
		clone.App = &appClone
	}

	// if c.Agents != nil {
	//	clone.Agents = make(map[string]*agent.Config)
	//	for k, v := range c.Agents {
	//		clone.Agents[k] = v.Clone()
	//	}
	// } // 暂时禁用

	// if c.Pipelines != nil {
	//	clone.Pipelines = make(map[string]*pipeline.Config)
	//	for k, v := range c.Pipelines {
	//		clone.Pipelines[k] = v.Clone()
	//	}
	// } // 暂时禁用

	// Add other field cloning as needed

	return &clone
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}

// AgentConfig Agent配置
type AgentConfig struct {
	Name         string                 `yaml:"name" json:"name"`                 // Agent名称
	Description  string                 `yaml:"description" json:"description"`   // Agent描述
	Role         *RoleConfig            `yaml:"role" json:"role"`                 // 角色配置
	Tools        []string               `yaml:"tools" json:"tools"`               // 工具列表
	Capabilities *CapabilitiesConfig    `yaml:"capabilities" json:"capabilities"` // 能力配置
	Parameters   map[string]interface{} `yaml:"parameters" json:"parameters"`     // 其他参数
}

// RoleConfig 角色配置
type RoleConfig struct {
	SystemPrompt string                `yaml:"system_prompt" json:"system_prompt"` // 系统提示词
	Personality  string                `yaml:"personality" json:"personality"`     // 个性描述
	Expertise    []string              `yaml:"expertise" json:"expertise"`         // 专业领域
	Constraints  []string              `yaml:"constraints" json:"constraints"`     // 约束条件
	Examples     []ConversationExample `yaml:"examples" json:"examples"`           // 对话示例
}

// ConversationExample 对话示例
type ConversationExample struct {
	User      string `yaml:"user" json:"user"`           // 用户输入
	Assistant string `yaml:"assistant" json:"assistant"` // 助手回复
	Context   string `yaml:"context" json:"context"`     // 上下文说明
}

// CapabilitiesConfig 能力配置
type CapabilitiesConfig struct {
	Classification    *CapabilityInstanceConfig  `yaml:"classification" json:"classification"`
	EmotionAnalysis   *CapabilityInstanceConfig  `yaml:"emotion_analysis" json:"emotion_analysis"`
	QualityAssessment *CapabilityInstanceConfig  `yaml:"quality_assessment" json:"quality_assessment"`
	AutoReply         *AutoReplyCapabilityConfig `yaml:"auto_reply" json:"auto_reply"`
}

// CapabilityInstanceConfig 能力实例配置
type CapabilityInstanceConfig struct {
	Enabled    bool                   `yaml:"enabled" json:"enabled"`       // 是否启用
	Provider   string                 `yaml:"provider" json:"provider"`     // 提供者名称
	Parameters map[string]interface{} `yaml:"parameters" json:"parameters"` // 参数配置
}

// AutoReplyCapabilityConfig 自动回复能力配置
type AutoReplyCapabilityConfig struct {
	Enabled bool  `yaml:"enabled" json:"enabled"` // 是否启用
	FAQs    []FAQ `yaml:"faqs" json:"faqs"`       // 常见问题列表
}

// FAQ 常见问题
type FAQ struct {
	Question string   `yaml:"question" json:"question"` // 问题
	Answer   string   `yaml:"answer" json:"answer"`     // 答案
	Keywords []string `yaml:"keywords" json:"keywords"` // 关键词
}

// PromptDrivenAgentConfig 提示词驱动Agent配置
type PromptDrivenAgentConfig struct {
	Name                  string          `yaml:"name" json:"name"`
	Description           string          `yaml:"description" json:"description"`
	BaseRole              string          `yaml:"base_role" json:"base_role"`
	AiCapabilities        []string        `yaml:"ai_capabilities" json:"ai_capabilities"`
	Tools                 []string        `yaml:"tools" json:"tools"`
	PersonalityTraits     []string        `yaml:"personality_traits" json:"personality_traits"`
	BehavioralConstraints []string        `yaml:"behavioral_constraints" json:"behavioral_constraints"`
	BusinessConfig        *BusinessConfig `yaml:"business_config" json:"business_config"`
}

// BusinessConfig 业务配置
type BusinessConfig struct {
	Classification   *ClassificationConfig   `yaml:"classification" json:"classification"`
	FAQs             []FAQ                   `yaml:"faqs,omitempty" json:"faqs,omitempty"` // 已废弃，使用KnowledgeBases替代
	KnowledgeBases   *KnowledgeBasesConfig   `yaml:"knowledge_bases" json:"knowledge_bases"`
	EmotionHandling  *EmotionHandlingConfig  `yaml:"emotion_handling" json:"emotion_handling"`
	QualityStandards *QualityStandardsConfig `yaml:"quality_standards" json:"quality_standards"`
}

// 使用现有的ClassificationConfig和CategoryConfig类型

// EmotionHandlingConfig 情绪处理配置
type EmotionHandlingConfig struct {
	EscalationThreshold float64  `yaml:"escalation_threshold" json:"escalation_threshold"`
	NegativeKeywords    []string `yaml:"negative_keywords" json:"negative_keywords"`
	CalmingPhrases      []string `yaml:"calming_phrases" json:"calming_phrases"`
}

// QualityStandardsConfig 质量标准配置
type QualityStandardsConfig struct {
	ResponseTime      string   `yaml:"response_time" json:"response_time"`
	ResolutionRate    string   `yaml:"resolution_rate" json:"resolution_rate"`
	SatisfactionScore string   `yaml:"satisfaction_score" json:"satisfaction_score"`
	KeyMetrics        []string `yaml:"key_metrics" json:"key_metrics"`
}

// KnowledgeBasesConfig 知识库配置
type KnowledgeBasesConfig struct {
	// 存储配置
	Repository KnowledgeRepositoryConfig `yaml:"repository" json:"repository"`
	// 检索配置
	Retriever KnowledgeRetrieverConfig `yaml:"retriever" json:"retriever"`
	// 知识库列表
	Bases []KnowledgeBaseRef `yaml:"bases" json:"bases"`
}

// KnowledgeRepositoryConfig 知识库存储配置
type KnowledgeRepositoryConfig struct {
	// 存储类型：file, database, api
	Type string `yaml:"type" json:"type"`
	// 基础路径（文件存储）
	BasePath string `yaml:"base_path,omitempty" json:"base_path,omitempty"`
	// 数据库连接（数据库存储）
	DatabaseURL string `yaml:"database_url,omitempty" json:"database_url,omitempty"`
	// API配置（API存储）
	APIConfig map[string]interface{} `yaml:"api_config,omitempty" json:"api_config,omitempty"`
}

// KnowledgeRetrieverConfig 知识检索配置
type KnowledgeRetrieverConfig struct {
	// 检索算法：keyword, semantic, hybrid
	Algorithm string `yaml:"algorithm" json:"algorithm"`
	// 最小相似度分数
	MinScore float64 `yaml:"min_score" json:"min_score"`
	// 最大返回结果数
	MaxResults int `yaml:"max_results" json:"max_results"`
	// 缓存配置
	Cache KnowledgeCacheConfig `yaml:"cache" json:"cache"`
}

// KnowledgeCacheConfig 知识库缓存配置
type KnowledgeCacheConfig struct {
	// 是否启用缓存
	Enabled bool `yaml:"enabled" json:"enabled"`
	// 缓存过期时间（秒）
	TTL int `yaml:"ttl" json:"ttl"`
	// 最大缓存大小
	MaxSize int `yaml:"max_size" json:"max_size"`
}

// KnowledgeBaseRef 知识库引用
type KnowledgeBaseRef struct {
	// 知识库ID
	ID string `yaml:"id" json:"id"`
	// 知识库名称
	Name string `yaml:"name,omitempty" json:"name,omitempty"`
	// 是否启用
	Enabled bool `yaml:"enabled" json:"enabled"`
	// 优先级（1-10，数字越大优先级越高）
	Priority int `yaml:"priority" json:"priority"`
	// 角色过滤（为空表示所有角色）
	Roles []string `yaml:"roles,omitempty" json:"roles,omitempty"`
}
