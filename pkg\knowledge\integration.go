package knowledge

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
)

// AgentKnowledgeManager Agent知识管理器
type AgentKnowledgeManager struct {
	knowledgeBase KnowledgeBase
	retrieval     *RetrievalSystem
	agentID       string
	logger        logger.Logger
}

// NewAgentKnowledgeManager 创建Agent知识管理器
func NewAgentKnowledgeManager(kb KnowledgeBase, agentID string) *AgentKnowledgeManager {
	return &AgentKnowledgeManager{
		knowledgeBase: kb,
		retrieval:     NewRetrievalSystem(kb),
		agentID:       agentID,
		logger:        logger.GetGlobalLogger(),
	}
}

// AddMessageAsKnowledge 将消息添加为知识
func (akm *AgentKnowledgeManager) AddMessageAsKnowledge(ctx context.Context, msg *message.Message) error {
	if msg == nil {
		return errors.NewValidationError("invalid_input", "message cannot be nil")
	}

	// 将消息转换为文档
	doc := &Document{
		ID:      fmt.Sprintf("msg-%s-%s", akm.agentID, msg.ID),
		Title:   fmt.Sprintf("Message from %s", msg.Name),
		Content: fmt.Sprintf("%v", msg.Content),
		Type:    DocumentTypeText,
		Source:  fmt.Sprintf("agent-%s", akm.agentID),
		Tags:    []string{"message", msg.Name, akm.agentID},
		Metadata: map[string]interface{}{
			"agent_id":       akm.agentID,
			"message_id":     msg.ID,
			"message_sender": msg.Name,
			"timestamp":      msg.Timestamp,
		},
	}

	// 添加到知识库
	if err := akm.knowledgeBase.AddDocument(ctx, doc); err != nil {
		return err
	}

	akm.logger.Debug("Added message %s as knowledge document %s", msg.ID, doc.ID)
	return nil
}

// SearchRelevantKnowledge 搜索相关知识
func (akm *AgentKnowledgeManager) SearchRelevantKnowledge(ctx context.Context, query string, limit int) ([]*Document, error) {
	if query == "" {
		return []*Document{}, nil
	}

	if limit <= 0 {
		limit = 5
	}

	// 执行搜索
	searchQuery := &SearchQuery{
		Query: query,
		Limit: limit,
	}

	results, err := akm.knowledgeBase.SearchDocuments(ctx, searchQuery)
	if err != nil {
		return nil, err
	}

	// 提取文档
	documents := make([]*Document, len(results))
	for i, result := range results {
		documents[i] = result.Document
	}

	akm.logger.Debug("Found %d relevant knowledge documents for query: %s", len(documents), query)
	return documents, nil
}

// BuildContextFromKnowledge 从知识构建上下文
func (akm *AgentKnowledgeManager) BuildContextFromKnowledge(ctx context.Context, query string, maxTokens int) (string, error) {
	if maxTokens <= 0 {
		maxTokens = 2000
	}

	// 搜索相关知识
	documents, err := akm.SearchRelevantKnowledge(ctx, query, 10)
	if err != nil {
		return "", err
	}

	if len(documents) == 0 {
		return "", nil
	}

	// 构建上下文
	var contextParts []string
	currentTokens := 0

	for _, doc := range documents {
		// 简单的token估算：每个字符约0.25个token
		docTokens := len(doc.Content) / 4

		if currentTokens+docTokens > maxTokens {
			// 如果添加这个文档会超过限制，截断内容
			remainingTokens := maxTokens - currentTokens
			if remainingTokens > 100 {
				truncatedContent := doc.Content[:remainingTokens*4]
				contextParts = append(contextParts, fmt.Sprintf("**%s**\n%s...", doc.Title, truncatedContent))
			}
			break
		}

		contextParts = append(contextParts, fmt.Sprintf("**%s**\n%s", doc.Title, doc.Content))
		currentTokens += docTokens
	}

	if len(contextParts) == 0 {
		return "", nil
	}

	context := "相关知识:\n\n" + strings.Join(contextParts, "\n\n---\n\n")
	akm.logger.Debug("Built knowledge context with %d documents, estimated %d tokens", len(contextParts), currentTokens)

	return context, nil
}

// AgentKnowledgeStats Agent知识统计
type AgentKnowledgeStats struct {
	AgentID         string         `json:"agent_id"`
	TotalDocuments  int            `json:"total_documents"`
	KnowledgeTypes  map[string]int `json:"knowledge_types"`
	GlobalDocuments int            `json:"global_documents"`
	GlobalEntities  int            `json:"global_entities"`
	GlobalRelations int            `json:"global_relations"`
	LastUpdated     time.Time      `json:"last_updated"`
}

// GetKnowledgeStats 获取知识统计
func (akm *AgentKnowledgeManager) GetKnowledgeStats(ctx context.Context) (*AgentKnowledgeStats, error) {
	// 获取全局统计
	globalStats, err := akm.knowledgeBase.GetStats(ctx)
	if err != nil {
		return nil, err
	}

	stats := &AgentKnowledgeStats{
		AgentID:         akm.agentID,
		TotalDocuments:  0,
		KnowledgeTypes:  make(map[string]int),
		GlobalDocuments: globalStats.DocumentCount,
		GlobalEntities:  globalStats.EntityCount,
		GlobalRelations: globalStats.RelationCount,
		LastUpdated:     time.Now(),
	}

	return stats, nil
}

// AddFactualKnowledge 添加事实性知识
func (akm *AgentKnowledgeManager) AddFactualKnowledge(ctx context.Context, title, content string, tags []string) error {
	if title == "" || content == "" {
		return errors.NewValidationError("invalid_input", "title and content cannot be empty")
	}

	// 创建文档
	doc := &Document{
		ID:      fmt.Sprintf("fact-%s-%d", akm.agentID, time.Now().UnixNano()),
		Title:   title,
		Content: content,
		Type:    DocumentTypeText,
		Source:  fmt.Sprintf("agent-%s", akm.agentID),
		Tags:    append(tags, "factual", akm.agentID),
		Metadata: map[string]interface{}{
			"agent_id":  akm.agentID,
			"type":      "factual",
			"timestamp": time.Now(),
		},
	}

	// 添加到知识库
	if err := akm.knowledgeBase.AddDocument(ctx, doc); err != nil {
		return err
	}

	akm.logger.Debug("Added factual knowledge: %s", title)
	return nil
}
