package main

import (
	"context"
	"fmt"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// StreamingAgent 模拟流式输出的智能体
type StreamingAgent struct {
	name string
}

func NewStreamingAgent(name string) *StreamingAgent {
	return &StreamingAgent{name: name}
}

func (a *StreamingAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *StreamingAgent) Description(ctx context.Context) string {
	return fmt.Sprintf("流式输出智能体: %s", a.name)
}

func (a *StreamingAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	// 使用专门为LLM流式输出优化的缓冲区
	pair := runtime.NewAsyncIteratorForLLMStreaming[*event.Event](ctx)

	go func() {
		defer pair.Generator.Close()

		// 模拟流式token输出
		tokens := []string{
			"你好", "，", "我是", "一个", "智能", "助手", "。",
			"我", "可以", "帮助", "你", "解决", "各种", "问题", "。",
			"请", "告诉", "我", "你", "需要", "什么", "帮助", "？",
		}

		for i, token := range tokens {
			// 模拟token生成的时间间隔
			time.Sleep(20 * time.Millisecond)

			// 发送token事件
			pair.Generator.Send(event.NewTokenEvent(token, i == len(tokens)-1))
		}

		// 发送最终事件
		pair.Generator.Send(event.NewFinalEvent("流式输出完成", "所有token已生成", map[string]any{
			"total_tokens": len(tokens),
			"agent":        a.name,
		}))
	}()

	return pair.Iterator
}

// BatchAgent 模拟批量输出的智能体
type BatchAgent struct {
	name string
}

func NewBatchAgent(name string) *BatchAgent {
	return &BatchAgent{name: name}
}

func (a *BatchAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *BatchAgent) Description(ctx context.Context) string {
	return fmt.Sprintf("批量输出智能体: %s", a.name)
}

func (a *BatchAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	// 使用专门为LLM批量输出优化的缓冲区
	pair := runtime.NewAsyncIteratorForLLMBatch[*event.Event](ctx)

	go func() {
		defer pair.Generator.Close()

		// 发送思考事件
		pair.Generator.Send(event.NewThoughtEvent("正在分析问题", "批量处理模式"))

		// 模拟批量处理时间
		time.Sleep(200 * time.Millisecond)

		// 发送完整响应
		response := "这是一个完整的批量响应。我已经分析了你的问题，并提供了详细的解答。"
		pair.Generator.Send(event.NewTokenEvent(response, true))

		// 发送最终事件
		pair.Generator.Send(event.NewFinalEvent("批量处理完成", "响应已生成", map[string]any{
			"response_length": len(response),
			"agent":           a.name,
		}))
	}()

	return pair.Iterator
}

// ToolAgent 模拟工具执行的智能体
type ToolAgent struct {
	name string
}

func NewToolAgent(name string) *ToolAgent {
	return &ToolAgent{name: name}
}

func (a *ToolAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *ToolAgent) Description(ctx context.Context) string {
	return fmt.Sprintf("工具执行智能体: %s", a.name)
}

func (a *ToolAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	// 使用专门为工具执行优化的缓冲区
	pair := runtime.NewAsyncIteratorForToolExecution[*event.Event](ctx)

	go func() {
		defer pair.Generator.Close()

		// 模拟工具调用序列
		tools := []string{"search_tool", "calculator", "file_reader"}

		for i, tool := range tools {
			// 发送工具调用事件
			pair.Generator.Send(event.NewToolCallEvent(
				fmt.Sprintf("call_%d", i),
				tool,
				map[string]any{"query": fmt.Sprintf("执行%s", tool)},
			))

			// 模拟工具执行时间
			time.Sleep(100 * time.Millisecond)

			// 发送工具结果事件
			pair.Generator.Send(event.NewToolResultEvent(
				fmt.Sprintf("call_%d", i),
				tool,
				map[string]any{"result": fmt.Sprintf("%s执行成功", tool)},
				nil,
			))
		}

		// 发送最终事件
		pair.Generator.Send(event.NewFinalEvent("工具执行完成", "所有工具调用成功", map[string]any{
			"tools_executed": len(tools),
			"agent":          a.name,
		}))
	}()

	return pair.Iterator
}

func main() {
	fmt.Println("=== 智能缓冲区演示 ===")
	fmt.Println("演示不同场景下的智能缓冲区大小优化")
	fmt.Println()

	ctx := context.Background()

	// 演示不同场景的缓冲区大小
	fmt.Println("1. 缓冲区大小计算:")
	fmt.Printf("   LLM流式输出: %d\n", runtime.CalculateOptimalBufferSize(runtime.LLMStreamingConfig))
	fmt.Printf("   LLM批量输出: %d\n", runtime.CalculateOptimalBufferSize(runtime.LLMBatchConfig))
	fmt.Printf("   工具执行: %d\n", runtime.CalculateOptimalBufferSize(runtime.ToolExecutionConfig))
	fmt.Printf("   默认配置: %d\n", runtime.CalculateOptimalBufferSize(runtime.DefaultConfig))
	fmt.Println()

	// 创建不同类型的智能体
	agents := []runtime.Agent{
		NewStreamingAgent("StreamingLLM"),
		NewBatchAgent("BatchLLM"),
		NewToolAgent("ToolExecutor"),
	}

	// 创建输入
	input := &runtime.Input{
		Messages: nil,
		Tools:    nil,
		Memory:   nil,
		Session:  nil,
		Options:  make(map[string]any),
	}

	// 运行每个智能体并收集事件
	for i, agent := range agents {
		fmt.Printf("%d. 运行 %s:\n", i+2, agent.Name(ctx))

		start := time.Now()
		iterator := agent.Run(ctx, input)

		eventCount := 0
		for event, ok := iterator.Next(); ok; event, ok = iterator.Next() {
			eventCount++
			fmt.Printf("   [%s] %s\n", event.Type, getEventDescription(event))
		}

		duration := time.Since(start)
		fmt.Printf("   完成: %d个事件, 耗时: %v\n", eventCount, duration)
		fmt.Println()
	}

	fmt.Println("演示完成！智能缓冲区根据不同场景自动优化了缓冲区大小。")
}

func getEventDescription(event *event.Event) string {
	switch event.Type {
	case "thought":
		return "思考事件"
	case "token":
		return "Token事件"
	case "tool_call":
		return "工具调用事件"
	case "tool_result":
		return "工具结果事件"
	case "final":
		return "完成事件"
	case "error":
		return "错误事件"
	default:
		return fmt.Sprintf("事件类型: %s", event.Type)
	}
}
