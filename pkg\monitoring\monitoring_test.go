package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/stretchr/testify/assert"
)

func TestMetricsCollector(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	if collector == nil {
		t.Fatal("NewMetricsCollector returned nil")
	}

	// 测试计数器
	counter := collector.RegisterCounter("test_counter", "Test counter", nil)
	counter.Inc()
	counter.Add(5)

	// 测试直方图
	histogram := collector.RegisterHistogram("test_histogram", "Test histogram", nil, []float64{1.0, 2.0, 5.0})
	histogram.Observe(1.5)
	histogram.Observe(2.5)

	// 测试仪表
	gauge := collector.RegisterGauge("test_gauge", "Test gauge", nil)
	gauge.Set(10.0)
	gauge.Add(5.0)

	// 获取指标
	metrics := collector.GetMetrics()
	if len(metrics) == 0 {
		t.Error("Expected metrics to be collected")
	}

	// 验证指标存在
	found := false
	for _, metric := range metrics {
		if metric.Name == "test_counter" {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected test_counter metric to exist")
	}
}

func TestTraceManager(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	if manager == nil {
		t.Fatal("NewTraceManager returned nil")
	}

	ctx := context.Background()

	// 测试开始追踪
	traceCtx, trace := manager.StartTrace(ctx, "test_operation", "test_service")
	if trace == nil {
		t.Fatal("StartTrace returned nil trace")
	}
	if trace.Operation != "test_operation" {
		t.Errorf("Expected operation 'test_operation', got '%s'", trace.Operation)
	}
	if trace.ServiceName != "test_service" {
		t.Errorf("Expected service 'test_service', got '%s'", trace.ServiceName)
	}

	// 测试开始跨度
	spanCtx, span := manager.StartSpan(traceCtx, "test_span", "test_service")
	if span == nil {
		t.Fatal("StartSpan returned nil span")
	}
	if span.Operation != "test_span" {
		t.Errorf("Expected span operation 'test_span', got '%s'", span.Operation)
	}

	// 测试添加标签
	manager.AddTag(spanCtx, "test_key", "test_value")

	// 测试完成跨度
	manager.FinishSpan(span)
	if span.EndTime.IsZero() {
		t.Error("Expected span to have end time after finishing")
	}

	// 测试完成追踪
	manager.FinishTrace(trace)
	if trace.EndTime.IsZero() {
		t.Error("Expected trace to have end time after finishing")
	}

	// 基本功能测试完成
}

func TestAgentTracer(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	tracer := NewAgentTracer(manager, "test_agent")
	if tracer == nil {
		t.Fatal("NewAgentTracer returned nil")
	}

	ctx := context.Background()

	// 测试追踪Agent执行
	executed := false
	err := tracer.TraceAgentExecution(ctx, "test_agent", "test_operation", func(ctx context.Context) error {
		executed = true
		return nil
	})
	if err != nil {
		t.Errorf("TraceAgentExecution failed: %v", err)
	}
	if !executed {
		t.Error("Expected function to be executed")
	}
}

func TestTraceContext(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	ctx := context.Background()

	// 测试追踪上下文传播
	traceCtx, trace := manager.StartTrace(ctx, "test_operation", "test_service")

	// 从上下文中获取追踪信息
	retrievedCtx := manager.getTraceContext(traceCtx)
	if retrievedCtx == nil {
		t.Error("Expected to retrieve trace context")
	}
	if retrievedCtx.TraceID != trace.TraceID {
		t.Error("Expected trace IDs to match")
	}
}

func TestBasicFunctionality(t *testing.T) {
	// 测试基本的监控功能
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	if collector == nil {
		t.Fatal("NewMetricsCollector returned nil")
	}

	manager := NewTraceManager(1000, time.Hour)
	if manager == nil {
		t.Fatal("NewTraceManager returned nil")
	}

	tracer := NewAgentTracer(manager, "test_service")
	if tracer == nil {
		t.Fatal("NewAgentTracer returned nil")
	}

	// 基本功能验证完成
	t.Log("All monitoring components created successfully")
}

// TestCounter 测试Counter功能
func TestCounter(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 注册计数器
	counter := collector.RegisterCounter("test_counter", "Test counter", map[string]string{"env": "test"})
	assert.NotNil(t, counter)

	// 测试增加
	counter.Inc()
	assert.Equal(t, float64(1), counter.Value())

	counter.Add(5)
	assert.Equal(t, float64(6), counter.Value())
}

// TestGauge 测试Gauge功能
func TestGauge(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 注册仪表
	gauge := collector.RegisterGauge("test_gauge", "Test gauge", map[string]string{"type": "memory"})
	assert.NotNil(t, gauge)

	// 测试设置值
	gauge.Set(100.0)
	assert.Equal(t, 100.0, gauge.Value())

	// 测试增加
	gauge.Add(50.0)
	assert.Equal(t, 150.0, gauge.Value())

	// 测试减少
	gauge.Sub(51.0)
	assert.Equal(t, 99.0, gauge.Value())
}

// TestHistogram 测试Histogram功能
func TestHistogram(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 注册直方图
	buckets := []float64{0.1, 0.5, 1.0, 2.5, 5.0, 10.0}
	histogram := collector.RegisterHistogram("test_histogram", "Test histogram", map[string]string{"operation": "test"}, buckets)
	assert.NotNil(t, histogram)

	// 测试观察值
	histogram.Observe(0.3)
	histogram.Observe(1.5)
	histogram.Observe(7.0)

	// 验证计数
	assert.Equal(t, int64(3), histogram.Count())

	// 验证总和
	assert.Equal(t, int64(8), histogram.Sum()) // 注意：Sum()返回int64，所以0.3+1.5+7.0会被转换为int64

	// 验证桶计数
	bucketCounts := histogram.Buckets()
	assert.Len(t, bucketCounts, len(buckets))
}

// TestMetricsCollectorConcurrency 测试MetricsCollector并发安全
func TestMetricsCollectorConcurrency(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	const numGoroutines = 10
	const operationsPerGoroutine = 100

	// 注册指标
	counter := collector.RegisterCounter("concurrent_counter", "Concurrent counter", nil)
	gauge := collector.RegisterGauge("concurrent_gauge", "Concurrent gauge", nil)

	var wg sync.WaitGroup
	wg.Add(numGoroutines * 2) // counter和gauge各有numGoroutines个goroutine

	// 并发操作counter
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				counter.Inc()
			}
		}()
	}

	// 并发操作gauge
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				gauge.Set(float64(id*operationsPerGoroutine + j))
			}
		}(i)
	}

	wg.Wait()

	// 验证counter的最终值
	assert.Equal(t, float64(numGoroutines*operationsPerGoroutine), counter.Value())
}

// TestMetricsCollectorGetMetrics 测试获取指标
func TestMetricsCollectorGetMetrics(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 注册多个指标
	counter := collector.RegisterCounter("test_counter", "Test counter", map[string]string{"type": "test"})
	gauge := collector.RegisterGauge("test_gauge", "Test gauge", map[string]string{"unit": "bytes"})

	// 设置一些值
	counter.Add(10)
	gauge.Set(256.0)

	// 获取所有指标
	metrics := collector.GetMetrics()
	assert.Len(t, metrics, 2)

	// 验证指标结构存在
	var foundCounter, foundGauge bool
	for _, metric := range metrics {
		switch metric.Name {
		case "test_counter":
			foundCounter = true
			assert.Equal(t, CounterType, metric.Type)
			assert.Equal(t, "test", metric.Labels["type"])
		case "test_gauge":
			foundGauge = true
			assert.Equal(t, GaugeType, metric.Type)
			assert.Equal(t, "bytes", metric.Labels["unit"])
		}
	}

	assert.True(t, foundCounter, "Should find counter metric")
	assert.True(t, foundGauge, "Should find gauge metric")

	// 验证实际值
	assert.Equal(t, float64(10), counter.Value())
	assert.Equal(t, 256.0, gauge.Value())
}

// TestMetricsCollectorSystemMetrics 测试系统指标收集
func TestMetricsCollectorSystemMetrics(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 启动系统指标收集
	collector.Start()

	// 等待一小段时间让系统指标被收集
	time.Sleep(200 * time.Millisecond)

	// 获取指标
	metrics := collector.GetMetrics()

	// 验证系统指标存在
	var foundMemory, foundGoroutines bool
	for _, metric := range metrics {
		if strings.Contains(metric.Name, "memory") {
			foundMemory = true
		}
		if strings.Contains(metric.Name, "goroutines") {
			foundGoroutines = true
		}
	}

	assert.True(t, foundMemory || foundGoroutines, "Should find at least one system metric")
}

// TestTraceManagerNew 测试链路追踪管理器创建
func TestTraceManagerNew(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	assert.NotNil(t, manager)

	ctx := context.Background()

	// 测试开始追踪
	newCtx, trace := manager.StartTrace(ctx, "test_operation", "test_service")
	assert.NotNil(t, newCtx)
	assert.NotNil(t, trace)
	assert.NotEmpty(t, trace.TraceID)
	assert.NotEmpty(t, trace.SpanID)
	assert.Equal(t, "test_operation", trace.Operation)
	assert.Equal(t, "test_service", trace.ServiceName)
	assert.Equal(t, TraceStatusOK, trace.Status)

	// 测试添加标签
	manager.AddTag(newCtx, "key1", "value1")
	manager.AddTag(newCtx, "key2", "value2")
	assert.Equal(t, "value1", trace.Tags["key1"])
	assert.Equal(t, "value2", trace.Tags["key2"])

	// 测试添加日志
	manager.LogEvent(newCtx, "info", "Test log message", nil)
	assert.Len(t, trace.Logs, 1)
	assert.Equal(t, "info", trace.Logs[0].Level)
	assert.Equal(t, "Test log message", trace.Logs[0].Message)

	// 获取追踪
	retrievedTrace, exists := manager.GetTrace(trace.TraceID)
	assert.True(t, exists)
	assert.NotNil(t, retrievedTrace)
	assert.Equal(t, TraceStatusOK, retrievedTrace.Status)
}

// TestTraceManagerSpan 测试Span功能
func TestTraceManagerSpan(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	ctx := context.Background()

	// 开始Span
	spanCtx, span := manager.StartSpan(ctx, "test_operation", "test_service")
	assert.NotNil(t, spanCtx)
	assert.NotNil(t, span)
	assert.NotEmpty(t, span.SpanID)
	assert.Equal(t, "test_operation", span.Operation)
	assert.Equal(t, "test_service", span.ServiceName)
	assert.Equal(t, TraceStatusOK, span.Status)

	// 完成Span
	manager.FinishSpan(span)
	// 注意：Duration可能为0，因为操作很快
	assert.GreaterOrEqual(t, span.Duration, time.Duration(0))
}

// TestTraceManagerError 测试错误处理
func TestTraceManagerError(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	ctx := context.Background()

	// 开始追踪
	newCtx, trace := manager.StartTrace(ctx, "error_operation", "test_service")

	// 设置错误
	testError := fmt.Errorf("Test error message")
	manager.SetError(newCtx, testError)
	assert.Equal(t, testError.Error(), trace.Error)
	assert.Equal(t, TraceStatusError, trace.Status)

	// 完成追踪
	manager.FinishTrace(trace)
}

// TestTraceManagerList 测试追踪列表
func TestTraceManagerList(t *testing.T) {
	manager := NewTraceManager(1000, time.Hour)
	ctx := context.Background()

	// 创建一些追踪
	for i := 0; i < 3; i++ {
		_, trace := manager.StartTrace(ctx, fmt.Sprintf("operation_%d", i), "test_service")
		manager.FinishTrace(trace)
	}

	// 获取追踪列表
	traces := manager.ListTraces()
	assert.Len(t, traces, 3)
}

// TestTraceStatus 测试追踪状态
func TestTraceStatus(t *testing.T) {
	tests := []struct {
		status   TraceStatus
		expected string
	}{
		{TraceStatusOK, "ok"},
		{TraceStatusError, "error"},
	}

	for _, tt := range tests {
		t.Run(string(tt.status), func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.status))
		})
	}
}

// TestMetricsCollectorAdvanced 测试指标收集器高级功能
func TestMetricsCollectorAdvanced(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 注册并使用Counter
	counter := collector.RegisterCounter("requests_total", "Total requests", map[string]string{"method": "GET"})
	counter.Inc()
	counter.Add(5)
	assert.Equal(t, float64(6), counter.Value())

	// 注册并使用Gauge
	gauge := collector.RegisterGauge("memory_usage", "Memory usage", map[string]string{"type": "heap"})
	gauge.Set(1024)
	gauge.Add(512)
	assert.Equal(t, float64(1536), gauge.Value())

	// 注册并使用Histogram
	histogram := collector.RegisterHistogram("request_duration", "Request duration",
		map[string]string{"endpoint": "/api"}, []float64{0.1, 0.5, 1.0, 2.0})
	histogram.Observe(0.3)
	histogram.Observe(1.5)

	// 获取所有指标
	metrics := collector.GetMetrics()
	assert.NotEmpty(t, metrics)

	// 验证指标存在
	foundCounter := false
	foundGauge := false
	foundHistogram := false

	for _, metric := range metrics {
		switch metric.Name {
		case "requests_total":
			foundCounter = true
			assert.Equal(t, CounterType, metric.Type)
		case "memory_usage":
			foundGauge = true
			assert.Equal(t, GaugeType, metric.Type)
		case "request_duration":
			foundHistogram = true
			assert.Equal(t, HistogramType, metric.Type)
		}
	}

	assert.True(t, foundCounter, "应该包含counter指标")
	assert.True(t, foundGauge, "应该包含gauge指标")
	assert.True(t, foundHistogram, "应该包含histogram指标")
}

// TestMetricsCollectorEdgeCases 测试指标收集器边界情况
func TestMetricsCollectorEdgeCases(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 测试空指标名称
	counter1 := collector.RegisterCounter("", "Empty name counter", nil)
	gauge1 := collector.RegisterGauge("", "Empty name gauge", nil)

	// 测试nil标签
	counter2 := collector.RegisterCounter("test_counter", "Test counter", nil)
	gauge2 := collector.RegisterGauge("test_gauge", "Test gauge", nil)

	// 测试负值
	counter3 := collector.RegisterCounter("negative_counter", "Negative counter", nil)
	gauge3 := collector.RegisterGauge("negative_gauge", "Negative gauge", nil)

	// 设置值
	counter1.Inc()
	gauge1.Set(100)
	counter2.Add(1)
	gauge2.Set(50)
	counter3.Add(-1) // Counter可能不支持负值，但测试边界情况
	gauge3.Set(-50)

	// 获取指标并验证
	metrics := collector.GetMetrics()
	assert.NotEmpty(t, metrics)

	// 验证指标被正确记录
	for _, metric := range metrics {
		assert.NotNil(t, metric)
		assert.True(t, metric.Timestamp.After(time.Time{}))
	}
}

// TestMetricsCollectorJSON 测试指标JSON序列化
func TestMetricsCollectorJSON(t *testing.T) {
	collector := NewMetricsCollector(logger.GetGlobalLogger())
	defer collector.Stop()

	// 注册并设置指标
	counter := collector.RegisterCounter("api_requests", "API requests", map[string]string{"endpoint": "/users"})
	gauge := collector.RegisterGauge("cpu_usage", "CPU usage", map[string]string{"core": "0"})

	counter.Add(100)
	gauge.Set(75.5)

	// 获取指标
	metrics := collector.GetMetrics()
	assert.NotEmpty(t, metrics)

	// 测试JSON序列化
	for _, metric := range metrics {
		jsonData, err := json.Marshal(metric)
		assert.NoError(t, err)
		assert.NotEmpty(t, jsonData)

		// 测试反序列化
		var deserializedMetric Metric
		err = json.Unmarshal(jsonData, &deserializedMetric)
		assert.NoError(t, err)
		assert.Equal(t, metric.Name, deserializedMetric.Name)
		assert.Equal(t, metric.Type, deserializedMetric.Type)
	}
}
