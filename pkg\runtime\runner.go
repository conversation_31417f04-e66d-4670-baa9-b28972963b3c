package runtime

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/session"
	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// Agent 表示智能体接口
type Agent interface {
	// Name 返回智能体名称
	Name(ctx context.Context) string
	// Description 返回智能体描述
	Description(ctx context.Context) string
	// Run 运行智能体，返回事件流
	Run(ctx context.Context, input *Input) *AsyncIterator[*event.Event]
}

// Input 表示智能体的输入
type Input struct {
	Messages []*message.Message // 输入消息
	Tools    []tool.Tool        // 可用工具（含 MCP 注入）
	Memory   memory.Store       // 记忆存储引用
	Session  session.Map        // 并发安全会话（跨 Agent 可见）
	Options  map[string]any     // 执行可选项（如温度、top_k、tool_choice）
}

// NewInput 创建新的智能体输入
func NewInput() *Input {
	return &Input{
		Messages: make([]*message.Message, 0),
		Tools:    make([]tool.Tool, 0),
		Options:  make(map[string]any),
	}
}

// AddMessage 添加消息到输入
func (in *Input) AddMessage(msg *message.Message) *Input {
	in.Messages = append(in.Messages, msg)
	return in
}

// AddTool 添加工具到输入
func (in *Input) AddTool(tool tool.Tool) *Input {
	in.Tools = append(in.Tools, tool)
	return in
}

// SetMemory 设置记忆存储
func (in *Input) SetMemory(mem memory.Store) *Input {
	in.Memory = mem
	return in
}

// SetSession 设置会话
func (in *Input) SetSession(sess session.Map) *Input {
	in.Session = sess
	return in
}

// SetOption 设置选项
func (in *Input) SetOption(key string, value any) *Input {
	if in.Options == nil {
		in.Options = make(map[string]any)
	}
	in.Options[key] = value
	return in
}

// GetOption 获取选项
func (in *Input) GetOption(key string) (any, bool) {
	value, exists := in.Options[key]
	return value, exists
}

// GetOptionString 获取字符串选项
func (in *Input) GetOptionString(key string) (string, bool) {
	value, exists := in.Options[key]
	if !exists {
		return "", false
	}
	if str, ok := value.(string); ok {
		return str, true
	}
	return "", false
}

// GetOptionFloat64 获取浮点数选项
func (in *Input) GetOptionFloat64(key string) (float64, bool) {
	value, exists := in.Options[key]
	if !exists {
		return 0, false
	}
	if f, ok := value.(float64); ok {
		return f, true
	}
	return 0, false
}

// GetOptionInt 获取整数选项
func (in *Input) GetOptionInt(key string) (int, bool) {
	value, exists := in.Options[key]
	if !exists {
		return 0, false
	}
	if i, ok := value.(int); ok {
		return i, true
	}
	return 0, false
}

// GetOptionBool 获取布尔选项
func (in *Input) GetOptionBool(key string) (bool, bool) {
	value, exists := in.Options[key]
	if !exists {
		return false, false
	}
	if b, ok := value.(bool); ok {
		return b, true
	}
	return false, false
}

// Clone 克隆输入（浅拷贝）
func (in *Input) Clone() *Input {
	clone := &Input{
		Messages: make([]*message.Message, len(in.Messages)),
		Tools:    make([]tool.Tool, len(in.Tools)),
		Memory:   in.Memory,
		Session:  in.Session,
		Options:  make(map[string]any),
	}

	copy(clone.Messages, in.Messages)
	copy(clone.Tools, in.Tools)

	for k, v := range in.Options {
		clone.Options[k] = v
	}

	return clone
}

// ValidationError 表示验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Index   *int   `json:"index,omitempty"`
}

func (e *ValidationError) Error() string {
	if e.Index != nil {
		return "验证错误 [" + e.Field + "[" + string(rune(*e.Index)) + "]]: " + e.Message
	}
	return "验证错误 [" + e.Field + "]: " + e.Message
}

// Validate 验证输入的有效性
func (in *Input) Validate() error {
	if in == nil {
		return &ValidationError{
			Field:   "input",
			Message: "输入不能为空",
		}
	}

	if len(in.Messages) == 0 {
		return &ValidationError{
			Field:   "messages",
			Message: "消息列表不能为空",
		}
	}

	// 验证消息
	for i, msg := range in.Messages {
		if msg == nil {
			return &ValidationError{
				Field:   "messages",
				Message: "消息不能为空",
				Index:   &i,
			}
		}
		if err := msg.Validate(); err != nil {
			return &ValidationError{
				Field:   "messages",
				Message: "消息验证失败: " + err.Error(),
				Index:   &i,
			}
		}
	}

	return nil
}

// RunnerConfig 表示执行器配置
type RunnerConfig struct {
	// EnableStreaming 是否启用流式输出
	EnableStreaming bool
	// BufferSize 事件缓冲区大小
	BufferSize int
	// Timeout 执行超时时间
	Timeout time.Duration
	// MaxRetries 最大重试次数
	MaxRetries int
	// RetryDelay 重试延迟
	RetryDelay time.Duration
	// CheckpointStore 检查点存储（可选）
	CheckpointStore CheckpointStore
	// EnableCheckpoint 是否启用检查点功能
	EnableCheckpoint bool
	// CheckpointInterval 检查点保存间隔（事件数量）
	CheckpointInterval int
}

// DefaultRunnerConfig 返回默认配置
func DefaultRunnerConfig() RunnerConfig {
	return RunnerConfig{
		EnableStreaming:    true,
		BufferSize:         100,
		Timeout:            5 * time.Minute,
		MaxRetries:         3,
		RetryDelay:         1 * time.Second,
		CheckpointStore:    nil, // 默认不启用检查点
		EnableCheckpoint:   false,
		CheckpointInterval: 10, // 每10个事件保存一次检查点
	}
}

// Runner 表示智能体执行器，负责统一管理智能体的执行
type Runner struct {
	config       RunnerConfig
	interceptors *InterceptorChain
	mu           sync.RWMutex
}

// NewRunner 创建新的执行器
func NewRunner(ctx context.Context, config RunnerConfig) *Runner {
	return &Runner{
		config:       config,
		interceptors: NewInterceptorChain(),
	}
}

// AddInterceptor 添加拦截器
func (r *Runner) AddInterceptor(interceptor Interceptor) {
	r.interceptors.Add(interceptor)
}

// RemoveInterceptor 移除拦截器
func (r *Runner) RemoveInterceptor(name string) bool {
	return r.interceptors.Remove(name)
}

// ListInterceptors 列出所有拦截器
func (r *Runner) ListInterceptors() []string {
	return r.interceptors.List()
}

// ClearInterceptors 清空所有拦截器
func (r *Runner) ClearInterceptors() {
	r.interceptors.Clear()
}

// Run 执行智能体并返回事件流
func (r *Runner) Run(ctx context.Context, agent Agent, input *Input) *AsyncIterator[*event.Event] {
	// 确保输入有会话
	if input.Session == nil {
		input.Session = session.New()
	}

	// 将会话添加到上下文
	runCtx := session.WithSession(ctx, input.Session)

	// 创建带超时的上下文
	if r.config.Timeout > 0 {
		var cancel context.CancelFunc
		runCtx, cancel = context.WithTimeout(runCtx, r.config.Timeout)
		// 注意：这里不能 defer cancel()，因为需要在 goroutine 中使用
		_ = cancel
	}

	// 创建事件流，使用智能缓冲区大小
	// 如果配置了BufferSize，使用配置值；否则使用默认智能配置
	var pair *GeneratorPair[*event.Event]
	if r.config.BufferSize > 0 {
		pair = NewAsyncIterator[*event.Event](runCtx, r.config.BufferSize)
	} else {
		// 使用默认配置的智能缓冲区大小
		pair = NewAsyncIteratorWithConfig[*event.Event](runCtx, DefaultConfig)
	}

	// 在后台执行智能体
	go r.runAgent(runCtx, agent, input, pair.Generator)

	return pair.Iterator
}

// runAgent 在后台执行智能体
func (r *Runner) runAgent(ctx context.Context, agent Agent, input *Input, generator *Generator[*event.Event]) {
	defer generator.Close()

	agentName := agent.Name(ctx)

	// 执行前置拦截器
	if err := r.interceptors.BeforeRun(ctx, agentName, input); err != nil {
		r.sendErrorEvent(generator, fmt.Errorf("前置拦截器失败: %w", err))
		r.interceptors.OnError(ctx, agentName, err)
		return
	}

	var runErr error

	// 执行智能体（带重试）
	for attempt := 0; attempt <= r.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 重试延迟
			select {
			case <-ctx.Done():
				runErr = ctx.Err()
				break
			case <-time.After(r.config.RetryDelay):
			}
		}

		// 运行智能体
		runErr = r.executeAgent(ctx, agent, input, generator)
		if runErr == nil {
			break // 成功，退出重试循环
		}

		// 如果是上下文取消，不重试
		if ctx.Err() != nil {
			break
		}

		// 记录重试
		if attempt < r.config.MaxRetries {
			retryEvent := event.NewErrorEvent(
				fmt.Errorf("执行失败，将重试 (第 %d 次): %w", attempt+1, runErr),
				"RETRY",
				map[string]any{"attempt": attempt + 1, "max_retries": r.config.MaxRetries},
			)
			r.sendEvent(generator, retryEvent)
		}
	}

	// 执行后置拦截器
	if afterErr := r.interceptors.AfterRun(ctx, agentName, input, runErr); afterErr != nil {
		r.sendErrorEvent(generator, fmt.Errorf("后置拦截器失败: %w", afterErr))
		r.interceptors.OnError(ctx, agentName, afterErr)
	}

	// 如果最终还是失败，发送错误事件
	if runErr != nil {
		r.sendErrorEvent(generator, runErr)
		r.interceptors.OnError(ctx, agentName, runErr)
	}
}

// executeAgent 执行智能体的核心逻辑
func (r *Runner) executeAgent(ctx context.Context, agent Agent, input *Input, generator *Generator[*event.Event]) error {
	agentName := agent.Name(ctx)

	// 尝试从检查点恢复状态
	sessionID := r.getSessionID(input)
	if r.config.EnableCheckpoint && r.config.CheckpointStore != nil && sessionID != "" {
		if err := r.loadCheckpoint(ctx, sessionID, input); err != nil {
			// 检查点加载失败不是致命错误，记录日志继续执行
			r.sendEvent(generator, event.NewErrorEvent(fmt.Errorf("加载检查点失败: %w", err), "CHECKPOINT_LOAD_WARN", nil))
		}
	}

	// 运行智能体
	eventIterator := agent.Run(ctx, input)
	if eventIterator == nil {
		return fmt.Errorf("智能体返回了空的事件迭代器")
	}

	// 事件计数器（用于检查点间隔）
	eventCount := 0

	// 转发事件
	for ev, ok := eventIterator.Next(); ok; ev, ok = eventIterator.Next() {
		// 通过拦截器处理事件
		if err := r.interceptors.OnEvent(ctx, agentName, ev); err != nil {
			return fmt.Errorf("事件拦截器失败: %w", err)
		}

		// 发送事件
		if err := r.sendEvent(generator, ev); err != nil {
			return fmt.Errorf("发送事件失败: %w", err)
		}

		// 增加事件计数
		eventCount++

		// 检查是否需要保存检查点
		if r.config.EnableCheckpoint && r.config.CheckpointStore != nil && sessionID != "" {
			if eventCount%r.config.CheckpointInterval == 0 {
				if err := r.saveCheckpoint(ctx, sessionID, agentName, input, eventCount); err != nil {
					// 检查点保存失败不是致命错误，记录日志继续执行
					r.sendEvent(generator, event.NewErrorEvent(fmt.Errorf("保存检查点失败: %w", err), "CHECKPOINT_SAVE_WARN", nil))
				}
			}
		}

		// 如果是错误事件，返回错误
		if ev.IsError() {
			return ev.Err
		}

		// 如果是最终事件，保存最终检查点并成功结束
		if ev.IsFinal() {
			if r.config.EnableCheckpoint && r.config.CheckpointStore != nil && sessionID != "" {
				if err := r.saveCheckpoint(ctx, sessionID, agentName, input, eventCount); err != nil {
					r.sendEvent(generator, event.NewErrorEvent(fmt.Errorf("保存最终检查点失败: %w", err), "CHECKPOINT_FINAL_WARN", nil))
				}
			}
			return nil
		}
	}

	return nil
}

// sendEvent 发送事件到生成器
func (r *Runner) sendEvent(generator *Generator[*event.Event], ev *event.Event) error {
	if err := generator.Send(ev); err != nil {
		return fmt.Errorf("发送事件失败: %w", err)
	}
	return nil
}

// sendErrorEvent 发送错误事件
func (r *Runner) sendErrorEvent(generator *Generator[*event.Event], err error) {
	errorEvent := event.NewErrorEvent(err, "EXECUTION_ERROR", map[string]any{
		"timestamp": time.Now(),
	})
	generator.Send(errorEvent) // 忽略发送错误，避免无限递归
}

// RunWithCallback 执行智能体并通过回调处理事件
func (r *Runner) RunWithCallback(ctx context.Context, agent Agent, input *Input, callback func(*event.Event) bool) error {
	iterator := r.Run(ctx, agent, input)

	for ev, ok := iterator.Next(); ok; ev, ok = iterator.Next() {
		if !callback(ev) {
			break
		}

		if ev.IsError() {
			return ev.Err
		}
	}

	return nil
}

// RunAndCollect 执行智能体并收集所有事件
func (r *Runner) RunAndCollect(ctx context.Context, agent Agent, input *Input) ([]*event.Event, error) {
	iterator := r.Run(ctx, agent, input)
	events := iterator.Collect()

	// 检查是否有错误事件
	for _, ev := range events {
		if ev.IsError() {
			return events, ev.Err
		}
	}

	return events, nil
}

// GetConfig 获取执行器配置
func (r *Runner) GetConfig() RunnerConfig {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.config
}

// getSessionID 从输入中获取会话ID
func (r *Runner) getSessionID(input *Input) string {
	if input.Session == nil {
		return ""
	}

	// 尝试从Session中获取会话ID
	if sessionID, exists := input.Session.Get("session_id"); exists {
		if id, ok := sessionID.(string); ok {
			return id
		}
	}

	// 如果没有会话ID，生成一个新的
	sessionID := fmt.Sprintf("session_%d", time.Now().UnixNano())
	input.Session.Set("session_id", sessionID)
	return sessionID
}

// loadCheckpoint 从检查点恢复状态
func (r *Runner) loadCheckpoint(ctx context.Context, sessionID string, input *Input) error {
	if r.config.CheckpointStore == nil {
		return fmt.Errorf("检查点存储未配置")
	}

	// 检查检查点是否存在
	exists, err := r.config.CheckpointStore.Exists(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("检查检查点是否存在失败: %w", err)
	}

	if !exists {
		// 检查点不存在，不是错误
		return nil
	}

	// 加载检查点数据
	checkpointData, err := r.config.CheckpointStore.Load(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("加载检查点数据失败: %w", err)
	}

	// 恢复状态到Session中
	if input.Session != nil && checkpointData.State != nil {
		for key, value := range checkpointData.State {
			input.Session.Set(key, value)
		}
	}

	return nil
}

// saveCheckpoint 保存检查点
func (r *Runner) saveCheckpoint(ctx context.Context, sessionID, agentName string, input *Input, eventCount int) error {
	if r.config.CheckpointStore == nil {
		return fmt.Errorf("检查点存储未配置")
	}

	// 收集状态数据
	state := make(map[string]any)
	if input.Session != nil {
		for _, key := range input.Session.Keys() {
			if value, exists := input.Session.Get(key); exists {
				state[key] = value
			}
		}
	}

	// 创建检查点数据
	checkpointData := &CheckpointData{
		SessionID: sessionID,
		AgentName: agentName,
		State:     state,
		Metadata: map[string]any{
			"event_count": eventCount,
			"timestamp":   time.Now(),
		},
	}

	// 保存检查点
	if err := r.config.CheckpointStore.Save(ctx, sessionID, checkpointData); err != nil {
		return fmt.Errorf("保存检查点失败: %w", err)
	}

	return nil
}

// UpdateConfig 更新执行器配置
func (r *Runner) UpdateConfig(config RunnerConfig) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.config = config
}

// Health 检查执行器健康状态
func (r *Runner) Health() map[string]any {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return map[string]any{
		"status":       "healthy",
		"interceptors": len(r.interceptors.List()),
		"config":       r.config,
		"timestamp":    time.Now(),
	}
}
