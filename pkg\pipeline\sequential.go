package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/session"
)

// SequentialPipeline executes agents sequentially
type SequentialPipeline struct {
	config *Config
	agents []agent.Agent
	mutex  sync.RWMutex
	logger logger.Logger
}

// NewSequentialPipeline creates a new sequential pipeline
func NewSequentialPipeline(config *Config) (*SequentialPipeline, error) {
	if config == nil {
		config = DefaultConfig()
		config.Type = PipelineTypeSequential
	}

	if err := config.Validate(); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "invalid_config", "invalid pipeline configuration")
	}

	return &SequentialPipeline{
		config: config,
		agents: make([]agent.Agent, 0),
		logger: logger.GetGlobalLogger(),
	}, nil
}

// AddAgent adds an agent to the pipeline
func (sp *SequentialPipeline) AddAgent(agent agent.Agent) error {
	if agent == nil {
		return errors.NewValidationError("invalid_input", "agent cannot be nil")
	}

	sp.mutex.Lock()
	defer sp.mutex.Unlock()

	// Check if agent is already in the pipeline
	ctx := context.Background()
	agentName := agent.Name(ctx)
	for _, existingAgent := range sp.agents {
		if existingAgent.Name(ctx) == agentName {
			return errors.NewValidationError("duplicate_agent",
				fmt.Sprintf("agent %s is already in the pipeline", agentName))
		}
	}

	sp.agents = append(sp.agents, agent)

	sp.logger.Info("Agent %s added to pipeline %s", agentName, sp.config.Name)
	return nil
}

// RemoveAgent removes an agent from the pipeline
func (sp *SequentialPipeline) RemoveAgent(agentID string) error {
	if agentID == "" {
		return errors.NewValidationError("invalid_input", "agent ID cannot be empty")
	}

	sp.mutex.Lock()
	defer sp.mutex.Unlock()

	ctx := context.Background()
	for i, agent := range sp.agents {
		if agent.Name(ctx) == agentID {
			sp.agents = append(sp.agents[:i], sp.agents[i+1:]...)
			sp.logger.Info("Agent %s removed from pipeline %s", agentID, sp.config.Name)
			return nil
		}
	}

	return errors.NewValidationError("agent_not_found",
		fmt.Sprintf("agent %s not found in pipeline", agentID))
}

// Execute executes the pipeline with the given message
func (sp *SequentialPipeline) Execute(ctx context.Context, msg *message.Message) (*PipelineResult, error) {
	if msg == nil {
		return nil, errors.NewValidationError("invalid_input", "message cannot be nil")
	}

	result := NewPipelineResult()
	result.SetMetadata("pipeline_name", sp.config.Name)
	result.SetMetadata("pipeline_type", sp.config.Type)

	sp.mutex.RLock()
	agents := make([]agent.Agent, len(sp.agents))
	copy(agents, sp.agents)
	sp.mutex.RUnlock()

	if len(agents) == 0 {
		result.SetError(errors.NewValidationError("empty_pipeline", "no agents in pipeline"))
		return result, nil
	}

	// Create execution context with timeout
	execCtx := ctx
	if sp.config.Timeout > 0 {
		var cancel context.CancelFunc
		execCtx, cancel = context.WithTimeout(ctx, sp.config.Timeout)
		defer cancel()
	}

	currentMsg := msg

	sp.logger.Info("Starting pipeline %s execution with %d agents", sp.config.Name, len(agents))

	// Execute agents sequentially
	for i, currentAgent := range agents {
		step := ExecutionStep{
			AgentID:   currentAgent.Name(ctx),
			AgentType: "agent", // 简化为固定值
			StartTime: time.Now(),
			InputMsg:  currentMsg,
			Metadata:  make(map[string]interface{}),
		}

		// Check if context is cancelled or timed out
		select {
		case <-execCtx.Done():
			step.EndTime = time.Now()
			step.Duration = step.EndTime.Sub(step.StartTime)
			step.Success = false
			step.Error = execCtx.Err()
			result.AddStep(step)

			result.SetError(errors.New(errors.ErrorTypeTimeout, "operation_timeout",
				fmt.Sprintf("pipeline execution timed out at agent %s", currentAgent.Name(ctx))))
			return result, nil
		default:
		}

		// Execute agent with retry logic
		var responseMsg *message.Message
		var err error

		for attempt := 0; attempt <= sp.config.MaxRetries; attempt++ {
			if attempt > 0 {
				sp.logger.Warn("Retrying agent %s execution, attempt %d", currentAgent.Name(ctx), attempt)

				// Add small delay between retries
				select {
				case <-time.After(time.Duration(attempt) * time.Second):
				case <-execCtx.Done():
					step.EndTime = time.Now()
					step.Duration = step.EndTime.Sub(step.StartTime)
					step.Success = false
					step.Error = execCtx.Err()
					result.AddStep(step)

					result.SetError(errors.New(errors.ErrorTypeTimeout, "operation_timeout",
						"pipeline execution cancelled during retry"))
					return result, nil
				}
			}

			// 使用新的Agent.Run()方法
			input := &agent.Input{
				Messages: []*message.Message{currentMsg},
				Memory:   memory.NewMemoryStore(),
				Session:  session.New(),
				Options:  make(map[string]any),
			}

			iterator := currentAgent.Run(execCtx, input)
			responseMsg, err = sp.extractResponseFromEvents(iterator)
			if err == nil {
				break
			}

			sp.logger.Warn("Agent %s execution failed on attempt %d: %v", currentAgent.Name(execCtx), attempt, err)
		}

		step.EndTime = time.Now()
		step.Duration = step.EndTime.Sub(step.StartTime)
		step.OutputMsg = responseMsg
		step.Error = err
		step.Success = err == nil

		// Add step metadata
		step.Metadata["step_index"] = i
		step.Metadata["retry_count"] = sp.config.MaxRetries
		if err != nil {
			step.Metadata["error_type"] = fmt.Sprintf("%T", err)
		}

		result.AddStep(step)

		if err != nil {
			agentName := currentAgent.Name(execCtx)
			sp.logger.Error("Agent %s failed after %d retries: %v", agentName, sp.config.MaxRetries, err)

			result.SetError(errors.Wrap(err, errors.ErrorTypePipeline, "agent_failed",
				fmt.Sprintf("agent %s failed after %d retries", agentName, sp.config.MaxRetries)))
			return result, nil
		}

		// Use the response as input for the next agent
		currentMsg = responseMsg

		sp.logger.Info("Agent %s executed successfully in %v", currentAgent.Name(execCtx), step.Duration)
	}

	result.SetSuccess(currentMsg)

	sp.logger.Info("Pipeline %s execution completed successfully in %v", sp.config.Name, result.Duration)
	return result, nil
}

// GetAgents returns all agents in the pipeline
func (sp *SequentialPipeline) GetAgents() []agent.Agent {
	sp.mutex.RLock()
	defer sp.mutex.RUnlock()

	agents := make([]agent.Agent, len(sp.agents))
	copy(agents, sp.agents)
	return agents
}

// GetAgentCount returns the number of agents in the pipeline
func (sp *SequentialPipeline) GetAgentCount() int {
	sp.mutex.RLock()
	defer sp.mutex.RUnlock()
	return len(sp.agents)
}

// Clear removes all agents from the pipeline
func (sp *SequentialPipeline) Clear() error {
	sp.mutex.Lock()
	defer sp.mutex.Unlock()

	sp.agents = make([]agent.Agent, 0)
	sp.logger.Info("Pipeline %s cleared", sp.config.Name)
	return nil
}

// GetConfig returns the pipeline configuration
func (sp *SequentialPipeline) GetConfig() *Config {
	sp.mutex.RLock()
	defer sp.mutex.RUnlock()
	return sp.config.Clone()
}

// UpdateConfig updates the pipeline configuration
func (sp *SequentialPipeline) UpdateConfig(config *Config) error {
	if config == nil {
		return errors.NewValidationError("invalid_input", "config cannot be nil")
	}

	if err := config.Validate(); err != nil {
		return errors.Wrap(err, errors.ErrorTypeValidation, "invalid_config", "invalid pipeline configuration")
	}

	sp.mutex.Lock()
	defer sp.mutex.Unlock()

	sp.config = config.Clone()
	sp.logger.Info("Pipeline %s configuration updated", sp.config.Name)
	return nil
}

// GetAgentByID returns an agent by its ID
func (sp *SequentialPipeline) GetAgentByName(agentName string) (agent.Agent, error) {
	sp.mutex.RLock()
	defer sp.mutex.RUnlock()

	ctx := context.Background() // 使用背景上下文获取名称
	for _, agent := range sp.agents {
		if agent.Name(ctx) == agentName {
			return agent, nil
		}
	}

	return nil, errors.NewValidationError("agent_not_found",
		fmt.Sprintf("agent %s not found in pipeline", agentName))
}

// GetAgentIndex returns the index of an agent in the pipeline
func (sp *SequentialPipeline) GetAgentIndex(agentName string) (int, error) {
	sp.mutex.RLock()
	defer sp.mutex.RUnlock()

	ctx := context.Background()
	for i, agent := range sp.agents {
		if agent.Name(ctx) == agentName {
			return i, nil
		}
	}

	return -1, errors.NewValidationError("agent_not_found",
		fmt.Sprintf("agent %s not found in pipeline", agentName))
}

// InsertAgent inserts an agent at a specific position in the pipeline
func (sp *SequentialPipeline) InsertAgent(index int, agentToInsert agent.Agent) error {
	if agentToInsert == nil {
		return errors.NewValidationError("invalid_input", "agent cannot be nil")
	}

	sp.mutex.Lock()
	defer sp.mutex.Unlock()

	if index < 0 || index > len(sp.agents) {
		return errors.NewValidationError("out_of_range",
			fmt.Sprintf("index %d is out of range [0, %d]", index, len(sp.agents)))
	}

	// Check if agent is already in the pipeline
	ctx := context.Background()
	agentToInsertName := agentToInsert.Name(ctx)
	for _, existingAgent := range sp.agents {
		if existingAgent.Name(ctx) == agentToInsertName {
			return errors.NewValidationError("duplicate_agent",
				fmt.Sprintf("agent %s is already in the pipeline", agentToInsertName))
		}
	}

	// Insert agent at the specified index
	newAgents := make([]agent.Agent, 0, len(sp.agents)+1)
	newAgents = append(newAgents, sp.agents[:index]...)
	newAgents = append(newAgents, agentToInsert)
	newAgents = append(newAgents, sp.agents[index:]...)
	sp.agents = newAgents

	sp.logger.Info("Agent %s inserted into pipeline %s at index %d", agentToInsertName, sp.config.Name, index)
	return nil
}

// MoveAgent moves an agent to a different position in the pipeline
func (sp *SequentialPipeline) MoveAgent(agentName string, newIndex int) error {
	if agentName == "" {
		return errors.NewValidationError("invalid_input", "agent name cannot be empty")
	}

	sp.mutex.Lock()
	defer sp.mutex.Unlock()

	if newIndex < 0 || newIndex >= len(sp.agents) {
		return errors.NewValidationError("out_of_range",
			fmt.Sprintf("index %d is out of range [0, %d)", newIndex, len(sp.agents)))
	}

	// Find the agent
	ctx := context.Background()
	var targetAgent agent.Agent
	oldIndex := -1
	for i, agent := range sp.agents {
		if agent.Name(ctx) == agentName {
			targetAgent = agent
			oldIndex = i
			break
		}
	}

	if oldIndex == -1 {
		return errors.NewValidationError("agent_not_found",
			fmt.Sprintf("agent %s not found in pipeline", agentName))
	}

	if oldIndex == newIndex {
		return nil // No movement needed
	}

	// Remove agent from old position
	sp.agents = append(sp.agents[:oldIndex], sp.agents[oldIndex+1:]...)

	// Adjust new index if necessary
	if newIndex > oldIndex {
		newIndex--
	}

	// Insert agent at new position
	newAgents := make([]agent.Agent, 0, len(sp.agents)+1)
	newAgents = append(newAgents, sp.agents[:newIndex]...)
	newAgents = append(newAgents, targetAgent)
	newAgents = append(newAgents, sp.agents[newIndex:]...)
	sp.agents = newAgents

	sp.logger.Info("Agent %s moved in pipeline %s from index %d to %d", agentName, sp.config.Name, oldIndex, newIndex)
	return nil
}

// extractResponseFromEvents 从事件迭代器中提取响应消息
func (sp *SequentialPipeline) extractResponseFromEvents(iterator *runtime.AsyncIterator[*event.Event]) (*message.Message, error) {
	var lastMessage *message.Message

	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}

		switch ev.Type {
		case event.EventFinal:
			if msg, ok := ev.Data.(*message.Message); ok {
				lastMessage = msg
			}
		case event.EventError:
			if err, ok := ev.Data.(error); ok {
				return nil, err
			}
			return nil, fmt.Errorf("agent execution failed: %v", ev.Data)
		}
	}

	if lastMessage == nil {
		return nil, fmt.Errorf("no response message received from agent")
	}

	return lastMessage, nil
}
