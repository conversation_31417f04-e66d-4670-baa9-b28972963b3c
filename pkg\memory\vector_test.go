package memory

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSQLiteVectorStore 测试SQLite向量存储功能
func TestSQLiteVectorStore(t *testing.T) {
	// 创建内存SQLite存储
	store, err := NewSQLiteStore(&SQLiteConfig{
		DatabasePath: ":memory:",
	})
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session"
	messageID := "test-message"

	// 测试保存向量
	vector := []float32{0.1, 0.2, 0.3, 0.4, 0.5}
	metadata := map[string]any{
		"type":   "test",
		"source": "unit_test",
	}

	err = store.SaveVector(ctx, sessionID, messageID, vector, metadata)
	assert.NoError(t, err)

	// 测试相似度搜索
	queryVector := []float32{0.1, 0.2, 0.3, 0.4, 0.5} // 相同向量，相似度应该为1.0
	results, err := store.SearchSimilar(ctx, sessionID, queryVector, 10, 0.5)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Equal(t, messageID, results[0].MessageID)
	assert.Equal(t, sessionID, results[0].SessionID)
	assert.InDelta(t, 1.0, results[0].Score, 0.001) // 相同向量的余弦相似度应该接近1.0

	// 测试保存另一个向量
	messageID2 := "test-message-2"
	vector2 := []float32{0.5, 0.4, 0.3, 0.2, 0.1} // 不同的向量
	err = store.SaveVector(ctx, sessionID, messageID2, vector2, metadata)
	assert.NoError(t, err)

	// 测试搜索多个结果
	results, err = store.SearchSimilar(ctx, sessionID, queryVector, 10, 0.0)
	assert.NoError(t, err)
	assert.Len(t, results, 2)
	// 结果应该按相似度排序，第一个应该是完全匹配的
	assert.Equal(t, messageID, results[0].MessageID)
	assert.InDelta(t, 1.0, results[0].Score, 0.001)

	// 测试删除单个向量
	err = store.DeleteVector(ctx, sessionID, messageID)
	assert.NoError(t, err)

	// 验证删除后搜索结果
	results, err = store.SearchSimilar(ctx, sessionID, queryVector, 10, 0.0)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Equal(t, messageID2, results[0].MessageID)

	// 测试删除会话所有向量
	err = store.DeleteSessionVectors(ctx, sessionID)
	assert.NoError(t, err)

	// 验证删除后没有结果
	results, err = store.SearchSimilar(ctx, sessionID, queryVector, 10, 0.0)
	assert.NoError(t, err)
	assert.Len(t, results, 0)
}

// TestSQLiteVectorStoreValidation 测试向量存储的输入验证
func TestSQLiteVectorStoreValidation(t *testing.T) {
	store, err := NewSQLiteStore(&SQLiteConfig{
		DatabasePath: ":memory:",
	})
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// 测试空sessionID
	err = store.SaveVector(ctx, "", "msg1", []float32{0.1}, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "会话ID不能为空")

	// 测试空messageID
	err = store.SaveVector(ctx, "session1", "", []float32{0.1}, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "消息ID不能为空")

	// 测试空向量
	err = store.SaveVector(ctx, "session1", "msg1", []float32{}, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "向量数据不能为空")

	// 测试搜索时空sessionID
	_, err = store.SearchSimilar(ctx, "", []float32{0.1}, 10, 0.5)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "会话ID不能为空")

	// 测试搜索时空向量
	_, err = store.SearchSimilar(ctx, "session1", []float32{}, 10, 0.5)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "查询向量不能为空")
}

// TestVectorSimilarityThroughSearch 通过搜索功能间接测试余弦相似度计算
func TestVectorSimilarityThroughSearch(t *testing.T) {
	store, err := NewSQLiteStore(&SQLiteConfig{
		DatabasePath: ":memory:",
	})
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	sessionID := "similarity-test"

	// 保存几个不同相似度的向量
	vectors := map[string][]float32{
		"identical":  {1.0, 2.0, 3.0},    // 与查询向量相同
		"similar":    {1.1, 2.1, 3.1},    // 相似
		"orthogonal": {0.0, 0.0, 1.0},    // 正交
		"opposite":   {-1.0, -2.0, -3.0}, // 相反
	}

	// 保存所有向量
	for msgID, vector := range vectors {
		err = store.SaveVector(ctx, sessionID, msgID, vector, nil)
		require.NoError(t, err)
	}

	// 使用查询向量搜索
	queryVector := []float32{1.0, 2.0, 3.0}
	results, err := store.SearchSimilar(ctx, sessionID, queryVector, 10, -1.0) // 阈值设为-1.0以获取所有结果
	require.NoError(t, err)
	require.Len(t, results, 4)

	// 验证结果按相似度排序
	assert.Equal(t, "identical", results[0].MessageID)
	assert.InDelta(t, 1.0, results[0].Score, 0.001) // 相同向量相似度为1

	assert.Equal(t, "similar", results[1].MessageID)
	assert.Greater(t, results[1].Score, float32(0.9)) // 相似向量相似度高

	// 相反向量的相似度应该是负数
	for _, result := range results {
		if result.MessageID == "opposite" {
			assert.Less(t, result.Score, float32(0.0))
		}
	}
}
