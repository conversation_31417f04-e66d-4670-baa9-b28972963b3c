package knowledge

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInMemoryKnowledgeBase(t *testing.T) {
	ctx := context.Background()
	kb := NewInMemoryKnowledgeBase()

	t.Run("AddAndGetDocument", func(t *testing.T) {
		doc := &Document{
			ID:       "doc-1",
			Title:    "Test Document",
			Content:  "This is a test document about artificial intelligence and machine learning.",
			Type:     DocumentTypeText,
			Language: "en",
			Tags:     []string{"ai", "ml", "test"},
			Metadata: map[string]interface{}{
				"author": "Test Author",
				"topic":  "AI",
			},
		}

		// 添加文档
		err := kb.AddDocument(ctx, doc)
		assert.NoError(t, err)

		// 获取文档
		retrieved, err := kb.GetDocument(ctx, "doc-1")
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, doc.ID, retrieved.ID)
		assert.Equal(t, doc.Title, retrieved.Title)
		assert.Equal(t, doc.Content, retrieved.Content)
		assert.Equal(t, doc.Type, retrieved.Type)
		assert.Equal(t, doc.Tags, retrieved.Tags)
	})

	t.Run("UpdateDocument", func(t *testing.T) {
		doc := &Document{
			ID:      "doc-2",
			Title:   "Original Title",
			Content: "Original content",
			Type:    DocumentTypeText,
		}

		// 添加文档
		err := kb.AddDocument(ctx, doc)
		require.NoError(t, err)

		// 更新文档
		doc.Title = "Updated Title"
		doc.Content = "Updated content"
		err = kb.UpdateDocument(ctx, doc)
		assert.NoError(t, err)

		// 验证更新
		retrieved, err := kb.GetDocument(ctx, "doc-2")
		assert.NoError(t, err)
		assert.Equal(t, "Updated Title", retrieved.Title)
		assert.Equal(t, "Updated content", retrieved.Content)
		assert.Equal(t, 2, retrieved.Version) // 版本应该增加
	})

	t.Run("DeleteDocument", func(t *testing.T) {
		doc := &Document{
			ID:      "doc-3",
			Title:   "To Be Deleted",
			Content: "This document will be deleted",
			Type:    DocumentTypeText,
		}

		// 添加文档
		err := kb.AddDocument(ctx, doc)
		require.NoError(t, err)

		// 删除文档
		err = kb.DeleteDocument(ctx, "doc-3")
		assert.NoError(t, err)

		// 验证删除
		_, err = kb.GetDocument(ctx, "doc-3")
		assert.Error(t, err)
	})

	t.Run("ListDocuments", func(t *testing.T) {
		// 添加多个文档
		for i := 0; i < 5; i++ {
			doc := &Document{
				ID:      fmt.Sprintf("list-doc-%d", i),
				Title:   fmt.Sprintf("List Document %d", i),
				Content: fmt.Sprintf("Content for document %d", i),
				Type:    DocumentTypeText,
			}
			err := kb.AddDocument(ctx, doc)
			require.NoError(t, err)
		}

		// 列出文档
		docs, err := kb.ListDocuments(ctx, 3, 0)
		assert.NoError(t, err)
		assert.Len(t, docs, 3)

		// 测试分页
		docs, err = kb.ListDocuments(ctx, 3, 2)
		assert.NoError(t, err)
		assert.True(t, len(docs) <= 3)
	})

	t.Run("SearchDocuments", func(t *testing.T) {
		// 添加测试文档
		docs := []*Document{
			{
				ID:      "search-1",
				Title:   "Machine Learning Basics",
				Content: "Introduction to machine learning algorithms and techniques.",
				Type:    DocumentTypeText,
				Tags:    []string{"ml", "basics"},
			},
			{
				ID:      "search-2",
				Title:   "Deep Learning Advanced",
				Content: "Advanced concepts in deep learning and neural networks.",
				Type:    DocumentTypeText,
				Tags:    []string{"dl", "advanced"},
			},
			{
				ID:      "search-3",
				Title:   "Natural Language Processing",
				Content: "NLP techniques for text processing and understanding.",
				Type:    DocumentTypeText,
				Tags:    []string{"nlp", "text"},
			},
		}

		for _, doc := range docs {
			err := kb.AddDocument(ctx, doc)
			require.NoError(t, err)
		}

		// 搜索测试
		query := &SearchQuery{
			Query: "machine learning",
			Limit: 10,
		}

		results, err := kb.SearchDocuments(ctx, query)
		assert.NoError(t, err)
		assert.True(t, len(results) > 0)

		// 验证结果按分数排序
		for i := 1; i < len(results); i++ {
			assert.True(t, results[i-1].Score >= results[i].Score)
		}

		// 标签过滤搜索
		tagQuery := &SearchQuery{
			Tags:  []string{"ml"},
			Limit: 10,
		}

		tagResults, err := kb.SearchDocuments(ctx, tagQuery)
		assert.NoError(t, err)
		assert.True(t, len(tagResults) > 0)
	})
}

func TestKnowledgeBaseEntities(t *testing.T) {
	ctx := context.Background()

	t.Run("AddAndGetEntity", func(t *testing.T) {
		kb := NewInMemoryKnowledgeBase()
		entity := &Entity{
			ID:          "entity-1",
			Name:        "John Doe",
			Type:        "person",
			Description: "A software engineer",
			Properties: map[string]interface{}{
				"age":        30,
				"occupation": "engineer",
			},
			Aliases:     []string{"Johnny", "J.Doe"},
			DocumentIDs: []string{"doc-1"},
		}

		// 添加实体
		err := kb.AddEntity(ctx, entity)
		assert.NoError(t, err)

		// 获取实体
		retrieved, err := kb.GetEntity(ctx, "entity-1")
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, entity.Name, retrieved.Name)
		assert.Equal(t, entity.Type, retrieved.Type)
		assert.Equal(t, entity.Properties, retrieved.Properties)
	})

	t.Run("SearchEntities", func(t *testing.T) {
		kb := NewInMemoryKnowledgeBase()
		// 添加多个实体
		entities := []*Entity{
			{
				ID:   "person-1",
				Name: "Alice Smith",
				Type: "person",
				Properties: map[string]interface{}{
					"department": "engineering",
				},
			},
			{
				ID:   "person-2",
				Name: "Bob Johnson",
				Type: "person",
				Properties: map[string]interface{}{
					"department": "marketing",
				},
			},
			{
				ID:   "org-1",
				Name: "TechCorp",
				Type: "organization",
			},
		}

		for _, entity := range entities {
			err := kb.AddEntity(ctx, entity)
			require.NoError(t, err)
		}

		// 按名称搜索
		query := &EntityQuery{
			Name:  "Alice",
			Limit: 10,
		}

		results, err := kb.SearchEntities(ctx, query)
		assert.NoError(t, err)
		assert.Len(t, results, 1)
		assert.Equal(t, "Alice Smith", results[0].Name)

		// 按类型搜索
		typeQuery := &EntityQuery{
			Type:  "person",
			Limit: 10,
		}

		typeResults, err := kb.SearchEntities(ctx, typeQuery)
		assert.NoError(t, err)
		assert.Len(t, typeResults, 2)
	})
}

func TestKnowledgeBaseRelations(t *testing.T) {
	ctx := context.Background()

	t.Run("AddAndGetRelation", func(t *testing.T) {
		kb := NewInMemoryKnowledgeBase()

		// 先添加实体
		entities := []*Entity{
			{ID: "person-1", Name: "Alice", Type: "person"},
			{ID: "person-2", Name: "Bob", Type: "person"},
			{ID: "org-1", Name: "Company", Type: "organization"},
		}

		for _, entity := range entities {
			err := kb.AddEntity(ctx, entity)
			require.NoError(t, err)
		}
		relation := &Relation{
			ID:          "rel-1",
			FromEntity:  "person-1",
			ToEntity:    "org-1",
			Type:        "works_for",
			Description: "Alice works for Company",
			Confidence:  0.9,
			Properties: map[string]interface{}{
				"start_date": "2020-01-01",
				"position":   "engineer",
			},
		}

		// 添加关系
		err := kb.AddRelation(ctx, relation)
		assert.NoError(t, err)

		// 获取关系
		retrieved, err := kb.GetRelation(ctx, "rel-1")
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, relation.FromEntity, retrieved.FromEntity)
		assert.Equal(t, relation.ToEntity, retrieved.ToEntity)
		assert.Equal(t, relation.Type, retrieved.Type)
	})

	t.Run("GetEntityRelations", func(t *testing.T) {
		kb := NewInMemoryKnowledgeBase()

		// 先添加实体
		entities := []*Entity{
			{ID: "person-1", Name: "Alice", Type: "person"},
			{ID: "person-2", Name: "Bob", Type: "person"},
			{ID: "org-1", Name: "Company", Type: "organization"},
		}

		for _, entity := range entities {
			err := kb.AddEntity(ctx, entity)
			require.NoError(t, err)
		}
		// 添加更多关系
		relations := []*Relation{
			{
				ID:         "rel-1",
				FromEntity: "person-1",
				ToEntity:   "org-1",
				Type:       "works_for",
				Confidence: 0.9,
			},
			{
				ID:         "rel-2",
				FromEntity: "person-1",
				ToEntity:   "person-2",
				Type:       "colleague",
				Confidence: 0.8,
			},
		}

		for _, relation := range relations {
			err := kb.AddRelation(ctx, relation)
			require.NoError(t, err)
		}

		// 获取实体的所有关系
		entityRelations, err := kb.GetEntityRelations(ctx, "person-1")
		assert.NoError(t, err)
		assert.Len(t, entityRelations, 2) // person-1 有两个关系
	})

	t.Run("GetRelatedEntities", func(t *testing.T) {
		kb := NewInMemoryKnowledgeBase()

		// 先添加实体
		entities := []*Entity{
			{ID: "person-1", Name: "Alice", Type: "person"},
			{ID: "person-2", Name: "Bob", Type: "person"},
			{ID: "org-1", Name: "Company", Type: "organization"},
		}

		for _, entity := range entities {
			err := kb.AddEntity(ctx, entity)
			require.NoError(t, err)
		}

		// 添加关系
		relations := []*Relation{
			{
				ID:         "rel-1",
				FromEntity: "person-1",
				ToEntity:   "org-1",
				Type:       "works_for",
				Confidence: 0.9,
			},
			{
				ID:         "rel-2",
				FromEntity: "person-1",
				ToEntity:   "person-2",
				Type:       "colleague",
				Confidence: 0.8,
			},
		}

		for _, relation := range relations {
			err := kb.AddRelation(ctx, relation)
			require.NoError(t, err)
		}
		// 获取相关实体
		relatedEntities, err := kb.GetRelatedEntities(ctx, "person-1", nil)
		assert.NoError(t, err)
		assert.True(t, len(relatedEntities) >= 2) // 至少有 person-2 和 org-1

		// 按关系类型过滤
		workRelated, err := kb.GetRelatedEntities(ctx, "person-1", []string{"works_for"})
		assert.NoError(t, err)
		assert.Len(t, workRelated, 1) // 只有 org-1
	})

	t.Run("FindPath", func(t *testing.T) {
		kb := NewInMemoryKnowledgeBase()

		// 先添加实体
		entities := []*Entity{
			{ID: "person-1", Name: "Alice", Type: "person"},
			{ID: "person-2", Name: "Bob", Type: "person"},
			{ID: "org-1", Name: "Company", Type: "organization"},
		}

		for _, entity := range entities {
			err := kb.AddEntity(ctx, entity)
			require.NoError(t, err)
		}

		// 添加关系
		relations := []*Relation{
			{
				ID:         "rel-1",
				FromEntity: "person-1",
				ToEntity:   "person-2",
				Type:       "colleague",
				Confidence: 0.8,
			},
			{
				ID:         "rel-2",
				FromEntity: "person-2",
				ToEntity:   "org-1",
				Type:       "works_for",
				Confidence: 0.9,
			},
		}

		for _, relation := range relations {
			err := kb.AddRelation(ctx, relation)
			require.NoError(t, err)
		}
		// 查找路径
		paths, err := kb.FindPath(ctx, "person-1", "org-1", 2)
		assert.NoError(t, err)
		assert.True(t, len(paths) > 0)

		// 验证路径
		path := paths[0]
		assert.Len(t, path, 2) // 路径有两个关系: person-1 -> person-2 -> org-1
		assert.Equal(t, "colleague", path[0].Type)
		assert.Equal(t, "works_for", path[1].Type)
	})
}

func TestKnowledgeBaseStats(t *testing.T) {
	ctx := context.Background()
	kb := NewInMemoryKnowledgeBase()

	// 添加测试数据
	doc := &Document{
		ID:      "stats-doc-1",
		Title:   "Stats Test",
		Content: "Test document for stats",
		Type:    DocumentTypeText,
	}
	err := kb.AddDocument(ctx, doc)
	require.NoError(t, err)

	entity := &Entity{
		ID:   "stats-entity-1",
		Name: "Stats Entity",
		Type: "test",
	}
	err = kb.AddEntity(ctx, entity)
	require.NoError(t, err)

	// 获取统计信息
	stats, err := kb.GetStats(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, stats)
	assert.Equal(t, 1, stats.DocumentCount)
	assert.Equal(t, 1, stats.EntityCount)
	assert.Equal(t, 0, stats.RelationCount)
	assert.Contains(t, stats.TypeCounts, DocumentTypeText)
	assert.Contains(t, stats.EntityTypes, "test")
}

func TestKnowledgeBaseFactory(t *testing.T) {
	t.Run("CreateInMemoryKnowledgeBase", func(t *testing.T) {
		config := &Config{
			Type: KnowledgeBaseTypeInMemory,
		}

		kb, err := NewKnowledgeBase(config)
		assert.NoError(t, err)
		assert.NotNil(t, kb)

		// 验证知识库可以正常工作
		ctx := context.Background()
		doc := &Document{
			ID:      "factory-test-1",
			Title:   "Factory Test",
			Content: "Test document from factory",
			Type:    DocumentTypeText,
		}

		err = kb.AddDocument(ctx, doc)
		assert.NoError(t, err)

		retrieved, err := kb.GetDocument(ctx, "factory-test-1")
		assert.NoError(t, err)
		assert.Equal(t, doc.Title, retrieved.Title)
	})

	t.Run("UnsupportedType", func(t *testing.T) {
		config := &Config{
			Type: "unsupported",
		}

		_, err := NewKnowledgeBase(config)
		assert.Error(t, err)
	})

	t.Run("GetSupportedTypes", func(t *testing.T) {
		types := GetSupportedKnowledgeBaseTypes()
		assert.Contains(t, types, KnowledgeBaseTypeInMemory)
	})
}

func TestRetrievalSystem(t *testing.T) {
	ctx := context.Background()
	kb := NewInMemoryKnowledgeBase()
	rs := NewRetrievalSystem(kb)

	// 添加测试文档
	docs := []*Document{
		{
			ID:      "retrieval-1",
			Title:   "Machine Learning Introduction",
			Content: "Basic concepts of machine learning and artificial intelligence.",
			Type:    DocumentTypeText,
			Tags:    []string{"ml", "ai", "basics"},
		},
		{
			ID:      "retrieval-2",
			Title:   "Deep Learning Networks",
			Content: "Neural networks and deep learning architectures.",
			Type:    DocumentTypeText,
			Tags:    []string{"dl", "neural", "advanced"},
		},
		{
			ID:      "retrieval-3",
			Title:   "Data Science Methods",
			Content: "Statistical methods and data analysis techniques.",
			Type:    DocumentTypeText,
			Tags:    []string{"data", "statistics", "analysis"},
		},
	}

	for _, doc := range docs {
		err := kb.AddDocument(ctx, doc)
		require.NoError(t, err)
	}

	t.Run("KeywordSearch", func(t *testing.T) {
		results, err := rs.KeywordSearch(ctx, "machine learning", 5)
		assert.NoError(t, err)
		assert.True(t, len(results) > 0)

		// 验证结果相关性
		found := false
		for _, result := range results {
			if result.Document.ID == "retrieval-1" {
				found = true
				break
			}
		}
		assert.True(t, found, "Should find the machine learning document")
	})

	t.Run("SemanticSearch", func(t *testing.T) {
		results, err := rs.SemanticSearch(ctx, "artificial intelligence", 5)
		assert.NoError(t, err)
		assert.True(t, len(results) > 0)
	})

	t.Run("HybridSearch", func(t *testing.T) {
		results, err := rs.HybridSearch(ctx, "neural networks", 5)
		assert.NoError(t, err)
		assert.True(t, len(results) > 0)
	})

	t.Run("FilteredRetrieval", func(t *testing.T) {
		filters := map[string]interface{}{
			"tags": []string{"ml"},
		}

		results, err := rs.FilteredRetrieval(ctx, filters, 5)
		assert.NoError(t, err)
		assert.True(t, len(results) > 0)
	})

	t.Run("RecentRetrieval", func(t *testing.T) {
		results, err := rs.RecentRetrieval(ctx, 5)
		assert.NoError(t, err)
		assert.Len(t, results, 3) // 应该返回所有3个文档
	})

	t.Run("RetrieveWithQuery", func(t *testing.T) {
		query := &RetrievalQuery{
			Query:    "machine learning",
			Type:     RetrievalTypeKeyword,
			Limit:    5,
			MinScore: 0.1,
		}

		result, err := rs.Retrieve(ctx, query)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, query.Query, result.Query)
		assert.Equal(t, query.Type, result.Type)
		assert.True(t, len(result.Documents) > 0)
	})
}

func TestKnowledgeBaseValidation(t *testing.T) {
	ctx := context.Background()
	kb := NewInMemoryKnowledgeBase()

	t.Run("NilDocument", func(t *testing.T) {
		err := kb.AddDocument(ctx, nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "document cannot be nil")
	})

	t.Run("EmptyDocumentID", func(t *testing.T) {
		_, err := kb.GetDocument(ctx, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "document ID cannot be empty")
	})

	t.Run("DocumentNotFound", func(t *testing.T) {
		_, err := kb.GetDocument(ctx, "non-existent")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "document not found")
	})

	t.Run("DuplicateDocument", func(t *testing.T) {
		doc := &Document{
			ID:      "duplicate-test",
			Title:   "Duplicate Test",
			Content: "Test document",
			Type:    DocumentTypeText,
		}

		// 第一次添加应该成功
		err := kb.AddDocument(ctx, doc)
		assert.NoError(t, err)

		// 第二次添加应该失败
		err = kb.AddDocument(ctx, doc)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "already exists")
	})

	t.Run("InvalidRelation", func(t *testing.T) {
		relation := &Relation{
			ID:         "invalid-rel",
			FromEntity: "non-existent-1",
			ToEntity:   "non-existent-2",
			Type:       "test",
		}

		err := kb.AddRelation(ctx, relation)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "does not exist")
	})
}

func TestConfigValidation(t *testing.T) {
	t.Run("ValidConfig", func(t *testing.T) {
		config := &Config{
			Type: KnowledgeBaseTypeInMemory,
		}

		err := config.Validate()
		assert.NoError(t, err)
	})

	t.Run("EmptyType", func(t *testing.T) {
		config := &Config{}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "type cannot be empty")
	})

	t.Run("UnsupportedType", func(t *testing.T) {
		config := &Config{
			Type: "unsupported",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported")
	})
}

// 性能测试
func TestKnowledgeBasePerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	ctx := context.Background()
	kb := NewInMemoryKnowledgeBase()

	t.Run("BulkDocumentOperations", func(t *testing.T) {
		// 批量添加文档
		start := time.Now()
		for i := 0; i < 1000; i++ {
			doc := &Document{
				ID:      fmt.Sprintf("perf-doc-%d", i),
				Title:   fmt.Sprintf("Performance Test Document %d", i),
				Content: fmt.Sprintf("This is test document number %d for performance testing.", i),
				Type:    DocumentTypeText,
				Tags:    []string{"performance", "test"},
			}
			err := kb.AddDocument(ctx, doc)
			require.NoError(t, err)
		}
		addDuration := time.Since(start)
		t.Logf("Added 1000 documents in %v", addDuration)

		// 批量搜索
		start = time.Now()
		for i := 0; i < 100; i++ {
			query := &SearchQuery{
				Query: "performance test",
				Limit: 10,
			}
			results, err := kb.SearchDocuments(ctx, query)
			require.NoError(t, err)
			require.True(t, len(results) > 0)
		}
		searchDuration := time.Since(start)
		t.Logf("Performed 100 searches in %v", searchDuration)

		// 性能断言
		assert.Less(t, addDuration, 10*time.Second, "Document addition should complete within 10 seconds")
		assert.Less(t, searchDuration, 5*time.Second, "Search operations should complete within 5 seconds")
	})
}
