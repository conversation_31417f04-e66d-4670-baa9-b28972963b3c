# AgentScope-Golang API 参考

## 概述

AgentScope-Golang 是一个完善的多智能体框架，为构建智能体系统提供坚实基础。本文档为所有公开接口与组件提供详细的 API 参考说明。

## 目录

- [Agent API](#agent-api)
- [Runtime API](#runtime-api)
- [Event API](#event-api)
- [Session API](#session-api)
- [Message API](#message-api)
- [LLM API](#llm-api)
- [工具 API](#tool-api)
- [记忆 API](#memory-api)
- [知识库 API](#knowledge-base-api)
- [审计 API](#audit-api)
- [组合 API](#compose-api)
- [MCP API](#mcp-api)
- [配置 API](#configuration-api)
- [Web API](#web-api)
- [分布式 API](#distributed-api)

## Agent API

### Agent 接口

核心的 `Agent` 接口定义了所有智能体实现必须遵循的契约。新架构采用事件驱动设计，所有智能体通过事件流进行通信。

```go
type Agent interface {
    // Name 返回智能体名称，用于标识与观测
    Name(ctx context.Context) string

    // Description 返回智能体描述，用于标识与观测
    Description(ctx context.Context) string

    // Run 启动异步任务并返回事件迭代器；调用方需要消费完毕
    Run(ctx context.Context, in *Input) *runtime.AsyncIterator[*event.Event]
}
```

### Input 结构

智能体的输入结构，包含消息、工具、记忆、会话和选项。

```go
type Input struct {
    Messages []*message.Message // 输入消息
    Tools    []tool.Tool        // 可用工具（含 MCP 注入）
    Memory   memory.Store       // 记忆存储引用
    Session  session.Map        // 并发安全会话（跨 Agent 可见）
    Options  map[string]any     // 执行可选项（如温度、top_k、tool_choice）
}
```

#### 方法说明

##### Name(ctx context.Context) string
返回智能体的名称，用于标识与观测。

##### Description(ctx context.Context) string
返回智能体的描述信息，用于标识与观测。

##### Run(ctx context.Context, in *Input) *runtime.AsyncIterator[*event.Event]
启动智能体异步任务并返回事件迭代器。调用方需要消费完毕所有事件。

## Runtime API

### Runner

Runner 是治理中枢，负责智能体的执行管理、拦截器链、超时重试、检查点和会话管理。

```go
type Runner struct {
    // 内部字段
}

type RunnerConfig struct {
    EnableStreaming    bool
    BufferSize         int
    Timeout            time.Duration
    MaxRetries         int
    RetryDelay         time.Duration
    CheckpointStore    CheckpointStore
    EnableCheckpoint   bool
    CheckpointInterval int
}

func NewRunner(ctx context.Context, config RunnerConfig) *Runner
func DefaultRunnerConfig() RunnerConfig
```

#### 主要方法

```go
// 执行智能体并返回事件流
func (r *Runner) Run(ctx context.Context, agent Agent, input *Input) *AsyncIterator[*event.Event]

// 执行智能体并收集所有事件
func (r *Runner) RunAndCollect(ctx context.Context, agent Agent, input *Input) ([]*event.Event, error)

// 执行智能体并使用回调处理事件
func (r *Runner) RunWithCallback(ctx context.Context, agent Agent, input *Input, callback func(*event.Event) bool) error

// 拦截器管理
func (r *Runner) AddInterceptor(interceptor Interceptor)
func (r *Runner) RemoveInterceptor(name string) bool
func (r *Runner) ListInterceptors() []string
func (r *Runner) ClearInterceptors()

// 配置管理
func (r *Runner) UpdateConfig(config RunnerConfig)
func (r *Runner) GetConfig() RunnerConfig

// 健康检查
func (r *Runner) Health() map[string]any
```

### AsyncIterator

异步事件迭代器，用于处理事件流。

```go
type AsyncIterator[T any] struct {
    // 内部字段
}

// 获取下一个事件
func (it *AsyncIterator[T]) Next() (T, bool)

// 收集所有事件到切片
func (it *AsyncIterator[T]) Collect() []T

// 使用回调处理每个事件
func (it *AsyncIterator[T]) ForEach(callback func(T) bool)
```

### 拦截器

拦截器用于在智能体执行过程中进行切面处理。

```go
type Interceptor interface {
    Name() string
    BeforeRun(ctx context.Context, agentName string, input any) error
    AfterRun(ctx context.Context, agentName string, input any, err error) error
    OnEvent(ctx context.Context, agentName string, ev *event.Event) error
    OnError(ctx context.Context, agentName string, err error) error
}
```

#### 内置拦截器

```go
// 日志拦截器
func NewLoggingInterceptor(name string, logger *log.Logger) Interceptor

// 指标拦截器
func NewMetricsInterceptor(name string) *MetricsInterceptor

// 运行时拦截器适配器（用于函数式拦截器）
func NewRuntimeInterceptorAdapter(interceptor FunctionalInterceptor) Interceptor
```

### CheckpointStore

检查点存储接口，用于保存和恢复智能体执行状态。

```go
type CheckpointStore interface {
    Save(ctx context.Context, sessionID string, data *CheckpointData) error
    Load(ctx context.Context, sessionID string) (*CheckpointData, error)
    Delete(ctx context.Context, sessionID string) error
    List(ctx context.Context) ([]string, error)
    Exists(ctx context.Context, sessionID string) (bool, error)
    Close() error
}

type CheckpointData struct {
    SessionID string
    AgentName string
    State     map[string]any
    Metadata  map[string]any
    CreatedAt time.Time
    UpdatedAt time.Time
}
```

#### 实现

```go
// 内存检查点存储（用于测试）
func NewMemoryCheckpointStore() CheckpointStore

// SQLite 检查点存储
func NewSQLiteCheckpointStore(dbPath string) (CheckpointStore, error)

// PostgreSQL 检查点存储
func NewPostgresCheckpointStore(dsn string) (CheckpointStore, error)
```

## Session API

Session 提供并发安全的会话存储，用于在智能体之间共享数据。

### Session Map 接口

```go
type Map interface {
    // 设置键值对
    Set(key string, val any)

    // 获取指定键的值，返回值和是否存在的标志
    Get(key string) (any, bool)

    // 删除指定键
    Delete(key string)

    // 返回所有键的列表
    Keys() []string

    // 清空所有数据
    Clear()

    // 返回存储的键值对数量
    Size() int

    // 创建当前会话的副本
    Clone() Map
}
```

### 创建和使用

```go
// 创建新的并发安全会话存储
func New() Map

// 上下文集成
func WithSession(ctx context.Context, session Map) context.Context
func FromContext(ctx context.Context) (Map, bool)
func GetFromContext(ctx context.Context) Map

// 便捷函数
func SetValue(ctx context.Context, key string, val any)
func GetValue(ctx context.Context, key string) (any, bool)
func DeleteValue(ctx context.Context, key string)
```

### 只读会话

```go
// 创建只读会话视图，防止误写
func NewReadOnly(underlying Map) Map
```

## Event API

事件系统是框架的核心，所有智能体执行过程都通过事件进行通信。

### 事件类型

```go
type Type string

const (
    EventToken     Type = "token"      // Token 事件
    EventThought   Type = "thought"    // 思考事件
    EventToolCall  Type = "tool_call"  // 工具调用事件
    EventToolResult Type = "tool_result" // 工具结果事件
    EventFinal     Type = "final"      // 最终事件
    EventError     Type = "error"      // 错误事件
    EventTransfer  Type = "transfer"   // 转移事件
)
```

### 事件结构

```go
type Event struct {
    ID        string    // 事件唯一标识
    Type      Type      // 事件类型
    Data      any       // 事件数据
    Timestamp time.Time // 时间戳
    Err       error     // 错误信息（仅错误事件）
}

// 检查是否为错误事件
func (e *Event) IsError() bool
```

### 事件创建函数

```go
// Token 事件
func NewTokenEvent(content string, isVisible bool) *Event

// 思考事件
func NewThoughtEvent(content, reasoning string) *Event

// 工具调用事件
func NewToolCallEvent(toolName string, params map[string]any) *Event

// 工具结果事件
func NewToolResultEvent(toolName string, result any, err error) *Event

// 最终事件
func NewFinalEvent(content, summary string, metadata map[string]any) *Event

// 错误事件
func NewErrorEvent(err error, code string, metadata map[string]any) *Event

// 转移事件
func NewTransferEvent(from, to, reason string) *Event
```

**返回：**
- `string`：智能体的唯一 ID

##### Name() string
返回智能体的人类可读名称。

**Returns:**
- `string`：智能体名称

##### Type() AgentType
返回智能体的类型。

**Returns:**
- `AgentType`：智能体类型（AssistantType、UserType 等）

##### Description() string
返回智能体的用途与能力描述。

**Returns:**
- `string`：智能体描述

##### State() AgentState
返回智能体当前状态。

**Returns:**
- `AgentState`：当前状态（Idle、Processing、Error 等）

##### Initialize() error
初始化智能体并为运行做好准备。

**Returns:**
- `error`：初始化失败时返回错误，否则为 nil

##### Shutdown()
优雅地关闭智能体并释放资源。

##### Reply(ctx context.Context, message message.Message) (message.Message, error)
处理传入消息并返回响应。

**参数：**
- `ctx context.Context`：请求上下文（用于取消与超时）
- `message message.Message`：要处理的传入消息

**返回：**
- `message.Message`：响应消息
- `error`：处理失败时返回错误，否则为 nil

##### UpdateConfig(config *Config) error
更新智能体配置。

**参数：**
- `config *Config`：需要应用的新配置

**返回：**
- `error`：更新失败时返回错误，否则为 nil

### AssistantAgent

LLM 驱动的助手智能体，支持对话、工具调用，并可接入记忆服务与知识库组件。
```go
type AssistantAgent struct {
    // Embedded BaseAgent
    *BaseAgent

    // LLM client for generating responses
    llmClient llm.LLMClient

    // Optional components
    memoryService memory.MemoryService
    toolRegistry  tool.Registry
    knowledgeBase knowledge.KnowledgeBase
}
```

#### 构造函数





```go
func NewAssistantAgent(config *Config, llmClient llm.LLMClient, logger logger.Logger) (*AssistantAgent, error)
```

**参数：**
- `config *Config`：智能体配置
- `llmClient llm.LLMClient`：用于生成响应的 LLM 客户端
- `logger logger.Logger`：日志记录器实例

**返回：**
- `*AssistantAgent`：新建的助手智能体实例
- `error`：创建失败返回错误，否则为 nil

#### 方法

##### SetMemoryService(service memory.MemoryService)
设置智能体的记忆服务。







**参数：**
- `service memory.MemoryService`：内存（记忆）服务实例

##### SetToolRegistry(registry tool.Registry)
设置工具注册表。







**参数：**
- `registry tool.Registry`：工具注册表实例

##### SetKnowledgeBase(kb knowledge.KnowledgeBase)
设置知识库实例。







**参数：**
- `kb knowledge.KnowledgeBase`：知识库实例

### UserAgent

负责处理用户输入与交互的智能体。
```go
type UserAgent struct {
    *BaseAgent
    inputHandler InputHandler
}
```

#### 构造函数





```go
func NewUserAgent(config *Config, inputHandler InputHandler, logger logger.Logger) (*UserAgent, error)
```

**参数：**
- `config *Config`：智能体配置
- `inputHandler InputHandler`：用户输入处理器
- `logger logger.Logger`：日志记录器实例

**返回：**
- `*UserAgent`：新建的用户智能体实例
- `error`：创建失败返回错误，否则为 nil

## Message API

### Message 接口

`Message` 接口表示智能体之间传递的通信单元。

```go
type Message interface {
    // Core properties
    ID() string
    Type() MessageType
    Sender() string
    Timestamp() time.Time
    Content() MessageContent

    // Serialization
    ToJSON() ([]byte, error)
    FromJSON(data []byte) error

    // Validation
    Validate() error
}
```

### MessageContent 接口

`MessageContent` 接口定义了消息的内容表示。

```go
type MessageContent interface {
    Type() string
    String() string
    Validate() error
}
```

### 内容类型

#### TextContent

```go
type TextContent struct {
    Text string `json:"text"`
}

func NewTextContent(text string) *TextContent
func (tc *TextContent) Type() string
func (tc *TextContent) String() string
func (tc *TextContent) Validate() error
```

#### ImageContent

```go
type ImageContent struct {
    Data     []byte            `json:"data"`
    MimeType string            `json:"mime_type"`
    Metadata map[string]string `json:"metadata,omitempty"`
}

func NewImageContent(data []byte, mimeType string) *ImageContent
func (ic *ImageContent) Type() string
func (ic *ImageContent) String() string
func (ic *ImageContent) Validate() error
```

#### ToolCallContent

```go
type ToolCallContent struct {
    ToolName   string                 `json:"tool_name"`
    Parameters map[string]interface{} `json:"parameters"`
    CallID     string                 `json:"call_id"`
}

func NewToolCallContent(toolName string, params map[string]interface{}) *ToolCallContent
func (tcc *ToolCallContent) Type() string
func (tcc *ToolCallContent) String() string
func (tcc *ToolCallContent) Validate() error
```

#### ToolResultContent

```go
type ToolResultContent struct {
    CallID  string      `json:"call_id"`
    Success bool        `json:"success"`
    Result  interface{} `json:"result,omitempty"`
    Error   string      `json:"error,omitempty"`
}

func NewToolResultContent(callID string, success bool, result interface{}, err string) *ToolResultContent
func (trc *ToolResultContent) Type() string
func (trc *ToolResultContent) String() string
func (trc *ToolResultContent) Validate() error
```

### Message Factory

```go
func NewTextMessage(sender, text string) Message
func NewImageMessage(sender string, data []byte, mimeType string) Message
func NewToolCallMessage(sender, toolName string, params map[string]interface{}) Message
func NewToolResultMessage(sender, callID string, success bool, result interface{}, err string) Message
```

## Pipeline API

### Pipeline 接口

`Pipeline` 接口定义了一组按顺序/条件执行的智能体处理流程。

```go
type Pipeline interface {
    // Configuration
    Name() string
    Description() string

    // Agent management
    AddAgent(agent agent.Agent) error
    RemoveAgent(agentID string) error
    ListAgents() []agent.Agent

    // Execution
    Execute(ctx context.Context, input message.Message) (message.Message, error)

    // State management
    GetState() PipelineState
    Reset() error
}
```

### Pipeline 类型

#### SequentialPipeline

按顺序执行各个智能体，并将前一个的输出传递给下一个。

```go
func NewSequentialPipeline(config *Config, logger logger.Logger) *SequentialPipeline
```

#### ParallelPipeline

并行执行多个智能体并聚合其输出。

```go
func NewParallelPipeline(config *Config, logger logger.Logger) *ParallelPipeline
```

#### ConditionalPipeline

依据条件逻辑选择性地执行智能体。

```go
func NewConditionalPipeline(config *Config, logger logger.Logger) *ConditionalPipeline
```

## LLM API

### 支持的 LLM 提供商

AgentScope-Golang 支持多种大语言模型提供商：

- **DeepSeek**：高性能的中文大语言模型
- **Qwen（通义千问）**：阿里云 DashScope 平台的大语言模型
- **豆包（Doubao）**：字节跳动火山引擎 Ark 平台的大语言模型

### 多提供商配置

支持在配置文件中同时配置多个 LLM 提供商，并可以动态切换：

```yaml
llm:
  default_provider: "qwen"  # 默认使用的提供商
  timeout: "30s"
  max_retries: 3
  providers:
    deepseek:
      type: "deepseek"
      api_key: "${DEEPSEEK_API_KEY}"
      base_url: "https://api.deepseek.com/v1"
      model: "deepseek-chat"
    qwen:
      type: "qwen"
      api_key: "${DASHSCOPE_API_KEY}"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model: "qwen-plus"
    doubao:
      type: "doubao"
      api_key: "${ARK_API_KEY}"
      base_url: "https://ark.cn-beijing.volces.com/api/v3"
      model: "ep-xxxxxxxx"  # 请替换为实际的 Endpoint ID
```

### 环境变量配置

各提供商支持的环境变量：

- **DeepSeek**：`DEEPSEEK_API_KEY`
- **Qwen**：`DASHSCOPE_API_KEY` 或 `QWEN_API_KEY`
- **豆包**：`ARK_API_KEY`、`DOUBAO_API_KEY` 或 `VOLC_API_KEY`

### LLMClient 接口

`LLMClient` 接口提供对大语言模型（LLM）的访问能力。

```go
type LLMClient interface {
    Generate(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error)
    GenerateStream(ctx context.Context, req *GenerateRequest) (<-chan *StreamResponse, error)
    GenerateWithTools(ctx context.Context, req *GenerateRequest, tools []tool.Tool) (*GenerateResponse, error)
}
```

### 请求/响应类型

#### GenerateRequest

```go
type GenerateRequest struct {
    Messages    []Message              `json:"messages"`
    Model       string                 `json:"model,omitempty"`
    Temperature float64                `json:"temperature,omitempty"`
    MaxTokens   int                    `json:"max_tokens,omitempty"`
    Tools       []ToolDefinition       `json:"tools,omitempty"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
}
```

#### GenerateResponse

```go
type GenerateResponse struct {
    Content   string    `json:"content"`
    ToolCalls []ToolCall `json:"tool_calls,omitempty"`
    Usage     *Usage    `json:"usage,omitempty"`
    Model     string    `json:"model"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
}
```

#### StreamResponse

```go
type StreamResponse struct {
    Content   string    `json:"content"`
    ToolCalls []ToolCall `json:"tool_calls,omitempty"`
    Done      bool      `json:"done"`
    Error     string    `json:"error,omitempty"`
}
```

### LLM Implementations

#### DeepseekClient

```go
type DeepseekConfig struct {
    APIKey      string        `json:"api_key"`
    BaseURL     string        `json:"base_url"`
    Model       string        `json:"model"`
    Timeout     time.Duration `json:"timeout"`
    RetryConfig *RetryConfig  `json:"retry_config,omitempty"`
}

func NewDeepseekClient(config *DeepseekConfig) *DeepseekClient
```

## 工具 API

### Tool 接口

`Tool` 接口定义了智能体可以使用的可执行工具。

```go
type Tool interface {
    Name() string
    Description() string
    Schema() *ToolSchema
    Execute(ctx context.Context, params map[string]interface{}) (*ToolResult, error)
}
```

### 工具注册表

```go
type Registry interface {
    RegisterTool(tool Tool) error
    UnregisterTool(name string) error
    GetTool(name string) (Tool, error)
    ListTools() []Tool
    HasTool(name string) bool
}

func NewDefaultRegistry() Registry
```

### Built-in Tools

#### Calculator Tool

```go
func NewCalculatorTool() Tool
```

Supports basic arithmetic operations: add, subtract, multiply, divide.

#### Time Tool

```go
func NewTimeTool() Tool
```

Provides current time and date formatting capabilities.

#### File Tool

```go
func NewFileTool() Tool
```

Provides file system operations: read, write, list, delete.

### Tool Schema

```go
type ToolSchema struct {
    Type       string               `json:"type"`
    Properties map[string]*Parameter `json:"properties"`
    Required   []string             `json:"required,omitempty"`
}

type Parameter struct {
    Type        string      `json:"type"`
    Description string      `json:"description"`
    Required    bool        `json:"required"`
    Default     interface{} `json:"default,omitempty"`
    Enum        []string    `json:"enum,omitempty"`
}
```

### Tool Result

```go
type ToolResult struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data,omitempty"`
    Error   string      `json:"error,omitempty"`
    Message string      `json:"message,omitempty"`
}
```

## 记忆 API

### MemoryService 接口

`MemoryService` 接口提供记忆管理能力。

```go
type MemoryService interface {
    AddMemory(ctx context.Context, memory *Memory) error
    RetrieveMemories(ctx context.Context, query *MemoryQuery) ([]*Memory, error)
    UpdateMemory(ctx context.Context, memory *Memory) error
    DeleteMemory(ctx context.Context, id string) error
    ClearMemories(ctx context.Context, memoryType MemoryType) error
}
```

### 记忆类型

```go
type Memory struct {
    ID          string                 `json:"id"`
    Content     string                 `json:"content"`
    Type        MemoryType             `json:"type"`
    Importance  float64                `json:"importance"`
    Timestamp   time.Time              `json:"timestamp"`
    ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
    Tags        []string               `json:"tags,omitempty"`
}

type MemoryQuery struct {
    Keywords    []string   `json:"keywords,omitempty"`
    Type        MemoryType `json:"type,omitempty"`
    TimeRange   *TimeRange `json:"time_range,omitempty"`
    Limit       int        `json:"limit,omitempty"`
    MinImportance float64  `json:"min_importance,omitempty"`
    Tags        []string   `json:"tags,omitempty"`
}
```

### Memory Factory

```go
func NewMemoryService(config *Config) (MemoryService, error)
```

## 知识库 API

### KnowledgeBase 接口

`KnowledgeBase` 接口提供知识管理能力。

```go
type KnowledgeBase interface {
    AddDocument(ctx context.Context, doc *Document) error
    GetDocument(ctx context.Context, id string) (*Document, error)
    UpdateDocument(ctx context.Context, doc *Document) error
    DeleteDocument(ctx context.Context, id string) error
    SearchDocuments(ctx context.Context, query *SearchQuery) ([]*Document, error)

    AddEntity(ctx context.Context, entity *Entity) error
    GetEntity(ctx context.Context, id string) (*Entity, error)
    AddRelation(ctx context.Context, relation *Relation) error
    GetRelations(ctx context.Context, entityID string) ([]*Relation, error)
}
```

### 知识类型

```go
type Document struct {
    ID          string                 `json:"id"`
    Title       string                 `json:"title"`
    Content     string                 `json:"content"`
    Type        string                 `json:"type"`
    Source      string                 `json:"source,omitempty"`
    CreatedAt   time.Time              `json:"created_at"`
    UpdatedAt   time.Time              `json:"updated_at"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
    Tags        []string               `json:"tags,omitempty"`
}

type Entity struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Type        string                 `json:"type"`
    Description string                 `json:"description,omitempty"`
    Properties  map[string]interface{} `json:"properties,omitempty"`
}

type Relation struct {
    ID         string `json:"id"`
    SourceID   string `json:"source_id"`
    TargetID   string `json:"target_id"`
    Type       string `json:"type"`
    Properties map[string]interface{} `json:"properties,omitempty"`
}
```

## 配置 API

### Config 结构

```go
type Config struct {
    App     AppConfig     `yaml:"app"`
    Agents  []AgentConfig `yaml:"agents"`
    LLM     LLMConfig     `yaml:"llm"`
    Memory  MemoryConfig  `yaml:"memory"`
    Tools   ToolsConfig   `yaml:"tools"`
    Web     WebConfig     `yaml:"web"`
    Logging LoggingConfig `yaml:"logging"`
}
```

### Configuration Loading

```go
func LoadConfig(path string) (*Config, error)
func LoadConfigFromBytes(data []byte) (*Config, error)
func (c *Config) Validate() error
func (c *Config) Save(path string) error
```

## Web API

### HTTP 端点

所有 API 端点都使用 `/api/v1` 前缀。

#### 健康检查
```
GET /api/v1/health
```

返回服务的健康状态。

**响应：**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00Z",
    "services": {
        "agents": "ok",
        "pipelines": "ok"
    }
}
```

#### 智能体管理

##### 列出智能体
```
GET /api/v1/agents
```

返回所有可用智能体的列表。

**响应：**
```json
[
    {
        "id": "agent-1",
        "name": "Assistant Agent",
        "type": "assistant",
        "description": "An AI assistant agent",
        "state": "idle"
    }
]
```

##### 创建智能体
```
POST /api/v1/agents
```

创建新的智能体实例。

**请求体：**
```json
{
    "name": "My Agent",
    "type": "assistant",
    "description": "Custom assistant agent",
    "config": {
        "model": "deepseek-chat",
        "temperature": 0.7
    }
}
```

**响应：**
```json
{
    "id": "agent-123",
    "name": "My Agent",
    "type": "assistant",
    "description": "Custom assistant agent",
    "state": "idle"
}
```

##### 获取智能体
```
GET /api/v1/agents/{id}
```

返回指定智能体的详细信息。

**参数：**
- `id`：智能体 ID

**响应：**
```json
{
    "id": "agent-1",
    "name": "Assistant Agent",
    "type": "assistant",
    "description": "An AI assistant agent",
    "state": "idle",
    "config": {...}
}
```

##### 更新智能体
```
PUT /api/v1/agents/{id}
```

更新智能体配置。

**参数：**
- `id`：智能体 ID

**请求体：**
```json
{
    "name": "Updated Agent",
    "description": "Updated description",
    "config": {...}
}
```

##### 删除智能体
```
DELETE /api/v1/agents/{id}
```

删除指定的智能体。

**参数：**
- `id`：智能体 ID

##### 智能体对话
```
POST /api/v1/agents/{id}/reply
```

与智能体进行对话交互。

**参数：**
- `id`：智能体 ID

**请求体：**
```json
{
    "content": "Hello, how are you?",
    "type": "text",
    "metadata": {...}
}
```

**响应：**
```json
{
    "id": "msg-123",
    "content": "I'm doing well, thank you!",
    "type": "text",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Pipeline 管理

##### 列出 Pipeline
```
GET /api/v1/pipelines
```

返回所有可用的 Pipeline 列表。

**响应：**
```json
[
    {
        "id": "simple_chat",
        "name": "Simple Chat Pipeline",
        "type": "sequential",
        "description": "Basic chat pipeline"
    }
]
```

##### 创建 Pipeline
```
POST /api/v1/pipelines
```

创建新的 Pipeline。

**请求体：**
```json
{
    "name": "My Pipeline",
    "type": "sequential",
    "description": "Custom pipeline",
    "config": {...}
}
```

##### 获取 Pipeline
```
GET /api/v1/pipelines/{id}
```

返回指定 Pipeline 的详细信息。

**参数：**
- `id`：Pipeline ID

##### 更新 Pipeline
```
PUT /api/v1/pipelines/{id}
```

更新 Pipeline 配置。

##### 删除 Pipeline
```
DELETE /api/v1/pipelines/{id}
```

删除指定的 Pipeline。

##### 执行 Pipeline
```
POST /api/v1/pipelines/{id}/execute
```

执行指定的 Pipeline。

**参数：**
- `id`：Pipeline ID

**请求体：**
```json
{
    "input": "Hello, world!",
    "metadata": {...}
}
```

**响应：**
```json
{
    "id": "exec-123",
    "status": "completed",
    "result": "Pipeline execution result",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 消息管理

##### 创建消息
```
POST /api/v1/messages
```

创建新的消息对象。

**请求体：**
```json
{
    "type": "user",
    "content": "Hello, world!",
    "sender": "user-123"
}
```

**响应：**
```json
{
    "id": "msg-123",
    "type": "user",
    "content": "Hello, world!",
    "sender": "user-123",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### WebSocket API

#### 连接建立
```
WS /ws
```

建立 WebSocket 连接进行实时通信。支持 Origin 校验和连接管理。

#### 消息格式

**客户端到服务器：**
```json
{
    "type": "message",
    "agent": "agent-1",
    "content": "Hello!",
    "metadata": {...}
}
```

**服务器到客户端：**
```json
{
    "type": "response",
    "agent": "agent-1",
    "content": "Hello! How can I help you?",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### WebSocket 事件类型

- `message`：用户消息
- `response`：智能体响应
- `error`：错误信息
- `status`：状态更新
- `ping`/`pong`：心跳检测

### 审计 API

审计 API 提供对会话和消息的查询功能。需要在配置中启用 `allow_read_api`。

#### 获取会话列表
```
GET /api/v1/audit/sessions
```

返回会话列表。

**查询参数：**
- `user_id`：用户 ID（可选）
- `page`：页码（默认 1）
- `page_size`：每页大小（默认 50，最大 100）

**响应：**
```json
{
    "sessions": [
        {
            "session_id": "session-123",
            "user_id": "user-456",
            "created_at": "2024-01-01T10:00:00Z",
            "last_active_at": "2024-01-01T10:30:00Z",
            "message_count": 15
        }
    ],
    "total": 1,
    "page": 1,
    "page_size": 50,
    "has_more": false
}
```

#### 获取会话消息
```
GET /api/v1/audit/sessions/{session_id}/messages
```

返回指定会话的消息列表。

**路径参数：**
- `session_id`：会话 ID

**查询参数：**
- `page`：页码（默认 1）
- `page_size`：每页大小（默认 50，最大 100）
- `type`：消息类型过滤
- `agent_id`：智能体 ID 过滤
- `q`：关键词搜索

**响应：**
```json
{
    "messages": [
        {
            "id": "msg-123",
            "session_id": "session-123",
            "user_id": "user-456",
            "role": "user",
            "msg_type": "text",
            "content": "Hello, world!",
            "created_at": "2024-01-01T10:00:00Z"
        }
    ],
    "total": 1,
    "page": 1,
    "page_size": 50,
    "has_more": false
}
```

#### 获取会话摘要
```
GET /api/v1/audit/sessions/{session_id}/summary
```

返回会话的统计摘要信息。

**响应：**
```json
{
    "session_id": "session-123",
    "user_id": "user-456",
    "message_count": 15,
    "agent_count": 2,
    "duration": "00:30:00",
    "created_at": "2024-01-01T10:00:00Z",
    "last_active_at": "2024-01-01T10:30:00Z"
}
```

#### 获取用户消息
```
GET /api/v1/audit/users/{user_id}/messages
```

返回指定用户的消息列表。

**路径参数：**
- `user_id`：用户 ID

**查询参数：**
- `page`：页码
- `page_size`：每页大小

#### 获取用户活动
```
GET /api/v1/audit/users/{user_id}/activity
```

返回用户活动统计信息。

**查询参数：**
- `since`：起始时间（RFC3339 格式）

#### 搜索消息
```
GET /api/v1/audit/search
```

全局搜索消息。

**查询参数：**
- `q`：搜索关键词
- `type`：消息类型
- `user_id`：用户 ID
- `session_id`：会话 ID
- `since`：起始时间
- `until`：结束时间
- `page`：页码
- `page_size`：每页大小

#### 执行保留策略
```
POST /api/v1/audit/retention/run
```

手动执行数据保留策略清理。需要管理员权限和启用 `allow_delete_api`。

#### 获取保留策略统计
```
GET /api/v1/audit/retention/stats
```

返回保留策略的执行统计信息。

### 错误处理

所有 API 端点都使用标准的 HTTP 状态码，并返回结构化的错误信息。

#### 状态码

- `200 OK`：请求成功
- `201 Created`：资源创建成功
- `400 Bad Request`：请求参数错误
- `401 Unauthorized`：未授权访问
- `403 Forbidden`：权限不足
- `404 Not Found`：资源不存在
- `409 Conflict`：资源冲突
- `429 Too Many Requests`：请求频率限制
- `500 Internal Server Error`：服务器内部错误
- `503 Service Unavailable`：服务不可用

#### 错误响应格式

```json
{
    "error": {
        "type": "validation",
        "code": "INVALID_INPUT",
        "message": "请求参数无效",
        "details": "字段 'name' 不能为空",
        "timestamp": "2024-01-01T00:00:00Z",
        "request_id": "req-123"
    }
}
```

#### 错误类型

- `validation`：输入验证错误
- `authentication`：认证错误
- `authorization`：授权错误
- `not_found`：资源不存在
- `conflict`：资源冲突
- `rate_limit`：频率限制
- `internal`：内部错误
- `service_unavailable`：服务不可用

### 认证和授权

#### API Key 认证

在请求头中包含 API Key：

```http
Authorization: Bearer your-api-key-here
```

#### JWT 认证

使用 JWT Token 进行认证：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 权限级别

- `read`：只读权限，可以查询数据
- `write`：写入权限，可以创建和更新资源
- `admin`：管理员权限，可以删除资源和执行管理操作

### 速率限制

API 实施速率限制以防止滥用：

- **默认限制**：每分钟 100 请求
- **认证用户**：每分钟 1000 请求
- **管理员**：每分钟 5000 请求

超出限制时返回 `429 Too Many Requests` 状态码。

### 分页

支持分页的端点使用以下参数：

- `page`：页码（从 1 开始）
- `page_size`：每页大小（默认 50，最大 100）

响应包含分页信息：

```json
{
    "data": [...],
    "pagination": {
        "page": 1,
        "page_size": 50,
        "total": 150,
        "has_more": true
    }
}
```

### 内容类型

- **请求**：`application/json`
- **响应**：`application/json`
- **WebSocket**：`application/json` 消息格式

### CORS 支持

API 支持跨域请求（CORS），允许的方法：

- `GET`
- `POST`
- `PUT`
- `DELETE`
- `OPTIONS`

### API 版本控制

当前 API 版本为 `v1`，所有端点都使用 `/api/v1` 前缀。未来版本将使用不同的前缀（如 `/api/v2`）以保持向后兼容性。
```

## 分布式 API

### 服务发现

```go
type ServiceDiscovery interface {
    RegisterService(ctx context.Context, service *ServiceInfo) error
    UnregisterService(ctx context.Context, serviceID string) error
    GetService(ctx context.Context, serviceID string) (*ServiceInfo, error)
    DiscoverServices(ctx context.Context, serviceName string) ([]*ServiceInfo, error)
    ListServices(ctx context.Context) ([]*ServiceInfo, error)
    HealthCheck(ctx context.Context, serviceID string) error
    Watch(ctx context.Context, serviceName string) (ServiceWatcher, error)
}
```

### Load Balancer

```go
type LoadBalancer interface {
    SelectService(ctx context.Context, serviceName string) (*ServiceInfo, error)
    UpdateServices(serviceName string, services []*ServiceInfo)
    GetStats() *LoadBalancerStats
}
```

### Service Types

```go
type ServiceInfo struct {
    ID       string                 `json:"id"`
    Name     string                 `json:"name"`
    Address  string                 `json:"address"`
    Port     int                    `json:"port"`
    Status   ServiceStatus          `json:"status"`
    Metadata map[string]interface{} `json:"metadata,omitempty"`
    Tags     []string               `json:"tags,omitempty"`
}
```

## Error Handling

### Error Types

```go
type AgentScopeError struct {
    Code      ErrorCode              `json:"code"`
    Message   string                 `json:"message"`
    Details   map[string]interface{} `json:"details,omitempty"`
    Cause     error                  `json:"-"`
    Timestamp time.Time              `json:"timestamp"`
}
```

### Error Codes

```go
const (
    // General errors
    ErrCodeUnknown ErrorCode = iota
    ErrCodeInvalidInput
    ErrCodeNotFound
    ErrCodeAlreadyExists
    ErrCodePermissionDenied
    ErrCodeTimeout

    // Agent errors
    ErrCodeAgentNotFound
    ErrCodeAgentInitFailed
    ErrCodeAgentProcessingFailed

    // LLM errors
    ErrCodeLLMUnavailable
    ErrCodeLLMRateLimited
    ErrCodeLLMInvalidResponse

    // Tool errors
    ErrCodeToolNotFound
    ErrCodeToolExecutionFailed
    ErrCodeToolInvalidParams
)
```

## Examples

### Basic Agent Usage

```go
// Create LLM client
llmConfig := &llm.DeepseekConfig{
    APIKey: os.Getenv("DEEPSEEK_API_KEY"),
    Model:  "deepseek-chat",
}
llmClient := llm.NewDeepseekClient(llmConfig)

// Create agent
agentConfig := &agent.Config{
    ID:   "my-assistant",
    Name: "My Assistant",
    Type: agent.AssistantType,
}
agent, err := agent.NewAssistantAgent(agentConfig, llmClient, logger)
if err != nil {
    log.Fatal(err)
}

// Initialize and use agent
err = agent.Initialize()
if err != nil {
    log.Fatal(err)
}
defer agent.Shutdown()

// Send message
msg := message.NewTextMessage("user", "Hello!")
response, err := agent.Reply(context.Background(), msg)
if err != nil {
    log.Fatal(err)
}

fmt.Println(response.Content().String())
```

### Pipeline Usage

```go
// Create pipeline
pipelineConfig := &pipeline.Config{
    Name: "my-pipeline",
}
pipeline := pipeline.NewSequentialPipeline(pipelineConfig, logger)

// Add agents
pipeline.AddAgent(agent1)
pipeline.AddAgent(agent2)

// Execute pipeline
result, err := pipeline.Execute(context.Background(), inputMessage)
if err != nil {
    log.Fatal(err)
}
```

### 工具使用

```go
// Create tool registry
registry := tool.NewDefaultRegistry()

// Register tools
calcTool := builtin.NewCalculatorTool()
registry.RegisterTool(calcTool)

// Use tool with agent
agent.SetToolRegistry(registry)
```

本 API 参考文档为 AgentScope-Golang 的所有公开接口与组件提供了全面的说明。更多详细示例与教程，请参阅[用户指南](user-guide.md)。