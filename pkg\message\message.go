package message

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"
)

// Message 表示消息
type Message struct {
	ID         string                 `json:"id"`                     // 消息ID
	Role       string                 `json:"role"`                   // 角色：user/assistant/system/tool
	Content    interface{}            `json:"content"`                // 消息内容
	Name       string                 `json:"name,omitempty"`         // 发送者名称
	ToolCalls  []*ToolCall            `json:"tool_calls,omitempty"`   // 工具调用
	ToolCallID string                 `json:"tool_call_id,omitempty"` // 工具调用ID（tool角色使用）
	Metadata   map[string]interface{} `json:"metadata,omitempty"`     // 元数据
	Timestamp  time.Time              `json:"timestamp"`              // 时间戳
}

// ToolCall 表示工具调用
type ToolCall struct {
	ID       string            `json:"id"`       // 工具调用ID
	Type     string            `json:"type"`     // 类型，通常为 "function"
	Function *ToolCallFunction `json:"function"` // 函数调用信息
}

// ToolCallFunction 表示函数调用
type ToolCallFunction struct {
	Name      string `json:"name"`      // 函数名
	Arguments string `json:"arguments"` // 参数（JSON字符串）
}

// NewMessage 创建新消息
func NewMessage(role string, content interface{}) *Message {
	return &Message{
		ID:        generateMessageID(),
		Role:      role,
		Content:   content,
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
	}
}

// NewUserMessage 创建用户消息
func NewUserMessage(content string) *Message {
	return NewMessage("user", content)
}

// NewAssistantMessage 创建助手消息
func NewAssistantMessage(content string) *Message {
	return NewMessage("assistant", content)
}

// NewSystemMessage 创建系统消息
func NewSystemMessage(content string) *Message {
	return NewMessage("system", content)
}

// NewToolMessage 创建工具消息
func NewToolMessage(content string, toolCallID string) *Message {
	msg := NewMessage("tool", content)
	msg.ToolCallID = toolCallID
	return msg
}

// NewAssistantMessageWithToolCalls 创建带工具调用的助手消息
func NewAssistantMessageWithToolCalls(content string, toolCalls []*ToolCall) *Message {
	msg := NewMessage("assistant", content)
	msg.ToolCalls = toolCalls
	return msg
}

// SetName 设置消息发送者名称
func (m *Message) SetName(name string) *Message {
	m.Name = name
	return m
}

// SetMetadata 设置元数据
func (m *Message) SetMetadata(key string, value interface{}) *Message {
	if m.Metadata == nil {
		m.Metadata = make(map[string]interface{})
	}
	m.Metadata[key] = value
	return m
}

// GetMetadata 获取元数据
func (m *Message) GetMetadata(key string) (interface{}, bool) {
	if m.Metadata == nil {
		return nil, false
	}
	value, exists := m.Metadata[key]
	return value, exists
}

// GetContentString 获取字符串内容
func (m *Message) GetContentString() string {
	if m.Content == nil {
		return ""
	}
	if str, ok := m.Content.(string); ok {
		return str
	}
	return ""
}

// HasToolCalls 检查是否包含工具调用
func (m *Message) HasToolCalls() bool {
	return len(m.ToolCalls) > 0
}

// IsUserMessage 检查是否为用户消息
func (m *Message) IsUserMessage() bool {
	return m.Role == "user"
}

// IsAssistantMessage 检查是否为助手消息
func (m *Message) IsAssistantMessage() bool {
	return m.Role == "assistant"
}

// IsSystemMessage 检查是否为系统消息
func (m *Message) IsSystemMessage() bool {
	return m.Role == "system"
}

// IsToolMessage 检查是否为工具消息
func (m *Message) IsToolMessage() bool {
	return m.Role == "tool"
}

// Clone 克隆消息
func (m *Message) Clone() *Message {
	clone := &Message{
		ID:         m.ID,
		Role:       m.Role,
		Content:    m.Content,
		Name:       m.Name,
		ToolCallID: m.ToolCallID,
		Timestamp:  m.Timestamp,
		Metadata:   make(map[string]interface{}),
	}

	// 克隆工具调用
	if len(m.ToolCalls) > 0 {
		clone.ToolCalls = make([]*ToolCall, len(m.ToolCalls))
		for i, tc := range m.ToolCalls {
			clone.ToolCalls[i] = &ToolCall{
				ID:   tc.ID,
				Type: tc.Type,
				Function: &ToolCallFunction{
					Name:      tc.Function.Name,
					Arguments: tc.Function.Arguments,
				},
			}
		}
	}

	// 克隆元数据
	for k, v := range m.Metadata {
		clone.Metadata[k] = v
	}

	return clone
}

// Validate 验证消息的有效性
func (m *Message) Validate() error {
	if m == nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息不能为空",
		}
	}

	if m.Role == "" {
		return &ValidationError{
			Field:   "role",
			Message: "角色不能为空",
		}
	}

	// 验证角色值
	validRoles := map[string]bool{
		"user":      true,
		"assistant": true,
		"system":    true,
		"tool":      true,
	}
	if !validRoles[m.Role] {
		return &ValidationError{
			Field:   "role",
			Message: "无效的角色: " + m.Role,
		}
	}

	// tool 角色必须有 tool_call_id
	if m.Role == "tool" && m.ToolCallID == "" {
		return &ValidationError{
			Field:   "tool_call_id",
			Message: "tool 角色消息必须包含 tool_call_id",
		}
	}

	// 验证工具调用
	for i, tc := range m.ToolCalls {
		if tc == nil {
			return &ValidationError{
				Field:   "tool_calls",
				Message: "工具调用不能为空",
				Index:   &i,
			}
		}
		if tc.ID == "" {
			return &ValidationError{
				Field:   "tool_calls",
				Message: "工具调用ID不能为空",
				Index:   &i,
			}
		}
		if tc.Function == nil {
			return &ValidationError{
				Field:   "tool_calls",
				Message: "工具调用函数不能为空",
				Index:   &i,
			}
		}
		if tc.Function.Name == "" {
			return &ValidationError{
				Field:   "tool_calls",
				Message: "工具调用函数名不能为空",
				Index:   &i,
			}
		}
	}

	return nil
}

// ToJSON 转换为JSON字符串
func (m *Message) ToJSON() (string, error) {
	data, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串创建消息
func FromJSON(jsonStr string) (*Message, error) {
	var msg Message
	err := json.Unmarshal([]byte(jsonStr), &msg)
	if err != nil {
		return nil, err
	}
	return &msg, nil
}

// ValidationError 表示验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Index   *int   `json:"index,omitempty"`
}

func (e *ValidationError) Error() string {
	if e.Index != nil {
		return "验证错误 [" + e.Field + "[" + string(rune(*e.Index)) + "]]: " + e.Message
	}
	return "验证错误 [" + e.Field + "]: " + e.Message
}

var messageCounter int64

// generateMessageID 生成消息ID
func generateMessageID() string {
	// 使用时间戳 + 计数器 + 随机数确保唯一性
	messageCounter++
	timestamp := time.Now().UnixNano()

	// 生成4字节随机数
	randomBytes := make([]byte, 4)
	rand.Read(randomBytes)
	randomHex := hex.EncodeToString(randomBytes)

	return fmt.Sprintf("msg_%d_%d_%s", timestamp, messageCounter, randomHex)
}
