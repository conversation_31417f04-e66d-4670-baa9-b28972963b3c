package deepseek

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"
)

func TestNewDeepSeekClient(t *testing.T) {
	// 保存原始环境变量
	originalAPIKey := os.Getenv("DEEPSEEK_API_KEY")
	defer func() {
		if originalAPIKey != "" {
			os.Setenv("DEEPSEEK_API_KEY", originalAPIKey)
		} else {
			os.Unsetenv("DEEPSEEK_API_KEY")
		}
	}()

	// 测试缺少 API Key
	os.Unsetenv("DEEPSEEK_API_KEY")
	_, err := NewDeepSeekClient()
	if err == nil {
		t.Error("缺少 API Key 应该返回错误")
	}

	// 测试有效 API Key
	os.Setenv("DEEPSEEK_API_KEY", "test-api-key")
	client, err := NewDeepSeekClient()
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}

	if client.apiKey != "test-api-key" {
		t.<PERSON>("API Key 不匹配: got %s, want test-api-key", client.apiKey)
	}

	if client.baseURL != "https://api.deepseek.com/v1" {
		t.Errorf("基础 URL 不匹配: got %s", client.baseURL)
	}

	if client.timeout != 60*time.Second {
		t.Errorf("超时时间不匹配: got %v", client.timeout)
	}

	if client.retries != 3 {
		t.Errorf("重试次数不匹配: got %d", client.retries)
	}
}

func TestNewDeepSeekClientWithConfig(t *testing.T) {
	apiKey := "custom-api-key"
	baseURL := "https://custom.api.com/v1"
	timeout := 30 * time.Second
	retries := 5

	client := NewDeepSeekClientWithConfig(apiKey, baseURL, timeout, retries)

	if client.apiKey != apiKey {
		t.Errorf("API Key 不匹配: got %s, want %s", client.apiKey, apiKey)
	}

	if client.baseURL != baseURL {
		t.Errorf("基础 URL 不匹配: got %s, want %s", client.baseURL, baseURL)
	}

	if client.timeout != timeout {
		t.Errorf("超时时间不匹配: got %v, want %v", client.timeout, timeout)
	}

	if client.retries != retries {
		t.Errorf("重试次数不匹配: got %d, want %d", client.retries, retries)
	}

	// 测试默认基础 URL
	client2 := NewDeepSeekClientWithConfig(apiKey, "", timeout, retries)
	if client2.baseURL != "https://api.deepseek.com/v1" {
		t.Errorf("默认基础 URL 不匹配: got %s", client2.baseURL)
	}
}

func TestDeepSeekClientSetters(t *testing.T) {
	client := NewDeepSeekClientWithConfig("test-key", "", 30*time.Second, 3)

	// 测试设置超时
	newTimeout := 60 * time.Second
	client.SetTimeout(newTimeout)
	if client.timeout != newTimeout {
		t.Errorf("设置超时失败: got %v, want %v", client.timeout, newTimeout)
	}

	// 测试设置重试次数
	newRetries := 5
	client.SetRetries(newRetries)
	if client.retries != newRetries {
		t.Errorf("设置重试次数失败: got %d, want %d", client.retries, newRetries)
	}

	// 测试设置基础 URL
	newBaseURL := "https://new.api.com/v1"
	client.SetBaseURL(newBaseURL)
	if client.baseURL != newBaseURL {
		t.Errorf("设置基础 URL 失败: got %s, want %s", client.baseURL, newBaseURL)
	}

	// 测试设置 API Key
	newAPIKey := "new-api-key"
	client.SetAPIKey(newAPIKey)
	if client.GetAPIKey() != newAPIKey {
		t.Errorf("设置 API Key 失败: got %s, want %s", client.GetAPIKey(), newAPIKey)
	}

	// 测试设置 HTTP 客户端
	newClient := &http.Client{Timeout: 10 * time.Second}
	client.SetHTTPClient(newClient)
	if client.GetHTTPClient() != newClient {
		t.Error("设置 HTTP 客户端失败")
	}
}

func TestDeepSeekClientChatWithMockServer(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			t.Errorf("期望 POST 请求，得到 %s", r.Method)
		}

		if r.URL.Path != "/chat/completions" {
			t.Errorf("期望路径 /chat/completions，得到 %s", r.URL.Path)
		}

		// 检查请求头
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望 Content-Type application/json，得到 %s", r.Header.Get("Content-Type"))
		}

		authHeader := r.Header.Get("Authorization")
		if !strings.HasPrefix(authHeader, "Bearer ") {
			t.Errorf("期望 Bearer 认证，得到 %s", authHeader)
		}

		// 模拟成功响应
		response := ChatResponse{
			ID:      "chatcmpl-test",
			Object:  "chat.completion",
			Created: time.Now().Unix(),
			Model:   "deepseek-chat",
			Choices: []struct {
				Index   int `json:"index"`
				Message struct {
					Role      string     `json:"role"`
					Content   string     `json:"content"`
					ToolCalls []ToolCall `json:"tool_calls,omitempty"`
				} `json:"message"`
				FinishReason string `json:"finish_reason"`
			}{
				{
					Index: 0,
					Message: struct {
						Role      string     `json:"role"`
						Content   string     `json:"content"`
						ToolCalls []ToolCall `json:"tool_calls,omitempty"`
					}{
						Role:    "assistant",
						Content: "这是一个测试响应",
					},
					FinishReason: "stop",
				},
			},
			Usage: struct {
				PromptTokens     int `json:"prompt_tokens"`
				CompletionTokens int `json:"completion_tokens"`
				TotalTokens      int `json:"total_tokens"`
			}{
				PromptTokens:     10,
				CompletionTokens: 5,
				TotalTokens:      15,
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建客户端
	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 30*time.Second, 0)

	// 发送聊天请求
	req := &ChatRequest{
		Model: "deepseek-chat",
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	resp, err := client.Chat(context.Background(), req)
	if err != nil {
		t.Fatalf("聊天请求失败: %v", err)
	}

	if resp.ID != "chatcmpl-test" {
		t.Errorf("响应 ID 不匹配: got %s", resp.ID)
	}

	if len(resp.Choices) != 1 {
		t.Errorf("期望 1 个选择，得到 %d", len(resp.Choices))
	}

	if resp.Choices[0].Message.Content != "这是一个测试响应" {
		t.Errorf("响应内容不匹配: got %s", resp.Choices[0].Message.Content)
	}
}

func TestDeepSeekClientChatError(t *testing.T) {
	// 创建返回错误的模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
		errorResp := ErrorResponse{
			Error: struct {
				Message string `json:"message"`
				Type    string `json:"type"`
				Code    string `json:"code"`
			}{
				Message: "Invalid request",
				Type:    "invalid_request_error",
				Code:    "400",
			},
		}
		json.NewEncoder(w).Encode(errorResp)
	}))
	defer server.Close()

	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 30*time.Second, 0)

	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	_, err := client.Chat(context.Background(), req)
	if err == nil {
		t.Error("期望返回错误")
	}

	if !strings.Contains(err.Error(), "Invalid request") {
		t.Errorf("错误信息不匹配: %v", err)
	}
}

func TestDeepSeekClientChatTimeout(t *testing.T) {
	// 创建延迟响应的模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 500*time.Millisecond, 0)

	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	_, err := client.Chat(context.Background(), req)
	if err == nil {
		t.Error("期望超时错误")
	}
}

func TestDeepSeekClientChatDefaultModel(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 读取请求体检查模型
		var reqBody ChatRequest
		json.NewDecoder(r.Body).Decode(&reqBody)

		if reqBody.Model != "deepseek-chat" {
			t.Errorf("期望默认模型 deepseek-chat，得到 %s", reqBody.Model)
		}

		response := ChatResponse{
			ID:    "test",
			Model: "deepseek-chat",
			Choices: []struct {
				Index   int `json:"index"`
				Message struct {
					Role      string     `json:"role"`
					Content   string     `json:"content"`
					ToolCalls []ToolCall `json:"tool_calls,omitempty"`
				} `json:"message"`
				FinishReason string `json:"finish_reason"`
			}{
				{
					Message: struct {
						Role      string     `json:"role"`
						Content   string     `json:"content"`
						ToolCalls []ToolCall `json:"tool_calls,omitempty"`
					}{
						Role:    "assistant",
						Content: "test",
					},
				},
			},
		}

		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 30*time.Second, 0)

	// 不设置模型，应该使用默认模型
	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	_, err := client.Chat(context.Background(), req)
	if err != nil {
		t.Fatalf("聊天请求失败: %v", err)
	}
}

// 集成测试（需要真实 API Key）
func TestDeepSeekClientIntegration(t *testing.T) {
	// 检查是否启用集成测试
	if os.Getenv("ENABLE_INTEGRATION_TESTS") != "true" {
		t.Skip("跳过集成测试，设置 ENABLE_INTEGRATION_TESTS=true 启用")
	}

	// 检查 API Key
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("跳过集成测试，需要设置 DEEPSEEK_API_KEY 环境变量")
	}

	client, err := NewDeepSeekClient()
	if err != nil {
		t.Fatalf("创建客户端失败: %v", err)
	}

	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好，请简单回复"},
		},
		MaxTokens: func() *int { v := 50; return &v }(),
	}

	resp, err := client.Chat(context.Background(), req)
	if err != nil {
		t.Fatalf("集成测试失败: %v", err)
	}

	if len(resp.Choices) == 0 {
		t.Error("响应应该包含至少一个选择")
	}

	if resp.Choices[0].Message.Content == "" {
		t.Error("响应内容不应该为空")
	}

	t.Logf("集成测试响应: %s", resp.Choices[0].Message.Content)
}

func TestDeepSeekClientChatStreamWithMockServer(t *testing.T) {
	// 创建模拟 SSE 服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查流式请求
		var reqBody ChatRequest
		json.NewDecoder(r.Body).Decode(&reqBody)

		if !reqBody.Stream {
			t.Error("期望流式请求")
		}

		// 设置 SSE 响应头
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")

		// 发送流式数据
		flusher, ok := w.(http.Flusher)
		if !ok {
			t.Fatal("无法获取 flusher")
		}

		// 第一个数据块
		delta1 := ChatDelta{
			ID:      "chatcmpl-stream-test",
			Object:  "chat.completion.chunk",
			Created: time.Now().Unix(),
			Model:   "deepseek-chat",
			Choices: []struct {
				Index int `json:"index"`
				Delta struct {
					Role      string     `json:"role,omitempty"`
					Content   string     `json:"content,omitempty"`
					ToolCalls []ToolCall `json:"tool_calls,omitempty"`
				} `json:"delta"`
				FinishReason *string `json:"finish_reason"`
			}{
				{
					Index: 0,
					Delta: struct {
						Role      string     `json:"role,omitempty"`
						Content   string     `json:"content,omitempty"`
						ToolCalls []ToolCall `json:"tool_calls,omitempty"`
					}{
						Role:    "assistant",
						Content: "你好",
					},
				},
			},
		}

		data1, _ := json.Marshal(delta1)
		w.Write([]byte("data: " + string(data1) + "\n\n"))
		flusher.Flush()

		// 第二个数据块
		delta2 := ChatDelta{
			ID:      "chatcmpl-stream-test",
			Object:  "chat.completion.chunk",
			Created: time.Now().Unix(),
			Model:   "deepseek-chat",
			Choices: []struct {
				Index int `json:"index"`
				Delta struct {
					Role      string     `json:"role,omitempty"`
					Content   string     `json:"content,omitempty"`
					ToolCalls []ToolCall `json:"tool_calls,omitempty"`
				} `json:"delta"`
				FinishReason *string `json:"finish_reason"`
			}{
				{
					Index: 0,
					Delta: struct {
						Role      string     `json:"role,omitempty"`
						Content   string     `json:"content,omitempty"`
						ToolCalls []ToolCall `json:"tool_calls,omitempty"`
					}{
						Content: "，世界！",
					},
				},
			},
		}

		data2, _ := json.Marshal(delta2)
		w.Write([]byte("data: " + string(data2) + "\n\n"))
		flusher.Flush()

		// 结束标记
		w.Write([]byte("data: [DONE]\n\n"))
		flusher.Flush()
	}))
	defer server.Close()

	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 30*time.Second, 0)

	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	deltaChan, err := client.ChatStream(context.Background(), req)
	if err != nil {
		t.Fatalf("流式聊天请求失败: %v", err)
	}

	var deltas []*ChatDelta
	for delta := range deltaChan {
		deltas = append(deltas, delta)
	}

	if len(deltas) != 2 {
		t.Errorf("期望 2 个增量，得到 %d", len(deltas))
	}

	if len(deltas) > 0 && deltas[0].Choices[0].Delta.Content != "你好" {
		t.Errorf("第一个增量内容不匹配: got %s", deltas[0].Choices[0].Delta.Content)
	}

	if len(deltas) > 1 && deltas[1].Choices[0].Delta.Content != "，世界！" {
		t.Errorf("第二个增量内容不匹配: got %s", deltas[1].Choices[0].Delta.Content)
	}
}

func TestDeepSeekClientChatStreamError(t *testing.T) {
	// 创建返回错误的模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusUnauthorized)
		errorResp := ErrorResponse{
			Error: struct {
				Message string `json:"message"`
				Type    string `json:"type"`
				Code    string `json:"code"`
			}{
				Message: "Unauthorized",
				Type:    "authentication_error",
				Code:    "401",
			},
		}
		json.NewEncoder(w).Encode(errorResp)
	}))
	defer server.Close()

	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 30*time.Second, 0)

	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	_, err := client.ChatStream(context.Background(), req)
	if err == nil {
		t.Error("期望返回错误")
	}

	if !strings.Contains(err.Error(), "Unauthorized") {
		t.Errorf("错误信息不匹配: %v", err)
	}
}

func TestDeepSeekClientChatStreamCancel(t *testing.T) {
	// 创建长时间运行的模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/event-stream")

		flusher, _ := w.(http.Flusher)

		// 发送一个数据块然后等待
		delta := ChatDelta{
			ID:    "test",
			Model: "deepseek-chat",
			Choices: []struct {
				Index int `json:"index"`
				Delta struct {
					Role      string     `json:"role,omitempty"`
					Content   string     `json:"content,omitempty"`
					ToolCalls []ToolCall `json:"tool_calls,omitempty"`
				} `json:"delta"`
				FinishReason *string `json:"finish_reason"`
			}{
				{
					Delta: struct {
						Role      string     `json:"role,omitempty"`
						Content   string     `json:"content,omitempty"`
						ToolCalls []ToolCall `json:"tool_calls,omitempty"`
					}{
						Content: "test",
					},
				},
			},
		}

		data, _ := json.Marshal(delta)
		w.Write([]byte("data: " + string(data) + "\n\n"))
		flusher.Flush()

		// 等待很长时间
		time.Sleep(5 * time.Second)
	}))
	defer server.Close()

	client := NewDeepSeekClientWithConfig("test-api-key", server.URL, 30*time.Second, 0)

	req := &ChatRequest{
		Messages: []ChatMessage{
			{Role: "user", Content: "你好"},
		},
	}

	ctx, cancel := context.WithCancel(context.Background())

	deltaChan, err := client.ChatStream(ctx, req)
	if err != nil {
		t.Fatalf("流式聊天请求失败: %v", err)
	}

	// 读取第一个增量
	select {
	case delta := <-deltaChan:
		if delta.Choices[0].Delta.Content != "test" {
			t.Errorf("增量内容不匹配: got %s", delta.Choices[0].Delta.Content)
		}
	case <-time.After(2 * time.Second):
		t.Fatal("超时等待第一个增量")
	}

	// 取消上下文
	cancel()

	// 验证通道被关闭
	select {
	case _, ok := <-deltaChan:
		if ok {
			t.Error("通道应该被关闭")
		}
	case <-time.After(1 * time.Second):
		t.Error("通道应该在取消后快速关闭")
	}
}
