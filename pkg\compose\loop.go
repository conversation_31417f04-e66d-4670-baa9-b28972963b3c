package compose

import (
	"context"
	"fmt"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// StopCondition 停止条件函数类型
// 接收当前迭代次数、智能体输出和所有历史输出，返回是否应该停止
type StopCondition func(iteration int, output any, history []any) bool

// LoopConfig 循环组合配置
type LoopConfig struct {
	Name          string        // 组合名称
	Description   string        // 组合描述
	Agent         agent.Agent   // 要循环执行的智能体
	MaxIterations int           // 最大迭代次数
	StopCondition StopCondition // 停止条件函数（可选）
}

// LoopAgent 循环组合智能体
type LoopAgent struct {
	*agent.BaseAgent
	agent         agent.Agent
	maxIterations int
	stopCondition StopCondition
}

// NewLoopAgent 创建循环组合智能体
func NewLoopAgent(config *LoopConfig) (*LoopAgent, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if config.Agent == nil {
		return nil, fmt.Errorf("智能体不能为空")
	}

	if config.MaxIterations <= 0 {
		config.MaxIterations = 10 // 默认最大迭代10次
	}

	if config.Name == "" {
		config.Name = "LoopAgent"
	}

	if config.Description == "" {
		config.Description = "循环执行智能体的组合智能体"
	}

	return &LoopAgent{
		BaseAgent:     agent.NewBaseAgent(config.Name, config.Description),
		agent:         config.Agent,
		maxIterations: config.MaxIterations,
		stopCondition: config.StopCondition,
	}, nil
}

// Run 运行循环组合智能体
func (l *LoopAgent) Run(ctx context.Context, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 50)

	go func() {
		defer pair.Generator.Close()
		emit := func(ev *event.Event) {
			if err := pair.Generator.Send(ev); err != nil {
				fmt.Printf("发送事件失败: %v\n", err)
			}
		}

		defer func() {
			if r := recover(); r != nil {
				emit(event.NewErrorEvent(fmt.Errorf("循环组合执行发生panic: %v", r), "PANIC", nil))
			}
		}()

		if err := l.validateInput(in); err != nil {
			emit(event.NewErrorEvent(err, "VALIDATION_ERROR", nil))
			return
		}

		// 发送开始事件
		emit(event.NewTransferEvent(l.Name(ctx), "Loop", fmt.Sprintf("开始循环执行，最大迭代次数: %d", l.maxIterations)))

		// 执行循环
		l.executeLoop(ctx, in, emit)
	}()

	return pair.Iterator
}

// executeLoop 执行循环逻辑
func (l *LoopAgent) executeLoop(ctx context.Context, in *agent.Input, emit func(*event.Event)) {
	var history []any
	currentInput := in
	agentName := l.agent.Name(ctx)

	for iteration := 1; iteration <= l.maxIterations; iteration++ {
		// 发送迭代开始事件
		emit(event.NewTransferEvent(l.Name(ctx), agentName, fmt.Sprintf("开始第 %d/%d 次迭代", iteration, l.maxIterations)))

		// 执行智能体
		output, success := l.executeIteration(ctx, currentInput, agentName, iteration, emit)

		if !success {
			// 智能体执行失败，停止循环
			emit(event.NewErrorEvent(fmt.Errorf("第 %d 次迭代执行失败", iteration), "ITERATION_FAILED", map[string]any{
				"iteration":      iteration,
				"max_iterations": l.maxIterations,
			}))
			return
		}

		// 记录输出到历史
		history = append(history, output)

		// 发送迭代完成事件
		emit(event.NewTransferEvent(agentName, l.Name(ctx), fmt.Sprintf("第 %d/%d 次迭代完成", iteration, l.maxIterations)))

		// 检查停止条件
		if l.stopCondition != nil && l.stopCondition(iteration, output, history) {
			emit(event.NewTransferEvent(l.Name(ctx), "StopCondition", fmt.Sprintf("满足停止条件，在第 %d 次迭代后停止", iteration)))
			break
		}

		// 检查是否达到最大迭代次数
		if iteration >= l.maxIterations {
			emit(event.NewTransferEvent(l.Name(ctx), "MaxIterations", fmt.Sprintf("达到最大迭代次数 %d，停止执行", l.maxIterations)))
			break
		}

		// 为下一次迭代准备输入
		// 这里可以根据需要修改输入，比如基于前一次的输出
		// 目前简化处理，保持原始输入
		currentInput = l.prepareNextInput(in, output, history)
	}

	// 发送最终完成事件
	finalContent := fmt.Sprintf("循环执行完成，共执行 %d 次迭代", len(history))
	emit(event.NewFinalEvent(finalContent, "循环组合执行完成", map[string]any{
		"total_iterations": len(history),
		"max_iterations":   l.maxIterations,
		"history":          history,
	}))
}

// executeIteration 执行单次迭代
func (l *LoopAgent) executeIteration(ctx context.Context, input *agent.Input, agentName string, iteration int, emit func(*event.Event)) (any, bool) {
	// 运行智能体
	iterator := l.agent.Run(ctx, input)

	var finalEvent *event.Event
	var errorEvent *event.Event

	// 收集智能体的所有事件
	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}

		// 转发事件，添加迭代信息
		forwardedEvent := l.wrapEvent(ev, agentName, iteration)
		emit(forwardedEvent)

		// 记录关键事件
		if ev.Type == event.EventFinal {
			finalEvent = ev
		} else if ev.Type == event.EventError {
			errorEvent = ev
		}
	}

	// 检查执行结果
	if errorEvent != nil {
		return nil, false
	} else if finalEvent != nil {
		return finalEvent.Data, true
	} else {
		// 没有最终事件也没有错误事件，认为是异常情况
		return nil, false
	}
}

// prepareNextInput 为下一次迭代准备输入
func (l *LoopAgent) prepareNextInput(originalInput *agent.Input, lastOutput any, history []any) *agent.Input {
	// 简化实现：返回原始输入
	// 在实际应用中，可能需要根据历史输出来修改输入
	return originalInput
}

// validateInput 验证输入
func (l *LoopAgent) validateInput(in *agent.Input) error {
	if in == nil {
		return fmt.Errorf("输入不能为空")
	}
	return nil
}

// wrapEvent 包装事件，添加迭代信息
func (l *LoopAgent) wrapEvent(originalEvent *event.Event, agentName string, iteration int) *event.Event {
	// 简化实现：直接转发原始事件，在元数据中添加迭代信息
	wrappedEvent := &event.Event{
		Type: originalEvent.Type,
		Data: originalEvent.Data,
		Err:  originalEvent.Err,
		At:   originalEvent.At,
		Metadata: map[string]any{
			"source_agent": agentName,
			"iteration":    iteration,
		},
	}

	// 如果原始事件有元数据，合并它们
	if originalEvent.Metadata != nil {
		for k, v := range originalEvent.Metadata {
			wrappedEvent.Metadata[k] = v
		}
	}

	return wrappedEvent
}

// GetAgent 获取循环执行的智能体
func (l *LoopAgent) GetAgent() agent.Agent {
	return l.agent
}

// GetMaxIterations 获取最大迭代次数
func (l *LoopAgent) GetMaxIterations() int {
	return l.maxIterations
}

// GetStopCondition 获取停止条件函数
func (l *LoopAgent) GetStopCondition() StopCondition {
	return l.stopCondition
}

// 预定义的停止条件函数

// StopOnOutputContains 当输出包含指定字符串时停止
func StopOnOutputContains(target string) StopCondition {
	return func(iteration int, output any, history []any) bool {
		if output == nil {
			return false
		}
		outputStr := fmt.Sprintf("%v", output)
		return fmt.Sprintf("%v", outputStr) != "" && fmt.Sprintf("%v", outputStr) == target
	}
}

// StopOnMaxOutputLength 当输出长度超过指定值时停止
func StopOnMaxOutputLength(maxLength int) StopCondition {
	return func(iteration int, output any, history []any) bool {
		if output == nil {
			return false
		}
		outputStr := fmt.Sprintf("%v", output)
		return len(outputStr) > maxLength
	}
}

// StopOnConsecutiveIdenticalOutputs 当连续N次输出相同时停止
func StopOnConsecutiveIdenticalOutputs(count int) StopCondition {
	return func(iteration int, output any, history []any) bool {
		if len(history) < count {
			return false
		}

		// 检查最后count个输出是否相同
		lastOutputs := history[len(history)-count:]
		firstOutput := fmt.Sprintf("%v", lastOutputs[0])

		for i := 1; i < len(lastOutputs); i++ {
			if fmt.Sprintf("%v", lastOutputs[i]) != firstOutput {
				return false
			}
		}

		return true
	}
}
