package web

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

// TestCompressionConfig 测试压缩配置
func TestCompressionConfig(t *testing.T) {
	config := DefaultCompressionConfig

	if config.MinSize <= 0 {
		t.Error("MinSize should be positive")
	}

	if config.Level < gzip.DefaultCompression || config.Level > gzip.BestCompression {
		t.Error("Level should be valid gzip compression level")
	}

	if len(config.CompressibleTypes) == 0 {
		t.Error("CompressibleTypes should not be empty")
	}
}

// TestCompressionMiddleware 测试压缩中间件
func TestCompressionMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		acceptEncoding string
		contentType    string
		content        string
		expectGzip     bool
		description    string
	}{
		{
			name:           "大JSON内容应该压缩",
			acceptEncoding: "gzip, deflate",
			contentType:    "application/json",
			content:        strings.Repeat(`{"key":"value","data":"test"}`, 100), // 大于1KB
			expectGzip:     true,
			description:    "大于阈值的JSON内容应该被压缩",
		},
		{
			name:           "小JSON内容不应该压缩",
			acceptEncoding: "gzip, deflate",
			contentType:    "application/json",
			content:        `{"key":"value"}`, // 小于1KB
			expectGzip:     false,
			description:    "小于阈值的内容不应该被压缩",
		},
		{
			name:           "不支持gzip的客户端",
			acceptEncoding: "deflate",
			contentType:    "application/json",
			content:        strings.Repeat(`{"key":"value"}`, 100),
			expectGzip:     false,
			description:    "客户端不支持gzip时不应该压缩",
		},
		{
			name:           "不可压缩的内容类型",
			acceptEncoding: "gzip, deflate",
			contentType:    "image/jpeg",
			content:        strings.Repeat("binary data", 100),
			expectGzip:     false,
			description:    "不可压缩的内容类型不应该被压缩",
		},
		{
			name:           "大文本内容应该压缩",
			acceptEncoding: "gzip, deflate",
			contentType:    "text/html",
			content:        strings.Repeat("<p>This is a test paragraph.</p>", 100),
			expectGzip:     true,
			description:    "大文本内容应该被压缩",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试处理器
			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", tt.contentType)
				w.Write([]byte(tt.content))
			})

			// 应用压缩中间件
			middleware := CompressionMiddleware()
			wrappedHandler := middleware(handler)

			// 创建测试请求
			req := httptest.NewRequest("GET", "/test", nil)
			req.Header.Set("Accept-Encoding", tt.acceptEncoding)

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 执行请求
			wrappedHandler.ServeHTTP(w, req)

			// 检查结果
			resp := w.Result()
			defer resp.Body.Close()

			// 检查是否设置了gzip编码
			contentEncoding := resp.Header.Get("Content-Encoding")
			hasGzip := contentEncoding == "gzip"

			if hasGzip != tt.expectGzip {
				t.Errorf("%s: expected gzip=%v, got gzip=%v (Content-Type: %s)",
					tt.description, tt.expectGzip, hasGzip, resp.Header.Get("Content-Type"))
			}

			// 如果期望压缩，验证内容可以正确解压
			if tt.expectGzip && hasGzip {
				body, err := io.ReadAll(resp.Body)
				if err != nil {
					t.Fatalf("Failed to read response body: %v", err)
				}

				// 解压内容
				gzReader, err := gzip.NewReader(bytes.NewReader(body))
				if err != nil {
					t.Fatalf("Failed to create gzip reader: %v", err)
				}
				defer gzReader.Close()

				decompressed, err := io.ReadAll(gzReader)
				if err != nil {
					t.Fatalf("Failed to decompress content: %v", err)
				}

				// 验证解压后的内容
				if string(decompressed) != tt.content {
					t.Error("Decompressed content does not match original")
				}

				// 验证压缩效果
				originalSize := len(tt.content)
				compressedSize := len(body)
				if compressedSize >= originalSize {
					t.Errorf("Compression not effective: original=%d, compressed=%d", originalSize, compressedSize)
				}

				t.Logf("压缩效果: %d bytes -> %d bytes (%.1f%% reduction)",
					originalSize, compressedSize, float64(originalSize-compressedSize)/float64(originalSize)*100)
			}
		})
	}
}

// TestCompressionThreshold 测试压缩阈值
func TestCompressionThreshold(t *testing.T) {
	config := &CompressionConfig{
		MinSize:           500, // 500字节阈值
		Level:             gzip.DefaultCompression,
		CompressibleTypes: []string{"text/", "application/json"},
	}

	middleware := CompressionMiddlewareWithConfig(config)

	tests := []struct {
		size       int
		expectGzip bool
	}{
		{400, false}, // 小于阈值
		{500, true},  // 等于阈值
		{600, true},  // 大于阈值
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("size_%d", tt.size), func(t *testing.T) {
			content := strings.Repeat("a", tt.size)

			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "text/plain")
				w.Write([]byte(content))
			})

			wrappedHandler := middleware(handler)

			req := httptest.NewRequest("GET", "/test", nil)
			req.Header.Set("Accept-Encoding", "gzip")

			w := httptest.NewRecorder()
			wrappedHandler.ServeHTTP(w, req)

			resp := w.Result()
			defer resp.Body.Close()

			hasGzip := resp.Header.Get("Content-Encoding") == "gzip"
			if hasGzip != tt.expectGzip {
				t.Errorf("Size %d: expected gzip=%v, got gzip=%v", tt.size, tt.expectGzip, hasGzip)
			}
		})
	}
}

// TestCompressionEffectiveness 测试压缩效果
func TestCompressionEffectiveness(t *testing.T) {
	// 创建高度重复的内容（容易压缩）
	repetitiveContent := strings.Repeat("This is a highly repetitive content that should compress very well. ", 100)

	// 创建随机内容（难以压缩）
	randomContent := "x1y2z3a4b5c6d7e8f9g0h1i2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0"
	randomContent = strings.Repeat(randomContent, 20)

	tests := []struct {
		name              string
		content           string
		expectedReduction float64 // 期望的压缩率（百分比）
	}{
		{
			name:              "高重复内容",
			content:           repetitiveContent,
			expectedReduction: 80.0, // 期望至少80%的压缩率
		},
		{
			name:              "随机内容",
			content:           randomContent,
			expectedReduction: 10.0, // 期望至少10%的压缩率
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "text/plain")
				w.Write([]byte(tt.content))
			})

			middleware := CompressionMiddleware()
			wrappedHandler := middleware(handler)

			req := httptest.NewRequest("GET", "/test", nil)
			req.Header.Set("Accept-Encoding", "gzip")

			w := httptest.NewRecorder()
			wrappedHandler.ServeHTTP(w, req)

			resp := w.Result()
			defer resp.Body.Close()

			if resp.Header.Get("Content-Encoding") != "gzip" {
				t.Fatal("Expected gzip compression")
			}

			body, err := io.ReadAll(resp.Body)
			if err != nil {
				t.Fatalf("Failed to read response body: %v", err)
			}

			originalSize := len(tt.content)
			compressedSize := len(body)
			reductionPercent := float64(originalSize-compressedSize) / float64(originalSize) * 100

			t.Logf("%s: %d bytes -> %d bytes (%.1f%% reduction)",
				tt.name, originalSize, compressedSize, reductionPercent)

			if reductionPercent < tt.expectedReduction {
				t.Errorf("Compression not effective enough: got %.1f%%, expected at least %.1f%%",
					reductionPercent, tt.expectedReduction)
			}
		})
	}
}

// BenchmarkCompressionMiddleware 基准测试压缩中间件
func BenchmarkCompressionMiddleware(b *testing.B) {
	content := strings.Repeat(`{"key":"value","data":"test content"}`, 100)

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(content))
	})

	middleware := CompressionMiddleware()
	wrappedHandler := middleware(handler)

	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Accept-Encoding", "gzip")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(w, req)
	}
}
