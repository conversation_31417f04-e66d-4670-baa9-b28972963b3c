package session

import (
	"context"
	"sync"
)

// Map 表示并发安全的会话存储接口，用于在 Agent 执行过程中共享数据
type Map interface {
	// Set 设置键值对
	Set(key string, val any)
	// Get 获取指定键的值，返回值和是否存在的标志
	Get(key string) (any, bool)
	// Delete 删除指定键
	Delete(key string)
	// Keys 返回所有键的列表
	Keys() []string
	// Clear 清空所有数据
	Clear()
	// Size 返回存储的键值对数量
	Size() int
	// Clone 创建当前会话的副本
	Clone() Map
}

// concurrentMap 是 Map 接口的并发安全实现
type concurrentMap struct {
	data map[string]any
	mu   sync.RWMutex
}

// New 创建一个新的并发安全会话存储
func New() Map {
	return &concurrentMap{
		data: make(map[string]any),
	}
}

// Set 设置键值对
func (cm *concurrentMap) Set(key string, val any) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.data[key] = val
}

// Get 获取指定键的值
func (cm *concurrentMap) Get(key string) (any, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	val, exists := cm.data[key]
	return val, exists
}

// Delete 删除指定键
func (cm *concurrentMap) Delete(key string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	delete(cm.data, key)
}

// Keys 返回所有键的列表
func (cm *concurrentMap) Keys() []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	keys := make([]string, 0, len(cm.data))
	for key := range cm.data {
		keys = append(keys, key)
	}
	return keys
}

// Clear 清空所有数据
func (cm *concurrentMap) Clear() {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.data = make(map[string]any)
}

// Size 返回存储的键值对数量
func (cm *concurrentMap) Size() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return len(cm.data)
}

// Clone 创建当前会话的副本
func (cm *concurrentMap) Clone() Map {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	clone := &concurrentMap{
		data: make(map[string]any, len(cm.data)),
	}

	for key, val := range cm.data {
		clone.data[key] = val
	}

	return clone
}

// contextKey 用于在 context 中存储会话的键类型
type contextKey string

const sessionContextKey contextKey = "session"

// WithSession 将会话存储添加到 context 中
func WithSession(ctx context.Context, session Map) context.Context {
	return context.WithValue(ctx, sessionContextKey, session)
}

// FromContext 从 context 中获取会话存储
func FromContext(ctx context.Context) (Map, bool) {
	session, ok := ctx.Value(sessionContextKey).(Map)
	return session, ok
}

// GetFromContext 从 context 中获取会话存储，如果不存在则创建新的
func GetFromContext(ctx context.Context) Map {
	if session, ok := FromContext(ctx); ok {
		return session
	}
	return New()
}

// SetValue 在 context 的会话中设置值
func SetValue(ctx context.Context, key string, val any) {
	if session, ok := FromContext(ctx); ok {
		session.Set(key, val)
	}
}

// GetValue 从 context 的会话中获取值
func GetValue(ctx context.Context, key string) (any, bool) {
	if session, ok := FromContext(ctx); ok {
		return session.Get(key)
	}
	return nil, false
}

// DeleteValue 从 context 的会话中删除值
func DeleteValue(ctx context.Context, key string) {
	if session, ok := FromContext(ctx); ok {
		session.Delete(key)
	}
}

// readOnlyMap 是只读会话存储的实现，用于防止误写
type readOnlyMap struct {
	underlying Map
}

// NewReadOnly 创建一个只读的会话存储视图
func NewReadOnly(underlying Map) Map {
	return &readOnlyMap{underlying: underlying}
}

// Set 只读模式下不允许设置值
func (rom *readOnlyMap) Set(key string, val any) {
	// 只读模式下忽略设置操作，不抛出错误以保持接口一致性
}

// Get 获取指定键的值
func (rom *readOnlyMap) Get(key string) (any, bool) {
	return rom.underlying.Get(key)
}

// Delete 只读模式下不允许删除
func (rom *readOnlyMap) Delete(key string) {
	// 只读模式下忽略删除操作
}

// Keys 返回所有键的列表
func (rom *readOnlyMap) Keys() []string {
	return rom.underlying.Keys()
}

// Clear 只读模式下不允许清空
func (rom *readOnlyMap) Clear() {
	// 只读模式下忽略清空操作
}

// Size 返回存储的键值对数量
func (rom *readOnlyMap) Size() int {
	return rom.underlying.Size()
}

// Clone 创建底层会话的副本
func (rom *readOnlyMap) Clone() Map {
	return rom.underlying.Clone()
}
