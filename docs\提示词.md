```
基于我们之前的架构分析和优化建议，请按照以下要求实施代码改进：

**修正要求：**
1. 所有文档和代码注释必须使用中文（不是英文）
2. 在 `.augment/rules/project.md` 开发规范文档中增加"文档即契约"的纪律要求
3. 项目处于早期开发阶段，不需要添加接口演进纪律、迁移说明等复杂的版本管理要求到文档中

**实施顺序：**
严格按照 P0 → P1 → P2 的优先级顺序执行所有优化任务：

- **P0（立即修复）**：修复构建失败问题，包括日志API误用、非法字符、缺失import、类型不一致等
- **P1（安全与持久化）**：实现Web安全中间件、真实数据库后端等
- **P2（观测与流程）**：引入监控、测试覆盖率、文档对齐等

**执行要求：**
- 除了上述3个修正要求外，其他所有建议都按原计划执行
- 项目处于早期开发阶段，无需过度严谨的确认流程
- 持续工作直到所有优化任务完成，中间不要停下来要求确认
- 不需要进行阶段性小结，专注于任务执行
- 确保每个修改都符合项目的开发纪律要求（禁止简化实现、使用真实API等）
```

```
请按照以下优先级顺序实施三个主要任务：

**P1 - 记忆系统 SQLite 支持与默认配置调整**
1. 在 `pkg/memory/` 包中新增 SQLite 持久化后端实现
   - 创建 `persistence_sqlite.go` 文件，实现 `MemoryPersistence` 接口
   - 使用 `github.com/mattn/go-sqlite3` 或 `modernc.org/sqlite` 驱动
   - 支持自动建表、事务操作、错误处理
   - 默认数据库文件路径：`./data/memory.db`
2. 修改 `pkg/memory/factory.go`：
   - 新增 `PersistDriver: "sqlite"` 支持
   - 将默认配置从当前方式改为 SQLite 持久化
   - 保持向后兼容性，支持通过配置显式选择 file/postgres/sqlite
3. 更新相关配置示例和测试用例

**P2 - 文档完整中文化**
1. 将 `docs/knowledge-base.md` 中的英文部分完全翻译为中文
2. 将 `docs/api-reference.md` 和 `docs/deployment-guide.md` 完整中文化
3. 确保所有技术术语在文档中保持一致性
4. 更新 README.md 为中文版本（如果存在英文版本）
5. 所有代码注释和错误信息保持中文（符合项目开发纪律）

**P3 - 数据库迁移管理系统**
1. 基于 `github.com/golang-migrate/migrate/v4` 实现迁移管理
2. 创建 `pkg/migration/` 包，包含：
   - 迁移管理器接口和实现
   - 支持 PostgreSQL 和 SQLite 的迁移脚本
   - 版本控制和回滚功能
3. 在 `migrations/` 目录下创建迁移脚本：
   - `001_initial_memory_tables.up.sql` / `001_initial_memory_tables.down.sql`
   - `002_initial_knowledge_tables.up.sql` / `002_initial_knowledge_tables.down.sql`
4. 集成到现有的工厂模式中，在数据库连接时自动执行迁移
5. 提供 CLI 工具或配置选项来手动管理迁移

**执行要求：**
- 严格遵循项目的"禁止简化实现"纪律，所有功能必须使用真实的数据库操作
- 所有新增代码的注释和文档必须使用中文
- 保持现有 PostgreSQL 和文件持久化功能的完整性
- 确保所有修改通过 `go test ./...` 测试
- 按 P1→P2→P3 顺序执行，无需阶段性确认
```

```
我希望扩展 AgentScope-Golang 框架的 LLM 支持，添加对 Qwen（通义千问）和豆包（字节跳动）大模型的支持。请为我设计一个详细的优化方案，具体要求如下：

**架构设计要求：**
1. **接口与实现分离**：设计清晰的抽象接口，将 LLM 客户端的接口定义与具体实现完全分离
2. **可扩展性**：架构应支持未来轻松添加其他服务商的 AI 大模型，包括但不限于：
   - OpenAI（GPT 系列）
   - Anthropic Claude 系列
   - Google Gemini
   - 百度文心一言
   - 其他自定义或私有部署的模型

**技术实现要求：**
1. **统一接口设计**：定义标准的 LLM 客户端接口，包括消息发送、流式响应、参数配置等
2. **配置管理**：支持在配置文件中灵活配置不同的 LLM 提供商和模型参数
3. **错误处理**：统一的错误处理机制，包括网络错误、API 限制、认证失败等场景
4. **工厂模式**：使用工厂模式来创建不同类型的 LLM 客户端实例
5. **向后兼容**：确保现有的 DeepSeek 集成不受影响

**具体输出要求：**
1. **接口定义**：Go 语言的接口定义代码
2. **实现结构**：Qwen 和豆包客户端的具体实现结构
3. **配置示例**：YAML 配置文件示例，展示如何配置多个 LLM 提供商
4. **工厂实现**：LLM 客户端工厂的实现代码
5. **集成方案**：如何将新的 LLM 支持集成到现有的 Agent 系统中
6. **测试策略**：单元测试和集成测试的建议

**代码风格要求：**
- 遵循现有项目的 Go 语言编码规范
- 所有注释和文档使用中文
- 错误信息使用中文
- 遵循项目的"禁止简化实现"纪律，使用真实的 API 调用

请提供完整的设计方案，包括代码结构、接口定义、实现示例和配置方案。
```

```
我希望基于当前的hzAgent项目开发一个AI智能体框架，请按照以下步骤进行：

1. **研究参考架构**：深入分析 Eino ADK 框架（https://www.cloudwego.io/zh/docs/eino/core_modules/eino_adk/outline/）的核心设计理念、架构模式和关键组件

2. **使用深度思考工具**：运用sequentialthinking工具进行系统性分析，包括：
   - 分析Eino ADK的优势和可借鉴的设计模式
   - 结合Go语言特性，思考如何设计适合的架构
   - 考虑框架的可扩展性、模块化和易用性
   - 设计核心组件和接口规范

3. **框架愿景实现**：设计一个具备以下特性的AI智能体框架：
   - **基础智能体能力**：提供完整的AI智能体核心功能（对话、推理、记忆、工具调用等）
   - **可扩展架构**：其他开发者可以轻松集成本框架的基础智能体
   - **插件化设计**：支持在基础智能体之上扩展新的技能和能力
   - **模块化组件**：各功能模块独立可复用，降低开发复杂度

4. **具体输出要求**：
   - 提供详细的架构设计方案
   - 说明核心组件和接口设计
   - 给出具体的实现路径和开发计划
   - 考虑与现有hzAgent项目的集成方案

请确保所有设计都遵循项目开发规范，使用真实的API实现，避免任何简化或模拟实现。
```

```
基于前面的分析，现在允许对现有API进行破坏性重构。请按照架构最优的原则，重新设计整个AI智能体框架方案，具体要求：

1. **架构重构权限**：可以完全重新设计pkg/agent、pkg/llm、pkg/tool等核心模块的接口和实现，不需要保持向后兼容性

2. **设计目标**：
   - 充分借鉴Eino ADK的优秀设计理念（事件流、Agent组合、Runner治理等）
   - 结合Go语言特性，设计最适合的架构模式
   - 实现真正的可扩展性、模块化和易用性
   - 支持完整的AI智能体核心功能（对话、推理、记忆、工具调用、MCP Server调用等）

3. **重新设计要求**：
   - 提供全新的核心接口设计方案
   - 重新规划目录结构和模块划分
   - 设计统一的事件驱动架构
   - 实现高效的多Agent协作机制
   - 提供完整的开发者友好API

4. **输出内容**：
   - 详细的新架构设计方案（包含核心接口定义）
   - 重新规划的项目目录结构
   - 关键组件的实现思路
   - 具体的开发实施计划
   - 迁移现有代码的策略

请确保所有设计都遵循项目开发规范，使用真实的API实现，避免任何简化或模拟实现。
```

```
基于前面讨论的AI智能体框架重构方案，请按照以下步骤进行实施：

1. **编写详细设计文档**：
   - 将前面分析的新架构设计（事件驱动、组合式Agent、Runner治理等）编写成专业的详细设计文档
   - 文档保存到 `docs/architecture-design.md`，使用中文编写
   - 文档必须包含：核心接口定义、目录结构规划、关键组件实现思路、开发者API示例、数据流图等
   - 设计文档将作为后续开发实施的唯一标准，所有代码实现必须严格遵循文档中的设计
   - 如果在后续开发过程中发现设计存在严重缺陷，必须先修改设计文档，经过评审确认后再进行代码实现

2. **设计文档质量检查**：
   - 使用sequentialthinking工具对已完成的设计文档进行系统性反思和分析
   - 重点检查：接口设计的合理性、模块划分的清晰度、事件流的完整性、错误处理的充分性、并发安全的考虑、与Go语言特性的契合度
   - 识别潜在的设计缺陷、功能遗漏、架构不合理之处
   - 如发现问题，立即优化设计文档，确保设计的完整性和可实施性

3. **制定详细实施计划**：
   - 使用任务管理工具将设计文档中的架构方案分解为具体的开发任务
   - 任务规划必须包含：代码实现、单元测试（覆盖率≥80%）、集成测试、示例程序、文档更新
   - 任务应按照依赖关系合理排序：先核心接口和基础设施，再具体实现，最后集成和示例
   - 每个任务应该是一个完整的功能单元，预估工作量约20分钟，便于跟踪进度

4. **按计划执行实施**：
   - 严格按照任务规划的顺序执行每项任务
   - 每项任务完成后立即更新任务状态，无需进行详细总结
   - 直接开始下一项任务，保持开发节奏的连续性
   - 所有代码必须遵循项目开发规范：使用真实API、完整错误处理、中文注释、go fmt/vet检查通过
   - 完成所有任务后，确保examples可以正常运行，测试全部通过

注意事项：
- 严格遵循"文档即契约"原则，代码实现不得偏离设计文档
- 所有外部API调用必须使用真实的认证凭据（环境变量）
- 禁止使用简化实现、模拟实现或硬编码
- 确保并发安全和资源正确释放
```

```
开始按照任务清单顺序执行实施，严格遵循以下要求：

**执行节奏：**
- 每完成一项任务后，立即使用 update_tasks 工具将该任务状态更新为 COMPLETE，并将下一项任务状态更新为 IN_PROGRESS
- 无需对已完成任务进行详细总结或回顾
- 立即开始下一项任务的实现，保持连续的开发节奏
- 如遇到阻塞问题，先尝试解决，若无法快速解决则暂停并说明情况

**代码质量强制要求：**
- 所有外部API调用必须使用真实的认证凭据（通过环境变量如 DEEPSEEK_API_KEY、DASHSCOPE_API_KEY 等获取）
- 严禁使用模拟实现、硬编码响应或占位符实现
- 实现完整的错误处理机制：包含上下文信息、可操作的错误消息（中文）、适当的错误类型分类
- 所有公开函数、结构体、接口必须有完整的中文注释，说明用途、参数、返回值和可能的错误
- 每个代码文件完成后必须运行 `go fmt` 和 `go vet` 检查，确保无格式问题和静态分析警告
- 单元测试覆盖率必须达到80%以上，测试用例需覆盖正常流程、边界条件和错误场景
- 所有并发代码必须确保线程安全，正确处理资源释放和通道关闭

**实现标准：**
- 严格按照 docs/architecture-design.md 中定义的接口和设计实现，不得偏离
- 如发现设计文档存在问题，必须先修改设计文档并确认后再继续实现
- 每个模块实现完成后，确保相关的单元测试能够通过
- 涉及网络调用的测试，通过环境变量控制是否启用（避免CI环境问题）
```

```
我对当前企业微信客服Agent的实现架构有不同的看法。当前实现将智能问题分类、情绪识别、自动回复、质量评估、工单管理等功能都写成了独立的代码模块，但我认为这种设计过于复杂且不够灵活。

**我的架构理念**：
1. **核心能力框架化**：智能问题分类、情绪识别、自动回复、质量评估等基础AI能力应该由框架本身提供，而不是每个Agent都重复实现
2. **角色驱动设计**：不同角色的Agent（如客服、销售、技术支持）应该通过不同的提示词（prompt）来配置其基础AI能力和行为模式
3. **工具扩展机制**：复杂的业务功能（如工单管理、CRM集成、知识库查询）通过注册工具（Tool）和MCP Server来实现，而不是硬编码

**具体重新设计要求**：
1. **重构框架核心**：在pkg/agent或pkg/llm中添加通用的AI能力抽象（如分类、情绪分析、质量评估），这些能力通过LLM + 结构化提示词实现
2. **简化Agent实现**：企业微信客服Agent应该主要通过配置文件定义角色提示词和注册的工具，核心逻辑应该非常简洁
3. **工具化扩展功能**：将工单管理等业务功能实现为独立的Tool，可以被任何Agent按需注册使用
4. **配置驱动**：Agent的行为主要通过YAML配置文件中的提示词、工具列表、MCP服务器配置来定义

**保持的要求**：
- 遵循项目的"禁止简化实现"规范，使用真实API
- 保持80%+测试覆盖率
- 使用中文注释和文档
- 提供完整的示例和使用说明

请基于这个架构理念重新设计和实现企业微信客服Agent示例，展示如何通过框架能力+提示词+工具注册的方式来构建专业的Agent。
```

```
我需要你对当前的配置驱动架构进行深度优化，从"代码实现AI能力"转向"提示词工程驱动AI能力"的设计理念。

**核心优化目标**：
将当前在`pkg/agent/capabilities/`中通过代码实现的AI能力（如情绪分析、问题分类、质量评估等），重构为通过精心设计的提示词和LLM原生能力来实现，从而大幅简化代码复杂度。

**具体实施要求**：

1. **提示词能力模板化**：
   - 创建标准化的AI能力提示词模板库（如情绪分析师、问题分类专家、质量评估师等角色模板）
   - 每个模板包含：角色定义、专业背景、行为指导、输出格式要求
   - 示例模板：
     ```
     情绪分析师模板：
     "你是一位经验丰富的心理咨询师，具有敏锐的情感洞察力。你能够：
     1. 准确识别用户的情绪状态（愉悦、焦虑、愤怒、沮丧、中性等）
     2. 分析情绪强度（1-10级）和触发原因
     3. 在回复中自然地体现对用户情感的理解和适当的情感回应
     4. 对负面情绪提供温和的安抚和建设性建议"
     ```

2. **能力分类重新定义**：
   - **提示词实现**：情绪分析、问题分类、语气调整、角色扮演、对话风格、质量自评等认知类能力
   - **代码/工具实现**：数据库CRUD、API调用、文件操作、工单系统、外部服务集成等操作类功能

3. **架构重构具体步骤**：
   - **第一步**：移除`pkg/agent/capabilities/`中的LLM-based能力实现（保留接口定义用于向后兼容）
   - **第二步**：在`pkg/llm/prompts/`中创建能力模板库，包含各种AI角色和能力的提示词模板
   - **第三步**：重新设计Agent配置文件，通过组合不同的能力模板来定义Agent的综合能力
   - **第四步**：简化`ConfigDrivenAgent`的实现，主要负责提示词组装、事件流控制和工具调用

4. **配置文件新结构设计**：
   ```yaml
   agents:
     wechat_customer_service:
       name: "企业微信客服助手"
       
       # 基础角色定义
       base_role: "professional_customer_service"
       
       # 组合AI能力（通过提示词模板实现）
       ai_capabilities:
         - emotion_analyst        # 情绪分析师能力
         - problem_classifier     # 问题分类专家能力  
         - quality_assessor       # 质量评估师能力
         - empathetic_responder   # 共情回应能力
       
       # 业务工具（通过代码实现）
       tools:
         - ticket_manager
         - knowledge_base_search
       
       # 个性化配置
       personality_traits:
         - "耐心细致"
         - "专业可靠" 
         - "温和友善"
       
       # 行为约束
       behavioral_constraints:
         - "始终保持专业态度"
         - "保护客户隐私信息"
         - "遇到超出能力范围的问题及时转人工"
   ```

5. **实现细节要求**：
   - 创建提示词模板管理器，支持模板组合和变量替换
   - Agent在处理消息时，根据配置动态组装包含多种能力的综合提示词
   - 保持事件驱动架构，但事件主要来源于LLM的智能分析而非独立的代码模块
   - 确保所有能力都通过真实的LLM API调用实现，严格遵循"禁止简化实现"规范

6. **验证标准**：
   - 代码行数显著减少（预期减少60%以上的能力实现代码）
   - Agent能力主要通过修改配置文件中的提示词模板来调整
   - 保持或提升当前的功能完整性和智能化水平
   - 测试覆盖率保持在合理水平

**交付要求**：
1. 重构后的完整代码实现
2. 新的配置文件示例
3. 提示词模板库
4. 运行演示和测试验证
5. 架构对比文档，说明优化前后的差异和优势

请按照这个详细的优化方案，重新设计和实现企业微信客服Agent，展示如何通过提示词工程实现更简洁、更灵活的AI Agent架构。
```