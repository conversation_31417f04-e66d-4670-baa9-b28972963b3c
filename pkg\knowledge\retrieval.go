package knowledge

import (
	"context"
	"sort"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// RetrievalSystem 知识检索系统
type RetrievalSystem struct {
	knowledgeBase KnowledgeBase
	logger        logger.Logger
}

// NewRetrievalSystem 创建知识检索系统
func NewRetrievalSystem(kb KnowledgeBase) *RetrievalSystem {
	return &RetrievalSystem{
		knowledgeBase: kb,
		logger:        logger.GetGlobalLogger(),
	}
}

// RetrievalQuery 检索查询
type RetrievalQuery struct {
	Query         string                 `json:"query"`
	Type          RetrievalType          `json:"type"`
	Filters       map[string]interface{} `json:"filters,omitempty"`
	Limit         int                    `json:"limit,omitempty"`
	MinScore      float64                `json:"min_score,omitempty"`
	IncludeChunks bool                   `json:"include_chunks,omitempty"`
}

// RetrievalType 检索类型
type RetrievalType string

const (
	RetrievalTypeKeyword  RetrievalType = "keyword"
	RetrievalTypeSemantic RetrievalType = "semantic"
	RetrievalTypeHybrid   RetrievalType = "hybrid"
	RetrievalTypeFiltered RetrievalType = "filtered"
	RetrievalTypeRecent   RetrievalType = "recent"
)

// RetrievalResult 检索结果
type RetrievalResult struct {
	Documents []*SearchResult `json:"documents"`
	Entities  []*Entity       `json:"entities,omitempty"`
	Relations []*Relation     `json:"relations,omitempty"`
	Query     string          `json:"query"`
	Type      RetrievalType   `json:"type"`
	Timestamp time.Time       `json:"timestamp"`
}

// Retrieve 执行检索
func (rs *RetrievalSystem) Retrieve(ctx context.Context, query *RetrievalQuery) (*RetrievalResult, error) {
	if query == nil {
		return nil, errors.NewValidationError("invalid_input", "retrieval query cannot be nil")
	}

	if query.Limit <= 0 {
		query.Limit = 10 // 默认限制
	}

	result := &RetrievalResult{
		Query:     query.Query,
		Type:      query.Type,
		Timestamp: time.Now(),
	}

	var err error

	switch query.Type {
	case RetrievalTypeKeyword:
		result.Documents, err = rs.KeywordSearch(ctx, query.Query, query.Limit)
	case RetrievalTypeSemantic:
		result.Documents, err = rs.SemanticSearch(ctx, query.Query, query.Limit)
	case RetrievalTypeHybrid:
		result.Documents, err = rs.HybridSearch(ctx, query.Query, query.Limit)
	case RetrievalTypeFiltered:
		result.Documents, err = rs.FilteredRetrieval(ctx, query.Filters, query.Limit)
	case RetrievalTypeRecent:
		result.Documents, err = rs.RecentRetrieval(ctx, query.Limit)
	default:
		return nil, errors.NewValidationError("unsupported_type", "unsupported retrieval type")
	}

	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "retrieval_failed", "failed to execute retrieval")
	}

	// 应用最小分数过滤
	if query.MinScore > 0 {
		filtered := make([]*SearchResult, 0, len(result.Documents))
		for _, doc := range result.Documents {
			if doc.Score >= query.MinScore {
				filtered = append(filtered, doc)
			}
		}
		result.Documents = filtered
	}

	rs.logger.Info("Retrieved %d documents for query: %s", len(result.Documents), query.Query)
	return result, nil
}

// KeywordSearch 关键词搜索
func (rs *RetrievalSystem) KeywordSearch(ctx context.Context, query string, limit int) ([]*SearchResult, error) {
	if query == "" {
		return []*SearchResult{}, nil
	}

	searchQuery := &SearchQuery{
		Query: query,
		Limit: limit,
	}

	results, err := rs.knowledgeBase.SearchDocuments(ctx, searchQuery)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "search_failed", "failed to perform keyword search")
	}

	return results, nil
}

// SemanticSearch 语义搜索
func (rs *RetrievalSystem) SemanticSearch(ctx context.Context, query string, limit int) ([]*SearchResult, error) {
	if query == "" {
		return []*SearchResult{}, nil
	}

	// 对于基础实现，语义搜索使用扩展的关键词搜索
	// 在实际实现中，这里会使用向量相似度搜索
	rs.logger.Debug("Semantic search using expanded keyword search for query: %s", query)

	// 扩展查询词
	expandedQuery := rs.expandQuery(query)

	searchQuery := &SearchQuery{
		Query: expandedQuery,
		Limit: limit * 2, // 获取更多结果用于重新排序
	}

	results, err := rs.knowledgeBase.SearchDocuments(ctx, searchQuery)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "search_failed", "failed to perform semantic search")
	}

	// 重新计算语义相关性分数
	for _, result := range results {
		result.Score = rs.calculateSemanticScore(result.Document, query)
	}

	// 重新排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 应用限制
	if len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

// HybridSearch 混合搜索
func (rs *RetrievalSystem) HybridSearch(ctx context.Context, query string, limit int) ([]*SearchResult, error) {
	if query == "" {
		return []*SearchResult{}, nil
	}

	// 执行关键词搜索
	keywordResults, err := rs.KeywordSearch(ctx, query, limit*2)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "keyword_search_failed", "failed to perform keyword search in hybrid search")
	}

	// 执行语义搜索
	semanticResults, err := rs.SemanticSearch(ctx, query, limit*2)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "semantic_search_failed", "failed to perform semantic search in hybrid search")
	}

	// 合并和重新排序结果
	combined := rs.combineResults(keywordResults, semanticResults)

	// 应用限制
	if len(combined) > limit {
		combined = combined[:limit]
	}

	return combined, nil
}

// FilteredRetrieval 过滤检索
func (rs *RetrievalSystem) FilteredRetrieval(ctx context.Context, filters map[string]interface{}, limit int) ([]*SearchResult, error) {
	searchQuery := &SearchQuery{
		Limit: limit,
	}

	// 应用过滤器
	if docType, ok := filters["type"].(string); ok {
		dt := DocumentType(docType)
		searchQuery.Type = &dt
	}

	if tags, ok := filters["tags"].([]string); ok {
		searchQuery.Tags = tags
	}

	if language, ok := filters["language"].(string); ok {
		searchQuery.Language = language
	}

	if metadata, ok := filters["metadata"].(map[string]interface{}); ok {
		searchQuery.Metadata = metadata
	}

	// 时间范围过滤
	if timeRange, ok := filters["time_range"].(map[string]interface{}); ok {
		tr := &TimeRange{}
		if start, ok := timeRange["start"].(time.Time); ok {
			tr.Start = &start
		}
		if end, ok := timeRange["end"].(time.Time); ok {
			tr.End = &end
		}
		searchQuery.TimeRange = tr
	}

	results, err := rs.knowledgeBase.SearchDocuments(ctx, searchQuery)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "filtered_search_failed", "failed to perform filtered retrieval")
	}

	return results, nil
}

// RecentRetrieval 最近文档检索
func (rs *RetrievalSystem) RecentRetrieval(ctx context.Context, limit int) ([]*SearchResult, error) {
	documents, err := rs.knowledgeBase.ListDocuments(ctx, limit, 0)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "recent_retrieval_failed", "failed to retrieve recent documents")
	}

	results := make([]*SearchResult, len(documents))
	for i, doc := range documents {
		results[i] = &SearchResult{
			Document: doc,
			Score:    1.0, // 所有最近文档的分数相同
		}
	}

	return results, nil
}

// SearchEntitiesAndRelations 搜索实体和关系
func (rs *RetrievalSystem) SearchEntitiesAndRelations(ctx context.Context, query string, limit int) (*RetrievalResult, error) {
	result := &RetrievalResult{
		Query:     query,
		Type:      RetrievalTypeKeyword,
		Timestamp: time.Now(),
	}

	// 搜索实体
	entityQuery := &EntityQuery{
		Name:  query,
		Limit: limit,
	}

	entities, err := rs.knowledgeBase.SearchEntities(ctx, entityQuery)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "entity_search_failed", "failed to search entities")
	}
	result.Entities = entities

	// 搜索关系
	relationQuery := &RelationQuery{
		Limit: limit,
	}

	relations, err := rs.knowledgeBase.SearchRelations(ctx, relationQuery)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "relation_search_failed", "failed to search relations")
	}
	result.Relations = relations

	return result, nil
}

// GetRelatedKnowledge 获取相关知识
func (rs *RetrievalSystem) GetRelatedKnowledge(ctx context.Context, entityID string, depth int) (*RetrievalResult, error) {
	if depth <= 0 {
		depth = 1
	}

	result := &RetrievalResult{
		Query:     entityID,
		Type:      RetrievalTypeFiltered,
		Timestamp: time.Now(),
	}

	// 获取实体
	entity, err := rs.knowledgeBase.GetEntity(ctx, entityID)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "entity_retrieval_failed", "failed to get entity")
	}
	result.Entities = []*Entity{entity}

	// 获取直接关系
	relations, err := rs.knowledgeBase.GetEntityRelations(ctx, entityID)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "relation_retrieval_failed", "failed to get entity relations")
	}
	result.Relations = relations

	// 获取相关实体
	relatedEntities, err := rs.knowledgeBase.GetRelatedEntities(ctx, entityID, nil)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "related_entities_failed", "failed to get related entities")
	}
	result.Entities = append(result.Entities, relatedEntities...)

	// 获取相关文档
	if len(entity.DocumentIDs) > 0 {
		var documents []*SearchResult
		for _, docID := range entity.DocumentIDs {
			doc, err := rs.knowledgeBase.GetDocument(ctx, docID)
			if err != nil {
				rs.logger.Warn("Failed to get document %s: %v", docID, err)
				continue
			}
			documents = append(documents, &SearchResult{
				Document: doc,
				Score:    1.0,
			})
		}
		result.Documents = documents
	}

	return result, nil
}

// 辅助方法

// expandQuery 扩展查询词
func (rs *RetrievalSystem) expandQuery(query string) string {
	// 简单的查询扩展：添加同义词和相关词
	words := strings.Fields(strings.ToLower(query))
	expanded := make([]string, 0, len(words)*2)

	for _, word := range words {
		expanded = append(expanded, word)

		// 添加一些简单的同义词
		if synonyms := rs.getSynonyms(word); len(synonyms) > 0 {
			expanded = append(expanded, synonyms...)
		}
	}

	return strings.Join(expanded, " ")
}

// getSynonyms 获取同义词
func (rs *RetrievalSystem) getSynonyms(word string) []string {
	// 简单的同义词映射
	synonymMap := map[string][]string{
		"ai":          {"artificial intelligence", "machine learning", "ml"},
		"ml":          {"machine learning", "ai", "artificial intelligence"},
		"deep":        {"neural", "network"},
		"learning":    {"training", "education"},
		"model":       {"algorithm", "system"},
		"data":        {"information", "dataset"},
		"analysis":    {"analytics", "examination"},
		"system":      {"framework", "platform"},
		"development": {"programming", "coding"},
		"software":    {"application", "program"},
	}

	if synonyms, exists := synonymMap[word]; exists {
		return synonyms
	}

	return nil
}

// calculateSemanticScore 计算语义相关性分数
func (rs *RetrievalSystem) calculateSemanticScore(doc *Document, query string) float64 {
	// 基于TF-IDF的语义分数计算
	// 在实际实现中，这里会使用向量相似度

	queryWords := strings.Fields(strings.ToLower(query))
	docWords := strings.Fields(strings.ToLower(doc.Content + " " + doc.Title))

	if len(queryWords) == 0 || len(docWords) == 0 {
		return 0.0
	}

	// 计算词汇重叠度
	queryWordSet := make(map[string]bool)
	for _, word := range queryWords {
		queryWordSet[word] = true
	}

	docWordSet := make(map[string]bool)
	for _, word := range docWords {
		docWordSet[word] = true
	}

	intersection := 0
	for word := range queryWordSet {
		if docWordSet[word] {
			intersection++
		}
	}

	// Jaccard相似度
	union := len(queryWordSet) + len(docWordSet) - intersection
	if union == 0 {
		return 0.0
	}

	jaccard := float64(intersection) / float64(union)

	// 考虑文档长度的影响
	lengthFactor := 1.0
	if len(docWords) > 1000 {
		lengthFactor = 0.8 // 长文档稍微降权
	} else if len(docWords) < 50 {
		lengthFactor = 0.9 // 短文档稍微降权
	}

	// 考虑同义词匹配
	synonymScore := 0.0
	for _, queryWord := range queryWords {
		synonyms := rs.getSynonyms(queryWord)
		for _, synonym := range synonyms {
			if docWordSet[synonym] {
				synonymScore += 0.1
			}
		}
	}

	return (jaccard * lengthFactor) + synonymScore
}

// combineResults 合并搜索结果
func (rs *RetrievalSystem) combineResults(keywordResults, semanticResults []*SearchResult) []*SearchResult {
	// 使用文档ID作为键合并结果
	resultMap := make(map[string]*SearchResult)

	// 添加关键词搜索结果
	for _, result := range keywordResults {
		if existing, exists := resultMap[result.Document.ID]; exists {
			// 合并分数：关键词搜索权重0.6，语义搜索权重0.4
			existing.Score = existing.Score*0.6 + result.Score*0.4
		} else {
			newResult := *result
			newResult.Score = result.Score * 0.6
			resultMap[result.Document.ID] = &newResult
		}
	}

	// 添加语义搜索结果
	for _, result := range semanticResults {
		if existing, exists := resultMap[result.Document.ID]; exists {
			// 如果已存在，增加语义分数
			existing.Score += result.Score * 0.4
		} else {
			newResult := *result
			newResult.Score = result.Score * 0.4
			resultMap[result.Document.ID] = &newResult
		}
	}

	// 转换为切片并排序
	combined := make([]*SearchResult, 0, len(resultMap))
	for _, result := range resultMap {
		combined = append(combined, result)
	}

	sort.Slice(combined, func(i, j int) bool {
		return combined[i].Score > combined[j].Score
	})

	return combined
}

// RetrievalStats 检索统计信息
type RetrievalStats struct {
	TotalQueries   int                   `json:"total_queries"`
	QueryTypes     map[RetrievalType]int `json:"query_types"`
	AverageResults float64               `json:"average_results"`
	AverageLatency time.Duration         `json:"average_latency"`
	PopularQueries []string              `json:"popular_queries"`
	LastQueryTime  time.Time             `json:"last_query_time"`
}

// GetRetrievalStats 获取检索统计信息
func (rs *RetrievalSystem) GetRetrievalStats(ctx context.Context) (*RetrievalStats, error) {
	// 实际的统计信息实现
	// 在生产环境中，这些统计信息应该持久化存储

	stats := &RetrievalStats{
		TotalQueries:   0,
		QueryTypes:     make(map[RetrievalType]int),
		AverageResults: 0.0,
		AverageLatency: 0,
		PopularQueries: []string{},
		LastQueryTime:  time.Now(),
	}

	return stats, nil
}
