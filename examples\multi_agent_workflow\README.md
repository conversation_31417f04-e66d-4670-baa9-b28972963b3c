# 多智能体工作流示例

这个示例展示了如何使用AgentScope-Golang ADK架构的组合模式创建复杂的多智能体工作流。

## 功能特性

- **顺序执行**: 智能体按照指定顺序依次执行
- **并行执行**: 多个智能体同时执行，支持不同的聚合策略
- **循环执行**: 智能体重复执行直到满足停止条件
- **复合工作流**: 组合多种执行模式创建复杂的工作流

## 架构概念

### 组合模式 (Composition Patterns)

AgentScope-Golang ADK提供了三种基本的智能体组合模式：

1. **Sequential (顺序)**
   - 智能体按顺序依次执行
   - 前一个智能体的输出作为下一个智能体的输入
   - 支持不同的失败处理策略

2. **Parallel (并行)**
   - 多个智能体同时执行
   - 支持多种聚合策略：FailFast、CollectAll、Majority
   - 适用于独立的并行任务

3. **Loop (循环)**
   - 单个智能体重复执行
   - 支持最大迭代次数限制
   - 支持自定义停止条件

### 事件驱动架构

所有组合器都使用事件驱动架构：

- `EventStart`: 智能体开始执行
- `EventFinal`: 智能体执行完成
- `EventError`: 执行过程中发生错误
- `EventStream`: 流式输出（如果支持）

## 运行示例

```bash
# 进入示例目录
cd examples/multi_agent_workflow

# 运行示例
go run main.go
```

## 示例演示

### 演示1: 顺序执行工作流

模拟软件开发流水线：

```
分析师 -> 设计师 -> 开发者 -> 测试员 -> 审核员
```

每个智能体依次执行，前一个完成后才开始下一个。

### 演示2: 并行执行工作流

模拟并行开发任务：

```
分析师 ┐
       ├─ 同时执行
设计师 ┘
```

两个智能体同时执行，等待所有完成后聚合结果。

### 演示3: 循环执行工作流

模拟迭代开发过程：

```
迭代开发 -> 迭代开发 -> 迭代开发 (重复3次)
```

同一个智能体重复执行，直到达到最大迭代次数。

### 演示4: 复合工作流

模拟复杂项目流程：

```
阶段1 (并行): 需求分析 + 技术调研
阶段2 (顺序): 架构设计 -> 编码实现 -> 集成测试
```

组合多种执行模式创建复杂的工作流。

## 代码结构

### MockAgent

```go
type MockAgent struct {
    name        string
    description string
    response    string
    delay       int
}
```

模拟智能体实现，用于演示不同的执行模式。

### 组合器配置

```go
// 顺序执行配置
sequentialConfig := &compose.SequentialConfig{
    Agents:      []agent.Agent{agent1, agent2, agent3},
    FailureMode: compose.FailureModeStop,
}

// 并行执行配置
parallelConfig := &compose.ParallelConfig{
    Agents:   []agent.Agent{agent1, agent2},
    Strategy: compose.CollectAll,
}

// 循环执行配置
loopConfig := &compose.LoopConfig{
    Agent:        loopAgent,
    MaxIteration: 3,
    StopCondition: func(ctx context.Context, iteration int, lastOutput string) bool {
        return iteration >= 3
    },
}
```

### 事件处理

```go
for {
    event, ok := iterator.Next()
    if !ok {
        break
    }

    switch event.Type {
    case event.EventStart:
        // 处理开始事件
    case event.EventFinal:
        // 处理完成事件
    case event.EventError:
        // 处理错误事件
    }
}
```

## 扩展示例

### 自定义智能体

```go
type CustomAgent struct {
    // 自定义字段
}

func (a *CustomAgent) Name(ctx context.Context) string {
    return "custom-agent"
}

func (a *CustomAgent) Description(ctx context.Context) string {
    return "自定义智能体"
}

func (a *CustomAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
    // 实现自定义逻辑
    pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)
    
    go func() {
        defer pair.Generator.Close()
        
        // 发送事件
        finalEvent := &event.Event{
            Type: event.EventFinal,
            Data: &event.FinalData{
                Content: "自定义处理结果",
            },
        }
        pair.Generator.Send(finalEvent)
    }()
    
    return pair.Iterator
}
```

### 自定义停止条件

```go
stopCondition := func(ctx context.Context, iteration int, lastOutput string) bool {
    // 基于输出内容的停止条件
    if strings.Contains(lastOutput, "完成") {
        return true
    }
    
    // 基于迭代次数的停止条件
    if iteration >= 5 {
        return true
    }
    
    return false
}
```

### 错误处理策略

```go
// 顺序执行的失败模式
type FailureMode int

const (
    FailureModeStop     FailureMode = iota // 遇到错误立即停止
    FailureModeContinue                    // 遇到错误继续执行
    FailureModeRetry                       // 遇到错误重试
)

// 并行执行的聚合策略
type Strategy int

const (
    FailFast   Strategy = iota // 任一失败立即返回
    CollectAll                 // 收集所有结果
    Majority                   // 多数决定
)
```

## 性能考虑

### 并发控制

- 并行执行使用goroutine实现真正的并发
- 每个智能体在独立的goroutine中运行
- 使用context进行超时和取消控制

### 内存管理

- 事件通过channel传递，支持背压控制
- AsyncIterator支持缓冲区大小配置
- 及时关闭Generator避免goroutine泄漏

### 错误恢复

- 支持多种错误处理策略
- 可配置重试次数和重试间隔
- 提供详细的错误信息和堆栈跟踪

## 实际应用场景

### 1. 软件开发流水线

```
需求分析 -> 设计 -> 开发 -> 测试 -> 部署
```

### 2. 数据处理管道

```
数据收集 ┐
数据清洗 ├─ 并行处理 -> 数据分析 -> 报告生成
数据验证 ┘
```

### 3. 内容创作工作流

```
主题研究 -> 内容创作 -> 审核修改 -> 发布 (循环优化)
```

### 4. 客户服务流程

```
问题分类 ┐
意图识别 ├─ 并行分析 -> 解决方案生成 -> 回复客户
情感分析 ┘
```

## 相关文档

- [架构设计文档](../../docs/architecture-design.md)
- [组合模式文档](../../docs/composition-patterns.md)
- [事件系统文档](../../docs/event-system.md)

## 许可证

本示例遵循项目的开源许可证。
