# AgentScope-Golang Makefile

.PHONY: help build test clean run-example fmt vet lint deps

# Default target
help:
	@echo "Available targets:"
	@echo "  deps        - Download dependencies"
	@echo "  fmt         - Format code"
	@echo "  vet         - Run go vet"
	@echo "  lint        - Run golint"
	@echo "  test        - Run tests"
	@echo "  test-cover  - Run tests with coverage"
	@echo "  test-knowledge - Test knowledge base system"
	@echo "  run-example - Run simple chat example"
	@echo "  run-knowledge-example - Run knowledge base example"
	@echo "  clean       - Clean build artifacts"

# Download dependencies
deps:
	go mod download
	go mod tidy

# Format code
fmt:
	go fmt ./...

# Run go vet
vet:
	go vet ./...

# Run golint (install with: go install golang.org/x/lint/golint@latest)
lint:
	golint ./...

# Run tests
test:
	go test -v ./...

# Run tests with coverage
test-cover:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Test knowledge base specifically
test-knowledge:
	go test -v ./pkg/knowledge/...

# Run simple chat example
run-example:
	go run examples/simple_chat/main.go

# Run knowledge base example
run-knowledge-example:
	go run examples/knowledge_base/main.go

# Clean build artifacts
clean:
	rm -f coverage.out coverage.html
	go clean ./...

# Check environment variables
check-env:
	@if [ -z "$$DEEPSEEK_API_KEY" ]; then \
		echo "Error: DEEPSEEK_API_KEY environment variable is not set"; \
		exit 1; \
	fi
	@echo "Environment check passed"