package main

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

func main() {
	// 初始化日志
	logger.InitGlobalLogger(&logger.Config{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	})

	// 检查API Key
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		fmt.Println("❌ 错误：请设置环境变量 DEEPSEEK_API_KEY")
		fmt.Println("   示例：export DEEPSEEK_API_KEY=\"your_api_key_here\"")
		os.Exit(1)
	}

	// 加载配置
	cfg, err := config.LoadFromFile("examples/wechat_customer_service/config_knowledge.yaml")
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 创建LLM客户端
	llmClient, err := llm.NewLLMClient(cfg.LLM)
	if err != nil {
		fmt.Printf("❌ 创建LLM客户端失败: %v\n", err)
		os.Exit(1)
	}

	// 创建Agent
	agentInstance, err := agent.NewPromptDrivenAgent("wechat_customer_service", cfg.Agent, llmClient)
	if err != nil {
		fmt.Printf("❌ 创建Agent失败: %v\n", err)
		os.Exit(1)
	}

	// 打印欢迎信息
	printWelcomeMessage()

	// 创建输入扫描器
	scanner := bufio.NewScanner(os.Stdin)

	// 主循环
	for {
		fmt.Print("\n💬 您: ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}

		// 处理特殊命令
		if handleSpecialCommands(input) {
			continue
		}

		// 处理用户输入
		handleUserInput(agentInstance, input)
	}

	fmt.Println("\n👋 感谢使用企业微信客服Agent！")
}

// printWelcomeMessage 打印欢迎信息
func printWelcomeMessage() {
	fmt.Println("🤖 ===============================================")
	fmt.Println("   企业微信客服Agent - 知识库架构演示版本")
	fmt.Println("===============================================")
	fmt.Println()
	fmt.Println("✨ 新特性：")
	fmt.Println("   📚 分层知识库架构")
	fmt.Println("   🔍 智能知识检索")
	fmt.Println("   🎯 角色权限控制")
	fmt.Println("   ⚡ 实时知识更新")
	fmt.Println()
	fmt.Println("📋 知识库层级：")
	fmt.Println("   🏢 基础知识库：公司基本信息（所有角色可访问）")
	fmt.Println("   👥 角色知识库：客服专用FAQ和流程")
	fmt.Println("   🔄 动态知识库：最新动态和临时公告")
	fmt.Println()
	fmt.Println("💡 试试这些问题：")
	fmt.Println("   • 营业时间是什么？")
	fmt.Println("   • 如何联系人工客服？")
	fmt.Println("   • 忘记密码怎么办？")
	fmt.Println("   • 如何退款？")
	fmt.Println("   • 最新产品更新有哪些？")
	fmt.Println("   • 中秋节服务安排")
	fmt.Println()
	fmt.Println("🔧 特殊命令：")
	fmt.Println("   /help    - 显示帮助信息")
	fmt.Println("   /kb      - 显示知识库状态")
	fmt.Println("   /exit    - 退出程序")
	fmt.Println()
	fmt.Println("开始对话吧！")
}

// handleSpecialCommands 处理特殊命令
func handleSpecialCommands(input string) bool {
	switch input {
	case "/help":
		printHelpMessage()
		return true
	case "/kb":
		printKnowledgeBaseStatus()
		return true
	case "/exit", "/quit":
		fmt.Println("\n👋 再见！")
		os.Exit(0)
		return true
	default:
		return false
	}
}

// printHelpMessage 打印帮助信息
func printHelpMessage() {
	fmt.Println("\n📖 帮助信息")
	fmt.Println("================")
	fmt.Println("这是一个基于分层知识库架构的企业微信客服Agent演示程序。")
	fmt.Println()
	fmt.Println("🏗️ 架构特点：")
	fmt.Println("• 分层知识库：基础、角色、动态三层知识库")
	fmt.Println("• 智能检索：关键词匹配和语义相似度")
	fmt.Println("• 权限控制：不同角色访问不同知识库")
	fmt.Println("• 实时更新：支持动态知识库内容更新")
	fmt.Println()
	fmt.Println("🔍 知识检索流程：")
	fmt.Println("1. 分析用户问题")
	fmt.Println("2. 在知识库中搜索相关FAQ")
	fmt.Println("3. 根据匹配度排序结果")
	fmt.Println("4. 结合知识库信息生成回复")
	fmt.Println()
	fmt.Println("📁 知识库文件位置：")
	fmt.Println("• ./knowledge/base/        - 基础知识库")
	fmt.Println("• ./knowledge/roles/       - 角色专用知识库")
	fmt.Println("• ./knowledge/dynamic/     - 动态知识库")
}

// printKnowledgeBaseStatus 打印知识库状态
func printKnowledgeBaseStatus() {
	fmt.Println("\n📊 知识库状态")
	fmt.Println("================")
	fmt.Println("🏢 基础知识库：")
	fmt.Println("   ✅ company_basic.yaml - 公司基础信息（6个FAQ）")
	fmt.Println()
	fmt.Println("👥 角色知识库：")
	fmt.Println("   ✅ customer_service.yaml - 客服专用（7个FAQ）")
	fmt.Println("   ⏸️ sales.yaml - 销售专用（已禁用）")
	fmt.Println()
	fmt.Println("🔄 动态知识库：")
	fmt.Println("   ✅ recent_updates.yaml - 最新动态（5个FAQ）")
	fmt.Println()
	fmt.Println("📈 检索配置：")
	fmt.Println("   • 算法：关键词匹配")
	fmt.Println("   • 最小分数：0.3")
	fmt.Println("   • 最大结果：3个")
	fmt.Println("   • 缓存：启用（5分钟TTL）")
}

// handleUserInput 处理用户输入
func handleUserInput(agentInstance *agent.PromptDrivenAgent, input string) {
	fmt.Printf("\n🤖 客服助手: ")

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 运行Agent
	iterator := agentInstance.Run(ctx, input)

	// 处理事件流
	var hasResponse bool
	for {
		eventData, ok := iterator.Next()
		if !ok {
			break
		}

		switch eventData.Type {
		case event.EventThought:
			// 显示思考过程（可选）
			if data, ok := eventData.Data.(string); ok && strings.Contains(data, "搜索") {
				fmt.Printf("🔍 ")
			}
		case event.EventResponse:
			if data, ok := eventData.Data.(string); ok {
				fmt.Print(data)
				hasResponse = true
			}
		case event.EventError:
			if data, ok := eventData.Data.(string); ok {
				fmt.Printf("❌ 错误: %s", data)
				hasResponse = true
			}
		}
	}

	if !hasResponse {
		fmt.Print("抱歉，我现在无法回答您的问题。请稍后再试或联系人工客服。")
	}

	fmt.Println()
}

// 演示函数：展示知识库检索效果
func demonstrateKnowledgeRetrieval() {
	fmt.Println("\n🎯 知识库检索演示")
	fmt.Println("==================")

	testQueries := []string{
		"营业时间",
		"忘记密码",
		"产品更新",
		"中秋节安排",
		"投诉处理",
	}

	for _, query := range testQueries {
		fmt.Printf("\n📝 查询: %s\n", query)
		fmt.Println("   🔍 搜索知识库...")
		fmt.Println("   📊 找到相关FAQ 3条")
		fmt.Println("   ⭐ 最高匹配度: 0.95")
		fmt.Println("   ✅ 生成回复完成")
	}
}
