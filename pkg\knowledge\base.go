package knowledge

import (
	"context"
	"crypto/md5"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// InMemoryKnowledgeBase 基于内存的知识库实现
type InMemoryKnowledgeBase struct {
	documents map[string]*Document
	entities  map[string]*Entity
	relations map[string]*Relation
	mu        sync.RWMutex
	logger    logger.Logger
}

// NewInMemoryKnowledgeBase 创建新的内存知识库
func NewInMemoryKnowledgeBase() *InMemoryKnowledgeBase {
	return &InMemoryKnowledgeBase{
		documents: make(map[string]*Document),
		entities:  make(map[string]*Entity),
		relations: make(map[string]*Relation),
		logger:    logger.GetGlobalLogger(),
	}
}

// AddDocument 添加文档
func (kb *InMemoryKnowledgeBase) AddDocument(ctx context.Context, doc *Document) error {
	if doc == nil {
		return errors.NewValidationError("invalid_input", "document cannot be nil")
	}

	if doc.ID == "" {
		doc.ID = fmt.Sprintf("doc_%d", time.Now().UnixNano())
	}

	doc.Hash = fmt.Sprintf("%x", md5.Sum([]byte(doc.Content)))
	doc.CreatedAt = time.Now()
	doc.UpdatedAt = time.Now()
	doc.Version = 1

	kb.mu.Lock()
	defer kb.mu.Unlock()

	// 检查是否已存在
	if _, exists := kb.documents[doc.ID]; exists {
		return errors.NewValidationError("document_exists", fmt.Sprintf("document %s already exists", doc.ID))
	}

	kb.documents[doc.ID] = doc
	return nil
}

// GetDocument 获取文档
func (kb *InMemoryKnowledgeBase) GetDocument(ctx context.Context, id string) (*Document, error) {
	if id == "" {
		return nil, errors.NewValidationError("invalid_input", "document ID cannot be empty")
	}

	kb.mu.RLock()
	defer kb.mu.RUnlock()

	doc, exists := kb.documents[id]
	if !exists {
		return nil, errors.NewNotFoundError("document_not_found", fmt.Sprintf("document not found: %s", id))
	}

	return doc, nil
}

// UpdateDocument 更新文档
func (kb *InMemoryKnowledgeBase) UpdateDocument(ctx context.Context, doc *Document) error {
	kb.mu.Lock()
	defer kb.mu.Unlock()

	existing, exists := kb.documents[doc.ID]
	if !exists {
		return errors.NewNotFoundError("document_not_found", fmt.Sprintf("document %s not found", doc.ID))
	}

	// 更新版本号和时间戳
	doc.Version = existing.Version + 1
	doc.UpdatedAt = time.Now()
	doc.CreatedAt = existing.CreatedAt // 保持原创建时间
	doc.Hash = fmt.Sprintf("%x", md5.Sum([]byte(doc.Content)))

	kb.documents[doc.ID] = doc
	return nil
}

// DeleteDocument 删除文档
func (kb *InMemoryKnowledgeBase) DeleteDocument(ctx context.Context, id string) error {
	kb.mu.Lock()
	defer kb.mu.Unlock()

	delete(kb.documents, id)
	return nil
}

// ListDocuments 列出文档
func (kb *InMemoryKnowledgeBase) ListDocuments(ctx context.Context, limit, offset int) ([]*Document, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	docs := make([]*Document, 0, len(kb.documents))
	for _, doc := range kb.documents {
		docs = append(docs, doc)
	}

	// 应用offset和limit
	if offset >= len(docs) {
		return []*Document{}, nil
	}

	end := len(docs)
	if limit > 0 && offset+limit < len(docs) {
		end = offset + limit
	}

	return docs[offset:end], nil
}

// SearchDocuments 搜索文档
func (kb *InMemoryKnowledgeBase) SearchDocuments(ctx context.Context, query *SearchQuery) ([]*SearchResult, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	var results []*SearchResult
	for _, doc := range kb.documents {
		if strings.Contains(strings.ToLower(doc.Content), strings.ToLower(query.Query)) {
			results = append(results, &SearchResult{
				Document: doc,
				Score:    0.8,
			})
		}
	}

	return results, nil
}

// SearchSimilar 搜索相似文档
func (kb *InMemoryKnowledgeBase) SearchSimilar(ctx context.Context, docID string, limit int) ([]*SearchResult, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	// 获取源文档
	sourceDoc, exists := kb.documents[docID]
	if !exists {
		return nil, fmt.Errorf("document not found: %s", docID)
	}

	if limit <= 0 {
		limit = 10
	}

	// 计算与其他文档的相似度
	var results []*SearchResult
	sourceText := sourceDoc.Title + " " + sourceDoc.Content
	sourceTerms := extractTermsFromText(sourceText)

	for id, doc := range kb.documents {
		if id == docID {
			continue // 跳过源文档本身
		}

		candidateText := doc.Title + " " + doc.Content
		candidateTerms := extractTermsFromText(candidateText)

		// 计算余弦相似度
		similarity := calculateCosineSimilarity(sourceTerms, candidateTerms)
		if similarity > 0.1 { // 设置最小相似度阈值
			results = append(results, &SearchResult{
				Document: doc,
				Score:    similarity,
			})
		}
	}

	// 按相似度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 限制结果数量
	if len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

// extractTermsFromText 提取文本中的词条
func extractTermsFromText(text string) map[string]int {
	terms := make(map[string]int)
	words := strings.Fields(strings.ToLower(text))

	for _, word := range words {
		// 简单的词条清理
		word = strings.Trim(word, ".,!?;:\"'()[]{}")
		if len(word) > 2 { // 忽略太短的词
			terms[word]++
		}
	}

	return terms
}

// calculateCosineSimilarity 计算两个文本的余弦相似度
func calculateCosineSimilarity(terms1, terms2 map[string]int) float64 {
	// 获取所有唯一词条
	allTerms := make(map[string]bool)
	for term := range terms1 {
		allTerms[term] = true
	}
	for term := range terms2 {
		allTerms[term] = true
	}

	// 构建向量
	var vector1, vector2 []float64
	for term := range allTerms {
		vector1 = append(vector1, float64(terms1[term]))
		vector2 = append(vector2, float64(terms2[term]))
	}

	// 计算余弦相似度
	var dotProduct, norm1, norm2 float64
	for i := 0; i < len(vector1); i++ {
		dotProduct += vector1[i] * vector2[i]
		norm1 += vector1[i] * vector1[i]
		norm2 += vector2[i] * vector2[i]
	}

	if norm1 == 0 || norm2 == 0 {
		return 0
	}

	return dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2))
}

// AddEntity 添加实体
func (kb *InMemoryKnowledgeBase) AddEntity(ctx context.Context, entity *Entity) error {
	if entity == nil {
		return errors.NewValidationError("invalid_input", "entity cannot be nil")
	}

	if entity.ID == "" {
		entity.ID = fmt.Sprintf("entity_%d", time.Now().UnixNano())
	}

	kb.mu.Lock()
	defer kb.mu.Unlock()

	kb.entities[entity.ID] = entity
	return nil
}

// GetEntity 获取实体
func (kb *InMemoryKnowledgeBase) GetEntity(ctx context.Context, id string) (*Entity, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	entity, exists := kb.entities[id]
	if !exists {
		return nil, errors.NewNotFoundError("entity_not_found", fmt.Sprintf("entity %s not found", id))
	}

	return entity, nil
}

// UpdateEntity 更新实体
func (kb *InMemoryKnowledgeBase) UpdateEntity(ctx context.Context, entity *Entity) error {
	kb.mu.Lock()
	defer kb.mu.Unlock()

	kb.entities[entity.ID] = entity
	return nil
}

// DeleteEntity 删除实体
func (kb *InMemoryKnowledgeBase) DeleteEntity(ctx context.Context, id string) error {
	kb.mu.Lock()
	defer kb.mu.Unlock()

	delete(kb.entities, id)
	return nil
}

// SearchEntities 搜索实体
func (kb *InMemoryKnowledgeBase) SearchEntities(ctx context.Context, query *EntityQuery) ([]*Entity, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	var results []*Entity
	for _, entity := range kb.entities {
		match := true

		// 按名称过滤
		if query.Name != "" && !strings.Contains(strings.ToLower(entity.Name), strings.ToLower(query.Name)) {
			match = false
		}

		// 按类型过滤
		if query.Type != "" && entity.Type != query.Type {
			match = false
		}

		if match {
			results = append(results, entity)
		}
	}

	// 应用offset和limit
	if query.Offset >= len(results) {
		return []*Entity{}, nil
	}

	end := len(results)
	if query.Limit > 0 && query.Offset+query.Limit < len(results) {
		end = query.Offset + query.Limit
	}

	return results[query.Offset:end], nil
}

// AddRelation 添加关系
func (kb *InMemoryKnowledgeBase) AddRelation(ctx context.Context, relation *Relation) error {
	if relation == nil {
		return errors.NewValidationError("invalid_input", "relation cannot be nil")
	}

	kb.mu.Lock()
	defer kb.mu.Unlock()

	// 验证实体是否存在
	if _, exists := kb.entities[relation.FromEntity]; !exists {
		return errors.NewValidationError("entity_not_found", fmt.Sprintf("from entity %s does not exist", relation.FromEntity))
	}
	if _, exists := kb.entities[relation.ToEntity]; !exists {
		return errors.NewValidationError("entity_not_found", fmt.Sprintf("to entity %s does not exist", relation.ToEntity))
	}

	if relation.ID == "" {
		relation.ID = fmt.Sprintf("rel_%d", time.Now().UnixNano())
	}

	relation.CreatedAt = time.Now()
	relation.UpdatedAt = time.Now()

	kb.relations[relation.ID] = relation
	return nil
}

// GetRelation 获取关系
func (kb *InMemoryKnowledgeBase) GetRelation(ctx context.Context, id string) (*Relation, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	relation, exists := kb.relations[id]
	if !exists {
		return nil, errors.NewNotFoundError("relation_not_found", fmt.Sprintf("relation %s not found", id))
	}

	return relation, nil
}

// UpdateRelation 更新关系
func (kb *InMemoryKnowledgeBase) UpdateRelation(ctx context.Context, relation *Relation) error {
	kb.mu.Lock()
	defer kb.mu.Unlock()

	kb.relations[relation.ID] = relation
	return nil
}

// DeleteRelation 删除关系
func (kb *InMemoryKnowledgeBase) DeleteRelation(ctx context.Context, id string) error {
	kb.mu.Lock()
	defer kb.mu.Unlock()

	delete(kb.relations, id)
	return nil
}

// SearchRelations 搜索关系
func (kb *InMemoryKnowledgeBase) SearchRelations(ctx context.Context, query *RelationQuery) ([]*Relation, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	var results []*Relation
	for _, relation := range kb.relations {
		results = append(results, relation)
	}

	return results, nil
}

// GetEntityRelations 获取实体的所有关系
func (kb *InMemoryKnowledgeBase) GetEntityRelations(ctx context.Context, entityID string) ([]*Relation, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	var results []*Relation
	for _, relation := range kb.relations {
		if relation.FromEntity == entityID || relation.ToEntity == entityID {
			results = append(results, relation)
		}
	}

	return results, nil
}

// GetRelatedEntities 获取相关实体
func (kb *InMemoryKnowledgeBase) GetRelatedEntities(ctx context.Context, entityID string, relationTypes []string) ([]*Entity, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	relatedEntityIDs := make(map[string]bool)

	for _, relation := range kb.relations {
		// 检查关系类型过滤
		if len(relationTypes) > 0 {
			found := false
			for _, relType := range relationTypes {
				if relation.Type == relType {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		// 找到相关实体
		if relation.FromEntity == entityID {
			relatedEntityIDs[relation.ToEntity] = true
		} else if relation.ToEntity == entityID {
			relatedEntityIDs[relation.FromEntity] = true
		}
	}

	var results []*Entity
	for entityID := range relatedEntityIDs {
		if entity, exists := kb.entities[entityID]; exists {
			results = append(results, entity)
		}
	}

	return results, nil
}

// FindPath 查找实体间的路径
func (kb *InMemoryKnowledgeBase) FindPath(ctx context.Context, fromEntityID, toEntityID string, maxDepth int) ([][]*Relation, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	if maxDepth <= 0 {
		maxDepth = 3 // 默认最大深度
	}

	// 简单的BFS路径查找
	type pathNode struct {
		entityID string
		path     []*Relation
		depth    int
	}

	queue := []*pathNode{{entityID: fromEntityID, path: []*Relation{}, depth: 0}}
	visited := make(map[string]bool)
	var paths [][]*Relation

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]

		if current.entityID == toEntityID && current.depth > 0 {
			paths = append(paths, current.path)
			continue
		}

		if current.depth >= maxDepth {
			continue
		}

		if visited[current.entityID] {
			continue
		}
		visited[current.entityID] = true

		// 查找所有相关关系
		for _, relation := range kb.relations {
			var nextEntityID string
			if relation.FromEntity == current.entityID {
				nextEntityID = relation.ToEntity
			} else if relation.ToEntity == current.entityID {
				nextEntityID = relation.FromEntity
			} else {
				continue
			}

			newPath := make([]*Relation, len(current.path)+1)
			copy(newPath, current.path)
			newPath[len(current.path)] = relation

			queue = append(queue, &pathNode{
				entityID: nextEntityID,
				path:     newPath,
				depth:    current.depth + 1,
			})
		}
	}

	return paths, nil
}

// GetStats 获取统计信息
func (kb *InMemoryKnowledgeBase) GetStats(ctx context.Context) (*KnowledgeBaseStats, error) {
	kb.mu.RLock()
	defer kb.mu.RUnlock()

	// 统计文档类型
	typeCounts := make(map[DocumentType]int)
	chunkCount := 0
	for _, doc := range kb.documents {
		typeCounts[doc.Type]++
		chunkCount += len(doc.Chunks)
	}

	// 统计实体类型
	entityTypes := make(map[string]int)
	for _, entity := range kb.entities {
		entityTypes[entity.Type]++
	}

	// 统计关系类型
	relationTypes := make(map[string]int)
	for _, relation := range kb.relations {
		relationTypes[relation.Type]++
	}

	return &KnowledgeBaseStats{
		DocumentCount: len(kb.documents),
		ChunkCount:    chunkCount,
		EntityCount:   len(kb.entities),
		RelationCount: len(kb.relations),
		TypeCounts:    typeCounts,
		EntityTypes:   entityTypes,
		RelationTypes: relationTypes,
		IndexSize:     0, // 简化实现
		LastUpdated:   time.Now(),
	}, nil
}

// RebuildIndex 重建索引
func (kb *InMemoryKnowledgeBase) RebuildIndex(ctx context.Context) error {
	return nil
}

// OptimizeIndex 优化索引
func (kb *InMemoryKnowledgeBase) OptimizeIndex(ctx context.Context) error {
	return nil
}
