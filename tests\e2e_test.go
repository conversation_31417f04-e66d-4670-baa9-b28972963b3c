//go:build integ

package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/distributed"
	"github.com/agentscope/agentscope-golang/pkg/knowledge"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/pipeline"
	"github.com/agentscope/agentscope-golang/pkg/tool"
	"github.com/agentscope/agentscope-golang/pkg/tool/builtin"
	"github.com/agentscope/agentscope-golang/pkg/web"
)

// TestCompleteConversationFlow tests a complete conversation flow from user input to agent response
func TestCompleteConversationFlow(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("DEEPSEEK_API_KEY not set, skipping E2E conversation test")
	}

	// Setup complete system
	system := setupCompleteSystem(t, apiKey)
	defer system.Cleanup()

	// Test conversation flow
	ctx := context.Background()

	// Step 1: User sends initial message
	userMsg := message.NewTextMessage("user", "Hello! I'm Alice. Can you help me with some calculations?")

	// Step 2: Agent processes message
	response, err := system.AssistantAgent.Reply(ctx, userMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Contains(t, strings.ToLower(response.Content().String()), "alice")

	t.Logf("Initial response: %s", response.Content().String())

	// Step 3: User asks for calculation
	calcMsg := message.NewTextMessage("user", "Please calculate 25 * 17 for me")

	response, err = system.AssistantAgent.Reply(ctx, calcMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)

	// Should contain the result (425) or show tool usage
	responseText := strings.ToLower(response.Content().String())
	assert.True(t, strings.Contains(responseText, "425") || strings.Contains(responseText, "calculator"))

	t.Logf("Calculation response: %s", response.Content().String())

	// Step 4: User asks about previous conversation (memory test)
	memoryMsg := message.NewTextMessage("user", "What's my name again?")

	response, err = system.AssistantAgent.Reply(ctx, memoryMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Contains(t, strings.ToLower(response.Content().String()), "alice")

	t.Logf("Memory response: %s", response.Content().String())
}

// TestMultiAgentWorkflow tests a workflow involving multiple agents
func TestMultiAgentWorkflow(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("DEEPSEEK_API_KEY not set, skipping E2E multi-agent test")
	}

	// Setup system with multiple agents
	system := setupMultiAgentSystem(t, apiKey)
	defer system.Cleanup()

	// Create a pipeline with multiple agents
	pipelineConfig := &pipeline.Config{
		Name:        "e2e-pipeline",
		Description: "End-to-end test pipeline",
	}

	seqPipeline := pipeline.NewSequentialPipeline(pipelineConfig, system.Logger)

	// Add agents to pipeline
	err := seqPipeline.AddAgent(system.ResearchAgent)
	require.NoError(t, err)

	err = seqPipeline.AddAgent(system.AnalysisAgent)
	require.NoError(t, err)

	// Execute pipeline with a complex task
	ctx := context.Background()
	taskMsg := message.NewTextMessage("user", "Research the topic of artificial intelligence and then analyze the key findings")

	result, err := seqPipeline.Execute(ctx, taskMsg)
	require.NoError(t, err)
	assert.NotNil(t, result)

	// Verify that the result contains analysis from both agents
	resultText := strings.ToLower(result.Content().String())
	assert.True(t, len(resultText) > 50, "Result should be substantial")

	t.Logf("Pipeline result: %s", result.Content().String())
}

// TestWebAPIEndToEnd tests the complete web API functionality
func TestWebAPIEndToEnd(t *testing.T) {
	// Setup web server with agents
	system := setupWebSystem(t)
	defer system.Cleanup()

	// Start web server
	server := httptest.NewServer(system.WebServer.Handler())
	defer server.Close()

	// Test health endpoint
	resp, err := http.Get(server.URL + "/health")
	require.NoError(t, err)
	defer resp.Body.Close()
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	// Test list agents endpoint
	resp, err = http.Get(server.URL + "/api/agents")
	require.NoError(t, err)
	defer resp.Body.Close()
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var agents []web.AgentInfo
	err = json.NewDecoder(resp.Body).Decode(&agents)
	require.NoError(t, err)
	assert.Len(t, agents, 1)
	assert.Equal(t, "web-assistant", agents[0].ID)

	// Test send message endpoint
	msgPayload := web.MessageRequest{
		Content: "Hello from web API test",
		Type:    "text",
	}

	msgJSON, err := json.Marshal(msgPayload)
	require.NoError(t, err)

	resp, err = http.Post(
		server.URL+"/api/agents/web-assistant/messages",
		"application/json",
		strings.NewReader(string(msgJSON)),
	)
	require.NoError(t, err)
	defer resp.Body.Close()
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var msgResponse web.MessageResponse
	err = json.NewDecoder(resp.Body).Decode(&msgResponse)
	require.NoError(t, err)
	assert.NotEmpty(t, msgResponse.ID)
	assert.NotEmpty(t, msgResponse.Content)

	t.Logf("Web API response: %s", msgResponse.Content)
}

// TestWebSocketEndToEnd tests WebSocket real-time communication
func TestWebSocketEndToEnd(t *testing.T) {
	// Setup web system
	system := setupWebSystem(t)
	defer system.Cleanup()

	// Start web server
	server := httptest.NewServer(system.WebServer.Handler())
	defer server.Close()

	// Convert HTTP URL to WebSocket URL
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"

	// Connect to WebSocket
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()

	// Send message via WebSocket
	wsMsg := map[string]interface{}{
		"type":    "message",
		"agent":   "web-assistant",
		"content": "Hello via WebSocket",
	}

	err = conn.WriteJSON(wsMsg)
	require.NoError(t, err)

	// Read response
	var response map[string]interface{}
	err = conn.ReadJSON(&response)
	require.NoError(t, err)

	assert.Equal(t, "response", response["type"])
	assert.NotEmpty(t, response["content"])

	t.Logf("WebSocket response: %v", response["content"])
}

// TestKnowledgeBaseEndToEnd tests knowledge base integration
func TestKnowledgeBaseEndToEnd(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("DEEPSEEK_API_KEY not set, skipping E2E knowledge base test")
	}

	// Setup system with knowledge base
	system := setupKnowledgeSystem(t, apiKey)
	defer system.Cleanup()

	ctx := context.Background()

	// Add documents to knowledge base
	docs := []*knowledge.Document{
		{
			ID:      "doc1",
			Title:   "AI Fundamentals",
			Content: "Artificial Intelligence is the simulation of human intelligence in machines.",
			Metadata: map[string]interface{}{
				"category": "technology",
				"author":   "AI Expert",
			},
		},
		{
			ID:      "doc2",
			Title:   "Machine Learning Basics",
			Content: "Machine Learning is a subset of AI that enables computers to learn without explicit programming.",
			Metadata: map[string]interface{}{
				"category": "technology",
				"author":   "ML Researcher",
			},
		},
	}

	for _, doc := range docs {
		err := system.KnowledgeBase.AddDocument(ctx, doc)
		require.NoError(t, err)
	}

	// Test knowledge-based conversation
	knowledgeMsg := message.NewTextMessage("user", "What is artificial intelligence?")

	response, err := system.AssistantAgent.Reply(ctx, knowledgeMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)

	// Response should contain information from the knowledge base
	responseText := strings.ToLower(response.Content().String())
	assert.True(t,
		strings.Contains(responseText, "artificial intelligence") ||
			strings.Contains(responseText, "simulation") ||
			strings.Contains(responseText, "machines"),
		"Response should contain knowledge base information")

	t.Logf("Knowledge-based response: %s", response.Content().String())
}

// TestDistributedSystemEndToEnd tests distributed system functionality
func TestDistributedSystemEndToEnd(t *testing.T) {
	// This test would require setting up multiple nodes
	// For now, we'll test the distributed components in isolation

	// Setup distributed system components
	system := setupDistributedSystem(t)
	defer system.Cleanup()

	ctx := context.Background()

	// Test service discovery
	service := &system.ServiceInfo{
		ID:      "test-service",
		Name:    "test-service",
		Address: "localhost:8080",
		Port:    8080,
		Status:  system.ServiceStatusHealthy,
	}

	err := system.Discovery.RegisterService(ctx, service)
	require.NoError(t, err)

	// Test service discovery
	discovered, err := system.Discovery.DiscoverServices(ctx, "test-service")
	require.NoError(t, err)
	assert.Len(t, discovered, 1)
	assert.Equal(t, "test-service", discovered[0].ID)

	// Test load balancer
	services := []*system.ServiceInfo{service}
	system.LoadBalancer.UpdateServices("test-service", services)

	selected, err := system.LoadBalancer.SelectService(ctx, "test-service")
	require.NoError(t, err)
	assert.Equal(t, "test-service", selected.ID)

	t.Logf("Distributed system test completed successfully")
}

// TestPerformanceEndToEnd tests system performance under load
func TestPerformanceEndToEnd(t *testing.T) {
	// Setup system for performance testing
	system := setupPerformanceSystem(t)
	defer system.Cleanup()

	ctx := context.Background()

	// Test concurrent message processing
	numConcurrent := 10
	numMessages := 5

	results := make(chan error, numConcurrent*numMessages)

	start := time.Now()

	for i := 0; i < numConcurrent; i++ {
		go func(workerID int) {
			for j := 0; j < numMessages; j++ {
				msg := message.NewTextMessage("user", fmt.Sprintf("Performance test message %d-%d", workerID, j))

				_, err := system.AssistantAgent.Reply(ctx, msg)
				results <- err
			}
		}(i)
	}

	// Collect results
	for i := 0; i < numConcurrent*numMessages; i++ {
		err := <-results
		assert.NoError(t, err)
	}

	duration := time.Since(start)

	// Performance assertions
	assert.Less(t, duration, 30*time.Second, "Performance test should complete within 30 seconds")

	messagesPerSecond := float64(numConcurrent*numMessages) / duration.Seconds()
	t.Logf("Performance: %.2f messages/second", messagesPerSecond)

	// Should handle at least 1 message per second
	assert.Greater(t, messagesPerSecond, 1.0, "Should process at least 1 message per second")
}

// System setup helpers

type CompleteSystem struct {
	AssistantAgent agent.Agent
	MemoryService  memory.MemoryService
	ToolRegistry   tool.Registry
	Logger         logger.Logger
	cleanup        []func()
}

func (s *CompleteSystem) Cleanup() {
	for _, fn := range s.cleanup {
		fn()
	}
}

func setupCompleteSystem(t *testing.T, apiKey string) *CompleteSystem {
	// Create logger
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create memory service
	memoryConfig := &memory.Config{
		Type:        memory.InMemoryType,
		MaxMemories: 100,
		TTL:         time.Hour,
	}
	memoryService, err := memory.NewMemoryService(memoryConfig)
	require.NoError(t, err)

	// Create tool registry
	toolRegistry := tool.NewDefaultRegistry()

	// Register built-in tools
	calcTool := builtin.NewCalculatorTool()
	err = toolRegistry.RegisterTool(calcTool)
	require.NoError(t, err)

	timeTool := builtin.NewTimeTool()
	err = toolRegistry.RegisterTool(timeTool)
	require.NoError(t, err)

	// Create LLM client
	llmConfig := &llm.DeepseekConfig{
		APIKey:  apiKey,
		Model:   "deepseek-chat",
		BaseURL: "https://api.deepseek.com/v1",
	}
	llmClient := llm.NewDeepseekClient(llmConfig)

	// Create assistant agent
	agentConfig := &agent.Config{
		ID:          "e2e-assistant",
		Name:        "E2E Assistant",
		Type:        agent.AssistantType,
		Description: "Assistant for end-to-end testing",
		LLMConfig: map[string]interface{}{
			"model":       "deepseek-chat",
			"temperature": 0.7,
		},
	}

	assistantAgent, err := agent.NewAssistantAgent(agentConfig, llmClient, logger)
	require.NoError(t, err)

	// Configure agent with memory and tools
	assistantAgent.SetMemoryService(memoryService)
	assistantAgent.SetToolRegistry(toolRegistry)

	// Initialize agent
	err = assistantAgent.Initialize()
	require.NoError(t, err)

	return &CompleteSystem{
		AssistantAgent: assistantAgent,
		MemoryService:  memoryService,
		ToolRegistry:   toolRegistry,
		Logger:         logger,
		cleanup: []func(){
			func() { assistantAgent.Shutdown() },
		},
	}
}

type MultiAgentSystem struct {
	ResearchAgent agent.Agent
	AnalysisAgent agent.Agent
	Logger        logger.Logger
	cleanup       []func()
}

func (s *MultiAgentSystem) Cleanup() {
	for _, fn := range s.cleanup {
		fn()
	}
}

func setupMultiAgentSystem(t *testing.T, apiKey string) *MultiAgentSystem {
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create LLM client
	llmConfig := &llm.DeepseekConfig{
		APIKey:  apiKey,
		Model:   "deepseek-chat",
		BaseURL: "https://api.deepseek.com/v1",
	}
	llmClient := llm.NewDeepseekClient(llmConfig)

	// Create research agent
	researchConfig := &agent.Config{
		ID:          "research-agent",
		Name:        "Research Agent",
		Type:        agent.AssistantType,
		Description: "Agent specialized in research tasks",
	}
	researchAgent, err := agent.NewAssistantAgent(researchConfig, llmClient, logger)
	require.NoError(t, err)

	// Create analysis agent
	analysisConfig := &agent.Config{
		ID:          "analysis-agent",
		Name:        "Analysis Agent",
		Type:        agent.AssistantType,
		Description: "Agent specialized in analysis tasks",
	}
	analysisAgent, err := agent.NewAssistantAgent(analysisConfig, llmClient, logger)
	require.NoError(t, err)

	// Initialize agents
	err = researchAgent.Initialize()
	require.NoError(t, err)

	err = analysisAgent.Initialize()
	require.NoError(t, err)

	return &MultiAgentSystem{
		ResearchAgent: researchAgent,
		AnalysisAgent: analysisAgent,
		Logger:        logger,
		cleanup: []func(){
			func() { researchAgent.Shutdown() },
			func() { analysisAgent.Shutdown() },
		},
	}
}

type WebSystem struct {
	WebServer      *web.Server
	AssistantAgent agent.Agent
	Logger         logger.Logger
	cleanup        []func()
}

func (s *WebSystem) Cleanup() {
	for _, fn := range s.cleanup {
		fn()
	}
}

func setupWebSystem(t *testing.T) *WebSystem {
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create mock LLM client for web testing
	mockLLM := &MockLLMClient{}

	// Create agent
	agentConfig := &agent.Config{
		ID:          "web-assistant",
		Name:        "Web Assistant",
		Type:        agent.AssistantType,
		Description: "Assistant for web API testing",
	}
	assistantAgent, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
	require.NoError(t, err)

	err = assistantAgent.Initialize()
	require.NoError(t, err)

	// Create agent manager
	agentManager := agent.NewManager()
	err = agentManager.AddAgent(assistantAgent)
	require.NoError(t, err)

	// Create web server configuration
	webConfig := &config.Config{
		Web: config.WebConfig{
			Host: "localhost",
			Port: 0, // Use random port for testing
			CORS: config.CORSConfig{
				AllowedOrigins: []string{"*"},
				AllowedMethods: []string{"GET", "POST", "PUT", "DELETE"},
				AllowedHeaders: []string{"Content-Type", "Authorization"},
			},
		},
	}

	// Create web server
	webServer := web.NewServer(webConfig, agentManager, logger)

	return &WebSystem{
		WebServer:      webServer,
		AssistantAgent: assistantAgent,
		Logger:         logger,
		cleanup: []func(){
			func() { assistantAgent.Shutdown() },
		},
	}
}

type KnowledgeSystem struct {
	AssistantAgent agent.Agent
	KnowledgeBase  knowledge.KnowledgeBase
	Logger         logger.Logger
	cleanup        []func()
}

func (s *KnowledgeSystem) Cleanup() {
	for _, fn := range s.cleanup {
		fn()
	}
}

func setupKnowledgeSystem(t *testing.T, apiKey string) *KnowledgeSystem {
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create knowledge base
	kbConfig := &knowledge.Config{
		Type: knowledge.InMemoryType,
	}
	kb, err := knowledge.NewKnowledgeBase(kbConfig)
	require.NoError(t, err)

	// Create LLM client
	llmConfig := &llm.DeepseekConfig{
		APIKey:  apiKey,
		Model:   "deepseek-chat",
		BaseURL: "https://api.deepseek.com/v1",
	}
	llmClient := llm.NewDeepseekClient(llmConfig)

	// Create assistant agent
	agentConfig := &agent.Config{
		ID:          "knowledge-assistant",
		Name:        "Knowledge Assistant",
		Type:        agent.AssistantType,
		Description: "Assistant with knowledge base integration",
	}
	assistantAgent, err := agent.NewAssistantAgent(agentConfig, llmClient, logger)
	require.NoError(t, err)

	// Set knowledge base
	assistantAgent.SetKnowledgeBase(kb)

	err = assistantAgent.Initialize()
	require.NoError(t, err)

	return &KnowledgeSystem{
		AssistantAgent: assistantAgent,
		KnowledgeBase:  kb,
		Logger:         logger,
		cleanup: []func(){
			func() { assistantAgent.Shutdown() },
		},
	}
}

type DistributedSystem struct {
	Discovery            *distributed.InMemoryServiceDiscovery
	LoadBalancer         distributed.LoadBalancer
	ServiceInfo          *distributed.ServiceInfo
	ServiceStatusHealthy distributed.ServiceStatus
	cleanup              []func()
}

func (s *DistributedSystem) Cleanup() {
	for _, fn := range s.cleanup {
		fn()
	}
}

func setupDistributedSystem(t *testing.T) *DistributedSystem {
	// This is a simplified setup for testing distributed components
	// In a real distributed system, these would be separate services

	discovery := distributed.NewInMemoryServiceDiscovery()
	loadBalancer := distributed.NewRoundRobinLoadBalancer()

	return &DistributedSystem{
		Discovery:            discovery,
		LoadBalancer:         loadBalancer,
		ServiceStatusHealthy: distributed.ServiceStatusHealthy,
		cleanup:              []func(){},
	}
}

type PerformanceSystem struct {
	AssistantAgent agent.Agent
	Logger         logger.Logger
	cleanup        []func()
}

func (s *PerformanceSystem) Cleanup() {
	for _, fn := range s.cleanup {
		fn()
	}
}

func setupPerformanceSystem(t *testing.T) *PerformanceSystem {
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Use mock LLM for performance testing to avoid API rate limits
	mockLLM := &MockLLMClient{}

	agentConfig := &agent.Config{
		ID:          "perf-assistant",
		Name:        "Performance Assistant",
		Type:        agent.AssistantType,
		Description: "Assistant for performance testing",
	}
	assistantAgent, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
	require.NoError(t, err)

	err = assistantAgent.Initialize()
	require.NoError(t, err)

	return &PerformanceSystem{
		AssistantAgent: assistantAgent,
		Logger:         logger,
		cleanup: []func(){
			func() { assistantAgent.Shutdown() },
		},
	}
}
