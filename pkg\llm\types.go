package llm

import (
	"context"
	"time"
)

// LLMClient is the interface that all LLM clients must implement
type LLMClient interface {
	// Generate generates a response for the given request
	Generate(ctx context.Context, request *GenerateRequest) (*GenerateResponse, error)

	// GenerateStream generates a streaming response for the given request
	GenerateStream(ctx context.Context, request *GenerateRequest) (<-chan *StreamResponse, error)

	// GenerateWithTools generates a response with tool calling support
	GenerateWithTools(ctx context.Context, request *GenerateRequest, tools []ToolDefinition) (*GenerateResponse, error)

	// GenerateMultiModal generates a response for multi-modal input
	GenerateMultiModal(ctx context.Context, request *MultiModalGenerateRequest) (*GenerateResponse, error)

	// GetModelInfo returns information about the model
	GetModelInfo() *ModelInfo

	// Close closes the client and releases resources
	Close() error
}

// GenerateRequest represents a request to generate text
type GenerateRequest struct {
	Messages    []*ChatMessage         `json:"messages"`
	Model       string                 `json:"model,omitempty"`
	Temperature *float64               `json:"temperature,omitempty"`
	MaxTokens   *int                   `json:"max_tokens,omitempty"`
	TopP        *float64               `json:"top_p,omitempty"`
	TopK        *int                   `json:"top_k,omitempty"`
	Stop        []string               `json:"stop,omitempty"`
	Stream      bool                   `json:"stream,omitempty"`
	Tools       []*Tool                `json:"tools,omitempty"`
	ToolChoice  interface{}            `json:"tool_choice,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// GenerateResponse represents a response from text generation
type GenerateResponse struct {
	ID      string     `json:"id"`
	Object  string     `json:"object"`
	Created int64      `json:"created"`
	Model   string     `json:"model"`
	Choices []*Choice  `json:"choices"`
	Usage   *Usage     `json:"usage"`
	Error   *ErrorInfo `json:"error,omitempty"`
}

// StreamResponse represents a streaming response chunk
type StreamResponse struct {
	ID      string          `json:"id"`
	Object  string          `json:"object"`
	Created int64           `json:"created"`
	Model   string          `json:"model"`
	Choices []*StreamChoice `json:"choices"`
	Usage   *Usage          `json:"usage,omitempty"`
	Error   *ErrorInfo      `json:"error,omitempty"`
	Done    bool            `json:"done"`
}

// ChatMessage represents a message in the chat
type ChatMessage struct {
	Role       string      `json:"role"`
	Content    interface{} `json:"content"`
	Name       string      `json:"name,omitempty"`
	ToolCalls  []*ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string      `json:"tool_call_id,omitempty"`
}

// Choice represents a choice in the response
type Choice struct {
	Index        int          `json:"index"`
	Message      *ChatMessage `json:"message"`
	FinishReason string       `json:"finish_reason"`
	Logprobs     interface{}  `json:"logprobs,omitempty"`
}

// StreamChoice represents a choice in the streaming response
type StreamChoice struct {
	Index        int          `json:"index"`
	Delta        *ChatMessage `json:"delta"`
	FinishReason string       `json:"finish_reason"`
	Logprobs     interface{}  `json:"logprobs,omitempty"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// Tool represents a tool that can be called by the LLM
type Tool struct {
	Type     string        `json:"type"`
	Function *ToolFunction `json:"function"`
}

// ToolFunction represents a function tool
type ToolFunction struct {
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	Parameters  interface{} `json:"parameters,omitempty"`
}

// ToolCall represents a tool call made by the LLM
type ToolCall struct {
	ID       string            `json:"id"`
	Type     string            `json:"type"`
	Function *ToolCallFunction `json:"function"`
}

// ToolCallFunction represents a function call
type ToolCallFunction struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// ErrorInfo represents error information from the LLM service
type ErrorInfo struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// ModelInfo represents information about a model
type ModelInfo struct {
	ID           string   `json:"id"`
	Object       string   `json:"object"`
	Created      int64    `json:"created"`
	OwnedBy      string   `json:"owned_by"`
	MaxTokens    int      `json:"max_tokens,omitempty"`
	Description  string   `json:"description,omitempty"`
	Capabilities []string `json:"capabilities,omitempty"`
}

// Config holds LLM client configuration
type Config struct {
	Provider   string                 `yaml:"provider" json:"provider"`
	APIKey     string                 `yaml:"api_key" json:"api_key"`
	BaseURL    string                 `yaml:"base_url" json:"base_url"`
	Model      string                 `yaml:"model" json:"model"`
	Timeout    time.Duration          `yaml:"timeout" json:"timeout"`
	MaxRetries int                    `yaml:"max_retries" json:"max_retries"`
	Parameters map[string]interface{} `yaml:"parameters" json:"parameters"`
}

// DefaultConfig returns a default LLM configuration
func DefaultConfig() *Config {
	return &Config{
		Provider:   "deepseek",
		Model:      "deepseek-chat",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		Parameters: make(map[string]interface{}),
	}
}

// Validate validates the LLM configuration
func (c *Config) Validate() error {
	if c.Provider == "" {
		return &ValidationError{Field: "provider", Message: "provider cannot be empty"}
	}
	if c.APIKey == "" {
		return &ValidationError{Field: "api_key", Message: "API key cannot be empty"}
	}
	if c.Model == "" {
		return &ValidationError{Field: "model", Message: "model cannot be empty"}
	}
	if c.Timeout <= 0 {
		c.Timeout = 30 * time.Second
	}
	if c.MaxRetries < 0 {
		c.MaxRetries = 3
	}
	if c.Parameters == nil {
		c.Parameters = make(map[string]interface{})
	}
	return nil
}

// Clone creates a deep copy of the configuration
func (c *Config) Clone() *Config {
	clone := *c

	// Deep copy Parameters map
	if c.Parameters != nil {
		clone.Parameters = make(map[string]interface{})
		for k, v := range c.Parameters {
			clone.Parameters[k] = v
		}
	}

	return &clone
}

// NewGenerateRequest creates a new generate request
func NewGenerateRequest(messages []*ChatMessage) *GenerateRequest {
	return &GenerateRequest{
		Messages:   messages,
		Parameters: make(map[string]interface{}),
	}
}

// AddMessage adds a message to the request
func (gr *GenerateRequest) AddMessage(role, content string) {
	gr.Messages = append(gr.Messages, &ChatMessage{
		Role:    role,
		Content: content,
	})
}

// AddSystemMessage adds a system message to the request
func (gr *GenerateRequest) AddSystemMessage(content string) {
	gr.AddMessage("system", content)
}

// AddUserMessage adds a user message to the request
func (gr *GenerateRequest) AddUserMessage(content string) {
	gr.AddMessage("user", content)
}

// AddAssistantMessage adds an assistant message to the request
func (gr *GenerateRequest) AddAssistantMessage(content string) {
	gr.AddMessage("assistant", content)
}

// SetTemperature sets the temperature parameter
func (gr *GenerateRequest) SetTemperature(temperature float64) {
	gr.Temperature = &temperature
}

// SetMaxTokens sets the max tokens parameter
func (gr *GenerateRequest) SetMaxTokens(maxTokens int) {
	gr.MaxTokens = &maxTokens
}

// SetTopP sets the top-p parameter
func (gr *GenerateRequest) SetTopP(topP float64) {
	gr.TopP = &topP
}

// SetTopK sets the top-k parameter
func (gr *GenerateRequest) SetTopK(topK int) {
	gr.TopK = &topK
}

// SetStop sets the stop sequences
func (gr *GenerateRequest) SetStop(stop []string) {
	gr.Stop = stop
}

// SetStream sets the streaming flag
func (gr *GenerateRequest) SetStream(stream bool) {
	gr.Stream = stream
}

// SetParameter sets a custom parameter
func (gr *GenerateRequest) SetParameter(key string, value interface{}) {
	if gr.Parameters == nil {
		gr.Parameters = make(map[string]interface{})
	}
	gr.Parameters[key] = value
}

// GetParameter gets a custom parameter
func (gr *GenerateRequest) GetParameter(key string) (interface{}, bool) {
	if gr.Parameters == nil {
		return nil, false
	}
	value, exists := gr.Parameters[key]
	return value, exists
}

// NewChatMessage creates a new chat message
func NewChatMessage(role, content string) *ChatMessage {
	return &ChatMessage{
		Role:    role,
		Content: content,
	}
}

// NewSystemMessage creates a new system message
func NewSystemMessage(content string) *ChatMessage {
	return NewChatMessage("system", content)
}

// NewUserMessage creates a new user message
func NewUserMessage(content string) *ChatMessage {
	return NewChatMessage("user", content)
}

// NewAssistantMessage creates a new assistant message
func NewAssistantMessage(content string) *ChatMessage {
	return NewChatMessage("assistant", content)
}

// IsSuccess returns true if the response is successful
func (gr *GenerateResponse) IsSuccess() bool {
	return gr.Error == nil
}

// GetContent returns the content of the first choice
func (gr *GenerateResponse) GetContent() string {
	if len(gr.Choices) > 0 && gr.Choices[0].Message != nil {
		if content, ok := gr.Choices[0].Message.Content.(string); ok {
			return content
		}
	}
	return ""
}

// GetFinishReason returns the finish reason of the first choice
func (gr *GenerateResponse) GetFinishReason() string {
	if len(gr.Choices) > 0 {
		return gr.Choices[0].FinishReason
	}
	return ""
}

// HasToolCalls returns true if the response contains tool calls
func (gr *GenerateResponse) HasToolCalls() bool {
	if len(gr.Choices) > 0 && gr.Choices[0].Message != nil {
		return len(gr.Choices[0].Message.ToolCalls) > 0
	}
	return false
}

// GetToolCalls returns the tool calls from the first choice
func (gr *GenerateResponse) GetToolCalls() []*ToolCall {
	if len(gr.Choices) > 0 && gr.Choices[0].Message != nil {
		return gr.Choices[0].Message.ToolCalls
	}
	return nil
}

// IsSuccess returns true if the stream response is successful
func (sr *StreamResponse) IsSuccess() bool {
	return sr.Error == nil
}

// GetContent returns the content delta from the first choice
func (sr *StreamResponse) GetContent() string {
	if len(sr.Choices) > 0 && sr.Choices[0].Delta != nil {
		if content, ok := sr.Choices[0].Delta.Content.(string); ok {
			return content
		}
	}
	return ""
}

// GetFinishReason returns the finish reason of the first choice
func (sr *StreamResponse) GetFinishReason() string {
	if len(sr.Choices) > 0 {
		return sr.Choices[0].FinishReason
	}
	return ""
}

// ConvertFromMessage converts an AgentScope message to LLM chat messages
// 注意：此函数已移除以避免循环导入，请在需要时在应用层实现转换逻辑
// func ConvertFromMessage(msg *message.Message) []*ChatMessage

// ConvertToMessage converts LLM chat messages to an AgentScope message
// 注意：此函数已移除以避免循环导入，请在需要时在应用层实现转换逻辑
// func ConvertToMessage(messages []*ChatMessage, sender, receiver string) *message.Message

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}

// ToolDefinition 工具定义
type ToolDefinition struct {
	Type     string                  `json:"type"`
	Function *ToolFunctionDefinition `json:"function"`
}

// ToolFunctionDefinition 工具函数定义
type ToolFunctionDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// MultiModalGenerateRequest 多模态生成请求
type MultiModalGenerateRequest struct {
	Messages    []*MultiModalMessage   `json:"messages"`
	Model       string                 `json:"model,omitempty"`
	Temperature *float64               `json:"temperature,omitempty"`
	MaxTokens   *int                   `json:"max_tokens,omitempty"`
	TopP        *float64               `json:"top_p,omitempty"`
	TopK        *int                   `json:"top_k,omitempty"`
	Stop        []string               `json:"stop,omitempty"`
	Stream      bool                   `json:"stream,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// MultiModalMessage 多模态消息
type MultiModalMessage struct {
	Role    string              `json:"role"`
	Content []MultiModalContent `json:"content"`
}

// MultiModalContent LLM多模态内容
type MultiModalContent struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
	AudioURL *AudioURL `json:"audio_url,omitempty"`
	VideoURL *VideoURL `json:"video_url,omitempty"`
	FileURL  *FileURL  `json:"file_url,omitempty"`
}

// ImageURL 图像URL
type ImageURL struct {
	URL    string `json:"url"`
	Detail string `json:"detail,omitempty"` // "low", "high", "auto"
}

// AudioURL 音频URL
type AudioURL struct {
	URL    string `json:"url"`
	Format string `json:"format,omitempty"`
}

// VideoURL 视频URL
type VideoURL struct {
	URL    string `json:"url"`
	Format string `json:"format,omitempty"`
}

// FileURL 文件URL
type FileURL struct {
	URL      string `json:"url"`
	FileName string `json:"file_name,omitempty"`
	MimeType string `json:"mime_type,omitempty"`
}
