//go:build ignore
// +build ignore

package web

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/pipeline"
)

func TestNewWebServer(t *testing.T) {
	cfg := &config.Config{
		Web: &config.WebConfig{
			Host: "localhost",
			Port: 8080,
		},
	}

	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})

	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)
	assert.NotNil(t, server)
	assert.Equal(t, cfg.Web, server.config)
	assert.Equal(t, agentMgr, server.agentMgr)
	assert.Equal(t, pipelineMgr, server.pipelineMgr)
}

func TestServerRoutes(t *testing.T) {
	cfg := &config.Config{
		Web: &config.WebConfig{
			Host: "localhost",
			Port: 8080,
		},
	}

	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	router := server.setupRoutes()
	assert.NotNil(t, router)

	// Test that routes are registered
	req := httptest.NewRequest("GET", "/health", nil)
	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)
	assert.Equal(t, http.StatusOK, rr.Code)
}

func TestHealthHandler(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	req := httptest.NewRequest("GET", "/health", nil)
	rr := httptest.NewRecorder()

	server.healthHandler(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	var response map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "healthy", response["status"])
}

func TestListAgentsHandler(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	// Create and add a test agent
	testAgent := &agent.BaseAgent{}
	testAgent.SetID("test-agent")
	testAgent.SetName("Test Agent")
	agentMgr.AddAgent(testAgent)

	req := httptest.NewRequest("GET", "/api/agents", nil)
	rr := httptest.NewRecorder()

	server.listAgentsHandler(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	var response []AgentInfo
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Len(t, response, 1)
	assert.Equal(t, "test-agent", response[0].ID)
	assert.Equal(t, "Test Agent", response[0].Name)
}

func TestGetAgentHandler(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	// Create and add a test agent
	testAgent := &agent.BaseAgent{}
	testAgent.SetID("test-agent")
	testAgent.SetName("Test Agent")
	agentMgr.AddAgent(testAgent)

	// Test existing agent
	req := httptest.NewRequest("GET", "/api/agents/test-agent", nil)
	req = mux.SetURLVars(req, map[string]string{"id": "test-agent"})
	rr := httptest.NewRecorder()

	server.getAgentHandler(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	var response AgentInfo
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "test-agent", response.ID)

	// Test non-existent agent
	req = httptest.NewRequest("GET", "/api/agents/non-existent", nil)
	req = mux.SetURLVars(req, map[string]string{"id": "non-existent"})
	rr = httptest.NewRecorder()

	server.getAgentHandler(rr, req)
	assert.Equal(t, http.StatusNotFound, rr.Code)
}

func TestSendMessageHandler(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	// Create and add a test agent
	testAgent := &agent.BaseAgent{}
	testAgent.SetID("test-agent")
	testAgent.SetName("Test Agent")
	agentMgr.AddAgent(testAgent)

	// Test valid message
	msgReq := MessageRequest{
		Content: "Hello, agent!",
		Type:    "text",
	}
	body, _ := json.Marshal(msgReq)

	req := httptest.NewRequest("POST", "/api/agents/test-agent/messages", bytes.NewBuffer(body))
	req = mux.SetURLVars(req, map[string]string{"id": "test-agent"})
	req.Header.Set("Content-Type", "application/json")
	rr := httptest.NewRecorder()

	server.sendMessageHandler(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	var response MessageResponse
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.NotEmpty(t, response.ID)

	// Test invalid JSON
	req = httptest.NewRequest("POST", "/api/agents/test-agent/messages", bytes.NewBufferString("invalid json"))
	req = mux.SetURLVars(req, map[string]string{"id": "test-agent"})
	req.Header.Set("Content-Type", "application/json")
	rr = httptest.NewRecorder()

	server.sendMessageHandler(rr, req)
	assert.Equal(t, http.StatusBadRequest, rr.Code)

	// Test non-existent agent
	req = httptest.NewRequest("POST", "/api/agents/non-existent/messages", bytes.NewBuffer(body))
	req = mux.SetURLVars(req, map[string]string{"id": "non-existent"})
	req.Header.Set("Content-Type", "application/json")
	rr = httptest.NewRecorder()

	server.sendMessageHandler(rr, req)
	assert.Equal(t, http.StatusNotFound, rr.Code)
}

func TestGetMessagesHandler(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	// Create and add a test agent
	testAgent := &agent.BaseAgent{}
	testAgent.SetID("test-agent")
	testAgent.SetName("Test Agent")
	agentMgr.AddAgent(testAgent)

	req := httptest.NewRequest("GET", "/api/agents/test-agent/messages", nil)
	req = mux.SetURLVars(req, map[string]string{"id": "test-agent"})
	rr := httptest.NewRecorder()

	server.getMessagesHandler(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	var response []message.Message
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	// Should return empty array for new agent
	assert.Len(t, response, 0)
}

func TestCORSMiddleware(t *testing.T) {
	cfg := &config.Config{
		Web: &config.WebConfig{
			CORS: config.CORSConfig{
				AllowedOrigins: []string{"http://localhost:3000"},
				AllowedMethods: []string{"GET", "POST"},
				AllowedHeaders: []string{"Content-Type"},
			},
		},
	}

	middleware := NewCORSMiddleware(cfg.Web.CORS)

	handler := middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	assert.Equal(t, "http://localhost:3000", rr.Header().Get("Access-Control-Allow-Origin"))
	assert.Equal(t, "GET, POST", rr.Header().Get("Access-Control-Allow-Methods"))
	assert.Equal(t, "Content-Type", rr.Header().Get("Access-Control-Allow-Headers"))
}

func TestRateLimitMiddleware(t *testing.T) {
	middleware := NewRateLimitMiddleware(2, time.Second) // 2 requests per second

	handler := middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	// First request should succeed
	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()
	handler.ServeHTTP(rr, req)
	assert.Equal(t, http.StatusOK, rr.Code)

	// Second request should succeed
	req = httptest.NewRequest("GET", "/test", nil)
	rr = httptest.NewRecorder()
	handler.ServeHTTP(rr, req)
	assert.Equal(t, http.StatusOK, rr.Code)

	// Third request should be rate limited
	req = httptest.NewRequest("GET", "/test", nil)
	rr = httptest.NewRecorder()
	handler.ServeHTTP(rr, req)
	assert.Equal(t, http.StatusTooManyRequests, rr.Code)
}

func TestServerStartStop(t *testing.T) {
	cfg := &config.Config{
		Web: &config.WebConfig{
			Host: "localhost",
			Port: 0, // Use random port
		},
	}

	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	// Start server in goroutine
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		err := server.Start(ctx)
		if err != nil && err != http.ErrServerClosed {
			t.Errorf("Server start error: %v", err)
		}
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Stop server
	err := server.Stop()
	assert.NoError(t, err)
}

func TestErrorResponse(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	rr := httptest.NewRecorder()
	server.errorResponse(rr, http.StatusBadRequest, "test error")

	assert.Equal(t, http.StatusBadRequest, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	var response map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "test error", response["error"])
}

func TestJSONResponse(t *testing.T) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	data := map[string]string{"message": "test"}
	rr := httptest.NewRecorder()
	server.jsonResponse(rr, http.StatusOK, data)

	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "test", response["message"])
}

func BenchmarkHealthHandler(b *testing.B) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	req := httptest.NewRequest("GET", "/health", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rr := httptest.NewRecorder()
		server.healthHandler(rr, req)
	}
}

func BenchmarkListAgentsHandler(b *testing.B) {
	cfg := &config.Config{}
	agentMgr := agent.NewManager()
	pipelineMgr := pipeline.NewManager()
	loggerInstance, _ := logger.NewLogger(&logger.Config{Level: "info"})
	server := NewWebServer(cfg.Web, agentMgr, pipelineMgr)

	// Add some test agents
	for i := 0; i < 10; i++ {
		testAgent := &agent.BaseAgent{}
		testAgent.SetID(fmt.Sprintf("agent-%d", i))
		testAgent.SetName(fmt.Sprintf("Agent %d", i))
		agentMgr.AddAgent(testAgent)
	}

	req := httptest.NewRequest("GET", "/api/agents", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rr := httptest.NewRecorder()
		server.listAgentsHandler(rr, req)
	}
}
