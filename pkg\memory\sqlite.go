package memory

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/message"
	_ "modernc.org/sqlite"
)

// SQLiteStore SQLite实现的记忆存储
type SQLiteStore struct {
	db *sql.DB
}

// SQLiteConfig SQLite存储配置
type SQLiteConfig struct {
	DatabasePath string // 数据库文件路径，":memory:" 表示内存数据库
}

// NewSQLiteStore 创建SQLite记忆存储
func NewSQLiteStore(config *SQLiteConfig) (*SQLiteStore, error) {
	if config == nil {
		config = &SQLiteConfig{
			DatabasePath: ":memory:",
		}
	}

	if config.DatabasePath == "" {
		config.DatabasePath = ":memory:"
	}

	db, err := sql.Open("sqlite", config.DatabasePath)
	if err != nil {
		return nil, fmt.Errorf("打开SQLite数据库失败: %w", err)
	}

	store := &SQLiteStore{
		db: db,
	}

	if err := store.initTables(); err != nil {
		db.Close()
		return nil, fmt.Errorf("初始化数据库表失败: %w", err)
	}

	return store, nil
}

// initTables 初始化数据库表
func (s *SQLiteStore) initTables() error {
	// 创建消息表
	createMessagesTable := `
	CREATE TABLE IF NOT EXISTS messages (
		id TEXT PRIMARY KEY,
		session_id TEXT NOT NULL,
		role TEXT NOT NULL,
		content TEXT NOT NULL,
		metadata TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);
	CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);
	CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);`

	// 创建向量表（支持向量存储和相似度搜索）
	createVectorsTable := `
	CREATE TABLE IF NOT EXISTS vectors (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		session_id TEXT NOT NULL,
		message_id TEXT NOT NULL,
		vector_data TEXT NOT NULL, -- JSON格式存储向量数据
		vector_dimension INTEGER NOT NULL, -- 向量维度
		metadata TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(session_id, message_id)
	);
	CREATE INDEX IF NOT EXISTS idx_vectors_session_id ON vectors(session_id);
	CREATE INDEX IF NOT EXISTS idx_vectors_message_id ON vectors(message_id);
	CREATE INDEX IF NOT EXISTS idx_vectors_dimension ON vectors(vector_dimension);`

	if _, err := s.db.Exec(createMessagesTable); err != nil {
		return fmt.Errorf("创建消息表失败: %w", err)
	}

	if _, err := s.db.Exec(createVectorsTable); err != nil {
		return fmt.Errorf("创建向量表失败: %w", err)
	}

	return nil
}

// Save 保存消息到记忆
func (s *SQLiteStore) Save(ctx context.Context, sessionID string, msg *message.Message) error {
	if msg == nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息不能为空",
		}
	}

	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if err := msg.Validate(); err != nil {
		return &ValidationError{
			Field:   "message",
			Message: "消息验证失败: " + err.Error(),
		}
	}

	// 序列化元数据
	var metadataJSON string
	if msg.Metadata != nil {
		metadataBytes, err := json.Marshal(msg.Metadata)
		if err != nil {
			return fmt.Errorf("序列化消息元数据失败: %w", err)
		}
		metadataJSON = string(metadataBytes)
	}

	// 插入消息
	query := `
	INSERT INTO messages (id, session_id, role, content, metadata)
	VALUES (?, ?, ?, ?, ?)`

	_, err := s.db.ExecContext(ctx, query, msg.ID, sessionID, msg.Role, msg.GetContentString(), metadataJSON)
	if err != nil {
		return fmt.Errorf("保存消息到数据库失败: %w", err)
	}

	return nil
}

// Load 加载指定会话的消息历史
func (s *SQLiteStore) Load(ctx context.Context, sessionID string, limit int) ([]*message.Message, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	var query string
	var args []any

	if limit <= 0 {
		query = `
		SELECT id, role, content, metadata, created_at
		FROM messages
		WHERE session_id = ?
		ORDER BY created_at ASC`
		args = []any{sessionID}
	} else {
		// 先获取总数，然后使用OFFSET获取最后limit条记录
		countQuery := "SELECT COUNT(*) FROM messages WHERE session_id = ?"
		var totalCount int
		err := s.db.QueryRowContext(ctx, countQuery, sessionID).Scan(&totalCount)
		if err != nil {
			return nil, fmt.Errorf("查询消息总数失败: %w", err)
		}

		offset := totalCount - limit
		if offset < 0 {
			offset = 0
		}

		query = `
		SELECT id, role, content, metadata, created_at
		FROM messages
		WHERE session_id = ?
		ORDER BY created_at ASC
		LIMIT ? OFFSET ?`
		args = []any{sessionID, limit, offset}
	}

	rows, err := s.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询消息失败: %w", err)
	}
	defer rows.Close()

	var messages []*message.Message
	for rows.Next() {
		var id, role, content, metadataJSON string
		var createdAt time.Time

		if err := rows.Scan(&id, &role, &content, &metadataJSON, &createdAt); err != nil {
			return nil, fmt.Errorf("扫描消息行失败: %w", err)
		}

		// 反序列化元数据
		var metadata map[string]any
		if metadataJSON != "" {
			if err := json.Unmarshal([]byte(metadataJSON), &metadata); err != nil {
				return nil, fmt.Errorf("反序列化消息元数据失败: %w", err)
			}
		}

		// 创建消息对象
		msg := &message.Message{
			ID:       id,
			Role:     role,
			Content:  content,
			Metadata: metadata,
		}

		messages = append(messages, msg)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历消息行失败: %w", err)
	}

	return messages, nil
}

// LoadAll 加载指定会话的所有消息
func (s *SQLiteStore) LoadAll(ctx context.Context, sessionID string) ([]*message.Message, error) {
	return s.Load(ctx, sessionID, 0)
}

// Delete 删除指定会话的消息
func (s *SQLiteStore) Delete(ctx context.Context, sessionID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 删除消息
	_, err = tx.ExecContext(ctx, "DELETE FROM messages WHERE session_id = ?", sessionID)
	if err != nil {
		return fmt.Errorf("删除会话消息失败: %w", err)
	}

	// 删除向量
	_, err = tx.ExecContext(ctx, "DELETE FROM vectors WHERE session_id = ?", sessionID)
	if err != nil {
		return fmt.Errorf("删除会话向量失败: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// DeleteMessage 删除指定消息
func (s *SQLiteStore) DeleteMessage(ctx context.Context, sessionID, messageID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if messageID == "" {
		return &ValidationError{
			Field:   "messageID",
			Message: "消息ID不能为空",
		}
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 删除消息
	_, err = tx.ExecContext(ctx, "DELETE FROM messages WHERE session_id = ? AND id = ?", sessionID, messageID)
	if err != nil {
		return fmt.Errorf("删除消息失败: %w", err)
	}

	// 删除对应的向量
	_, err = tx.ExecContext(ctx, "DELETE FROM vectors WHERE session_id = ? AND message_id = ?", sessionID, messageID)
	if err != nil {
		return fmt.Errorf("删除消息向量失败: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// Search 搜索消息（基于内容）
func (s *SQLiteStore) Search(ctx context.Context, sessionID, query string, limit int) ([]*message.Message, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	if query == "" {
		return []*message.Message{}, nil
	}

	sqlQuery := `
	SELECT id, role, content, metadata, created_at
	FROM messages
	WHERE session_id = ? AND content LIKE ?
	ORDER BY created_at ASC`

	var args []any
	args = append(args, sessionID, "%"+query+"%")

	if limit > 0 {
		sqlQuery += " LIMIT ?"
		args = append(args, limit)
	}

	rows, err := s.db.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("搜索消息失败: %w", err)
	}
	defer rows.Close()

	var messages []*message.Message
	for rows.Next() {
		var id, role, content, metadataJSON string
		var createdAt time.Time

		if err := rows.Scan(&id, &role, &content, &metadataJSON, &createdAt); err != nil {
			return nil, fmt.Errorf("扫描搜索结果失败: %w", err)
		}

		// 反序列化元数据
		var metadata map[string]any
		if metadataJSON != "" {
			if err := json.Unmarshal([]byte(metadataJSON), &metadata); err != nil {
				return nil, fmt.Errorf("反序列化搜索结果元数据失败: %w", err)
			}
		}

		// 创建消息对象
		msg := &message.Message{
			ID:       id,
			Role:     role,
			Content:  content,
			Metadata: metadata,
		}

		messages = append(messages, msg)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历搜索结果失败: %w", err)
	}

	return messages, nil
}

// GetSessions 获取所有会话ID
func (s *SQLiteStore) GetSessions(ctx context.Context) ([]string, error) {
	query := "SELECT DISTINCT session_id FROM messages ORDER BY session_id"

	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询会话列表失败: %w", err)
	}
	defer rows.Close()

	var sessions []string
	for rows.Next() {
		var sessionID string
		if err := rows.Scan(&sessionID); err != nil {
			return nil, fmt.Errorf("扫描会话ID失败: %w", err)
		}
		sessions = append(sessions, sessionID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历会话列表失败: %w", err)
	}

	return sessions, nil
}

// Close 关闭存储
func (s *SQLiteStore) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// SaveVector 保存消息的向量表示
func (s *SQLiteStore) SaveVector(ctx context.Context, sessionID, messageID string, vector []float32, metadata map[string]any) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if messageID == "" {
		return &ValidationError{
			Field:   "messageID",
			Message: "消息ID不能为空",
		}
	}
	if len(vector) == 0 {
		return &ValidationError{
			Field:   "vector",
			Message: "向量数据不能为空",
		}
	}

	// 序列化向量数据为JSON
	vectorJSON, err := json.Marshal(vector)
	if err != nil {
		return fmt.Errorf("序列化向量数据失败: %w", err)
	}

	// 序列化元数据
	var metadataJSON []byte
	if metadata != nil {
		metadataJSON, err = json.Marshal(metadata)
		if err != nil {
			return fmt.Errorf("序列化元数据失败: %w", err)
		}
	}

	// 插入或更新向量数据
	query := `
	INSERT OR REPLACE INTO vectors (session_id, message_id, vector_data, vector_dimension, metadata)
	VALUES (?, ?, ?, ?, ?)`

	_, err = s.db.ExecContext(ctx, query, sessionID, messageID, string(vectorJSON), len(vector), string(metadataJSON))
	if err != nil {
		return fmt.Errorf("保存向量数据失败: %w", err)
	}

	return nil
}

// SearchSimilar 基于向量相似度搜索消息
func (s *SQLiteStore) SearchSimilar(ctx context.Context, sessionID string, queryVector []float32, limit int, threshold float32) ([]*VectorSearchResult, error) {
	if sessionID == "" {
		return nil, &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if len(queryVector) == 0 {
		return nil, &ValidationError{
			Field:   "queryVector",
			Message: "查询向量不能为空",
		}
	}

	// 查询所有相同维度的向量
	query := `
	SELECT session_id, message_id, vector_data, metadata
	FROM vectors
	WHERE session_id = ? AND vector_dimension = ?`

	rows, err := s.db.QueryContext(ctx, query, sessionID, len(queryVector))
	if err != nil {
		return nil, fmt.Errorf("查询向量数据失败: %w", err)
	}
	defer rows.Close()

	var results []*VectorSearchResult
	for rows.Next() {
		var sessionID, messageID, vectorDataJSON, metadataJSON string

		if err := rows.Scan(&sessionID, &messageID, &vectorDataJSON, &metadataJSON); err != nil {
			return nil, fmt.Errorf("扫描向量数据失败: %w", err)
		}

		// 反序列化向量数据
		var vector []float32
		if err := json.Unmarshal([]byte(vectorDataJSON), &vector); err != nil {
			continue // 跳过无效的向量数据
		}

		// 计算余弦相似度
		similarity := cosineSimilarity(queryVector, vector)
		if similarity < threshold {
			continue // 跳过相似度低于阈值的结果
		}

		// 反序列化元数据
		var metadata map[string]any
		if metadataJSON != "" {
			if err := json.Unmarshal([]byte(metadataJSON), &metadata); err != nil {
				metadata = nil // 忽略元数据解析错误
			}
		}

		results = append(results, &VectorSearchResult{
			MessageID: messageID,
			SessionID: sessionID,
			Score:     similarity,
			Vector:    vector,
			Metadata:  metadata,
		})
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历向量数据失败: %w", err)
	}

	// 按相似度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 限制结果数量
	if limit > 0 && len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

// DeleteVector 删除指定消息的向量
func (s *SQLiteStore) DeleteVector(ctx context.Context, sessionID, messageID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}
	if messageID == "" {
		return &ValidationError{
			Field:   "messageID",
			Message: "消息ID不能为空",
		}
	}

	_, err := s.db.ExecContext(ctx, "DELETE FROM vectors WHERE session_id = ? AND message_id = ?", sessionID, messageID)
	if err != nil {
		return fmt.Errorf("删除向量数据失败: %w", err)
	}

	return nil
}

// DeleteSessionVectors 删除指定会话的所有向量
func (s *SQLiteStore) DeleteSessionVectors(ctx context.Context, sessionID string) error {
	if sessionID == "" {
		return &ValidationError{
			Field:   "sessionID",
			Message: "会话ID不能为空",
		}
	}

	_, err := s.db.ExecContext(ctx, "DELETE FROM vectors WHERE session_id = ?", sessionID)
	if err != nil {
		return fmt.Errorf("删除会话向量数据失败: %w", err)
	}

	return nil
}

// cosineSimilarity 计算两个向量的余弦相似度
func cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// Validate 验证SQLite配置
func (c *SQLiteConfig) Validate() error {
	// 空路径是允许的，会使用默认的:memory:
	return nil
}
