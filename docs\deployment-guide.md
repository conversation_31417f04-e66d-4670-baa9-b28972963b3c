# AgentScope-Golang 部署与运维指南

## 目录

1. [概述](#overview)
2. [系统要求](#system-requirements)
3. [单机部署](#single-node-deployment)
4. [分布式部署](#distributed-deployment)
5. [配置管理](#configuration-management)
6. [监控与日志](#monitoring-and-logging)
7. [安全](#security)
8. [性能调优](#performance-tuning)
9. [备份与恢复](#backup-and-recovery)
10. [故障排查](#troubleshooting)
11. [维护](#maintenance)

## 概述

本指南面向生产环境，覆盖 AgentScope-Golang 的部署与运维要点，包含关于可扩展性、可靠性与可维护性的最佳实践。

### 部署架构

- **单机**：所有组件运行在同一台机器上
- **多节点**：组件分布在多台机器上
- **容器化**：基于 Docker/Kubernetes 的部署
- **云原生**：使用云厂商托管服务

## 系统要求

### 最低配置

- **CPU**：2 核
- **内存**：4 GB RAM
- **存储**：可用空间 20 GB
- **网络**：100 Mbps 带宽
- **操作系统**：Linux（Ubuntu 20.04+、CentOS 8+）、macOS、Windows

### 推荐配置

- **CPU**：8 核及以上
- **内存**：16 GB 及以上
- **存储**：100 GB 及以上 SSD
- **网络**：1 Gbps 带宽
- **操作系统**：Linux（Ubuntu 22.04 LTS）

### 依赖项

- Go 1.21+
- Git
- Make
- 可选：Docker、Kubernetes、Redis、PostgreSQL

## 单机部署

### 1. 安装

#### 从源码

```bash
# Clone repository
git clone https://github.com/agentscope/agentscope-golang.git
cd agentscope-golang

# Build application
make build

# Install binary
sudo cp bin/agentscope /usr/local/bin/
```

#### 使用 Docker

```bash
# Pull image
docker pull agentscope/agentscope-golang:latest

# Run container
docker run -d \
  --name agentscope \
  -p 8080:8080 \
  -e DEEPSEEK_API_KEY="your-api-key" \
  -v /path/to/config:/app/config \
  -v /path/to/data:/app/data \
  agentscope/agentscope-golang:latest
```

### 2. 配置

创建 `/etc/agentscope/config.yaml`：

```yaml
app:
  name: "AgentScope Production"
  version: "1.0.0"
  debug: false

agents:
  - id: "main-assistant"
    name: "Main Assistant"
    type: "assistant"
    description: "Production AI assistant"
    llm_config:
      model: "deepseek-chat"
      temperature: 0.7
      max_tokens: 2000

llm:
  provider: "deepseek"
  config:
    api_key: "${DEEPSEEK_API_KEY}"
    base_url: "https://api.deepseek.com/v1"
    timeout: "30s"
    retry_config:
      max_retries: 3
      initial_delay: "1s"
      max_delay: "10s"

memory:
  type: "persistent"
  persist_driver: "sqlite"
  database_dsn: "file:./data/memory.db?cache=shared&_fk=1"
  max_size: 10000
  auto_save: true
  save_interval: "30s"
  cleanup_ttl: "5m"

web:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  cors:
    allowed_origins: ["https://yourdomain.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["Content-Type", "Authorization"]

logging:
  level: "info"
  format: "json"
  outputs:
    - type: "console"
    - type: "file"
      path: "/var/log/agentscope/app.log"
      max_size: "100MB"
      max_backups: 5
      max_age: 30

monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: "30s"
```
#### SQLite 迁移脚本说明

- 使用 SQLite 后端时，系统会自动执行 migrations/*.sqlite.up.sql，无需人工干预。
- 已提供脚本：001_initial_memory_tables.sqlite.up/down.sql、002_initial_knowledge_tables.sqlite.up/down.sql。
- 如需手动执行或回滚，请在停止服务后按顺序运行对应 up/down 脚本。


### 3. 服务配置

创建 systemd 服务 `/etc/systemd/system/agentscope.service`：

```ini
[Unit]
Description=AgentScope Multi-Agent Framework
After=network.target

[Service]
Type=simple
User=agentscope
Group=agentscope
WorkingDirectory=/opt/agentscope
ExecStart=/usr/local/bin/agentscope --config /etc/agentscope/config.yaml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=agentscope

# Environment
Environment=DEEPSEEK_API_KEY=your-api-key
Environment=LOG_LEVEL=info

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/agentscope /var/lib/agentscope

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

### 4. 启动服务

```bash
# Create user and directories
sudo useradd -r -s /bin/false agentscope
sudo mkdir -p /var/log/agentscope /var/lib/agentscope
sudo chown agentscope:agentscope /var/log/agentscope /var/lib/agentscope

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable agentscope
sudo systemctl start agentscope

# Check status
sudo systemctl status agentscope
```

## 分布式部署

### 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │   Web Frontend  │
│    (nginx)      │    │   (optional)    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  AgentScope     │    │  AgentScope     │    │  AgentScope     │
│  Node 1         │    │  Node 2         │    │  Node 3         │
│  (Web + Agents) │    │  (Agents Only)  │    │  (Agents Only)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Service       │    │   Shared        │    │   Monitoring    │
│   Discovery     │    │   Storage       │    │   & Logging     │
│   (Consul)      │    │   (Redis/PG)    │    │   (Prometheus)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1. 服务发现配置

#### 使用 Consul

```bash
# Install Consul
wget https://releases.hashicorp.com/consul/1.16.1/consul_1.16.1_linux_amd64.zip
unzip consul_1.16.1_linux_amd64.zip
sudo mv consul /usr/local/bin/

# Create Consul configuration
sudo mkdir -p /etc/consul.d
cat > /etc/consul.d/consul.hcl << EOF
datacenter = "dc1"
data_dir = "/var/lib/consul"
log_level = "INFO"
server = true
bootstrap_expect = 1
bind_addr = "0.0.0.0"
client_addr = "0.0.0.0"
ui_config {
  enabled = true
}
connect {
  enabled = true
}
EOF

# Start Consul
consul agent -config-dir=/etc/consul.d
```

#### AgentScope 服务发现配置

```yaml
distributed:
  enabled: true
  service_discovery:
    type: "consul"
    config:
      address: "localhost:8500"
      datacenter: "dc1"
      health_check_interval: "10s"

  load_balancer:
    type: "round_robin"
    health_check_enabled: true

  node:
    id: "${NODE_ID}"
    name: "${NODE_NAME}"
    address: "${NODE_ADDRESS}"
    port: 8080
    tags: ["production", "agent-node"]
```

### 2. 多节点配置

#### 节点 1（Web + Agents）

```yaml
# config-node1.yaml
app:
  name: "AgentScope Node 1"
  node_id: "node-1"

web:
  enabled: true
  host: "0.0.0.0"
  port: 8080

agents:
  - id: "web-assistant"
    name: "Web Assistant"
    type: "assistant"

distributed:
  enabled: true
  node:
    id: "node-1"
    name: "Web Node"
    address: "*********"
    port: 8080
    roles: ["web", "agent"]
```

#### 节点 2 与 3（仅 Agents）

```yaml
# config-node2.yaml
app:
  name: "AgentScope Node 2"
  node_id: "node-2"

web:
  enabled: false

agents:
  - id: "processing-assistant-1"
    name: "Processing Assistant 1"
    type: "assistant"
  - id: "processing-assistant-2"
    name: "Processing Assistant 2"
    type: "assistant"

distributed:
  enabled: true
  node:
    id: "node-2"
    name: "Processing Node 2"
    address: "*********"
    port: 8080
    roles: ["agent"]
```

### 3. 负载均衡配置

#### Nginx 配置

```nginx
upstream agentscope_web {
    least_conn;
    server *********:8080 max_fails=3 fail_timeout=30s;
    server *********:8080 max_fails=3 fail_timeout=30s backup;
    server *********:8080 max_fails=3 fail_timeout=30s backup;
}

server {
    listen 80;
    server_name agentscope.yourdomain.com;

    # Redirect to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name agentscope.yourdomain.com;

    ssl_certificate /etc/ssl/certs/agentscope.crt;
    ssl_certificate_key /etc/ssl/private/agentscope.key;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API endpoints
    location /api/ {
        proxy_pass http://agentscope_web;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # WebSocket endpoints
    location /ws {
        proxy_pass http://agentscope_web;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        proxy_pass http://agentscope_web;
        access_log off;
    }
}
```

### 4. Container Orchestration

#### Docker Compose

```yaml
version: '3.8'

services:
  consul:
    image: consul:1.16
    ports:
      - "8500:8500"
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    volumes:
      - consul_data:/consul/data
    command: >
      consul agent -server -bootstrap-expect=1 -ui -bind=0.0.0.0 -client=0.0.0.0

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  agentscope-web:
    image: agentscope/agentscope-golang:latest
    ports:
      - "8080:8080"
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - NODE_ID=web-node
      - NODE_NAME=Web Node
      - NODE_ADDRESS=agentscope-web
    volumes:
      - ./config/web-node.yaml:/app/config.yaml
      - logs_data:/var/log/agentscope
    depends_on:
      - consul
      - redis

  agentscope-worker-1:
    image: agentscope/agentscope-golang:latest
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - NODE_ID=worker-1
      - NODE_NAME=Worker Node 1
      - NODE_ADDRESS=agentscope-worker-1
    volumes:
      - ./config/worker-node.yaml:/app/config.yaml
      - logs_data:/var/log/agentscope
    depends_on:
      - consul
      - redis

  agentscope-worker-2:
    image: agentscope/agentscope-golang:latest
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - NODE_ID=worker-2
      - NODE_NAME=Worker Node 2
      - NODE_ADDRESS=agentscope-worker-2
    volumes:
      - ./config/worker-node.yaml:/app/config.yaml
      - logs_data:/var/log/agentscope
    depends_on:
      - consul
      - redis

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - agentscope-web

volumes:
  consul_data:
  redis_data:
  logs_data:
```

#### Kubernetes Deployment

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: agentscope

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: agentscope-config
  namespace: agentscope
data:
  config.yaml: |
    app:
      name: "AgentScope Kubernetes"
    web:
      host: "0.0.0.0"
      port: 8080
    distributed:
      enabled: true
      service_discovery:
        type: "kubernetes"
        config:
          namespace: "agentscope"

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agentscope
  namespace: agentscope
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agentscope
  template:
    metadata:
      labels:
        app: agentscope
    spec:
      containers:
      - name: agentscope
        image: agentscope/agentscope-golang:latest
        ports:
        - containerPort: 8080
        env:
        - name: DEEPSEEK_API_KEY
          valueFrom:
            secretKeyRef:
              name: agentscope-secrets
              key: deepseek-api-key
        - name: NODE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts:
        - name: config
          mountPath: /app/config.yaml
          subPath: config.yaml
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: agentscope-config

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: agentscope-service
  namespace: agentscope
spec:
  selector:
    app: agentscope
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: agentscope-secrets
  namespace: agentscope
type: Opaque
data:
  deepseek-api-key: <base64-encoded-api-key>
```

## 配置管理

### 基于环境的配置

#### 开发环境

```yaml
# config-dev.yaml
app:
  debug: true

logging:
  level: "debug"

llm:
  config:
    timeout: "60s"

web:
  cors:
    allowed_origins: ["*"]
```

#### 预发布环境

```yaml
# config-staging.yaml
app:
  debug: false

logging:
  level: "info"

llm:
  config:
    timeout: "30s"

web:
  cors:
    allowed_origins: ["https://staging.yourdomain.com"]
```

#### 生产环境

```yaml
# config-prod.yaml
app:
  debug: false

logging:
  level: "warn"
  format: "json"

llm:
  config:
    timeout: "30s"
    retry_config:
      max_retries: 5

web:
  cors:
    allowed_origins: ["https://yourdomain.com"]

security:
  rate_limiting:
    enabled: true
    requests_per_minute: 100
```

### Configuration Validation

```bash
# Validate configuration before deployment
agentscope --config config.yaml --validate-only

# Check configuration syntax
agentscope --config config.yaml --check-syntax
```

### Dynamic Configuration Updates

```bash
# Reload configuration without restart
curl -X POST http://localhost:8080/admin/reload-config

# Update specific agent configuration
curl -X PUT http://localhost:8080/admin/agents/agent-1/config \
  -H "Content-Type: application/json" \
  -d '{"temperature": 0.8}'
```

## 监控与日志

### 指标收集

#### Prometheus 配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'agentscope'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 10s
```

#### Key Metrics

- **Agent Performance**
  - `agentscope_agent_requests_total`
  - `agentscope_agent_request_duration_seconds`
  - `agentscope_agent_errors_total`

- **LLM Performance**
  - `agentscope_llm_requests_total`
  - `agentscope_llm_request_duration_seconds`
  - `agentscope_llm_tokens_total`

- **System Metrics**
  - `agentscope_memory_usage_bytes`
  - `agentscope_goroutines_total`
  - `agentscope_gc_duration_seconds`

### Logging Setup

#### Centralized Logging with ELK Stack

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/agentscope/*.log
  json.keys_under_root: true
  json.add_error_key: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "agentscope-%{+yyyy.MM.dd}"

setup.template.name: "agentscope"
setup.template.pattern: "agentscope-*"
```

#### Log Aggregation

```yaml
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "agentscope" {
    json {
      source => "message"
    }

    date {
      match => [ "timestamp", "ISO8601" ]
    }

    mutate {
      add_field => { "service" => "agentscope" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "agentscope-%{+YYYY.MM.dd}"
  }
}
```

### Alerting Rules

#### Prometheus Alerts

```yaml
# alerts.yml
groups:
- name: agentscope
  rules:
  - alert: HighErrorRate
    expr: rate(agentscope_agent_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Agent error rate is {{ $value }} errors per second"

  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(agentscope_agent_request_duration_seconds_bucket[5m])) > 10
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High latency detected"
      description: "95th percentile latency is {{ $value }} seconds"

  - alert: ServiceDown
    expr: up{job="agentscope"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "AgentScope service is down"
      description: "AgentScope service has been down for more than 1 minute"
```

## 安全

### 认证与授权

#### JWT 配置

```yaml
security:
  jwt:
    secret: "${JWT_SECRET}"
    expiration: "24h"
    issuer: "agentscope"

  auth:
    enabled: true
    providers:
      - type: "local"
        config:
          users_file: "/etc/agentscope/users.yaml"
      - type: "oauth2"
        config:
          provider: "google"
          client_id: "${OAUTH_CLIENT_ID}"
          client_secret: "${OAUTH_CLIENT_SECRET}"
```

#### API Key Management

```yaml
security:
  api_keys:
    enabled: true
    keys:
      - name: "admin"
        key: "${ADMIN_API_KEY}"
        permissions: ["admin", "read", "write"]
      - name: "readonly"
        key: "${READONLY_API_KEY}"
        permissions: ["read"]
```

### Network Security

#### Firewall Rules

```bash
# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow AgentScope API (internal only)
sudo ufw allow from 10.0.0.0/8 to any port 8080

# Allow monitoring (internal only)
sudo ufw allow from 10.0.0.0/8 to any port 9090

# Enable firewall
sudo ufw enable
```

#### TLS Configuration

```yaml
web:
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/agentscope.crt"
    key_file: "/etc/ssl/private/agentscope.key"
    min_version: "1.2"
    cipher_suites:
      - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
      - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
```

### Data Protection

#### Encryption at Rest

```yaml
storage:
  encryption:
    enabled: true
    key_file: "/etc/agentscope/encryption.key"
    algorithm: "AES-256-GCM"

memory:
  encryption:
    enabled: true
    key_rotation_interval: "24h"
```

#### 数据净化

```yaml
security:
  data_sanitization:
    enabled: true
    rules:
      - type: "pii"
        action: "mask"
        patterns: ["email", "phone", "ssn"]
      - type: "sensitive"
        action: "remove"
        patterns: ["password", "token", "key"]
```

## 性能调优

### Go 运行时优化

```bash
# Environment variables for production
export GOGC=100
export GOMAXPROCS=8
export GOMEMLIMIT=8GiB
```

### 应用调优

#### 内存管理

```yaml
memory:
  gc_target_percentage: 100
  max_heap_size: "4GB"

  pools:
    message_pool_size: 10000
    agent_pool_size: 1000
```

#### 连接池

```yaml
llm:
  connection_pool:
    max_idle_conns: 100
    max_open_conns: 200
    conn_max_lifetime: "1h"

web:
  connection_limits:
    max_connections: 10000
    read_timeout: "30s"
    write_timeout: "30s"
    idle_timeout: "120s"
```

#### Caching

```yaml
cache:
  enabled: true
  type: "redis"
  config:
    address: "redis:6379"
    password: "${REDIS_PASSWORD}"
    db: 0
    max_retries: 3

  policies:
    llm_responses:
      ttl: "1h"
      max_size: "100MB"
    agent_configs:
      ttl: "24h"
      max_size: "10MB"
```

### Database Optimization

#### PostgreSQL Configuration

```sql
-- postgresql.conf optimizations
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

#### Connection Pooling

```yaml
database:
  connection_pool:
    max_open_conns: 25
    max_idle_conns: 5
    conn_max_lifetime: "1h"
    conn_max_idle_time: "10m"
```

## 备份与恢复

### 数据备份策略

#### 配置备份

```bash
#!/bin/bash
# backup-config.sh

BACKUP_DIR="/backup/agentscope/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# Backup configuration files
cp -r /etc/agentscope "$BACKUP_DIR/config"

# Backup SSL certificates
cp -r /etc/ssl/agentscope "$BACKUP_DIR/ssl"

# Create archive
tar -czf "$BACKUP_DIR.tar.gz" -C /backup/agentscope "$(basename $BACKUP_DIR)"

# Upload to S3 (optional)
aws s3 cp "$BACKUP_DIR.tar.gz" s3://your-backup-bucket/agentscope/
```

#### Database Backup

```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/backup/database/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# PostgreSQL backup
pg_dump -h localhost -U agentscope agentscope_db > "$BACKUP_DIR/agentscope.sql"

# Redis backup
redis-cli --rdb "$BACKUP_DIR/dump.rdb"

# Compress and upload
tar -czf "$BACKUP_DIR.tar.gz" -C /backup/database "$(basename $BACKUP_DIR)"
aws s3 cp "$BACKUP_DIR.tar.gz" s3://your-backup-bucket/database/
```

### Disaster Recovery

#### Recovery Procedures

```bash
#!/bin/bash
# disaster-recovery.sh

# 1. Stop services
sudo systemctl stop agentscope
sudo systemctl stop nginx

# 2. Restore configuration
aws s3 cp s3://your-backup-bucket/agentscope/latest.tar.gz /tmp/
tar -xzf /tmp/latest.tar.gz -C /tmp/
sudo cp -r /tmp/config/* /etc/agentscope/

# 3. Restore database
aws s3 cp s3://your-backup-bucket/database/latest.tar.gz /tmp/
tar -xzf /tmp/latest.tar.gz -C /tmp/
psql -h localhost -U agentscope agentscope_db < /tmp/agentscope.sql

# 4. Start services
sudo systemctl start agentscope
sudo systemctl start nginx

# 5. Verify recovery
curl http://localhost:8080/health
```

#### High Availability Setup

```yaml
# ha-config.yaml
distributed:
  high_availability:
    enabled: true
    replication_factor: 3
    consistency_level: "quorum"

  failover:
    enabled: true
    detection_interval: "10s"
    failover_timeout: "30s"

  backup_nodes:
    - address: "*********:8080"
      priority: 1
    - address: "*********:8080"
      priority: 2
```

## 故障排查

### 常见问题

#### 1. 服务无法启动

**症状：**
- 服务启动失败
- 日志错误："bind: address already in use"

**诊断：**
```bash
# Check if port is in use
sudo netstat -tlnp | grep :8080

# Check service status
sudo systemctl status agentscope

# Check logs
sudo journalctl -u agentscope -f
```

**Resolution:**
```bash
# Kill process using port
sudo kill -9 $(sudo lsof -t -i:8080)

# Or change port in configuration
# Edit /etc/agentscope/config.yaml
```

#### 2. High Memory Usage

**Symptoms:**
- OOM killer activated
- Slow response times
- Memory usage continuously growing

**Diagnosis:**
```bash
# Check memory usage
free -h
ps aux | grep agentscope

# Check Go memory stats
curl http://localhost:9090/debug/pprof/heap
```

**Resolution:**
```yaml
# Adjust memory limits in config
memory:
  max_memories: 10000  # Reduce from 50000
  cleanup_interval: "30m"  # More frequent cleanup

# Adjust Go runtime
export GOMEMLIMIT=2GiB
```

#### 3. LLM API Errors

**Symptoms:**
- Agents not responding
- API rate limit errors
- Connection timeouts

**Diagnosis:**
```bash
# Test API connectivity
curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  https://api.deepseek.com/v1/models

# Check agent logs
grep "LLM" /var/log/agentscope/app.log
```

**Resolution:**
```yaml
# Adjust retry configuration
llm:
  config:
    retry_config:
      max_retries: 5
      initial_delay: "2s"
      max_delay: "30s"
      backoff_multiplier: 2.0
```

#### 4. Database Connection Issues

**Symptoms:**
- "connection refused" errors
- Slow database queries
- Connection pool exhausted

**Diagnosis:**
```bash
# Check database status
sudo systemctl status postgresql

# Check connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Test connection
psql -h localhost -U agentscope -d agentscope_db -c "SELECT 1;"
```

**Resolution:**
```yaml
# Adjust connection pool
database:
  connection_pool:
    max_open_conns: 50  # Increase from 25
    max_idle_conns: 10  # Increase from 5
    conn_max_lifetime: "30m"  # Reduce from 1h
```

### Performance Issues

#### Slow Response Times

**Diagnosis:**
```bash
# Check system load
top
htop

# Check network latency
ping api.deepseek.com

# Profile application
curl http://localhost:9090/debug/pprof/profile?seconds=30
```

**Resolution:**
- Increase CPU/memory resources
- Optimize database queries
- Enable caching
- Use connection pooling

#### High CPU Usage

**Diagnosis:**
```bash
# Check CPU usage by process
top -p $(pgrep agentscope)

# Profile CPU usage
go tool pprof http://localhost:9090/debug/pprof/profile
```

**Resolution:**
- Optimize agent algorithms
- Reduce concurrent operations
- Scale horizontally

### Log Analysis

#### Important Log Patterns

```bash
# Error patterns to monitor
grep -E "(ERROR|FATAL|panic)" /var/log/agentscope/app.log

# Performance issues
grep -E "(timeout|slow|latency)" /var/log/agentscope/app.log

# Security issues
grep -E "(unauthorized|forbidden|invalid.*token)" /var/log/agentscope/app.log
```

#### Log Analysis Tools

```bash
# Real-time log monitoring
tail -f /var/log/agentscope/app.log | jq '.'

# Log aggregation
cat /var/log/agentscope/app.log | jq -r 'select(.level=="ERROR") | .message'

# Performance analysis
cat /var/log/agentscope/app.log | jq -r 'select(.duration > 5000) | "\(.timestamp) \(.agent_id) \(.duration)ms"'
```

## 维护

### 定期维护任务

#### 日常任务

```bash
#!/bin/bash
# daily-maintenance.sh

# Check service health
curl -f http://localhost:8080/health || echo "Health check failed"

# Check disk space
df -h | awk '$5 > 80 {print "Disk usage warning: " $0}'

# Check log file sizes
find /var/log/agentscope -name "*.log" -size +100M

# Rotate logs if needed
sudo logrotate /etc/logrotate.d/agentscope
```

#### Weekly Tasks

```bash
#!/bin/bash
# weekly-maintenance.sh

# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean old log files
find /var/log/agentscope -name "*.log.*" -mtime +7 -delete

# Backup configuration
./backup-config.sh

# Check SSL certificate expiration
openssl x509 -in /etc/ssl/certs/agentscope.crt -noout -dates
```

#### Monthly Tasks

```bash
#!/bin/bash
# monthly-maintenance.sh

# Full system backup
./backup-database.sh

# Security updates
sudo unattended-upgrades

# Performance review
# - Analyze metrics
# - Review resource usage
# - Plan capacity changes

# Security audit
# - Review access logs
# - Update passwords/keys
# - Check for vulnerabilities
```

### Updates and Upgrades

#### Application Updates

```bash
#!/bin/bash
# update-agentscope.sh

# 1. Backup current version
sudo systemctl stop agentscope
cp /usr/local/bin/agentscope /usr/local/bin/agentscope.backup

# 2. Download new version
wget https://github.com/agentscope/agentscope-golang/releases/download/v1.1.0/agentscope-linux-amd64
chmod +x agentscope-linux-amd64

# 3. Test new version
./agentscope-linux-amd64 --config /etc/agentscope/config.yaml --validate-only

# 4. Install new version
sudo mv agentscope-linux-amd64 /usr/local/bin/agentscope

# 5. Start service
sudo systemctl start agentscope

# 6. Verify update
curl http://localhost:8080/health
```

#### Rolling Updates (Kubernetes)

```bash
# Update deployment image
kubectl set image deployment/agentscope agentscope=agentscope/agentscope-golang:v1.1.0 -n agentscope

# Monitor rollout
kubectl rollout status deployment/agentscope -n agentscope

# Rollback if needed
kubectl rollout undo deployment/agentscope -n agentscope
```

### Capacity Planning

#### Monitoring Resource Usage

```bash
# CPU and memory trends
prometheus_query='rate(node_cpu_seconds_total[5m])'
prometheus_query='node_memory_MemAvailable_bytes'

# Application metrics
prometheus_query='agentscope_agent_requests_total'
prometheus_query='agentscope_agent_request_duration_seconds'
```

#### Scaling Decisions

- **Scale Up**: Increase resources on existing nodes
- **Scale Out**: Add more nodes to the cluster
- **Optimize**: Improve application efficiency

This deployment guide provides comprehensive coverage of production deployment scenarios for AgentScope-Golang. Follow these practices to ensure reliable, secure, and scalable operations.