package audit

import (
	"crypto/sha256"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// GenerateID 生成唯一ID
func GenerateID() string {
	return uuid.New().String()
}

// GenerateSessionID 生成会话ID
func GenerateSessionID() string {
	return fmt.Sprintf("sess_%d_%s", time.Now().UnixNano(), uuid.New().String()[:8])
}

// HashContent 计算内容哈希
func HashContent(content string) string {
	if content == "" {
		return ""
	}
	hash := sha256.Sum256([]byte(content))
	return fmt.Sprintf("%x", hash)
}

// ValidateRecord 验证记录有效性
func ValidateRecord(r *Record) error {
	if r == nil {
		return NewInvalidRecordError("记录不能为空", nil)
	}
	
	if r.ID == "" {
		return NewInvalidRecordError("记录ID不能为空", nil)
	}
	
	if r.SessionID == "" {
		return NewInvalidRecordError("会话ID不能为空", nil)
	}
	
	if r.Role == "" {
		return NewInvalidRecordError("消息角色不能为空", nil)
	}
	
	if r.MsgType == "" {
		return NewInvalidRecordError("消息类型不能为空", nil)
	}
	
	if r.CreatedAt.IsZero() {
		return NewInvalidRecordError("创建时间不能为空", nil)
	}
	
	// 验证角色值
	validRoles := map[string]bool{
		"user":      true,
		"assistant": true,
		"system":    true,
		"tool":      true,
	}
	if !validRoles[r.Role] {
		return NewInvalidRecordError(fmt.Sprintf("无效的消息角色: %s", r.Role), nil)
	}
	
	// 验证消息类型值
	validMsgTypes := map[string]bool{
		"text":        true,
		"tool_result": true,
		"error":       true,
		"token":       true,
		"final":       true,
	}
	if !validMsgTypes[r.MsgType] {
		return NewInvalidRecordError(fmt.Sprintf("无效的消息类型: %s", r.MsgType), nil)
	}
	
	return nil
}

// ValidateQuery 验证查询条件有效性
func ValidateQuery(q Query) error {
	if q.Limit < 0 {
		return NewInvalidQueryError("查询限制数量不能为负数", nil)
	}
	
	if q.Offset < 0 {
		return NewInvalidQueryError("查询偏移量不能为负数", nil)
	}
	
	if q.Since != nil && q.Until != nil && q.Since.After(*q.Until) {
		return NewInvalidQueryError("开始时间不能晚于结束时间", nil)
	}
	
	// 设置默认限制
	if q.Limit == 0 {
		q.Limit = 100
	}
	
	// 限制最大查询数量
	if q.Limit > 1000 {
		return NewInvalidQueryError("查询限制数量不能超过1000", nil)
	}
	
	return nil
}

// NormalizeQuery 标准化查询条件
func NormalizeQuery(q *Query) {
	if q.Limit <= 0 {
		q.Limit = 100
	}
	if q.Limit > 1000 {
		q.Limit = 1000
	}
	if q.Offset < 0 {
		q.Offset = 0
	}
}

// BuildRecordFromEvent 从事件构建审计记录
func BuildRecordFromEvent(sessionID, userID, agentID string, eventType string, content string) *Record {
	now := time.Now()
	record := &Record{
		ID:        GenerateID(),
		SessionID: sessionID,
		UserID:    userID,
		AgentID:   agentID,
		EventType: eventType,
		CreatedAt: now,
	}
	
	// 根据事件类型设置角色和消息类型
	switch eventType {
	case "token", "final":
		record.Role = "assistant"
		record.MsgType = eventType
	case "error":
		record.Role = "assistant"
		record.MsgType = "error"
	case "tool_call":
		record.Role = "assistant"
		record.MsgType = "tool_result"
	case "tool_result":
		record.Role = "tool"
		record.MsgType = "tool_result"
	default:
		record.Role = "system"
		record.MsgType = "text"
	}
	
	record.Content = content
	record.ContentHash = HashContent(content)
	
	return record
}

// BuildUserRecord 构建用户消息记录
func BuildUserRecord(sessionID, userID, content string) *Record {
	now := time.Now()
	return &Record{
		ID:          GenerateID(),
		SessionID:   sessionID,
		UserID:      userID,
		Role:        "user",
		MsgType:     "text",
		Content:     content,
		ContentHash: HashContent(content),
		CreatedAt:   now,
	}
}
