package audit

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRecord 测试记录结构
func TestRecord(t *testing.T) {
	record := &Record{
		ID:        "test-id",
		SessionID: "test-session",
		UserID:    "test-user",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}

	// 测试记录验证
	if err := ValidateRecord(record); err != nil {
		t.Errorf("有效记录验证失败: %v", err)
	}

	// 测试无效记录
	invalidRecord := &Record{}
	if err := ValidateRecord(invalidRecord); err == nil {
		t.Error("无效记录应该验证失败")
	}
}

// TestQuery 测试查询结构
func TestQuery(t *testing.T) {
	query := Query{
		Limit:  10,
		Offset: 0,
	}

	// 测试查询验证
	if err := ValidateQuery(query); err != nil {
		t.Errorf("有效查询验证失败: %v", err)
	}

	// 测试无效查询
	invalidQuery := Query{
		Limit:  -1,
		Offset: -1,
	}
	if err := ValidateQuery(invalidQuery); err == nil {
		t.Error("无效查询应该验证失败")
	}
}

// TestUtils 测试工具函数
func TestUtils(t *testing.T) {
	// 测试ID生成
	id1 := GenerateID()
	id2 := GenerateID()
	if id1 == id2 {
		t.Error("生成的ID应该是唯一的")
	}

	// 测试会话ID生成
	sessionID := GenerateSessionID()
	if sessionID == "" {
		t.Error("会话ID不应该为空")
	}

	// 测试内容哈希
	content := "test content"
	hash1 := HashContent(content)
	hash2 := HashContent(content)
	if hash1 != hash2 {
		t.Error("相同内容的哈希应该相同")
	}

	emptyHash := HashContent("")
	if emptyHash != "" {
		t.Error("空内容的哈希应该为空")
	}
}

// TestBuildRecordFromEvent 测试从事件构建记录
func TestBuildRecordFromEvent(t *testing.T) {
	sessionID := "test-session"
	userID := "test-user"
	agentID := "test-agent"
	eventType := "final"
	content := "test response"

	record := BuildRecordFromEvent(sessionID, userID, agentID, eventType, content)

	if record.SessionID != sessionID {
		t.Errorf("期望会话ID %s，得到 %s", sessionID, record.SessionID)
	}

	if record.UserID != userID {
		t.Errorf("期望用户ID %s，得到 %s", userID, record.UserID)
	}

	if record.AgentID != agentID {
		t.Errorf("期望AgentID %s，得到 %s", agentID, record.AgentID)
	}

	if record.Role != "assistant" {
		t.Errorf("期望角色 assistant，得到 %s", record.Role)
	}

	if record.MsgType != "final" {
		t.Errorf("期望消息类型 final，得到 %s", record.MsgType)
	}

	if record.Content != content {
		t.Errorf("期望内容 %s，得到 %s", content, record.Content)
	}
}

// TestBuildUserRecord 测试构建用户记录
func TestBuildUserRecord(t *testing.T) {
	sessionID := "test-session"
	userID := "test-user"
	content := "user message"

	record := BuildUserRecord(sessionID, userID, content)

	if record.SessionID != sessionID {
		t.Errorf("期望会话ID %s，得到 %s", sessionID, record.SessionID)
	}

	if record.UserID != userID {
		t.Errorf("期望用户ID %s，得到 %s", userID, record.UserID)
	}

	if record.Role != "user" {
		t.Errorf("期望角色 user，得到 %s", record.Role)
	}

	if record.MsgType != "text" {
		t.Errorf("期望消息类型 text，得到 %s", record.MsgType)
	}

	if record.Content != content {
		t.Errorf("期望内容 %s，得到 %s", content, record.Content)
	}
}

// TestDefaultConfig 测试默认配置
func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config == nil {
		t.Error("默认配置不应该为空")
	}

	if config.Driver != "sqlite" {
		t.Errorf("期望默认驱动 sqlite，得到 %s", config.Driver)
	}

	if config.Retention.MaxDays != 90 {
		t.Errorf("期望默认保留天数 90，得到 %d", config.Retention.MaxDays)
	}

	if !config.Privacy.RedactPII {
		t.Error("期望默认启用PII脱敏")
	}

	if !config.Batch.Async {
		t.Error("期望默认启用异步批处理")
	}
}

// TestSanitizer 测试数据脱敏器
func TestSanitizer(t *testing.T) {
	config := &PrivacyConfig{
		RedactPII:   true,
		PIIPatterns: []string{"email", "phone"},
	}

	sanitizer, err := NewSanitizer(config)
	if err != nil {
		t.Fatalf("创建脱敏器失败: %v", err)
	}

	// 测试邮箱脱敏
	content := "我的邮箱是 <EMAIL>"
	sanitized, err := sanitizer.SanitizeContent(content)
	if err != nil {
		t.Fatalf("脱敏失败: %v", err)
	}

	if sanitized == content {
		t.Error("邮箱应该被脱敏")
	}

	// 测试记录脱敏
	record := &Record{
		ID:        "test-id",
		SessionID: "test-session",
		Role:      "user",
		MsgType:   "text",
		Content:   "我的邮箱是 <EMAIL>",
		CreatedAt: time.Now(),
	}

	err = sanitizer.SanitizeRecord(record)
	if err != nil {
		t.Fatalf("记录脱敏失败: %v", err)
	}

	if record.Content == "我的邮箱是 <EMAIL>" {
		t.Error("记录中的邮箱应该被脱敏")
	}
}

// TestAuditWriter 测试审计写入器
func TestAuditWriter(t *testing.T) {
	// 创建内存存储桩
	store := &MockStore{}

	config := &BatchConfig{
		Async:         false, // 使用同步模式便于测试
		ChanBuffer:    10,
		FlushInterval: time.Millisecond * 100,
	}

	writer := NewAuditWriter(store, nil, config)

	// 启动写入器
	ctx := context.Background()
	err := writer.Start(ctx)
	if err != nil {
		t.Fatalf("启动写入器失败: %v", err)
	}

	// 测试写入记录
	record := &Record{
		ID:        "test-id",
		SessionID: "test-session",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}

	err = writer.WriteRecord(ctx, record)
	if err != nil {
		t.Fatalf("写入记录失败: %v", err)
	}

	// 检查统计信息
	stats := writer.GetStats()
	if stats.IsClosed {
		t.Error("写入器不应该已关闭")
	}

	// 关闭写入器
	err = writer.Close()
	if err != nil {
		t.Fatalf("关闭写入器失败: %v", err)
	}
}

// MockStore 模拟存储实现
type MockStore struct {
	records  []*Record
	sessions []*Session
}

func (m *MockStore) SaveMessage(ctx context.Context, r *Record) error {
	m.records = append(m.records, r)
	return nil
}

func (m *MockStore) SaveSession(ctx context.Context, sessionID, userID string, meta map[string]any) error {
	session := &Session{
		SessionID:    sessionID,
		UserID:       userID,
		CreatedAt:    time.Now(),
		LastActiveAt: time.Now(),
		Metadata:     meta,
	}
	m.sessions = append(m.sessions, session)
	return nil
}

func (m *MockStore) TouchSession(ctx context.Context, sessionID string, at time.Time) error {
	for _, session := range m.sessions {
		if session.SessionID == sessionID {
			session.LastActiveAt = at
			break
		}
	}
	return nil
}

func (m *MockStore) QueryMessages(ctx context.Context, q Query) (*QueryResult, error) {
	var filteredRecords []*Record

	// 简单的过滤逻辑
	for _, record := range m.records {
		match := true

		if q.SessionID != nil && record.SessionID != *q.SessionID {
			match = false
		}
		if q.UserID != nil && record.UserID != *q.UserID {
			match = false
		}
		if q.AgentID != nil && record.AgentID != *q.AgentID {
			match = false
		}

		if match {
			filteredRecords = append(filteredRecords, record)
		}
	}

	return &QueryResult{
		Records: filteredRecords,
		Total:   len(filteredRecords),
		HasMore: false,
	}, nil
}

func (m *MockStore) QuerySessions(ctx context.Context, userID string, limit, offset int) ([]*Session, int, error) {
	return m.sessions, len(m.sessions), nil
}

func (m *MockStore) RunRetention(ctx context.Context) error {
	return nil
}

func (m *MockStore) SaveMessages(ctx context.Context, records []*Record) error {
	for _, record := range records {
		if err := m.SaveMessage(ctx, record); err != nil {
			return err
		}
	}
	return nil
}

func (m *MockStore) Close() error {
	return nil
}

// TestSQLiteStore 测试SQLite存储
func TestSQLiteStore(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过SQLite存储测试")
	}

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	config := DefaultConfig()
	store, err := NewSQLiteStore(dbPath, config)
	if err != nil {
		t.Fatalf("创建SQLite存储失败: %v", err)
	}
	defer store.Close()

	testStoreOperations(t, store)
}

// TestPostgresStore 测试PostgreSQL存储
func TestPostgresStore(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过PostgreSQL存储测试")
	}

	// 只有在设置了环境变量时才运行PostgreSQL测试
	dsn := os.Getenv("POSTGRES_TEST_DSN")
	if dsn == "" {
		t.Skip("跳过PostgreSQL测试，设置 POSTGRES_TEST_DSN 环境变量来运行")
	}

	config := DefaultConfig()
	store, err := NewPostgresStore(dsn, config)
	if err != nil {
		t.Fatalf("创建PostgreSQL存储失败: %v", err)
	}
	defer store.Close()

	testStoreOperations(t, store)
}

// testStoreOperations 通用存储操作测试
func testStoreOperations(t *testing.T, store Store) {
	ctx := context.Background()

	// 测试保存和查询消息
	record := &Record{
		ID:        GenerateID(),
		SessionID: "test-session",
		UserID:    "test-user",
		Role:      "user",
		MsgType:   "text",
		Content:   "测试消息",
		CreatedAt: time.Now(),
	}

	err := store.SaveMessage(ctx, record)
	if err != nil {
		t.Fatalf("保存消息失败: %v", err)
	}

	// 查询消息
	query := Query{
		SessionID: &record.SessionID,
		Limit:     10,
		Offset:    0,
	}

	result, err := store.QueryMessages(ctx, query)
	if err != nil {
		t.Fatalf("查询消息失败: %v", err)
	}

	if len(result.Records) != 1 {
		t.Fatalf("期望1条记录，得到%d条", len(result.Records))
	}

	if result.Records[0].Content != record.Content {
		t.Errorf("内容不匹配，期望: %s，得到: %s", record.Content, result.Records[0].Content)
	}

	// 测试会话操作
	err = store.SaveSession(ctx, record.SessionID, record.UserID, map[string]any{"test": "value"})
	if err != nil {
		t.Fatalf("保存会话失败: %v", err)
	}

	sessions, total, err := store.QuerySessions(ctx, record.UserID, 10, 0)
	if err != nil {
		t.Fatalf("查询会话失败: %v", err)
	}

	if total == 0 {
		t.Error("应该查询到至少一个会话")
	}

	if len(sessions) == 0 {
		t.Error("会话列表不应该为空")
	}

	// 测试更新会话活跃时间
	err = store.TouchSession(ctx, record.SessionID, time.Now())
	if err != nil {
		t.Fatalf("更新会话活跃时间失败: %v", err)
	}
}

// TestAdvancedSanitizer 测试高级数据脱敏功能
func TestAdvancedSanitizer(t *testing.T) {
	config := &PrivacyConfig{
		RedactPII:     true,
		PIIPatterns:   []string{"email", "phone", "id_card", "credit_card", "ip"},
		HashContent:   true,
		EncryptAtRest: false,
	}

	sanitizer, err := NewSanitizer(config)
	if err != nil {
		t.Fatalf("创建脱敏器失败: %v", err)
	}

	testCases := []struct {
		name         string
		input        string
		shouldChange bool
	}{
		{"邮箱脱敏", "联系我：<EMAIL>", true},
		{"电话脱敏", "我的电话是13812345678", true},
		{"身份证脱敏", "身份证号：110101199001011234", true},
		{"信用卡脱敏", "卡号：****************", true},
		{"IP地址脱敏", "服务器IP：***********", true},
		{"普通文本", "这是普通的文本内容", false},
		{"混合内容", "邮箱*************，电话13900000000", true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := sanitizer.SanitizeContent(tc.input)
			if err != nil {
				t.Fatalf("脱敏失败: %v", err)
			}

			if tc.shouldChange && result == tc.input {
				t.Errorf("内容应该被脱敏，但未发生变化: %s", tc.input)
			}

			if !tc.shouldChange && result != tc.input {
				t.Errorf("内容不应该被脱敏，但发生了变化: %s -> %s", tc.input, result)
			}
		})
	}
}

// TestEncryption 测试加密功能
func TestEncryption(t *testing.T) {
	// 设置测试加密密钥（32字节）
	testKey := "12345678901234567890123456789012"
	if len(testKey) != 32 {
		t.Fatalf("测试密钥长度错误: %d，应该是32字节", len(testKey))
	}
	os.Setenv("TEST_AES_KEY", testKey)
	defer os.Unsetenv("TEST_AES_KEY")

	config := &PrivacyConfig{
		RedactPII:     false,
		EncryptAtRest: true,
		EncryptKeyEnv: "TEST_AES_KEY",
	}

	sanitizer, err := NewSanitizer(config)
	if err != nil {
		t.Fatalf("创建加密脱敏器失败: %v", err)
	}

	originalContent := "这是需要加密的敏感内容"

	// 测试加密
	encrypted, err := sanitizer.SanitizeContent(originalContent)
	if err != nil {
		t.Fatalf("加密失败: %v", err)
	}

	if encrypted == originalContent {
		t.Error("内容应该被加密")
	}

	// 测试解密
	decrypted, err := sanitizer.UnsanitizeContent(encrypted)
	if err != nil {
		t.Fatalf("解密失败: %v", err)
	}

	if decrypted != originalContent {
		t.Errorf("解密后内容不匹配，期望: %s，得到: %s", originalContent, decrypted)
	}
}

// TestRetentionJob 测试保留策略
func TestRetentionJob(t *testing.T) {
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "retention_test.db")

	config := DefaultConfig()
	store, err := NewSQLiteStore(dbPath, config)
	if err != nil {
		t.Fatalf("创建存储失败: %v", err)
	}
	defer store.Close()

	ctx := context.Background()

	// 插入一些过期数据
	oldTime := time.Now().AddDate(0, 0, -100) // 100天前
	for i := 0; i < 5; i++ {
		record := &Record{
			ID:        GenerateID(),
			SessionID: "old-session",
			UserID:    "old-user",
			Role:      "user",
			MsgType:   "text",
			Content:   "过期消息",
			CreatedAt: oldTime,
		}
		store.SaveMessage(ctx, record)
	}

	// 插入一些新数据
	newTime := time.Now()
	for i := 0; i < 3; i++ {
		record := &Record{
			ID:        GenerateID(),
			SessionID: "new-session",
			UserID:    "new-user",
			Role:      "user",
			MsgType:   "text",
			Content:   "新消息",
			CreatedAt: newTime,
		}
		store.SaveMessage(ctx, record)
	}

	// 创建保留策略任务
	retentionConfig := &RetentionConfig{
		Enabled: true,
		MaxDays: 90,
		Cron:    "@daily",
	}

	job := NewRetentionJob(store, retentionConfig)

	// 查询清理前的记录数
	allQuery := Query{Limit: 100, Offset: 0}
	beforeResult, err := store.QueryMessages(ctx, allQuery)
	if err != nil {
		t.Fatalf("查询清理前记录失败: %v", err)
	}
	beforeCount := len(beforeResult.Records)

	// 执行清理
	_, err = job.RunOnce(ctx)
	if err != nil {
		t.Fatalf("执行保留策略失败: %v", err)
	}

	// 查询清理后的记录数
	afterResult, err := store.QueryMessages(ctx, allQuery)
	if err != nil {
		t.Fatalf("查询清理后记录失败: %v", err)
	}
	afterCount := len(afterResult.Records)

	deletedCount := beforeCount - afterCount
	if deletedCount == 0 {
		t.Error("应该删除一些过期记录")
	}

	t.Logf("保留策略执行结果: 清理前 %d 条，清理后 %d 条，删除了 %d 条记录",
		beforeCount, afterCount, deletedCount)
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	// 测试无效记录验证
	invalidRecord := &Record{}
	err := ValidateRecord(invalidRecord)
	if err == nil {
		t.Error("无效记录应该验证失败")
	}

	// 测试无效查询验证
	invalidQuery := Query{Limit: -1}
	err = ValidateQuery(invalidQuery)
	if err == nil {
		t.Error("无效查询应该验证失败")
	}

	// 测试无效配置
	invalidConfig := &PrivacyConfig{
		EncryptAtRest: true,
		EncryptKeyEnv: "NON_EXISTENT_KEY",
	}

	_, err = NewSanitizer(invalidConfig)
	if err == nil {
		t.Error("无效加密配置应该创建失败")
	}
}

// TestRuntimeInterceptorAdapter 测试运行时拦截器适配器
func TestRuntimeInterceptorAdapter(t *testing.T) {
	// 创建临时数据库
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	config := DefaultConfig()
	store, err := NewSQLiteStore(dbPath, config)
	require.NoError(t, err)
	defer store.Close()

	// 创建脱敏器
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 创建审计写入器
	batchConfig := &BatchConfig{
		Async:         false, // 使用同步模式便于测试
		ChanBuffer:    100,
		FlushInterval: time.Second,
	}
	writer := NewAuditWriter(store, sanitizer, batchConfig)
	defer writer.Close()

	// 创建审计拦截器
	auditInterceptor := NewAuditInterceptor(writer, config)
	auditInterceptor.SetSessionID("test-session")

	// 创建适配器
	adapter := NewRuntimeInterceptorAdapter(auditInterceptor, "test-audit")

	// 测试Name方法
	assert.Equal(t, "test-audit", adapter.Name())

	// 测试BeforeRun方法
	ctx := context.Background()
	input := runtime.NewInput()
	input.AddMessage(message.NewUserMessage("测试消息"))

	err = adapter.BeforeRun(ctx, "test-agent", input)
	assert.NoError(t, err)

	// 测试OnEvent方法
	ev := event.NewTokenEvent("测试token", false)
	err = adapter.OnEvent(ctx, "test-agent", ev)
	assert.NoError(t, err)

	// 测试OnError方法
	testErr := errors.New("测试错误")
	err = adapter.OnError(ctx, "test-agent", testErr)
	assert.NoError(t, err)

	// 测试AfterRun方法
	err = adapter.AfterRun(ctx, "test-agent", input, nil)
	assert.NoError(t, err)

	// 等待异步写入完成
	time.Sleep(100 * time.Millisecond)

	// 验证记录是否被写入
	sessionID := "test-session"
	query := Query{
		SessionID: &sessionID,
		Limit:     10,
	}
	result, err := store.QueryMessages(ctx, query)
	assert.NoError(t, err)
	assert.Greater(t, len(result.Records), 0)
}

// TestAuditWriterAdvanced 测试审计写入器高级功能
func TestAuditWriterAdvanced(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 测试创建写入器
	writer := NewAuditWriter(store, sanitizer, nil)
	assert.NotNil(t, writer)

	// 测试启动
	ctx := context.Background()
	err = writer.Start(ctx)
	assert.NoError(t, err)
	defer writer.Close()

	// 测试写入记录
	record := &Record{
		ID:        "test-1",
		SessionID: "session-1",
		UserID:    "user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}

	err = writer.WriteRecord(ctx, record)
	assert.NoError(t, err)

	// 测试统计信息
	stats := writer.GetStats()
	assert.NotNil(t, stats)

	// 测试批量大小设置
	writer.SetBatchSize(50)
	assert.Equal(t, 50, writer.GetBatchSize())

	// 测试无效批量大小
	writer.SetBatchSize(0)
	assert.Equal(t, 50, writer.GetBatchSize()) // 应该保持不变

	writer.SetBatchSize(2000)
	assert.Equal(t, 50, writer.GetBatchSize()) // 应该保持不变
}

// TestAuditReader 测试审计查询器
func TestAuditReader(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 添加测试数据
	testRecord := &Record{
		ID:        "test-1",
		SessionID: "session-1",
		UserID:    "user-1",
		AgentID:   "agent-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}
	store.records = append(store.records, testRecord)

	// 添加测试会话数据
	testSession := &Session{
		SessionID:    "session-1",
		UserID:       "user-1",
		CreatedAt:    time.Now(),
		LastActiveAt: time.Now(),
		Metadata:     map[string]any{"test": "data"},
	}
	store.sessions = append(store.sessions, testSession)

	reader := NewAuditReader(store, sanitizer)
	assert.NotNil(t, reader)

	ctx := context.Background()

	// 测试按会话查询
	result, err := reader.QueryMessagesBySession(ctx, "session-1", 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.Records))

	// 测试按用户查询
	result, err = reader.QueryMessagesByUser(ctx, "user-1", 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 测试按Agent查询
	result, err = reader.QueryMessagesByAgent(ctx, "agent-1", 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 测试按时间范围查询
	since := time.Now().Add(-time.Hour)
	until := time.Now().Add(time.Hour)
	result, err = reader.QueryMessagesByTimeRange(ctx, &since, &until, 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 测试按关键词查询
	result, err = reader.QueryMessagesByKeyword(ctx, "test", 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 测试按类型查询
	result, err = reader.QueryMessagesByType(ctx, []string{"text"}, 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 测试查询会话
	sessions, total, err := reader.QuerySessions(ctx, "user-1", 10, 0)
	assert.NoError(t, err)
	assert.NotNil(t, sessions)
	assert.GreaterOrEqual(t, total, 0)

	// 测试关闭查询器
	err = reader.Close()
	assert.NoError(t, err)

	// 测试关闭后查询
	_, err = reader.QueryMessagesBySession(ctx, "session-1", 10, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")
}

// TestSanitizerAdvanced 测试数据脱敏器高级功能
func TestSanitizerAdvanced(t *testing.T) {
	// 测试创建脱敏器
	config := &PrivacyConfig{
		RedactPII:     true,
		PIIPatterns:   []string{"email", "phone"},
		HashContent:   false,
		EncryptAtRest: false,
	}
	sanitizer, err := NewSanitizer(config)
	assert.NoError(t, err)
	assert.NotNil(t, sanitizer)

	// 测试脱敏内容
	content := "我的邮箱是****************，电话是13812345678"
	sanitized, err := sanitizer.SanitizeContent(content)
	assert.NoError(t, err)
	assert.Contains(t, sanitized, "[EMAIL]")
	assert.Contains(t, sanitized, "[PHONE]")

	// 测试脱敏记录
	record := &Record{
		ID:      "test-1",
		Content: "联系邮箱：<EMAIL>",
	}
	err = sanitizer.SanitizeRecord(record)
	assert.NoError(t, err)
	assert.Contains(t, record.Content, "[EMAIL]")

	// 测试空记录
	err = sanitizer.SanitizeRecord(nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "不能为空")

	// 测试空内容
	empty, err := sanitizer.SanitizeContent("")
	assert.NoError(t, err)
	assert.Equal(t, "", empty)
}

// TestEncryptor 测试加密器
func TestEncryptor(t *testing.T) {
	// 设置测试环境变量
	testKey := "12345678901234567890123456789012" // 32字节密钥
	os.Setenv("TEST_ENCRYPTION_KEY", testKey)
	defer os.Unsetenv("TEST_ENCRYPTION_KEY")

	// 测试创建加密器
	encryptor, err := NewEncryptor("TEST_ENCRYPTION_KEY")
	assert.NoError(t, err)
	assert.NotNil(t, encryptor)

	// 测试加密解密
	plaintext := "这是需要加密的敏感信息"
	encrypted, err := encryptor.Encrypt(plaintext)
	assert.NoError(t, err)
	assert.NotEqual(t, plaintext, encrypted)

	decrypted, err := encryptor.Decrypt(encrypted)
	assert.NoError(t, err)
	assert.Equal(t, plaintext, decrypted)

	// 测试空内容
	emptyEncrypted, err := encryptor.Encrypt("")
	assert.NoError(t, err)
	assert.Equal(t, "", emptyEncrypted)

	emptyDecrypted, err := encryptor.Decrypt("")
	assert.NoError(t, err)
	assert.Equal(t, "", emptyDecrypted)

	// 测试无效密钥
	_, err = NewEncryptor("INVALID_KEY_ENV")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "未找到加密密钥")
}

// TestCustomRedactor 测试自定义脱敏器
func TestCustomRedactor(t *testing.T) {
	patterns := map[string]string{
		"custom": `custom-\d+`,
		"secret": `secret:\w+`,
	}

	redactor := CreateCustomRedactor(patterns)

	content := "这里有custom-123和secret:abc123的信息"
	result := redactor(content)

	assert.Contains(t, result, "[CUSTOM]")
	assert.Contains(t, result, "[SECRET]")
	assert.NotContains(t, result, "custom-123")
	assert.NotContains(t, result, "secret:abc123")
}

// TestHashSensitiveFields 测试敏感字段哈希
func TestHashSensitiveFields(t *testing.T) {
	fields := map[string]string{
		"password": "secret123",
		"token":    "abc123def456",
		"empty":    "",
	}

	hashed := HashSensitiveFields(fields)

	assert.NotEqual(t, fields["password"], hashed["password"])
	assert.NotEqual(t, fields["token"], hashed["token"])
	assert.Equal(t, "", hashed["empty"])  // 空值应该保持为空
	assert.Len(t, hashed["password"], 64) // SHA256哈希长度
}

// TestValidateEncryptionKey 测试加密密钥验证
func TestValidateEncryptionKey(t *testing.T) {
	// 测试有效密钥
	validKey := "12345678901234567890123456789012" // 32字节
	err := ValidateEncryptionKey(validKey)
	assert.NoError(t, err)

	// 测试无效密钥长度
	invalidKey := "short"
	err = ValidateEncryptionKey(invalidKey)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "长度必须为32字节")
}

// TestAuditErrors 测试审计错误类型
func TestAuditErrors(t *testing.T) {
	// 测试基础审计错误
	baseErr := NewAuditError("TEST_CODE", "测试错误", nil)
	assert.NotNil(t, baseErr)
	assert.Equal(t, "TEST_CODE", baseErr.Code)
	assert.Equal(t, "测试错误", baseErr.Message)
	assert.Contains(t, baseErr.Error(), "TEST_CODE")
	assert.Contains(t, baseErr.Error(), "测试错误")

	// 测试带原因的错误
	cause := errors.New("原始错误")
	errWithCause := NewAuditError("TEST_CODE", "测试错误", cause)
	assert.Equal(t, cause, errWithCause.Cause)

	// 测试特定错误类型
	invalidRecordErr := NewInvalidRecordError("无效记录", nil)
	assert.Equal(t, "INVALID_RECORD", invalidRecordErr.Code)

	storeOpErr := NewStoreOperationError("存储操作失败", nil)
	assert.Equal(t, "STORE_OPERATION_FAILED", storeOpErr.Code)

	encryptionErr := NewEncryptionError("加密失败", nil)
	assert.Equal(t, "ENCRYPTION_FAILED", encryptionErr.Code)

	writerClosedErr := NewWriterClosedError()
	assert.Equal(t, "WRITER_CLOSED", writerClosedErr.Code)

	writerFullErr := NewWriterFullError()
	assert.Equal(t, "WRITER_BUFFER_FULL", writerFullErr.Code)
}

// TestDefaultPIIPatterns 测试默认PII模式
func TestDefaultPIIPatterns(t *testing.T) {
	patterns := GetDefaultPIIPatterns()
	assert.NotEmpty(t, patterns)

	// 验证包含基本模式
	assert.Contains(t, patterns, "email")
	assert.Contains(t, patterns, "phone")
	assert.Contains(t, patterns, "idcard")
	assert.Contains(t, patterns, "creditcard")

	// 测试邮箱模式
	emailPattern := patterns["email"]
	assert.NotEmpty(t, emailPattern.Pattern)
	assert.NotEmpty(t, emailPattern.Replace)
}

// TestQueryValidation 测试查询验证
func TestQueryValidation(t *testing.T) {
	// 测试有效查询
	validQuery := Query{
		Limit:  10,
		Offset: 0,
	}
	err := ValidateQuery(validQuery)
	assert.NoError(t, err)

	// 测试Limit为0（应该设置默认值）
	zeroLimitQuery := Query{
		Limit:  0,
		Offset: 0,
	}
	err = ValidateQuery(zeroLimitQuery)
	assert.NoError(t, err)

	// 测试负数Limit
	invalidQuery := Query{
		Limit:  -1,
		Offset: 0,
	}
	err = ValidateQuery(invalidQuery)
	assert.Error(t, err)

	// 测试负数Offset
	invalidQuery2 := Query{
		Limit:  10,
		Offset: -1,
	}
	err = ValidateQuery(invalidQuery2)
	assert.Error(t, err)

	// 测试时间范围错误
	now := time.Now()
	past := now.Add(-time.Hour)
	invalidTimeQuery := Query{
		Limit:  10,
		Offset: 0,
		Since:  &now,
		Until:  &past,
	}
	err = ValidateQuery(invalidTimeQuery)
	assert.Error(t, err)
}

// TestRecordValidation 测试记录验证
func TestRecordValidation(t *testing.T) {
	// 测试有效记录
	validRecord := &Record{
		ID:        "test-1",
		SessionID: "session-1",
		UserID:    "user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}
	err := ValidateRecord(validRecord)
	assert.NoError(t, err)

	// 测试空ID
	invalidRecord := &Record{
		SessionID: "session-1",
		UserID:    "user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}
	err = ValidateRecord(invalidRecord)
	assert.Error(t, err)

	// 测试空SessionID
	invalidRecord2 := &Record{
		ID:        "test-1",
		UserID:    "user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}
	err = ValidateRecord(invalidRecord2)
	assert.Error(t, err)
}

// TestSessionSummary 测试会话摘要
func TestSessionSummary(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 添加测试数据
	testRecord := &Record{
		ID:        "test-1",
		SessionID: "session-1",
		UserID:    "user-1",
		AgentID:   "agent-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "test content",
		CreatedAt: time.Now(),
	}
	store.records = append(store.records, testRecord)

	reader := NewAuditReader(store, sanitizer)
	ctx := context.Background()

	// 测试获取会话摘要
	summary, err := reader.GetSessionSummary(ctx, "session-1")
	assert.NoError(t, err)
	assert.NotNil(t, summary)
	assert.Equal(t, "session-1", summary.SessionID)
	assert.Equal(t, 1, summary.MessageCount)

	// 测试不存在的会话
	_, err = reader.GetSessionSummary(ctx, "nonexistent-session")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "会话不存在或无消息")
}

// TestUserActivity 测试用户活动统计
func TestUserActivity(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	reader := NewAuditReader(store, sanitizer)
	ctx := context.Background()

	// 测试获取用户活动
	since := time.Now().Add(-24 * time.Hour)
	activity, err := reader.GetUserActivity(ctx, "user-1", &since)
	assert.NoError(t, err)
	assert.NotNil(t, activity)
}

// TestRetentionPolicy 测试数据保留策略
func TestRetentionPolicy(t *testing.T) {
	// 测试创建保留策略
	policy := &RetentionConfig{
		Enabled: true,
		MaxDays: 30,
		Cron:    "0 2 * * *",
	}

	assert.True(t, policy.Enabled)
	assert.Equal(t, 30, policy.MaxDays)
	assert.Equal(t, "0 2 * * *", policy.Cron)

	// 测试默认配置
	config := DefaultConfig()
	assert.NotNil(t, config)
	assert.NotNil(t, config.Retention)
	assert.NotNil(t, config.Privacy)
	assert.NotNil(t, config.Batch)
}

// TestConfigValidation 测试配置验证
func TestConfigValidation(t *testing.T) {
	// 测试有效配置
	validConfig := &Config{
		Enabled: true,
		Driver:  "sqlite",
		DSN:     ":memory:",
		Privacy: PrivacyConfig{
			RedactPII:   true,
			PIIPatterns: []string{"email"},
		},
		Retention: RetentionConfig{
			Enabled: true,
			MaxDays: 30,
		},
		Batch: BatchConfig{
			Async:         true,
			ChanBuffer:    1024,
			FlushInterval: time.Second,
		},
	}

	assert.NotNil(t, validConfig)
	assert.Equal(t, "sqlite", validConfig.Driver)
	assert.True(t, validConfig.Privacy.RedactPII)
	assert.True(t, validConfig.Retention.Enabled)
	assert.True(t, validConfig.Batch.Async)
}

// TestAsyncWriterConfig 测试异步写入器配置
func TestAsyncWriterConfig(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 测试异步配置
	asyncConfig := &BatchConfig{
		Async:         true,
		ChanBuffer:    512,
		FlushInterval: 500 * time.Millisecond,
	}

	writer := NewAuditWriter(store, sanitizer, asyncConfig)
	assert.NotNil(t, writer)

	ctx := context.Background()
	err = writer.Start(ctx)
	assert.NoError(t, err)
	defer writer.Close()

	// 测试异步写入
	record := &Record{
		ID:        "async-test",
		SessionID: "session-async",
		UserID:    "user-async",
		Role:      "user",
		MsgType:   "text",
		Content:   "async test content",
		CreatedAt: time.Now(),
	}

	err = writer.WriteRecord(ctx, record)
	assert.NoError(t, err)

	// 等待异步处理
	time.Sleep(100 * time.Millisecond)

	// 验证统计信息
	stats := writer.GetStats()
	assert.NotNil(t, stats)
	assert.GreaterOrEqual(t, stats.PendingRequests, 0)
}

// TestEncryptionIntegration 测试加密集成
func TestEncryptionIntegration(t *testing.T) {
	// 设置加密环境
	testKey := "12345678901234567890123456789012"
	os.Setenv("TEST_AUDIT_KEY", testKey)
	defer os.Unsetenv("TEST_AUDIT_KEY")

	// 测试带加密的配置
	config := &PrivacyConfig{
		RedactPII:     true,
		PIIPatterns:   []string{"email", "phone"},
		HashContent:   false,
		EncryptAtRest: true,
		EncryptKeyEnv: "TEST_AUDIT_KEY",
	}

	sanitizer, err := NewSanitizer(config)
	assert.NoError(t, err)
	assert.NotNil(t, sanitizer)

	// 测试加密内容
	content := "敏感信息：<EMAIL>"
	sanitized, err := sanitizer.SanitizeContent(content)
	assert.NoError(t, err)
	assert.NotEqual(t, content, sanitized)
	// 加密模式下内容会被加密，不会包含[EMAIL]标记
	assert.NotContains(t, sanitized, "<EMAIL>")
}

// TestBatchProcessing 测试批量处理
func TestBatchProcessing(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 配置小批量大小以便测试
	config := &BatchConfig{
		Async:         true,
		ChanBuffer:    100,
		FlushInterval: 50 * time.Millisecond,
	}

	writer := NewAuditWriter(store, sanitizer, config)
	writer.SetBatchSize(2) // 设置小批量大小

	ctx := context.Background()
	err = writer.Start(ctx)
	require.NoError(t, err)
	defer writer.Close()

	// 写入多条记录触发批量处理
	for i := 0; i < 5; i++ {
		record := &Record{
			ID:        fmt.Sprintf("batch-test-%d", i),
			SessionID: "batch-session",
			UserID:    "batch-user",
			Role:      "user",
			MsgType:   "text",
			Content:   fmt.Sprintf("batch content %d", i),
			CreatedAt: time.Now(),
		}

		err = writer.WriteRecord(ctx, record)
		assert.NoError(t, err)
	}

	// 等待批量处理完成
	time.Sleep(200 * time.Millisecond)

	// 验证记录已保存
	assert.GreaterOrEqual(t, len(store.records), 2) // 至少一个批次已处理
}

// TestSQLiteStoreOperations 测试SQLite存储操作
func TestSQLiteStoreOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过SQLite存储测试")
	}

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_operations.db")

	config := DefaultConfig()
	store, err := NewSQLiteStore(dbPath, config)
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// 测试保存消息
	record := &Record{
		ID:        "sqlite-test-1",
		SessionID: "sqlite-session-1",
		UserID:    "sqlite-user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "SQLite test content",
		CreatedAt: time.Now(),
	}

	err = store.SaveMessage(ctx, record)
	assert.NoError(t, err)

	// 测试查询消息
	query := Query{
		SessionID: &record.SessionID,
		Limit:     10,
	}
	result, err := store.QueryMessages(ctx, query)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.Records))
	assert.Equal(t, record.ID, result.Records[0].ID)

	// 测试保存会话
	err = store.SaveSession(ctx, "sqlite-session-1", "sqlite-user-1", map[string]any{"test": "data"})
	assert.NoError(t, err)

	// 测试查询会话
	sessions, total, err := store.QuerySessions(ctx, "sqlite-user-1", 10, 0)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, total, 1)
	assert.NotEmpty(t, sessions)

	// 测试更新会话活跃时间
	err = store.TouchSession(ctx, "sqlite-session-1", time.Now())
	assert.NoError(t, err)

	// 测试数据保留
	err = store.RunRetention(ctx)
	assert.NoError(t, err)
}

// TestPostgresStoreOperations 测试PostgreSQL存储操作
func TestPostgresStoreOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过PostgreSQL存储测试")
	}

	// 检查是否有PostgreSQL环境变量
	dsn := os.Getenv("POSTGRES_TEST_DSN")
	if dsn == "" {
		t.Skip("跳过PostgreSQL测试：未设置POSTGRES_TEST_DSN环境变量")
	}

	config := DefaultConfig()
	store, err := NewPostgresStore(dsn, config)
	if err != nil {
		t.Skipf("跳过PostgreSQL测试：连接失败 %v", err)
	}
	defer store.Close()

	ctx := context.Background()

	// 测试保存消息
	record := &Record{
		ID:        "postgres-test-1",
		SessionID: "postgres-session-1",
		UserID:    "postgres-user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "PostgreSQL test content",
		CreatedAt: time.Now(),
	}

	err = store.SaveMessage(ctx, record)
	assert.NoError(t, err)

	// 测试查询消息
	query := Query{
		SessionID: &record.SessionID,
		Limit:     10,
	}
	result, err := store.QueryMessages(ctx, query)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.GreaterOrEqual(t, len(result.Records), 1)
}

// TestWriterErrorHandling 测试写入器错误处理
func TestWriterErrorHandling(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	config := &BatchConfig{
		Async:         false,
		FlushInterval: time.Second,
	}

	writer := NewAuditWriter(store, sanitizer, config)
	ctx := context.Background()

	// 测试写入nil记录（同步模式）
	err = writer.WriteRecord(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "不能为空")

	// 启动写入器
	err = writer.Start(ctx)
	require.NoError(t, err)
	defer writer.Close()

	// 测试正常写入
	record := &Record{
		ID:        "error-test-1",
		SessionID: "error-session",
		UserID:    "error-user",
		Role:      "user",
		MsgType:   "text",
		Content:   "error test content",
		CreatedAt: time.Now(),
	}

	err = writer.WriteRecord(ctx, record)
	assert.NoError(t, err)

	// 测试重复启动
	err = writer.Start(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已经启动")

	// 关闭写入器
	err = writer.Close()
	assert.NoError(t, err)

	// 测试关闭后写入
	err = writer.WriteRecord(ctx, record)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")
}

// TestReaderErrorHandling 测试查询器错误处理
func TestReaderErrorHandling(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	reader := NewAuditReader(store, sanitizer)
	ctx := context.Background()

	// 关闭查询器
	err = reader.Close()
	assert.NoError(t, err)

	// 测试关闭后的各种查询操作
	_, err = reader.QueryMessagesBySession(ctx, "session-1", 10, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")

	_, err = reader.QueryMessagesByUser(ctx, "user-1", 10, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")

	_, err = reader.QueryMessagesByAgent(ctx, "agent-1", 10, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")

	_, _, err = reader.QuerySessions(ctx, "user-1", 10, 0)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")

	_, err = reader.GetSessionSummary(ctx, "session-1")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")

	_, err = reader.GetUserActivity(ctx, "user-1", nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已关闭")
}

// TestComplexQueryScenarios 测试复杂查询场景
func TestComplexQueryScenarios(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 添加多种类型的测试数据
	records := []*Record{
		{
			ID:        "complex-1",
			SessionID: "session-1",
			UserID:    "user-1",
			AgentID:   "agent-1",
			Role:      "user",
			MsgType:   "text",
			Content:   "用户消息1",
			CreatedAt: time.Now().Add(-2 * time.Hour),
		},
		{
			ID:        "complex-2",
			SessionID: "session-1",
			UserID:    "user-1",
			AgentID:   "agent-1",
			Role:      "assistant",
			MsgType:   "text",
			Content:   "助手回复1",
			CreatedAt: time.Now().Add(-1 * time.Hour),
		},
		{
			ID:        "complex-3",
			SessionID: "session-2",
			UserID:    "user-2",
			AgentID:   "agent-2",
			Role:      "user",
			MsgType:   "tool_result",
			Content:   "工具调用结果",
			ToolName:  "test_tool",
			CreatedAt: time.Now(),
		},
	}

	store.records = records

	reader := NewAuditReader(store, sanitizer)
	ctx := context.Background()

	// 测试按时间范围查询
	since := time.Now().Add(-3 * time.Hour)
	until := time.Now().Add(time.Hour)
	result, err := reader.QueryMessagesByTimeRange(ctx, &since, &until, 10, 0)
	assert.NoError(t, err)
	assert.Equal(t, 3, len(result.Records))

	// 测试按关键词查询
	result, err = reader.QueryMessagesByKeyword(ctx, "助手", 10, 0)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(result.Records), 1)

	// 测试按消息类型查询
	result, err = reader.QueryMessagesByType(ctx, []string{"text", "tool_result"}, 10, 0)
	assert.NoError(t, err)
	assert.Equal(t, 3, len(result.Records))

	// 测试会话摘要
	summary, err := reader.GetSessionSummary(ctx, "session-1")
	assert.NoError(t, err)
	assert.NotNil(t, summary)
	assert.Equal(t, "session-1", summary.SessionID)
	assert.Equal(t, 2, summary.MessageCount)
	assert.Equal(t, 1, summary.UserMessages)
	assert.Equal(t, 1, summary.AgentMessages)

	// 测试用户活动统计
	activitySince := time.Now().Add(-24 * time.Hour)
	activity, err := reader.GetUserActivity(ctx, "user-1", &activitySince)
	assert.NoError(t, err)
	assert.NotNil(t, activity)
	assert.Equal(t, "user-1", activity.UserID)
}

// TestSanitizerEdgeCases 测试脱敏器边界情况
func TestSanitizerEdgeCases(t *testing.T) {
	// 测试无PII模式的脱敏器
	config := &PrivacyConfig{
		RedactPII:   false,
		PIIPatterns: []string{},
	}
	sanitizer, err := NewSanitizer(config)
	assert.NoError(t, err)

	// 测试不脱敏的内容
	content := "<EMAIL> 和 13812345678"
	sanitized, err := sanitizer.SanitizeContent(content)
	assert.NoError(t, err)
	assert.Equal(t, content, sanitized) // 应该保持不变

	// 测试哈希模式
	hashConfig := &PrivacyConfig{
		RedactPII:   true,
		PIIPatterns: []string{"email"},
		HashContent: true,
	}
	hashSanitizer, err := NewSanitizer(hashConfig)
	assert.NoError(t, err)

	record := &Record{
		ID:      "hash-test",
		Content: "敏感内容需要哈希",
	}

	err = hashSanitizer.SanitizeRecord(record)
	assert.NoError(t, err)
	assert.NotEmpty(t, record.ContentHash)

	// 测试反脱敏（无加密器）
	unsanitized, err := sanitizer.UnsanitizeContent("test content")
	assert.NoError(t, err)
	assert.Equal(t, "test content", unsanitized)
}

// TestWriterChannelFull 测试写入器通道满的情况
func TestWriterChannelFull(t *testing.T) {
	store := &MockStore{}
	sanitizer, err := NewSanitizer(&PrivacyConfig{})
	require.NoError(t, err)

	// 配置很小的通道缓冲区
	config := &BatchConfig{
		Async:         true,
		ChanBuffer:    1,         // 很小的缓冲区
		FlushInterval: time.Hour, // 很长的刷新间隔
	}

	writer := NewAuditWriter(store, sanitizer, config)
	ctx := context.Background()

	err = writer.Start(ctx)
	require.NoError(t, err)
	defer writer.Close()

	// 快速写入多条记录，可能导致通道满
	for i := 0; i < 10; i++ {
		record := &Record{
			ID:        fmt.Sprintf("channel-test-%d", i),
			SessionID: "channel-session",
			UserID:    "channel-user",
			Role:      "user",
			MsgType:   "text",
			Content:   fmt.Sprintf("channel content %d", i),
			CreatedAt: time.Now(),
		}

		// 某些写入可能会因为通道满而失败
		writer.WriteRecord(ctx, record)
	}

	// 验证统计信息
	stats := writer.GetStats()
	assert.NotNil(t, stats)
}

// TestDefaultConfigValues 测试默认配置值
func TestDefaultConfigValues(t *testing.T) {
	config := DefaultConfig()

	assert.NotNil(t, config)
	assert.False(t, config.Enabled) // 默认配置中Enabled为false
	assert.Equal(t, "sqlite", config.Driver)
	assert.NotEmpty(t, config.DSN)

	// 验证默认隐私配置
	assert.True(t, config.Privacy.RedactPII)
	assert.NotEmpty(t, config.Privacy.PIIPatterns)

	// 验证默认保留配置
	assert.True(t, config.Retention.Enabled)
	assert.Greater(t, config.Retention.MaxDays, 0)
	assert.NotEmpty(t, config.Retention.Cron)

	// 验证默认批处理配置
	assert.True(t, config.Batch.Async)
	assert.Greater(t, config.Batch.ChanBuffer, 0)
	assert.Greater(t, int64(config.Batch.FlushInterval), int64(0))
}

// TestPIIPatternsMatching 测试PII模式匹配
func TestPIIPatternsMatching(t *testing.T) {
	patterns := GetDefaultPIIPatterns()

	// 测试邮箱模式
	emailPattern := patterns["email"]
	assert.NotEmpty(t, emailPattern.Pattern)

	// 测试电话模式
	phonePattern := patterns["phone"]
	assert.NotEmpty(t, phonePattern.Pattern)

	// 测试身份证模式
	idPattern := patterns["idcard"]
	assert.NotEmpty(t, idPattern.Pattern)

	// 测试信用卡模式
	cardPattern := patterns["creditcard"]
	assert.NotEmpty(t, cardPattern.Pattern)
}
