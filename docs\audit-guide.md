# AgentScope-Golang 对话审计功能指南

## 概述

对话审计功能为AgentScope-Golang ADK提供了完整的对话记录、查询和管理能力。该功能支持：

- 自动记录所有用户与Agent之间的对话
- 数据隐私保护和PII脱敏
- 灵活的查询和检索功能
- 自动化数据保留策略
- 高性能异步批处理
- 支持SQLite和PostgreSQL数据库

## 快速开始

### 1. 启用审计功能

在配置文件中添加审计配置：

```yaml
audit:
  enabled: true
  driver: "sqlite"
  dsn: "./audit.db"
  privacy:
    redact_pii: true
    pii_patterns: ["email", "phone"]
  batch:
    async: true
    chan_buffer: 1024
    flush_interval: "1s"
```

### 2. 启动应用

```bash
go run examples/simple_chat/main.go
```

### 3. 验证审计功能

发送消息后，审计数据会自动记录到数据库中。

## 配置详解

### 基础配置

```yaml
audit:
  enabled: true          # 启用审计功能
  driver: "sqlite"       # 数据库驱动：sqlite 或 postgres
  dsn: "./audit.db"      # 数据库连接字符串
```

### 数据库配置

#### SQLite配置
```yaml
audit:
  driver: "sqlite"
  dsn: "./audit.db"     # 数据库文件路径
```

#### PostgreSQL配置
```yaml
audit:
  driver: "postgres"
  dsn: "postgres://user:password@localhost/audit_db?sslmode=disable"
```

### 隐私保护配置

```yaml
audit:
  privacy:
    redact_pii: true                    # 启用PII脱敏
    pii_patterns:                       # PII模式列表
      - "email"                         # 邮箱地址
      - "phone"                         # 电话号码
      - "id_card"                       # 身份证号
      - "credit_card"                   # 信用卡号
      - "ip"                           # IP地址
    hash_content: false                 # 内容哈希
    encrypt_at_rest: false              # 静态加密
    encrypt_key_env: "AUDIT_AES_KEY"    # 加密密钥环境变量
```

### 批处理配置

```yaml
audit:
  batch:
    async: true                         # 异步批处理
    chan_buffer: 1024                   # 通道缓冲区大小
    flush_interval: "1s"                # 刷新间隔
```

### 数据保留配置

```yaml
audit:
  retention:
    enabled: true                       # 启用自动清理
    max_days: 90                        # 保留天数
    cron: "@daily"                      # 执行时间
```

### Web API配置

```yaml
audit:
  web:
    allow_read_api: false               # 允许查询API
    allow_delete_api: false             # 允许删除API
```

## API参考

### 查询会话列表

```http
GET /api/v1/audit/sessions?user_id=user123&page=1&page_size=20
```

响应：
```json
{
  "sessions": [
    {
      "session_id": "session-123",
      "user_id": "user123",
      "created_at": "2024-01-01T10:00:00Z",
      "last_active_at": "2024-01-01T10:30:00Z",
      "message_count": 15
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 20,
  "has_more": false
}
```

### 查询会话消息

```http
GET /api/v1/audit/sessions/{session_id}/messages?page=1&page_size=50
```

响应：
```json
{
  "messages": [
    {
      "id": "msg-123",
      "session_id": "session-123",
      "user_id": "user123",
      "role": "user",
      "msg_type": "text",
      "content": "你好",
      "created_at": "2024-01-01T10:00:00Z"
    },
    {
      "id": "msg-124",
      "session_id": "session-123",
      "agent_id": "assistant-001",
      "role": "assistant",
      "msg_type": "final",
      "content": "你好！有什么可以帮助您的吗？",
      "created_at": "2024-01-01T10:00:05Z"
    }
  ],
  "total": 2,
  "page": 1,
  "page_size": 50,
  "has_more": false
}
```

### 搜索消息

```http
GET /api/v1/audit/search?q=关键词&user_id=user123&page=1&page_size=20
```

### 获取会话摘要

```http
GET /api/v1/audit/sessions/{session_id}/summary
```

响应：
```json
{
  "session_id": "session-123",
  "user_id": "user123",
  "message_count": 15,
  "first_message_at": "2024-01-01T10:00:00Z",
  "last_message_at": "2024-01-01T10:30:00Z",
  "duration_minutes": 30,
  "agents": ["assistant-001"],
  "message_types": ["text", "final"]
}
```

### 获取用户活动

```http
GET /api/v1/audit/users/{user_id}/activity?since=2024-01-01T00:00:00Z
```

## 最佳实践

### 生产环境配置

1. **数据库选择**：
   - 小规模应用：使用SQLite
   - 大规模应用：使用PostgreSQL

2. **隐私保护**：
   - 始终启用PII脱敏
   - 根据需要启用内容加密
   - 定期审查PII模式配置

3. **性能优化**：
   - 使用异步批处理
   - 适当调整缓冲区大小
   - 设置合理的刷新间隔

4. **数据管理**：
   - 设置合理的数据保留期
   - 定期备份审计数据
   - 监控存储空间使用

5. **安全配置**：
   - 生产环境关闭查询API
   - 严格控制删除API访问
   - 使用强加密密钥

### 故障排查

#### 常见问题

1. **审计数据未记录**
   - 检查`audit.enabled`是否为true
   - 验证数据库连接配置
   - 查看应用日志中的错误信息

2. **数据库连接失败**
   - 验证DSN配置格式
   - 检查数据库服务是否运行
   - 确认数据库权限设置

3. **PII脱敏不生效**
   - 检查`privacy.redact_pii`配置
   - 验证PII模式配置
   - 查看脱敏器日志

4. **性能问题**
   - 调整批处理配置
   - 检查数据库索引
   - 监控系统资源使用

#### 调试方法

1. **启用调试日志**：
```yaml
logging:
  level: "debug"
```

2. **检查审计统计**：
```bash
curl http://localhost:8080/api/v1/audit/stats
```

3. **验证数据库表**：
```sql
-- SQLite
.tables
SELECT COUNT(*) FROM audit_messages;

-- PostgreSQL
\dt
SELECT COUNT(*) FROM audit_messages;
```

## 环境变量

| 变量名 | 描述 | 示例 |
|--------|------|------|
| `AUDIT_AES_KEY` | 审计数据加密密钥 | `your-32-byte-encryption-key-here` |
| `RUN_INTEGRATION_TESTS` | 启用集成测试 | `1` |
| `RUN_CONCURRENT_TESTS` | 启用并发测试 | `1` |

## 监控指标

审计系统提供以下监控指标：

- `audit_writes_total`: 总写入次数
- `audit_write_errors_total`: 写入错误次数
- `audit_batch_size`: 批处理大小
- `audit_flush_duration`: 刷新耗时
- `audit_queue_size`: 队列大小

## 数据模型

### Record（消息记录）

```go
type Record struct {
    ID        string    `json:"id"`         // 记录唯一ID
    SessionID string    `json:"session_id"` // 会话ID
    UserID    string    `json:"user_id"`    // 用户ID
    AgentID   string    `json:"agent_id"`   // Agent ID
    Role      string    `json:"role"`       // 角色：user/assistant/tool
    MsgType   string    `json:"msg_type"`   // 消息类型：text/final/token/error
    Content   string    `json:"content"`    // 消息内容
    EventType string    `json:"event_type"` // 事件类型
    ToolName  string    `json:"tool_name"`  // 工具名称
    CreatedAt time.Time `json:"created_at"` // 创建时间
}
```

### Session（会话）

```go
type Session struct {
    SessionID    string                 `json:"session_id"`     // 会话ID
    UserID       string                 `json:"user_id"`        // 用户ID
    CreatedAt    time.Time              `json:"created_at"`     // 创建时间
    LastActiveAt time.Time              `json:"last_active_at"` // 最后活跃时间
    MessageCount int                    `json:"message_count"`  // 消息数量
    Metadata     map[string]interface{} `json:"metadata"`       // 元数据
}
```

### Query（查询条件）

```go
type Query struct {
    SessionID *string    `json:"session_id,omitempty"` // 会话ID过滤
    UserID    *string    `json:"user_id,omitempty"`    // 用户ID过滤
    AgentID   *string    `json:"agent_id,omitempty"`   // Agent ID过滤
    Roles     []string   `json:"roles,omitempty"`      // 角色过滤
    Types     []string   `json:"types,omitempty"`      // 消息类型过滤
    Since     *time.Time `json:"since,omitempty"`      // 开始时间
    Until     *time.Time `json:"until,omitempty"`      // 结束时间
    Keyword   string     `json:"keyword,omitempty"`    // 关键词搜索
    Limit     int        `json:"limit"`                // 限制数量
    Offset    int        `json:"offset"`               // 偏移量
}
```

## 许可证

本功能遵循项目主许可证。
