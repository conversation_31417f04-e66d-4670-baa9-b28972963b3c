package logger

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"github.com/sirupsen/logrus"
)

// EnhancedLogger 增强的日志记录器，支持更多功能
type EnhancedLogger struct {
	*logrusLogger
	enableCaller   bool
	enableDuration bool
}

// EnhancedConfig 增强日志配置
type EnhancedConfig struct {
	*Config
	EnableCaller   bool // 是否记录调用位置
	EnableDuration bool // 是否记录操作耗时
}

// NewEnhancedLogger 创建增强的日志记录器
func NewEnhancedLogger(config *EnhancedConfig) (Logger, error) {
	if config == nil {
		config = &EnhancedConfig{
			Config:         DefaultConfig(),
			EnableCaller:   true,
			EnableDuration: true,
		}
	}

	baseLogger, err := NewLogger(config.Config)
	if err != nil {
		return nil, err
	}

	return &EnhancedLogger{
		logrusLogger:   baseLogger.(*logrusLogger),
		enableCaller:   config.EnableCaller,
		enableDuration: config.EnableDuration,
	}, nil
}

// WithOperation 记录操作开始，返回一个可以记录操作结束的函数
func (l *EnhancedLogger) WithOperation(operation string) func(error) {
	start := time.Now()
	
	fields := logrus.Fields{
		"operation": operation,
	}
	
	if l.enableCaller {
		if pc, file, line, ok := runtime.Caller(1); ok {
			if fn := runtime.FuncForPC(pc); fn != nil {
				fields["caller"] = fmt.Sprintf("%s:%d %s", file, line, fn.Name())
			}
		}
	}

	l.WithFields(fields).Info("操作开始")

	return func(err error) {
		duration := time.Since(start)
		endFields := logrus.Fields{
			"operation": operation,
		}

		if l.enableDuration {
			endFields["duration"] = duration.String()
			endFields["duration_ms"] = duration.Milliseconds()
		}

		if err != nil {
			l.WithFields(endFields).WithError(err).Error("操作失败")
		} else {
			l.WithFields(endFields).Info("操作成功")
		}
	}
}

// WithContext 从上下文中提取日志字段
func (l *EnhancedLogger) WithContext(ctx context.Context) Logger {
	fields := extractFieldsFromContext(ctx)
	if len(fields) > 0 {
		return l.WithFields(fields)
	}
	return l
}

// WithAgentScopeError 专门处理 AgentScope 错误的日志记录
func (l *EnhancedLogger) WithAgentScopeError(err error) Logger {
	// 尝试转换为 AgentScopeError
	if asErr, ok := err.(interface {
		Error() string
		Type() string
		Code() string
		Context() map[string]interface{}
	}); ok {
		fields := logrus.Fields{
			"error_type": asErr.Type(),
			"error_code": asErr.Code(),
		}

		// 添加错误上下文
		if ctx := asErr.Context(); ctx != nil {
			for k, v := range ctx {
				fields["error_"+k] = v
			}
		}

		return l.WithFields(fields).WithError(err)
	}

	return l.WithError(err)
}

// LogSlowOperation 记录慢操作
func (l *EnhancedLogger) LogSlowOperation(operation string, duration time.Duration, threshold time.Duration) {
	if duration > threshold {
		fields := logrus.Fields{
			"operation":      operation,
			"duration":       duration.String(),
			"duration_ms":    duration.Milliseconds(),
			"threshold":      threshold.String(),
			"threshold_ms":   threshold.Milliseconds(),
			"slow_operation": true,
		}

		if l.enableCaller {
			if pc, file, line, ok := runtime.Caller(1); ok {
				if fn := runtime.FuncForPC(pc); fn != nil {
					fields["caller"] = fmt.Sprintf("%s:%d %s", file, line, fn.Name())
				}
			}
		}

		l.WithFields(fields).Warn("检测到慢操作")
	}
}

// LogMemoryUsage 记录内存使用情况
func (l *EnhancedLogger) LogMemoryUsage(component string) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	fields := logrus.Fields{
		"component":       component,
		"alloc_mb":        bToMb(m.Alloc),
		"total_alloc_mb":  bToMb(m.TotalAlloc),
		"sys_mb":          bToMb(m.Sys),
		"num_gc":          m.NumGC,
		"goroutines":      runtime.NumGoroutine(),
	}

	l.WithFields(fields).Debug("内存使用情况")
}

// LogPanic 记录 panic 信息
func (l *EnhancedLogger) LogPanic(recovered interface{}, stack []byte) {
	fields := logrus.Fields{
		"panic_value": recovered,
		"stack_trace": string(stack),
	}

	if l.enableCaller {
		if pc, file, line, ok := runtime.Caller(2); ok {
			if fn := runtime.FuncForPC(pc); fn != nil {
				fields["panic_location"] = fmt.Sprintf("%s:%d %s", file, line, fn.Name())
			}
		}
	}

	l.WithFields(fields).Error("系统发生 panic")
}

// bToMb 将字节转换为 MB
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

// extractFieldsFromContext 从上下文中提取日志字段
func extractFieldsFromContext(ctx context.Context) map[string]interface{} {
	fields := make(map[string]interface{})

	// 提取常见的上下文字段
	if requestID := ctx.Value("request_id"); requestID != nil {
		fields["request_id"] = requestID
	}

	if sessionID := ctx.Value("session_id"); sessionID != nil {
		fields["session_id"] = sessionID
	}

	if userID := ctx.Value("user_id"); userID != nil {
		fields["user_id"] = userID
	}

	if agentID := ctx.Value("agent_id"); agentID != nil {
		fields["agent_id"] = agentID
	}

	if traceID := ctx.Value("trace_id"); traceID != nil {
		fields["trace_id"] = traceID
	}

	return fields
}

// PerformanceLogger 性能日志记录器
type PerformanceLogger struct {
	logger    Logger
	threshold time.Duration
}

// NewPerformanceLogger 创建性能日志记录器
func NewPerformanceLogger(logger Logger, threshold time.Duration) *PerformanceLogger {
	if threshold <= 0 {
		threshold = 100 * time.Millisecond // 默认阈值
	}

	return &PerformanceLogger{
		logger:    logger,
		threshold: threshold,
	}
}

// Track 跟踪操作性能
func (p *PerformanceLogger) Track(operation string) func() {
	start := time.Now()
	
	return func() {
		duration := time.Since(start)
		
		fields := map[string]interface{}{
			"operation":   operation,
			"duration":    duration.String(),
			"duration_ms": duration.Milliseconds(),
		}

		if duration > p.threshold {
			fields["slow_operation"] = true
			p.logger.WithFields(fields).Warn("慢操作检测")
		} else {
			p.logger.WithFields(fields).Debug("操作完成")
		}
	}
}

// TrackWithContext 带上下文的性能跟踪
func (p *PerformanceLogger) TrackWithContext(ctx context.Context, operation string) func(error) {
	start := time.Now()
	
	return func(err error) {
		duration := time.Since(start)
		
		fields := map[string]interface{}{
			"operation":   operation,
			"duration":    duration.String(),
			"duration_ms": duration.Milliseconds(),
		}

		// 从上下文提取字段
		if ctxFields := extractFieldsFromContext(ctx); len(ctxFields) > 0 {
			for k, v := range ctxFields {
				fields[k] = v
			}
		}

		logger := p.logger.WithFields(fields)

		if err != nil {
			logger.WithError(err).Error("操作失败")
		} else if duration > p.threshold {
			fields["slow_operation"] = true
			logger.Warn("慢操作检测")
		} else {
			logger.Debug("操作成功")
		}
	}
}

// ErrorLogger 错误日志记录器
type ErrorLogger struct {
	logger Logger
}

// NewErrorLogger 创建错误日志记录器
func NewErrorLogger(logger Logger) *ErrorLogger {
	return &ErrorLogger{
		logger: logger,
	}
}

// LogError 记录错误，自动提取错误信息
func (e *ErrorLogger) LogError(ctx context.Context, err error, operation string, additionalFields ...map[string]interface{}) {
	fields := map[string]interface{}{
		"operation": operation,
	}

	// 合并额外字段
	for _, additional := range additionalFields {
		for k, v := range additional {
			fields[k] = v
		}
	}

	// 从上下文提取字段
	if ctxFields := extractFieldsFromContext(ctx); len(ctxFields) > 0 {
		for k, v := range ctxFields {
			fields[k] = v
		}
	}

	// 尝试提取 AgentScope 错误信息
	if asErr, ok := err.(interface {
		Type() string
		Code() string
		Context() map[string]interface{}
	}); ok {
		fields["error_type"] = asErr.Type()
		fields["error_code"] = asErr.Code()
		
		if errCtx := asErr.Context(); errCtx != nil {
			for k, v := range errCtx {
				fields["error_"+k] = v
			}
		}
	}

	e.logger.WithFields(fields).WithError(err).Error("操作错误")
}
