package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"gopkg.in/yaml.v3"
)

// Loader 配置加载器
type Loader struct {
	logger logger.Logger
}

// NewLoader 创建新的配置加载器
func NewLoader() *Loader {
	return &Loader{
		logger: logger.GetGlobalLogger(),
	}
}

// LoadFromFile 从文件加载配置
func (l *Loader) LoadFromFile(filePath string) (*Config, error) {
	if filePath == "" {
		return nil, errors.NewValidationError("invalid_input", "file path cannot be empty")
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, errors.NewValidationError("file_not_found", fmt.Sprintf("config file not found: %s", filePath))
	}

	// 读取文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "read_failed", fmt.Sprintf("failed to read config file: %s", filePath))
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "parse_failed", fmt.Sprintf("failed to parse config file: %s", filePath))
	}

	// 应用环境变量覆盖
	l.applyEnvironmentOverrides(&config)

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "validation_failed", "config validation failed")
	}

	l.logger.Info("Configuration loaded successfully from %s", filePath)
	return &config, nil
}

// LoadFromEnv 从环境变量加载配置
func (l *Loader) LoadFromEnv() (*Config, error) {
	config := &Config{
		App: &AppConfig{
			Name:        getEnvOrDefault("APP_NAME", "AgentScope"),
			Version:     getEnvOrDefault("APP_VERSION", "1.0.0"),
			Environment: getEnvOrDefault("APP_ENV", "development"),
			Debug:       getEnvBool("APP_DEBUG", false),
		},
		Logging: &LoggingConfig{
			Level:  getEnvOrDefault("LOG_LEVEL", "info"),
			Format: getEnvOrDefault("LOG_FORMAT", "json"),
			Output: getEnvOrDefault("LOG_OUTPUT", "stdout"),
		},
		Web: &WebConfig{
			Enabled: getEnvBool("WEB_ENABLED", true),
			Host:    getEnvOrDefault("WEB_HOST", "localhost"),
			Port:    getEnvInt("WEB_PORT", 8080),
		},
	}

	// 加载LLM配置
	if apiKey := os.Getenv("DEEPSEEK_API_KEY"); apiKey != "" {
		config.LLM = &LLMConfig{
			DefaultProvider: getEnvOrDefault("LLM_PROVIDER", "deepseek"),
			Providers: map[string]*LLMProvider{
				"deepseek": {
					Type:    "deepseek",
					APIKey:  apiKey,
					BaseURL: getEnvOrDefault("LLM_BASE_URL", "https://api.deepseek.com/v1"),
					Model:   getEnvOrDefault("LLM_MODEL", "deepseek-chat"),
					Timeout: time.Duration(getEnvInt("LLM_TIMEOUT", 30)) * time.Second,
				},
			},
			Timeout:    time.Duration(getEnvInt("LLM_TIMEOUT", 30)) * time.Second,
			MaxRetries: getEnvInt("LLM_MAX_RETRIES", 3),
		}
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, "validation_failed", "config validation failed")
	}

	l.logger.Info("Configuration loaded from environment variables")
	return config, nil
}

// applyEnvironmentOverrides 应用环境变量覆盖
func (l *Loader) applyEnvironmentOverrides(config *Config) {
	// 应用程序配置覆盖
	if config.App != nil {
		if name := os.Getenv("APP_NAME"); name != "" {
			config.App.Name = name
		}
		if version := os.Getenv("APP_VERSION"); version != "" {
			config.App.Version = version
		}
		if env := os.Getenv("APP_ENV"); env != "" {
			config.App.Environment = env
		}
		if debug := os.Getenv("APP_DEBUG"); debug != "" {
			config.App.Debug = strings.ToLower(debug) == "true"
		}
	}

	// 日志配置覆盖
	if config.Logging != nil {
		if level := os.Getenv("LOG_LEVEL"); level != "" {
			config.Logging.Level = level
		}
		if format := os.Getenv("LOG_FORMAT"); format != "" {
			config.Logging.Format = format
		}
		if output := os.Getenv("LOG_OUTPUT"); output != "" {
			config.Logging.Output = output
		}
	}

	// LLM配置覆盖
	if apiKey := os.Getenv("DEEPSEEK_API_KEY"); apiKey != "" {
		if config.LLM == nil {
			config.LLM = &LLMConfig{
				DefaultProvider: "deepseek",
				Providers:       make(map[string]*LLMProvider),
			}
		}

		if config.LLM.Providers == nil {
			config.LLM.Providers = make(map[string]*LLMProvider)
		}

		// 只有在明确设置了 LLM_PROVIDER 环境变量时才覆盖默认提供商
		if providerName := os.Getenv("LLM_PROVIDER"); providerName != "" {
			config.LLM.DefaultProvider = providerName
		}

		// 确保 deepseek 提供商存在（因为有 API key）
		if config.LLM.Providers["deepseek"] == nil {
			config.LLM.Providers["deepseek"] = &LLMProvider{}
		}

		config.LLM.Providers["deepseek"].APIKey = apiKey
		config.LLM.Providers["deepseek"].Type = "deepseek"
		if baseURL := os.Getenv("LLM_BASE_URL"); baseURL != "" {
			config.LLM.Providers["deepseek"].BaseURL = baseURL
		}
		if model := os.Getenv("LLM_MODEL"); model != "" {
			config.LLM.Providers["deepseek"].Model = model
		}
		if timeout := os.Getenv("LLM_TIMEOUT"); timeout != "" {
			if t, err := strconv.Atoi(timeout); err == nil {
				config.LLM.Timeout = time.Duration(t) * time.Second
			}
		}
	}
}

// LoadWithDefaults 加载配置并应用默认值
func (l *Loader) LoadWithDefaults(filePath string) (*Config, error) {
	var config *Config
	var err error

	// 尝试从文件加载
	if filePath != "" {
		if _, statErr := os.Stat(filePath); statErr == nil {
			config, err = l.LoadFromFile(filePath)
			if err != nil {
				return nil, err
			}
		}
	}

	// 如果没有从文件加载成功，从环境变量加载
	if config == nil {
		config, err = l.LoadFromEnv()
		if err != nil {
			return nil, err
		}
	}

	// 应用默认值
	l.applyDefaults(config)

	return config, nil
}

// applyDefaults 应用默认配置
func (l *Loader) applyDefaults(config *Config) {
	// 应用程序默认值
	if config.App == nil {
		config.App = &AppConfig{}
	}
	if config.App.Name == "" {
		config.App.Name = "AgentScope"
	}
	if config.App.Version == "" {
		config.App.Version = "1.0.0"
	}
	if config.App.Environment == "" {
		config.App.Environment = "development"
	}

	// 日志默认值
	if config.Logging == nil {
		config.Logging = &LoggingConfig{}
	}
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "json"
	}
	if config.Logging.Output == "" {
		config.Logging.Output = "stdout"
	}

	// Web配置默认值
	if config.Web == nil {
		config.Web = &WebConfig{}
	}
	if config.Web.Host == "" {
		config.Web.Host = "localhost"
	}
	if config.Web.Port == 0 {
		config.Web.Port = 8080
	}

	// LLM配置默认值
	if config.LLM == nil {
		config.LLM = &LLMConfig{}
	}
	if config.LLM.DefaultProvider == "" {
		config.LLM.DefaultProvider = "deepseek"
	}
	if config.LLM.Timeout <= 0 {
		config.LLM.Timeout = 30 * time.Second
	}
	if config.LLM.MaxRetries <= 0 {
		config.LLM.MaxRetries = 3
	}
	if config.LLM.Providers == nil {
		config.LLM.Providers = make(map[string]*LLMProvider)
	}
	if config.LLM.Parameters == nil {
		config.LLM.Parameters = make(map[string]interface{})
	}
}

// 环境变量辅助函数

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		return strings.ToLower(value) == "true"
	}
	return defaultValue
}

// getEnvDuration 获取时间间隔环境变量
func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// 全局加载器实例
var defaultLoader = NewLoader()

// LoadConfig 使用默认加载器加载配置
func LoadConfig(filePath string) (*Config, error) {
	return defaultLoader.LoadFromFile(filePath)
}

// LoadConfigFromEnv 使用默认加载器从环境变量加载配置
func LoadConfigFromEnv() (*Config, error) {
	return defaultLoader.LoadFromEnv()
}

// LoadConfigWithDefaults 使用默认加载器加载配置并应用默认值
func LoadConfigWithDefaults(filePath string) (*Config, error) {
	return defaultLoader.LoadWithDefaults(filePath)
}

// ValidateEnvironment 验证环境配置
func ValidateEnvironment() error {
	// 检查必需的环境变量
	requiredEnvVars := []string{
		"DEEPSEEK_API_KEY",
	}

	var missingVars []string
	for _, envVar := range requiredEnvVars {
		if os.Getenv(envVar) == "" {
			missingVars = append(missingVars, envVar)
		}
	}

	if len(missingVars) > 0 {
		return errors.NewValidationError("missing_env_vars",
			fmt.Sprintf("Missing required environment variables: %s", strings.Join(missingVars, ", ")))
	}

	return nil
}
