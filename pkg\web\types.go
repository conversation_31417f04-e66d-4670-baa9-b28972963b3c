package web

import "time"

// API请求和响应类型

// AgentInfo Agent信息
type AgentInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
	State       string `json:"state"`
}

// CreateAgentRequest 创建Agent请求
type CreateAgentRequest struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// UpdateAgentRequest 更新Agent请求
type UpdateAgentRequest struct {
	Name        string                 `json:"name,omitempty"`
	Description string                 `json:"description,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// AgentReplyRequest Agent回复请求
type AgentReplyRequest struct {
	Message MessageInfo `json:"message"`
}

// AgentReplyResponse Agent回复响应
type AgentReplyResponse struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Sender  string `json:"sender"`
	Content string `json:"content"`
}

// PipelineInfo Pipeline信息
type PipelineInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// CreatePipelineRequest 创建Pipeline请求
type CreatePipelineRequest struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// ExecutePipelineRequest 执行Pipeline请求
type ExecutePipelineRequest struct {
	Message MessageInfo `json:"message"`
}

// ExecutePipelineResponse 执行Pipeline响应
type ExecutePipelineResponse struct {
	Success  bool                   `json:"success"`
	Message  MessageInfo            `json:"message"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// MessageInfo 消息信息
type MessageInfo struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Sender  string `json:"sender"`
	Content string `json:"content"`
}

// CreateMessageRequest 创建消息请求
type CreateMessageRequest struct {
	Type    string `json:"type"`
	Sender  string `json:"sender"`
	Content string `json:"content"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error  string    `json:"error"`
	Status int       `json:"status"`
	Time   time.Time `json:"time"`
}
