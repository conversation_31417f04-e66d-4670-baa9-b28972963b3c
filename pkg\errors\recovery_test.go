package errors

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultRecoveryHandler(t *testing.T) {
	handler := NewDefaultRecoveryHandler(nil)

	t.Run("HandlePanic", func(t *testing.T) {
		ctx := context.Background()
		panicValue := "test panic"
		stack := []byte("stack trace")

		err := handler.HandlePanic(ctx, panicValue, stack)
		require.Error(t, err)

		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, ErrorTypeInternal, asErr.Type)
		assert.Equal(t, CodePanicRecovered, asErr.Code)
		assert.Contains(t, asErr.Message, "test panic")
	})

	t.Run("HandleError", func(t *testing.T) {
		ctx := context.Background()
		originalErr := errors.New("test error")

		err := handler.HandleError(ctx, originalErr)
		require.Error(t, err)

		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, ErrorTypeInternal, asErr.Type)
		assert.Equal(t, CodeInternalError, asErr.Code)
		assert.Equal(t, originalErr, asErr.Unwrap())
	})

	t.Run("HandleAgentScopeError", func(t *testing.T) {
		ctx := context.Background()
		originalErr := NewValidationError("TEST_001", "test validation error")

		err := handler.HandleError(ctx, originalErr)
		require.Error(t, err)

		// 应该直接返回原错误
		assert.Equal(t, originalErr, err)
	})
}

func TestSafeExecute(t *testing.T) {
	ctx := context.Background()
	handler := NewDefaultRecoveryHandler(nil)

	t.Run("NormalExecution", func(t *testing.T) {
		executed := false
		err := SafeExecute(ctx, handler, func() error {
			executed = true
			return nil
		})

		assert.NoError(t, err)
		assert.True(t, executed)
	})

	t.Run("ErrorExecution", func(t *testing.T) {
		originalErr := errors.New("test error")
		err := SafeExecute(ctx, handler, func() error {
			return originalErr
		})

		require.Error(t, err)
		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, originalErr, asErr.Unwrap())
	})

	t.Run("PanicExecution", func(t *testing.T) {
		err := SafeExecute(ctx, handler, func() error {
			panic("test panic")
		})

		require.Error(t, err)
		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, ErrorTypeInternal, asErr.Type)
		assert.Equal(t, CodePanicRecovered, asErr.Code)
		assert.Contains(t, asErr.Message, "test panic")
	})
}

func TestSafeExecuteWithResult(t *testing.T) {
	ctx := context.Background()
	handler := NewDefaultRecoveryHandler(nil)

	t.Run("NormalExecution", func(t *testing.T) {
		result, err := SafeExecuteWithResult(ctx, handler, func() (int, error) {
			return 42, nil
		})

		assert.NoError(t, err)
		assert.Equal(t, 42, result)
	})

	t.Run("ErrorExecution", func(t *testing.T) {
		originalErr := errors.New("test error")
		result, err := SafeExecuteWithResult(ctx, handler, func() (int, error) {
			return 0, originalErr
		})

		require.Error(t, err)
		assert.Equal(t, 0, result)
		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, originalErr, asErr.Unwrap())
	})

	t.Run("PanicExecution", func(t *testing.T) {
		result, err := SafeExecuteWithResult(ctx, handler, func() (string, error) {
			panic("test panic")
		})

		require.Error(t, err)
		assert.Equal(t, "", result)
		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, CodePanicRecovered, asErr.Code)
	})
}

func TestCircuitBreaker(t *testing.T) {
	t.Run("NormalOperation", func(t *testing.T) {
		cb := NewCircuitBreaker(CircuitBreakerConfig{
			Name:         "test",
			MaxFailures:  3,
			ResetTimeout: 100 * time.Millisecond,
		})

		ctx := context.Background()

		// 正常执行应该成功
		err := cb.Execute(ctx, func() error {
			return nil
		})
		assert.NoError(t, err)
		assert.Equal(t, StateClosed, cb.GetState())
		assert.Equal(t, 0, cb.GetFailureCount())
	})

	t.Run("FailureHandling", func(t *testing.T) {
		cb := NewCircuitBreaker(CircuitBreakerConfig{
			Name:         "test",
			MaxFailures:  2,
			ResetTimeout: 100 * time.Millisecond,
		})

		ctx := context.Background()
		testErr := errors.New("test error")

		// 第一次失败
		err := cb.Execute(ctx, func() error {
			return testErr
		})
		assert.Equal(t, testErr, err)
		assert.Equal(t, StateClosed, cb.GetState())
		assert.Equal(t, 1, cb.GetFailureCount())

		// 第二次失败，应该触发断路器开启
		err = cb.Execute(ctx, func() error {
			return testErr
		})
		assert.Equal(t, testErr, err)
		assert.Equal(t, StateOpen, cb.GetState())
		assert.Equal(t, 2, cb.GetFailureCount())

		// 断路器开启后，应该直接拒绝请求
		err = cb.Execute(ctx, func() error {
			t.Error("不应该执行这个函数")
			return nil
		})
		require.Error(t, err)
		asErr, ok := AsAgentScopeError(err)
		require.True(t, ok)
		assert.Equal(t, CodeCircuitBreakerOpen, asErr.Code)
	})

	t.Run("HalfOpenState", func(t *testing.T) {
		cb := NewCircuitBreaker(CircuitBreakerConfig{
			Name:         "test",
			MaxFailures:  1,
			ResetTimeout: 50 * time.Millisecond,
		})

		ctx := context.Background()
		testErr := errors.New("test error")

		// 触发断路器开启
		err := cb.Execute(ctx, func() error {
			return testErr
		})
		assert.Equal(t, testErr, err)
		assert.Equal(t, StateOpen, cb.GetState())

		// 等待重置超时
		time.Sleep(60 * time.Millisecond)

		// 下一次请求应该进入半开状态
		executed := false
		err = cb.Execute(ctx, func() error {
			executed = true
			return nil
		})
		assert.NoError(t, err)
		assert.True(t, executed)
		assert.Equal(t, StateClosed, cb.GetState())
		assert.Equal(t, 0, cb.GetFailureCount())
	})

	t.Run("HalfOpenFailure", func(t *testing.T) {
		cb := NewCircuitBreaker(CircuitBreakerConfig{
			Name:         "test",
			MaxFailures:  1,
			ResetTimeout: 50 * time.Millisecond,
		})

		ctx := context.Background()
		testErr := errors.New("test error")

		// 触发断路器开启
		err := cb.Execute(ctx, func() error {
			return testErr
		})
		assert.Equal(t, StateOpen, cb.GetState())

		// 等待重置超时
		time.Sleep(60 * time.Millisecond)

		// 半开状态下失败，应该重新开启断路器
		err = cb.Execute(ctx, func() error {
			return testErr
		})
		assert.Equal(t, testErr, err)
		assert.Equal(t, StateOpen, cb.GetState())
	})

	t.Run("StateChangeCallback", func(t *testing.T) {
		var stateChanges []string
		cb := NewCircuitBreaker(CircuitBreakerConfig{
			Name:         "test",
			MaxFailures:  1,
			ResetTimeout: 50 * time.Millisecond,
			OnStateChange: func(from, to CircuitBreakerState) {
				stateChanges = append(stateChanges, fmt.Sprintf("%s->%s", from.String(), to.String()))
			},
		})

		ctx := context.Background()
		testErr := errors.New("test error")

		// 触发状态变化
		cb.Execute(ctx, func() error {
			return testErr
		})

		assert.Contains(t, stateChanges, "CLOSED->OPEN")
	})

	t.Run("Reset", func(t *testing.T) {
		cb := NewCircuitBreaker(CircuitBreakerConfig{
			Name:         "test",
			MaxFailures:  1,
			ResetTimeout: 100 * time.Millisecond,
		})

		ctx := context.Background()
		testErr := errors.New("test error")

		// 触发断路器开启
		cb.Execute(ctx, func() error {
			return testErr
		})
		assert.Equal(t, StateOpen, cb.GetState())
		assert.Equal(t, 1, cb.GetFailureCount())

		// 重置断路器
		cb.Reset()
		assert.Equal(t, StateClosed, cb.GetState())
		assert.Equal(t, 0, cb.GetFailureCount())

		// 重置后应该能正常执行
		err := cb.Execute(ctx, func() error {
			return nil
		})
		assert.NoError(t, err)
	})
}
