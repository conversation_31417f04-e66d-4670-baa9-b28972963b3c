# 知识库系统（PostgreSQL 持久化版）

本项目已实现基于 PostgreSQL 的真实持久化知识库（遵循“禁止简化实现”的开发纪律）。主要能力：

- 文档/分块/实体/关系 全量落库，支持事务与外键级联删除
- 简单文本检索（title/content ILIKE）、实体/关系过滤查询
- 基础图查询：获取实体关联关系、相邻实体、基于 BFS 的最短路径（限定深度）
- 索引/统计：必要索引、Analyze、基础统计

## 快速开始

- DSN 示例：`postgres://user:pass@localhost:5432/hzagent?sslmode=disable`
- 使用工厂创建 PostgreSQL 知识库：

```go
kb, err := knowledge.NewKnowledgeBase(&knowledge.Config{
    Type:   knowledge.KnowledgeBaseTypePostgreSQL,
    Config: map[string]interface{}{"dsn": "postgres://user:pass@localhost:5432/hzagent?sslmode=disable"},
})
if err != nil { panic(err) }
```

## 数据库表结构（自动创建）

- kb_documents（文档，含 tags 数组、metadata JSONB）
- kb_chunks（文档分块，含 vector_json JSONB）
- kb_entities（实体，含 aliases 数组、properties JSONB）
- kb_document_entities（文档-实体 关联表）
- kb_relations（关系，含 properties JSONB、confidence）
- kb_relation_documents（关系-文档 关联表）

## 常用操作示例

- 新增文档（可携带分块/实体/关系，一次事务落库）
- 查询文档并回填分块/实体/关系
- 基于标题近似的简单相似搜索（LIKE），用于早期阶段快速可用

更多接口与结构体说明详见下文中文内容。


# 知识库系统（通用说明）

AgentScope-Golang 的知识库系统为智能体提供了完整的文档存储、检索与知识管理能力。

## 概述

知识库系统由以下关键组件构成：

- 文档管理：支持多种类型文档的存储与管理（文本、Markdown、HTML、JSON、PDF、代码等）
- 实体与关系管理：抽取并管理实体及其关系
- 全文检索：内置文本索引与检索能力
- 代理集成：与智能体的对话历史无缝集成
- 检索系统：提供多种检索策略（关键词、语义、混合）

## 核心组件

### KnowledgeBase 接口

知识库所有操作的主接口：

```go
type KnowledgeBase interface {
    // Document management
    AddDocument(ctx context.Context, doc *Document) error
    GetDocument(ctx context.Context, id string) (*Document, error)
    UpdateDocument(ctx context.Context, doc *Document) error
    DeleteDocument(ctx context.Context, id string) error
    ListDocuments(ctx context.Context, limit, offset int) ([]*Document, error)

    // Document search
    SearchDocuments(ctx context.Context, query *SearchQuery) ([]*SearchResult, error)
    SearchSimilar(ctx context.Context, docID string, limit int) ([]*SearchResult, error)

    // Entity management
    AddEntity(ctx context.Context, entity *Entity) error
    GetEntity(ctx context.Context, id string) (*Entity, error)
    UpdateEntity(ctx context.Context, entity *Entity) error
    DeleteEntity(ctx context.Context, id string) error
    SearchEntities(ctx context.Context, query *EntityQuery) ([]*Entity, error)

    // Relation management
    AddRelation(ctx context.Context, relation *Relation) error
    GetRelation(ctx context.Context, id string) (*Relation, error)
    UpdateRelation(ctx context.Context, relation *Relation) error
    DeleteRelation(ctx context.Context, id string) error
    SearchRelations(ctx context.Context, query *RelationQuery) ([]*Relation, error)

    // Graph queries
    GetEntityRelations(ctx context.Context, entityID string) ([]*Relation, error)
    GetRelatedEntities(ctx context.Context, entityID string, relationTypes []string) ([]*Entity, error)
    FindPath(ctx context.Context, fromEntityID, toEntityID string, maxDepth int) ([][]*Relation, error)

    // Statistics and maintenance
    GetStats(ctx context.Context) (*KnowledgeBaseStats, error)
    RebuildIndex(ctx context.Context) error
    OptimizeIndex(ctx context.Context) error
}
```

### 文档类型

系统支持多种文档类型：

```go
const (
    DocumentTypeText     DocumentType = "text"
    DocumentTypeMarkdown DocumentType = "markdown"
    DocumentTypeHTML     DocumentType = "html"
    DocumentTypeJSON     DocumentType = "json"
    DocumentTypePDF      DocumentType = "pdf"
    DocumentTypeCode     DocumentType = "code"
)
```

### 文档结构

```go
type Document struct {
    ID        string                 `json:"id"`
    Title     string                 `json:"title"`
    Content   string                 `json:"content"`
    Type      DocumentType           `json:"type"`
    Source    string                 `json:"source,omitempty"`
    Language  string                 `json:"language,omitempty"`
    Tags      []string               `json:"tags,omitempty"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
    Chunks    []*DocumentChunk       `json:"chunks,omitempty"`
    Entities  []*Entity              `json:"entities,omitempty"`
    Relations []*Relation            `json:"relations,omitempty"`
    CreatedAt time.Time              `json:"created_at"`
    UpdatedAt time.Time              `json:"updated_at"`
    Version   int                    `json:"version"`
    Hash      string                 `json:"hash"`
}
```

## 使用示例

### 基本文档操作

```go
package main

import (
    "context"
    "fmt"
    "log"

    "github.com/agentscope/agentscope-golang/pkg/knowledge"
)

func main() {
    // 创建一个知识库（示例）
    kb := knowledge.NewSimpleKnowledgeBase()
    ctx := context.Background()

    // 新增文档
    doc := &knowledge.Document{
        Title:   "Machine Learning Basics",
        Content: "Machine learning is a subset of artificial intelligence...",
        Type:    knowledge.DocumentTypeText,
        Tags:    []string{"ml", "ai", "basics"},
        Metadata: map[string]interface{}{
            "author": "AI Researcher",
            "topic":  "Machine Learning",
        },
    }

    err := kb.AddDocument(ctx, doc)
    if err != nil {
        log.Fatal(err)
    }

    // 获取文档
    retrieved, err := kb.GetDocument(ctx, doc.ID)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("已获取：%s\n", retrieved.Title)
}
```

### 检索操作

```go
// 根据查询条件搜索文档
query := &knowledge.SearchQuery{
    Query: "machine learning",
    Tags:  []string{"ml"},
    Limit: 10,
}

results, err := kb.SearchDocuments(ctx, query)
if err != nil {
    log.Fatal(err)
}

for _, result := range results {
    fmt.Printf("找到：%s（得分：%.2f）\n", result.Document.Title, result.Score)
}
```

### 实体与关系管理

```go
// 新增实体
entity := &knowledge.Entity{
    Name: "Neural Networks",
    Type: "concept",
    Description: "A computing system inspired by biological neural networks",
    Properties: map[string]interface{}{
        "field": "machine learning",
        "complexity": "high",
    },
}

err := kb.AddEntity(ctx, entity)
if err != nil {
    log.Fatal(err)
}

// 新增关系
relation := &knowledge.Relation{
    FromEntity: entity.ID,
    ToEntity:   "machine_learning_entity_id",
    Type:       "is_part_of",
    Description: "Neural networks are part of machine learning",
    Confidence: 0.9,
}

err = kb.AddRelation(ctx, relation)
if err != nil {
    log.Fatal(err)
}

// 查找相关实体
related, err := kb.GetRelatedEntities(ctx, entity.ID, []string{"is_part_of"})
if err != nil {
    log.Fatal(err)
}

for _, rel := range related {
    fmt.Printf("Related: %s\n", rel.Name)
}
```

## 与智能体集成

### AgentKnowledgeManager（智能体知识管理器）

`AgentKnowledgeManager` 提供了智能体与知识库之间的无缝集成能力：

```go
// 创建智能体知识管理器
akm := knowledge.NewAgentKnowledgeManager(kb, "agent-001")

// 将消息加入为知识
msg := message.NewMessage("user", "agent", message.NewTextContent("What is machine learning?"))
err := akm.AddMessageAsKnowledge(ctx, msg)
if err != nil {
    log.Fatal(err)
}

// 搜索相关知识
docs, err := akm.SearchRelevantKnowledge(ctx, "machine learning", 5)
if err != nil {
    log.Fatal(err)
}

// 基于知识构建上下文
context, err := akm.BuildContextFromKnowledge(ctx, "neural networks", 2000)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("知识上下文：\n%s\n", context)
```

### 添加事实性知识

```go
// Add structured factual knowledge
err := akm.AddFactualKnowledge(ctx,
    "Python Programming",
    "Python is a high-level programming language known for its simplicity and readability.",
    []string{"programming", "python", "language"})
if err != nil {
    log.Fatal(err)
}
```

## 检索系统

检索系统提供多种搜索策略：

```go
// 创建检索系统
rs := knowledge.NewRetrievalSystem(kb)

// 关键词检索
results, err := rs.KeywordSearch(ctx, "machine learning", 10)
if err != nil {
    log.Fatal(err)
}

// 语义检索（可接入向量数据库实现）
results, err = rs.SemanticSearch(ctx, "artificial intelligence", 10)
if err != nil {
    log.Fatal(err)
}

// 混合检索
results, err = rs.HybridSearch(ctx, "neural networks", 10)
if err != nil {
    log.Fatal(err)
}

// 过滤检索
filters := map[string]interface{}{
    "tags": []string{"ml"},
}
results, err = rs.FilteredRetrieval(ctx, filters, 10)
if err != nil {
    log.Fatal(err)
}
```

## 配置

### 使用工厂创建知识库

```go
// 使用工厂模式
config := &knowledge.Config{
    Type: knowledge.KnowledgeBaseTypeInMemory,
}

kb, err := knowledge.NewKnowledgeBase(config)
if err != nil {
    log.Fatal(err)
}
```

### 支持的类型

```go
// 获取支持的知识库类型
types := knowledge.GetSupportedKnowledgeBaseTypes()
for _, t := range types {
    fmt.Printf("Supported type: %s\n", t)
}
```

## 性能注意事项

### 索引

内存知识库会自动维护如下索引：

- 文档类型索引：按文档类型快速检索
- 实体类型索引：按实体类型快速检索
- 关系类型索引：按关系类型快速检索
- 全文索引：基于分词的搜索索引

### 优化

```go
// 重建索引
err := kb.RebuildIndex(ctx)
if err != nil {
    log.Fatal(err)
}

// 优化索引（清理空条目等）
err = kb.OptimizeIndex(ctx)
if err != nil {
    log.Fatal(err)
}
```

### 统计

```go
// 获取知识库统计信息
stats, err := kb.GetStats(ctx)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Documents: %d\n", stats.DocumentCount)
fmt.Printf("Entities: %d\n", stats.EntityCount)
fmt.Printf("Relations: %d\n", stats.RelationCount)
```

## 最佳实践

### 文档管理

1. **使用有意义的 ID**：虽然系统可以自动生成 ID，提供有意义的 ID 有助于调试
2. **添加元数据**：使用 metadata 提供额外上下文与过滤
3. **合理使用标签**：通过 tags 实现高效过滤与分类
4. **版本管理**：系统会自动维护版本，但在冲突处理时需关注版本字段

### 实体与关系

1. **命名一致**：为实体类型与关系类型使用一致的命名约定
2. **设置置信度**：为关系设置合适的置信度以便质量过滤
3. **关联文档**：总是将实体与关系关联到其来源文档

### 搜索与检索

1. **合理设置限制**：设置合理的 limit 以避免性能问题
2. **优先过滤**：使用类型与标签过滤尽早缩小范围
3. **监控性能**：使用统计信息监控知识库增长与性能

### 与智能体集成

1. **选择性入库**：不要将所有消息都写入知识库，应有选择地添加
2. **上下文管理**：构建上下文时设置合适的 token 限制
3. **定期清理**：定期清理过时或不相关的知识

## 错误处理

知识库系统使用结构化错误处理：

```go
import "github.com/agentscope/agentscope-golang/pkg/errors"

// 检查特定错误类型
if err != nil {
    if errors.IsCode(err, "document_not_found") {
        fmt.Println("未找到文档")
    } else if errors.IsType(err, errors.ErrorTypeValidation) {
        fmt.Println("校验错误:", err)
    } else {
        fmt.Println("其他错误:", err)
    }
}
```

## 后续扩展

知识库系统在设计上强调可扩展性：

- 向量存储：集成向量数据库以实现语义检索
- 外部数据库：已支持 PostgreSQL，后续可扩展 Elasticsearch 等
- 文档处理：自动实体与关系抽取
- 缓存：基于 Redis 的缓存以提升性能
- 分布式存储：支持分布式知识库

## 测试

系统包含完善的测试：

```bash
# Run all knowledge base tests
go test ./pkg/knowledge/...

# Run specific test
go test ./pkg/knowledge -run TestInMemoryKnowledgeBase

# Run with verbose output
go test ./pkg/knowledge -v

# Run performance tests
go test ./pkg/knowledge -run TestKnowledgeBasePerformance
```