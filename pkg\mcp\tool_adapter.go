package mcp

import (
	"context"
	"fmt"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// MCPToolAdapter MCP工具适配器
type MCPToolAdapter struct {
	mcpTool *MCPTool
	client  MCPClient
	logger  logger.Logger
}

// NewMCPToolAdapter 创建MCP工具适配器
func NewMCPToolAdapter(mcpTool *MCPTool, client MCPClient) *MCPToolAdapter {
	return &MCPToolAdapter{
		mcpTool: mcpTool,
		client:  client,
		logger:  logger.GetGlobalLogger(),
	}
}

// Name 返回工具名称
func (adapter *MCPToolAdapter) Name() string {
	return adapter.mcpTool.Name
}

// Description 返回工具描述
func (adapter *MCPToolAdapter) Description() string {
	return adapter.mcpTool.Description
}

// Schema 返回工具参数模式
func (adapter *MCPToolAdapter) Schema() *tool.JSONSchema {
	if adapter.mcpTool.InputSchema == nil {
		return &tool.JSONSchema{
			Type:       "object",
			Properties: make(map[string]*tool.JSONSchema),
		}
	}

	return convertMCPSchemaToJSONSchema(adapter.mcpTool.InputSchema)
}

// Execute 执行工具
func (adapter *MCPToolAdapter) Execute(ctx context.Context, params map[string]any) (any, error) {
	if !adapter.client.IsConnected() {
		return nil, fmt.Errorf("MCP client is not connected")
	}

	adapter.logger.Debugf("Executing MCP tool: %s with params: %v", adapter.mcpTool.Name, params)

	// 调用MCP工具
	result, err := adapter.client.CallTool(ctx, adapter.mcpTool.Name, params)
	if err != nil {
		return nil, fmt.Errorf("MCP tool execution failed: %w", err)
	}

	if result.IsError {
		errorMsg := extractErrorFromMCPContent(result.Content)
		return nil, fmt.Errorf("MCP tool returned error: %s", errorMsg)
	}

	// 提取并返回数据
	return extractDataFromMCPContent(result.Content), nil
}

// convertMCPSchemaToJSONSchema 转换MCP模式为JSON模式
func convertMCPSchemaToJSONSchema(mcpSchema *MCPSchema) *tool.JSONSchema {
	schema := &tool.JSONSchema{
		Type:       mcpSchema.Type,
		Properties: make(map[string]*tool.JSONSchema),
		Required:   mcpSchema.Required,
	}

	for name, mcpProp := range mcpSchema.Properties {
		schema.Properties[name] = convertMCPPropertyToJSONSchema(mcpProp)
	}

	return schema
}

// convertMCPPropertyToJSONSchema 转换MCP属性为JSON Schema
func convertMCPPropertyToJSONSchema(mcpProp *MCPProperty) *tool.JSONSchema {
	// 转换Enum类型
	var enumValues []any
	if mcpProp.Enum != nil {
		enumValues = make([]any, len(mcpProp.Enum))
		for i, v := range mcpProp.Enum {
			enumValues[i] = v
		}
	}

	return &tool.JSONSchema{
		Type:        mcpProp.Type,
		Description: mcpProp.Description,
		Enum:        enumValues,
		Minimum:     mcpProp.Minimum,
		Maximum:     mcpProp.Maximum,
	}
}

// extractDataFromMCPContent 从MCP内容中提取数据
func extractDataFromMCPContent(contents []MCPContent) interface{} {
	if len(contents) == 0 {
		return nil
	}

	if len(contents) == 1 {
		content := contents[0]
		switch content.Type {
		case "text":
			return content.Text
		default:
			return content.Data
		}
	}

	// 多个内容项，返回数组
	var results []interface{}
	for _, content := range contents {
		switch content.Type {
		case "text":
			results = append(results, content.Text)
		default:
			results = append(results, content.Data)
		}
	}

	return results
}

// extractErrorFromMCPContent 从MCP内容中提取错误信息
func extractErrorFromMCPContent(contents []MCPContent) string {
	if len(contents) == 0 {
		return "Unknown MCP tool error"
	}

	var errorMessages []string
	for _, content := range contents {
		if content.Type == "text" && content.Text != "" {
			errorMessages = append(errorMessages, content.Text)
		}
	}

	if len(errorMessages) == 0 {
		return "Unknown MCP tool error"
	}

	if len(errorMessages) == 1 {
		return errorMessages[0]
	}

	result := ""
	for i, msg := range errorMessages {
		if i > 0 {
			result += "; "
		}
		result += msg
	}

	return result
}

// MCPToolRegistry MCP工具注册表
type MCPToolRegistry struct {
	client   MCPClient
	adapters map[string]*MCPToolAdapter
	logger   logger.Logger
}

// NewMCPToolRegistry 创建MCP工具注册表
func NewMCPToolRegistry(client MCPClient) *MCPToolRegistry {
	return &MCPToolRegistry{
		client:   client,
		adapters: make(map[string]*MCPToolAdapter),
		logger:   logger.GetGlobalLogger(),
	}
}

// RefreshTools 刷新工具列表
func (registry *MCPToolRegistry) RefreshTools(ctx context.Context) error {
	if !registry.client.IsConnected() {
		return errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	// 获取MCP工具列表
	mcpTools, err := registry.client.ListTools(ctx)
	if err != nil {
		return fmt.Errorf("failed to list MCP tools: %w", err)
	}

	// 清空现有适配器
	registry.adapters = make(map[string]*MCPToolAdapter)

	// 创建新的适配器
	for _, mcpTool := range mcpTools {
		adapter := NewMCPToolAdapter(mcpTool, registry.client)
		registry.adapters[mcpTool.Name] = adapter
		registry.logger.Debugf("Registered MCP tool: %s", mcpTool.Name)
	}

	registry.logger.Infof("Refreshed %d MCP tools", len(mcpTools))
	return nil
}

// GetTool 获取工具适配器
func (registry *MCPToolRegistry) GetTool(name string) (*MCPToolAdapter, error) {
	adapter, exists := registry.adapters[name]
	if !exists {
		return nil, errors.NewNotFoundError("tool_not_found", fmt.Sprintf("MCP tool %s not found", name))
	}
	return adapter, nil
}

// ListTools 列出所有工具适配器
func (registry *MCPToolRegistry) ListTools() []*MCPToolAdapter {
	adapters := make([]*MCPToolAdapter, 0, len(registry.adapters))
	for _, adapter := range registry.adapters {
		adapters = append(adapters, adapter)
	}
	return adapters
}

// HasTool 检查工具是否存在
func (registry *MCPToolRegistry) HasTool(name string) bool {
	_, exists := registry.adapters[name]
	return exists
}

// GetToolNames 获取所有工具名称
func (registry *MCPToolRegistry) GetToolNames() []string {
	names := make([]string, 0, len(registry.adapters))
	for name := range registry.adapters {
		names = append(names, name)
	}
	return names
}

// RegisterToGlobalRegistry 注册到全局工具注册表
func (registry *MCPToolRegistry) RegisterToGlobalRegistry() error {
	for _, adapter := range registry.adapters {
		// 直接注册MCPToolAdapter到新的全局注册表
		if err := tool.Register(adapter); err != nil {
			registry.logger.Warnf("Failed to register MCP tool %s to global registry: %v", adapter.Name(), err)
			// 继续注册其他工具，不因为一个失败而停止
		} else {
			registry.logger.Debugf("Registered MCP tool %s to global registry", adapter.Name())
		}
	}
	return nil
}

// getStringValue 从map中安全获取字符串值
func getStringValue(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// UnregisterFromGlobalRegistry 从全局工具注册表注销
func (registry *MCPToolRegistry) UnregisterFromGlobalRegistry() error {
	for _, adapter := range registry.adapters {
		if success := tool.Unregister(adapter.Name()); !success {
			registry.logger.Warnf("Failed to unregister MCP tool %s from global registry", adapter.Name())
		} else {
			registry.logger.Debugf("Unregistered MCP tool %s from global registry", adapter.Name())
		}
	}
	return nil
}

// MCPToolManager MCP工具管理器
type MCPToolManager struct {
	clients    map[string]MCPClient
	registries map[string]*MCPToolRegistry
	logger     logger.Logger
}

// NewMCPToolManager 创建MCP工具管理器
func NewMCPToolManager() *MCPToolManager {
	return &MCPToolManager{
		clients:    make(map[string]MCPClient),
		registries: make(map[string]*MCPToolRegistry),
		logger:     logger.GetGlobalLogger(),
	}
}

// AddMCPServer 添加MCP服务器
func (manager *MCPToolManager) AddMCPServer(ctx context.Context, name string, config *MCPConfig) error {
	client := NewDefaultMCPClient()

	if err := client.Connect(ctx, config); err != nil {
		return fmt.Errorf("failed to connect to MCP server %s: %w", name, err)
	}

	registry := NewMCPToolRegistry(client)
	if err := registry.RefreshTools(ctx); err != nil {
		client.Disconnect()
		return fmt.Errorf("failed to refresh tools for MCP server %s: %w", name, err)
	}

	manager.clients[name] = client
	manager.registries[name] = registry

	manager.logger.Infof("Added MCP server: %s", name)
	return nil
}

// RemoveMCPServer 移除MCP服务器
func (manager *MCPToolManager) RemoveMCPServer(name string) error {
	client, exists := manager.clients[name]
	if !exists {
		return errors.NewNotFoundError("server_not_found", fmt.Sprintf("MCP server %s not found", name))
	}

	// 从全局注册表注销工具
	if registry, exists := manager.registries[name]; exists {
		registry.UnregisterFromGlobalRegistry()
		delete(manager.registries, name)
	}

	// 断开连接
	client.Disconnect()
	delete(manager.clients, name)

	manager.logger.Infof("Removed MCP server: %s", name)
	return nil
}

// RefreshAllTools 刷新所有工具
func (manager *MCPToolManager) RefreshAllTools(ctx context.Context) error {
	for name, registry := range manager.registries {
		if err := registry.RefreshTools(ctx); err != nil {
			manager.logger.Warnf("Failed to refresh tools for MCP server %s: %v", name, err)
		}
	}
	return nil
}

// RegisterAllToGlobalRegistry 注册所有工具到全局注册表
func (manager *MCPToolManager) RegisterAllToGlobalRegistry() error {
	for _, registry := range manager.registries {
		registry.RegisterToGlobalRegistry()
	}
	return nil
}

// GetServerNames 获取所有服务器名称
func (manager *MCPToolManager) GetServerNames() []string {
	names := make([]string, 0, len(manager.clients))
	for name := range manager.clients {
		names = append(names, name)
	}
	return names
}

// GetServerRegistry 获取服务器的工具注册表
func (manager *MCPToolManager) GetServerRegistry(name string) (*MCPToolRegistry, error) {
	registry, exists := manager.registries[name]
	if !exists {
		return nil, errors.NewNotFoundError("server_not_found", fmt.Sprintf("MCP server %s not found", name))
	}
	return registry, nil
}

// Shutdown 关闭所有连接
func (manager *MCPToolManager) Shutdown() error {
	for name := range manager.clients {
		manager.RemoveMCPServer(name)
	}
	manager.logger.Info("MCP tool manager shutdown completed")
	return nil
}
