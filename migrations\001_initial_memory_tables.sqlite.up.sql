-- SQLite 初始迁移：记忆系统表
-- 说明：使用 TEXT 存储 JSON 字段；如启用 JSON1 扩展，可添加 CHECK(json_valid(...)) 约束

BEGIN TRANSACTION;

CREATE TABLE IF NOT EXISTS memory_entries (
    id          TEXT PRIMARY KEY,
    agent_id    TEXT,
    user_id     TEXT,
    type        TEXT NOT NULL,
    content     TEXT NOT NULL,
    metadata    TEXT,
    score       REAL DEFAULT 0,
    tags        TEXT,
    created_at  DATETIME NOT NULL DEFAULT (datetime('now'))
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_memory_entries_type ON memory_entries(type);
CREATE INDEX IF NOT EXISTS idx_memory_entries_created_at ON memory_entries(created_at);
CREATE INDEX IF NOT EXISTS idx_memory_entries_score ON memory_entries(score);

COMMIT;

