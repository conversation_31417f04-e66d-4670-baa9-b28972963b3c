package tooling

import (
	"context"
	"testing"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// MockAgent 模拟智能体用于测试
type MockAgent struct {
	name        string
	description string
	response    string
	shouldError bool
}

// NewMockAgent 创建模拟智能体
func NewMockAgent(name, description, response string) *MockAgent {
	return &MockAgent{
		name:        name,
		description: description,
		response:    response,
		shouldError: false,
	}
}

// NewMockAgentWithError 创建会产生错误的模拟智能体
func NewMockAgentWithError(name, description string) *MockAgent {
	return &MockAgent{
		name:        name,
		description: description,
		shouldError: true,
	}
}

// Name 返回智能体名称
func (m *MockAgent) Name(ctx context.Context) string {
	return m.name
}

// Description 返回智能体描述
func (m *MockAgent) Description(ctx context.Context) string {
	return m.description
}

// Run 运行智能体
func (m *MockAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	// 在goroutine中发送事件
	go func() {
		defer pair.Generator.Close()

		if m.shouldError {
			// 产生错误事件
			errorEvent := &event.Event{
				Type: event.EventError,
				Data: &event.ErrorData{
					Message: "模拟智能体错误",
				},
			}
			pair.Generator.Send(errorEvent)
		} else {
			// 产生最终结果事件
			finalEvent := &event.Event{
				Type: event.EventFinal,
				Data: &event.FinalData{
					Content: m.response,
				},
			}
			pair.Generator.Send(finalEvent)
		}
	}()

	return pair.Iterator
}

// TestNewAgentAsTool 测试创建智能体作为工具
func TestNewAgentAsTool(t *testing.T) {
	mockAgent := NewMockAgent("test-agent", "测试智能体", "测试响应")

	config := &AgentAsToolConfig{
		Agent:       mockAgent,
		Name:        "test-tool",
		Description: "测试工具",
	}

	tool, err := NewAgentAsTool(config)
	if err != nil {
		t.Errorf("创建智能体工具失败: %v", err)
	}

	if tool.Name() != "test-tool" {
		t.Errorf("工具名称不匹配: got %s, want test-tool", tool.Name())
	}

	if tool.Description() != "测试工具" {
		t.Errorf("工具描述不匹配: got %s, want 测试工具", tool.Description())
	}
}

// TestNewAgentAsToolWithDefaults 测试使用默认值创建智能体作为工具
func TestNewAgentAsToolWithDefaults(t *testing.T) {
	mockAgent := NewMockAgent("test-agent", "测试智能体", "测试响应")

	config := &AgentAsToolConfig{
		Agent: mockAgent,
	}

	tool, err := NewAgentAsTool(config)
	if err != nil {
		t.Errorf("创建智能体工具失败: %v", err)
	}

	if tool.Name() != "test-agent" {
		t.Errorf("工具名称不匹配: got %s, want test-agent", tool.Name())
	}

	if tool.Description() != "测试智能体" {
		t.Errorf("工具描述不匹配: got %s, want 测试智能体", tool.Description())
	}

	// 检查默认模式
	schema := tool.Schema()
	if schema == nil {
		t.Error("工具模式不应该为空")
	}
}

// TestNewAgentAsToolValidation 测试创建智能体工具的验证
func TestNewAgentAsToolValidation(t *testing.T) {
	// 测试空配置
	_, err := NewAgentAsTool(nil)
	if err == nil {
		t.Error("空配置应该返回错误")
	}

	// 测试空智能体
	config := &AgentAsToolConfig{}
	_, err = NewAgentAsTool(config)
	if err == nil {
		t.Error("空智能体应该返回错误")
	}
}

// TestAgentAsToolExecute 测试智能体工具执行
func TestAgentAsToolExecute(t *testing.T) {
	mockAgent := NewMockAgent("test-agent", "测试智能体", "Hello, World!")

	config := &AgentAsToolConfig{
		Agent: mockAgent,
	}

	tool, err := NewAgentAsTool(config)
	if err != nil {
		t.Fatalf("创建智能体工具失败: %v", err)
	}

	ctx := context.Background()
	params := map[string]any{"input": "测试输入"}

	result, err := tool.Execute(ctx, params)
	if err != nil {
		t.Errorf("执行智能体工具失败: %v", err)
	}

	if result != "Hello, World!" {
		t.Errorf("执行结果不匹配: got %s, want Hello, World!", result)
	}
}

// TestAgentAsToolExecuteError 测试智能体工具执行错误
func TestAgentAsToolExecuteError(t *testing.T) {
	mockAgent := NewMockAgentWithError("test-agent", "测试智能体")

	config := &AgentAsToolConfig{
		Agent: mockAgent,
	}

	tool, err := NewAgentAsTool(config)
	if err != nil {
		t.Fatalf("创建智能体工具失败: %v", err)
	}

	ctx := context.Background()
	params := map[string]any{"input": "测试输入"}

	_, err = tool.Execute(ctx, params)
	if err == nil {
		t.Error("应该返回错误")
	}
}

// TestAgentAsToolValidate 测试智能体工具输入验证
func TestAgentAsToolValidate(t *testing.T) {
	mockAgent := NewMockAgent("test-agent", "测试智能体", "测试响应")

	config := &AgentAsToolConfig{
		Agent: mockAgent,
	}

	tool, err := NewAgentAsTool(config)
	if err != nil {
		t.Fatalf("创建智能体工具失败: %v", err)
	}

	// 测试有效输入
	validInput := `{"input": "测试输入"}`
	err = tool.Validate(validInput)
	if err != nil {
		t.Errorf("有效输入验证失败: %v", err)
	}

	// 测试无效JSON
	invalidJSON := `{"input": "测试输入"`
	err = tool.Validate(invalidJSON)
	if err == nil {
		t.Error("无效JSON应该返回错误")
	}

	// 测试缺少input字段
	missingInput := `{"other": "值"}`
	err = tool.Validate(missingInput)
	if err == nil {
		t.Error("缺少input字段应该返回错误")
	}

	// 测试input字段类型错误
	wrongType := `{"input": 123}`
	err = tool.Validate(wrongType)
	if err == nil {
		t.Error("input字段类型错误应该返回错误")
	}
}

// TestAgentAsToolRegistry 测试智能体工具注册表
func TestAgentAsToolRegistry(t *testing.T) {
	registry := NewAgentAsToolRegistry()

	if registry.Count() != 0 {
		t.Errorf("新注册表应该为空: got %d", registry.Count())
	}

	// 注册工具
	mockAgent := NewMockAgent("test-agent", "测试智能体", "测试响应")
	config := &AgentAsToolConfig{
		Agent: mockAgent,
		Name:  "test-tool",
	}

	err := registry.Register(config)
	if err != nil {
		t.Errorf("注册工具失败: %v", err)
	}

	if registry.Count() != 1 {
		t.Errorf("注册后工具数量不匹配: got %d, want 1", registry.Count())
	}

	// 获取工具
	tool, exists := registry.Get("test-tool")
	if !exists {
		t.Error("应该能够获取注册的工具")
	}

	if tool.Name() != "test-tool" {
		t.Errorf("获取的工具名称不匹配: got %s, want test-tool", tool.Name())
	}

	// 列出工具
	tools := registry.List()
	if len(tools) != 1 {
		t.Errorf("工具列表长度不匹配: got %d, want 1", len(tools))
	}

	// 移除工具
	removed := registry.Remove("test-tool")
	if !removed {
		t.Error("应该能够移除工具")
	}

	if registry.Count() != 0 {
		t.Errorf("移除后工具数量不匹配: got %d, want 0", registry.Count())
	}

	// 尝试移除不存在的工具
	removed = registry.Remove("non-existent")
	if removed {
		t.Error("不应该能够移除不存在的工具")
	}
}

// TestAgentToolChain 测试智能体工具链
func TestAgentToolChain(t *testing.T) {
	chain := NewAgentToolChain()

	// 添加智能体
	mockAgent1 := NewMockAgent("agent1", "智能体1", "响应1")
	mockAgent2 := NewMockAgent("agent2", "智能体2", "响应2")

	err := chain.AddAgent(mockAgent1, "tool1", "工具1")
	if err != nil {
		t.Errorf("添加智能体1失败: %v", err)
	}

	err = chain.AddAgent(mockAgent2, "tool2", "工具2")
	if err != nil {
		t.Errorf("添加智能体2失败: %v", err)
	}

	// 检查工具数量
	tools := chain.GetTools()
	if len(tools) != 2 {
		t.Errorf("工具数量不匹配: got %d, want 2", len(tools))
	}

	// 检查智能体数量
	agents := chain.GetAgents()
	if len(agents) != 2 {
		t.Errorf("智能体数量不匹配: got %d, want 2", len(agents))
	}

	// 获取特定工具
	tool, exists := chain.GetTool("tool1")
	if !exists {
		t.Error("应该能够获取工具1")
	}

	if tool.Name() != "tool1" {
		t.Errorf("工具名称不匹配: got %s, want tool1", tool.Name())
	}

	// 执行工具
	ctx := context.Background()
	input := `{"input": "测试输入"}`

	result, err := chain.ExecuteTool(ctx, "tool1", input)
	if err != nil {
		t.Errorf("执行工具失败: %v", err)
	}

	if result != "响应1" {
		t.Errorf("执行结果不匹配: got %s, want 响应1", result)
	}

	// 尝试执行不存在的工具
	_, err = chain.ExecuteTool(ctx, "non-existent", input)
	if err == nil {
		t.Error("执行不存在的工具应该返回错误")
	}

	// 清空工具链
	chain.Clear()
	if len(chain.GetTools()) != 0 {
		t.Error("清空后工具链应该为空")
	}

	if len(chain.GetAgents()) != 0 {
		t.Error("清空后智能体列表应该为空")
	}
}
