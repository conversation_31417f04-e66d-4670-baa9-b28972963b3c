# hzAgent 使用指南（数据库持久化）

本指南介绍如何在开发与测试环境中启用 PostgreSQL 持久化（记忆系统与知识库）。

## 依赖与准备

- Go 1.21+（项目会自动拉取工具链）
- 运行中的 PostgreSQL（建议本地 14/15/16 均可）
- 依赖包：
  - github.com/jackc/pgx/v5/stdlib
  - github.com/jmoiron/sqlx（可选，项目当前实现主要使用 database/sql）
  - github.com/golang-migrate/migrate/v4（可选）
- DSN 示例：`postgres://user:pass@localhost:5432/hzagent?sslmode=disable`

## 启用记忆系统的数据库持久化

```go
import "github.com/agentscope/agentscope-golang/pkg/memory"

mc := &memory.MemoryConfig{
    Type:          "persistent",            // 使用持久化模式
    PersistDriver: "postgres",              // file | postgres
    DatabaseDSN:   "postgres://user:pass@localhost:5432/hzagent?sslmode=disable",
    AutoSave:      true,                     // 可选：定时自动保存
}
mem, err := memory.NewMemory(mc)
if err != nil { panic(err) }
```

说明：
- 当 `PersistDriver` = `postgres` 时，优先使用 `DatabaseDSN`（未提供则回退到 `PersistPath`）。
- 当 `PersistDriver` = `file` 时，需要提供 `PersistPath` 作为 JSON 备份文件路径。

## 启用 PostgreSQL 知识库

```go
import "github.com/agentscope/agentscope-golang/pkg/knowledge"

kb, err := knowledge.NewKnowledgeBase(&knowledge.Config{
    Type:   knowledge.KnowledgeBaseTypePostgreSQL,
    Config: map[string]interface{}{
        "dsn": "postgres://user:pass@localhost:5432/hzagent?sslmode=disable",
    },
})
if err != nil { panic(err) }
```

## 数据库结构与维护

- 本项目在首次连接时会自动 `CREATE TABLE IF NOT EXISTS` 建表：
  - kb_documents、kb_chunks、kb_entities、kb_document_entities
  - kb_relations、kb_relation_documents
- 可通过 `kb.RebuildIndex(ctx)` 补充必要索引（幂等）
- 可通过 `kb.OptimizeIndex(ctx)` 触发 `ANALYZE`（安全）

## 常见问题

- 连接失败：检查 `pg_hba.conf`/监听端口/用户名密码
- SSL：开发阶段建议 `sslmode=disable`
- 权限：确保数据库用户具备对应 schema 的创建/写入权限

## 参考

- docs/knowledge-base.md（已新增 PostgreSQL 版快速开始）
- pkg/memory/persistence_postgres.go（记忆持久化实现）
- pkg/knowledge/postgres*.go（知识库持久化实现）

