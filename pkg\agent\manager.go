package agent

import (
	"context"
	"fmt"
	"sync"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// Manager Agent管理器，负责Agent的注册、查找和生命周期管理
type Manager struct {
	agents map[string]Agent // 按名称索引的Agent集合
	mutex  sync.RWMutex     // 读写锁保护并发访问
	logger logger.Logger    // 日志记录器
}

// NewManager 创建新的Agent管理器
func NewManager() *Manager {
	return &Manager{
		agents: make(map[string]Agent),
		logger: logger.GetGlobalLogger(),
	}
}

// RegisterAgent 注册Agent到管理器
func (m *Manager) RegisterAgent(ctx context.Context, agent Agent) error {
	if agent == nil {
		return errors.NewValidationError("invalid_agent", "agent cannot be nil")
	}

	agentName := agent.Name(ctx)
	if agentName == "" {
		return errors.NewValidationError("invalid_agent_name", "agent name cannot be empty")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查是否已存在同名Agent
	if _, exists := m.agents[agentName]; exists {
		return errors.NewValidationError("duplicate_agent",
			fmt.Sprintf("agent with name '%s' already exists", agentName))
	}

	m.agents[agentName] = agent
	m.logger.Info("Agent registered successfully: %s", agentName)

	return nil
}

// UnregisterAgent 从管理器中注销Agent
func (m *Manager) UnregisterAgent(agentName string) error {
	if agentName == "" {
		return errors.NewValidationError("invalid_agent_name", "agent name cannot be empty")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.agents[agentName]; !exists {
		return errors.NewValidationError("agent_not_found",
			fmt.Sprintf("agent with name '%s' not found", agentName))
	}

	delete(m.agents, agentName)
	m.logger.Info("Agent unregistered successfully: %s", agentName)

	return nil
}

// GetAgent 根据名称获取Agent
func (m *Manager) GetAgent(agentName string) (Agent, error) {
	if agentName == "" {
		return nil, errors.NewValidationError("invalid_agent_name", "agent name cannot be empty")
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	agent, exists := m.agents[agentName]
	if !exists {
		return nil, errors.NewValidationError("agent_not_found",
			fmt.Sprintf("agent with name '%s' not found", agentName))
	}

	return agent, nil
}

// ListAgents 获取所有已注册的Agent名称列表
func (m *Manager) ListAgents() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	names := make([]string, 0, len(m.agents))
	for name := range m.agents {
		names = append(names, name)
	}

	return names
}

// GetAgentCount 获取已注册的Agent数量
func (m *Manager) GetAgentCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.agents)
}

// HasAgent 检查是否存在指定名称的Agent
func (m *Manager) HasAgent(agentName string) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	_, exists := m.agents[agentName]
	return exists
}

// Clear 清空所有已注册的Agent
func (m *Manager) Clear() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	count := len(m.agents)
	m.agents = make(map[string]Agent)
	m.logger.Info("Cleared all agents, removed %d agents", count)
}

// GetAllAgents 获取所有已注册的Agent（返回副本以避免并发问题）
func (m *Manager) GetAllAgents() map[string]Agent {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 创建副本以避免并发访问问题
	agents := make(map[string]Agent, len(m.agents))
	for name, agent := range m.agents {
		agents[name] = agent
	}

	return agents
}

// UpdateAgent 更新已注册的Agent（先注销旧的，再注册新的）
func (m *Manager) UpdateAgent(ctx context.Context, agentName string, newAgent Agent) error {
	if agentName == "" {
		return errors.NewValidationError("invalid_agent_name", "agent name cannot be empty")
	}
	if newAgent == nil {
		return errors.NewValidationError("invalid_agent", "new agent cannot be nil")
	}

	newAgentName := newAgent.Name(ctx)
	if newAgentName != agentName {
		return errors.NewValidationError("agent_name_mismatch",
			fmt.Sprintf("new agent name '%s' does not match expected name '%s'", newAgentName, agentName))
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.agents[agentName]; !exists {
		return errors.NewValidationError("agent_not_found",
			fmt.Sprintf("agent with name '%s' not found", agentName))
	}

	m.agents[agentName] = newAgent
	m.logger.Info("Agent updated successfully: %s", agentName)

	return nil
}

// Shutdown 关闭管理器，清理所有资源
func (m *Manager) Shutdown() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	count := len(m.agents)
	m.agents = make(map[string]Agent)
	m.logger.Info("Agent manager shutdown, cleared %d agents", count)

	return nil
}
