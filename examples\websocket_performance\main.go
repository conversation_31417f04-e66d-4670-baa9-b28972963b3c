package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/web"
)

// MockWebSocketClient 模拟WebSocket客户端用于性能测试
type MockWebSocketClient struct {
	id           string
	perfConfig   *web.WebSocketPerformanceConfig
	batchBuffer  *bytes.Buffer
	batchMutex   sync.Mutex
	messagesSent int
	bytesSent    int64
}

// NewMockWebSocketClient 创建模拟WebSocket客户端
func NewMockWebSocketClient(id string, config *web.WebSocketPerformanceConfig) *MockWebSocketClient {
	if config == nil {
		config = &web.WebSocketPerformanceConfig{
			MaxBatchSize:      10,
			BatchTimeout:      10 * time.Millisecond,
			WriteBufferSize:   4096,
			ReadBufferSize:    4096,
			EnableCompression: true,
		}
	}

	return &MockWebSocketClient{
		id:          id,
		perfConfig:  config,
		batchBuffer: bytes.NewBuffer(make([]byte, 0, config.WriteBufferSize)),
	}
}

// AddToBatch 添加消息到批量缓冲区
func (c *MockWebSocketClient) AddToBatch(message []byte) bool {
	c.batchMutex.Lock()
	defer c.batchMutex.Unlock()

	// 如果缓冲区不为空，添加分隔符
	if c.batchBuffer.Len() > 0 {
		c.batchBuffer.WriteByte('\n')
	}

	// 添加消息到缓冲区
	c.batchBuffer.Write(message)
	c.messagesSent++

	// 检查是否需要立即发送
	shouldFlush := false

	// 检查批量大小限制
	messageCount := bytes.Count(c.batchBuffer.Bytes(), []byte{'\n'}) + 1
	if messageCount >= c.perfConfig.MaxBatchSize {
		shouldFlush = true
	}

	// 检查缓冲区大小限制
	if c.batchBuffer.Len() >= c.perfConfig.WriteBufferSize {
		shouldFlush = true
	}

	if shouldFlush {
		c.FlushBatchUnsafe()
		return true
	}

	return false
}

// FlushBatch 刷新批量缓冲区
func (c *MockWebSocketClient) FlushBatch() {
	c.batchMutex.Lock()
	defer c.batchMutex.Unlock()
	c.FlushBatchUnsafe()
}

// FlushBatchUnsafe 刷新批量缓冲区（非线程安全）
func (c *MockWebSocketClient) FlushBatchUnsafe() {
	if c.batchBuffer.Len() == 0 {
		return
	}

	// 模拟网络发送
	c.bytesSent += int64(c.batchBuffer.Len())

	// 清空缓冲区
	c.batchBuffer.Reset()
}

// GetStats 获取统计信息
func (c *MockWebSocketClient) GetStats() (int, int64) {
	c.batchMutex.Lock()
	defer c.batchMutex.Unlock()
	return c.messagesSent, c.bytesSent
}

// TestMessage 测试消息结构
type TestMessage struct {
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	ClientID  string                 `json:"client_id"`
}

func main() {
	fmt.Println("=== WebSocket性能优化演示 ===")
	fmt.Println("对比批量发送与单条发送的性能差异")
	fmt.Println()

	// 测试参数
	messageCount := 1000
	clientCount := 5

	// 准备测试消息
	testMessage := TestMessage{
		Type: "performance_test",
		Data: map[string]interface{}{
			"content": "这是一条用于性能测试的消息，包含一些内容来模拟真实使用场景",
			"index":   0,
		},
		Timestamp: time.Now(),
		ClientID:  "test-client",
	}

	messageData, err := json.Marshal(testMessage)
	if err != nil {
		log.Fatalf("Failed to marshal test message: %v", err)
	}

	fmt.Printf("测试配置:\n")
	fmt.Printf("  消息数量: %d\n", messageCount)
	fmt.Printf("  客户端数量: %d\n", clientCount)
	fmt.Printf("  消息大小: %d 字节\n", len(messageData))
	fmt.Printf("  总数据量: %.2f KB\n", float64(len(messageData)*messageCount*clientCount)/1024)
	fmt.Println()

	// 测试1: 批量发送模式
	fmt.Println("1. 批量发送模式测试:")
	batchClients := make([]*MockWebSocketClient, clientCount)
	for i := 0; i < clientCount; i++ {
		batchClients[i] = NewMockWebSocketClient(fmt.Sprintf("batch-client-%d", i), nil)
	}

	start := time.Now()
	var wg sync.WaitGroup

	for i, client := range batchClients {
		wg.Add(1)
		go func(clientIndex int, c *MockWebSocketClient) {
			defer wg.Done()

			for j := 0; j < messageCount; j++ {
				// 创建新的消息副本避免并发修改
				msg := TestMessage{
					Type: testMessage.Type,
					Data: map[string]interface{}{
						"content": testMessage.Data["content"],
						"index":   j,
					},
					Timestamp: time.Now(),
					ClientID:  c.id,
				}

				msgData, _ := json.Marshal(msg)
				c.AddToBatch(msgData)
			}

			// 刷新剩余消息
			c.FlushBatch()
		}(i, client)
	}

	wg.Wait()
	batchDuration := time.Since(start)

	// 统计批量发送结果
	totalBatchMessages := 0
	totalBatchBytes := int64(0)
	for _, client := range batchClients {
		messages, bytes := client.GetStats()
		totalBatchMessages += messages
		totalBatchBytes += bytes
	}

	fmt.Printf("  耗时: %v\n", batchDuration)
	fmt.Printf("  消息数: %d\n", totalBatchMessages)
	fmt.Printf("  字节数: %d\n", totalBatchBytes)
	fmt.Printf("  吞吐量: %.2f msg/ms\n", float64(totalBatchMessages)/float64(batchDuration.Milliseconds()))
	fmt.Printf("  带宽: %.2f KB/s\n", float64(totalBatchBytes)/1024/batchDuration.Seconds())
	fmt.Println()

	// 测试2: 单条发送模式
	fmt.Println("2. 单条发送模式测试:")

	// 创建单条发送配置（批量大小为1）
	singleConfig := &web.WebSocketPerformanceConfig{
		MaxBatchSize:      1,
		BatchTimeout:      1 * time.Millisecond,
		WriteBufferSize:   4096,
		ReadBufferSize:    4096,
		EnableCompression: true,
	}

	singleClients := make([]*MockWebSocketClient, clientCount)
	for i := 0; i < clientCount; i++ {
		singleClients[i] = NewMockWebSocketClient(fmt.Sprintf("single-client-%d", i), singleConfig)
	}

	start = time.Now()
	wg = sync.WaitGroup{}

	for i, client := range singleClients {
		wg.Add(1)
		go func(clientIndex int, c *MockWebSocketClient) {
			defer wg.Done()

			for j := 0; j < messageCount; j++ {
				// 创建新的消息副本避免并发修改
				msg := TestMessage{
					Type: testMessage.Type,
					Data: map[string]interface{}{
						"content": testMessage.Data["content"],
						"index":   j,
					},
					Timestamp: time.Now(),
					ClientID:  c.id,
				}

				msgData, _ := json.Marshal(msg)
				c.AddToBatch(msgData) // 由于MaxBatchSize=1，每次都会立即发送
			}
		}(i, client)
	}

	wg.Wait()
	singleDuration := time.Since(start)

	// 统计单条发送结果
	totalSingleMessages := 0
	totalSingleBytes := int64(0)
	for _, client := range singleClients {
		messages, bytes := client.GetStats()
		totalSingleMessages += messages
		totalSingleBytes += bytes
	}

	fmt.Printf("  耗时: %v\n", singleDuration)
	fmt.Printf("  消息数: %d\n", totalSingleMessages)
	fmt.Printf("  字节数: %d\n", totalSingleBytes)
	fmt.Printf("  吞吐量: %.2f msg/ms\n", float64(totalSingleMessages)/float64(singleDuration.Milliseconds()))
	fmt.Printf("  带宽: %.2f KB/s\n", float64(totalSingleBytes)/1024/singleDuration.Seconds())
	fmt.Println()

	// 性能对比
	fmt.Println("3. 性能对比:")
	speedupRatio := float64(singleDuration) / float64(batchDuration)
	throughputImprovement := (float64(totalBatchMessages)/float64(batchDuration.Milliseconds()) -
		float64(totalSingleMessages)/float64(singleDuration.Milliseconds())) /
		(float64(totalSingleMessages) / float64(singleDuration.Milliseconds())) * 100

	fmt.Printf("  批量发送比单条发送快 %.2fx\n", speedupRatio)
	fmt.Printf("  吞吐量提升: %.1f%%\n", throughputImprovement)

	if speedupRatio > 1.5 {
		fmt.Println("  ✅ 批量发送显著提升了性能")
	} else if speedupRatio > 1.1 {
		fmt.Println("  ✅ 批量发送有一定性能提升")
	} else {
		fmt.Println("  ⚠️  批量发送性能提升不明显")
	}

	fmt.Println()
	fmt.Println("演示完成！WebSocket批量发送优化有效提升了消息传输性能。")
}
