-- SQLite 初始迁移：知识库相关表
-- 说明：JSON 字段使用 TEXT 存储（可选开启 JSON1 校验）

BEGIN TRANSACTION;

-- 文档
CREATE TABLE IF NOT EXISTS kb_documents (
    id         TEXT PRIMARY KEY,
    title      TEXT NOT NULL,
    content    TEXT,
    type       TEXT NOT NULL,
    source     TEXT,
    language   TEXT,
    tags       TEXT,
    metadata   TEXT,
    created_at DATETIME NOT NULL DEFAULT (datetime('now')),
    updated_at DATETIME NOT NULL DEFAULT (datetime('now')),
    version    INTEGER NOT NULL DEFAULT 1,
    hash       TEXT
);
CREATE INDEX IF NOT EXISTS idx_kb_documents_title ON kb_documents(title);

-- 文档分块
CREATE TABLE IF NOT EXISTS kb_chunks (
    id           TEXT PRIMARY KEY,
    document_id  TEXT NOT NULL,
    content      TEXT,
    order_index  INTEGER,
    vector_json  TEXT,
    created_at   DATETIME NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY(document_id) REFERENCES kb_documents(id) ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS idx_kb_chunks_document_id ON kb_chunks(document_id);

-- 实体
CREATE TABLE IF NOT EXISTS kb_entities (
    id          TEXT PRIMARY KEY,
    name        TEXT NOT NULL,
    type        TEXT,
    description TEXT,
    aliases     TEXT,
    properties  TEXT,
    created_at  DATETIME NOT NULL DEFAULT (datetime('now')),
    updated_at  DATETIME NOT NULL DEFAULT (datetime('now'))
);
CREATE INDEX IF NOT EXISTS idx_kb_entities_name ON kb_entities(name);

-- 文档-实体 关联
CREATE TABLE IF NOT EXISTS kb_document_entities (
    document_id  TEXT NOT NULL,
    entity_id    TEXT NOT NULL,
    relation_type TEXT,
    PRIMARY KEY(document_id, entity_id),
    FOREIGN KEY(document_id) REFERENCES kb_documents(id) ON DELETE CASCADE,
    FOREIGN KEY(entity_id)   REFERENCES kb_entities(id)  ON DELETE CASCADE
);

-- 关系
CREATE TABLE IF NOT EXISTS kb_relations (
    id          TEXT PRIMARY KEY,
    from_entity TEXT NOT NULL,
    to_entity   TEXT NOT NULL,
    type        TEXT NOT NULL,
    description TEXT,
    properties  TEXT,
    confidence  REAL,
    created_at  DATETIME NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY(from_entity) REFERENCES kb_entities(id) ON DELETE CASCADE,
    FOREIGN KEY(to_entity)   REFERENCES kb_entities(id) ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS idx_kb_relations_from ON kb_relations(from_entity);
CREATE INDEX IF NOT EXISTS idx_kb_relations_to   ON kb_relations(to_entity);
CREATE INDEX IF NOT EXISTS idx_kb_relations_type ON kb_relations(type);

-- 关系-文档 关联
CREATE TABLE IF NOT EXISTS kb_relation_documents (
    relation_id  TEXT NOT NULL,
    document_id  TEXT NOT NULL,
    PRIMARY KEY(relation_id, document_id),
    FOREIGN KEY(relation_id) REFERENCES kb_relations(id) ON DELETE CASCADE,
    FOREIGN KEY(document_id) REFERENCES kb_documents(id) ON DELETE CASCADE
);

COMMIT;

