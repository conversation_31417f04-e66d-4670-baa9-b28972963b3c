package audit

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// RetentionJob 数据保留策略任务
type RetentionJob struct {
	store    Store
	config   *RetentionConfig
	
	// 状态管理
	mu       sync.RWMutex
	running  bool
	stopped  bool
	stopCh   chan struct{}
	doneCh   chan struct{}
	
	// 统计信息
	lastRun        time.Time
	lastDuration   time.Duration
	totalRuns      int64
	totalDeleted   int64
	lastError      error
}

// NewRetentionJob 创建数据保留策略任务
func NewRetentionJob(store Store, config *RetentionConfig) *RetentionJob {
	if config == nil {
		config = &RetentionConfig{
			Enabled: false,
			MaxDays: 90,
			Cron:    "@daily",
		}
	}
	
	return &RetentionJob{
		store:  store,
		config: config,
		stopCh: make(chan struct{}),
		doneCh: make(chan struct{}),
	}
}

// Start 启动保留策略任务
func (j *RetentionJob) Start(ctx context.Context) error {
	j.mu.Lock()
	defer j.mu.Unlock()
	
	if !j.config.Enabled {
		return nil // 未启用保留策略
	}
	
	if j.running {
		return NewRetentionError("保留策略任务已在运行", nil)
	}
	
	if j.stopped {
		return NewRetentionError("保留策略任务已停止", nil)
	}
	
	j.running = true
	
	// 启动定时任务
	go j.runScheduler(ctx)
	
	return nil
}

// Stop 停止保留策略任务
func (j *RetentionJob) Stop() error {
	j.mu.Lock()
	defer j.mu.Unlock()
	
	if !j.running {
		return nil
	}
	
	j.stopped = true
	close(j.stopCh)
	
	// 等待任务结束
	<-j.doneCh
	
	j.running = false
	
	return nil
}

// RunOnce 手动执行一次保留策略
func (j *RetentionJob) RunOnce(ctx context.Context) (*RetentionResult, error) {
	j.mu.Lock()
	defer j.mu.Unlock()
	
	if !j.config.Enabled {
		return nil, NewRetentionError("保留策略未启用", nil)
	}
	
	return j.executeRetention(ctx)
}

// runScheduler 运行调度器
func (j *RetentionJob) runScheduler(ctx context.Context) {
	defer close(j.doneCh)
	
	// 解析cron表达式，这里简化为固定间隔
	interval := j.parseCronInterval()
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			j.executeRetentionSafe(ctx)
			
		case <-j.stopCh:
			return
			
		case <-ctx.Done():
			return
		}
	}
}

// parseCronInterval 解析cron间隔（简化实现）
func (j *RetentionJob) parseCronInterval() time.Duration {
	switch j.config.Cron {
	case "@hourly":
		return time.Hour
	case "@daily":
		return 24 * time.Hour
	case "@weekly":
		return 7 * 24 * time.Hour
	case "@monthly":
		return 30 * 24 * time.Hour
	default:
		return 24 * time.Hour // 默认每天
	}
}

// executeRetentionSafe 安全执行保留策略
func (j *RetentionJob) executeRetentionSafe(ctx context.Context) {
	j.mu.Lock()
	defer j.mu.Unlock()
	
	result, err := j.executeRetention(ctx)
	
	j.lastRun = time.Now()
	j.totalRuns++
	j.lastError = err
	
	if result != nil {
		j.lastDuration = result.Duration
		j.totalDeleted += result.DeletedMessages + result.DeletedSessions
	}
}

// executeRetention 执行保留策略
func (j *RetentionJob) executeRetention(ctx context.Context) (*RetentionResult, error) {
	startTime := time.Now()
	
	result := &RetentionResult{
		StartTime: startTime,
		MaxDays:   j.config.MaxDays,
	}
	
	// 计算截止时间
	cutoffTime := startTime.AddDate(0, 0, -j.config.MaxDays)
	result.CutoffTime = cutoffTime
	
	// 执行清理
	err := j.store.RunRetention(ctx)
	if err != nil {
		result.Error = err
		return result, err
	}
	
	// 计算执行时间
	result.Duration = time.Since(startTime)
	result.Success = true
	
	// 注意：这里无法获取实际删除的记录数，因为Store接口没有返回
	// 在实际实现中可能需要扩展Store接口或在这里执行查询统计
	
	return result, nil
}

// GetStats 获取保留策略统计信息
func (j *RetentionJob) GetStats() *RetentionStats {
	j.mu.RLock()
	defer j.mu.RUnlock()
	
	return &RetentionStats{
		Enabled:       j.config.Enabled,
		MaxDays:       j.config.MaxDays,
		Cron:          j.config.Cron,
		Running:       j.running,
		LastRun:       j.lastRun,
		LastDuration:  j.lastDuration,
		TotalRuns:     j.totalRuns,
		TotalDeleted:  j.totalDeleted,
		LastError:     j.lastError,
	}
}

// IsRunning 检查是否正在运行
func (j *RetentionJob) IsRunning() bool {
	j.mu.RLock()
	defer j.mu.RUnlock()
	
	return j.running
}

// UpdateConfig 更新配置
func (j *RetentionJob) UpdateConfig(config *RetentionConfig) error {
	j.mu.Lock()
	defer j.mu.Unlock()
	
	if j.running {
		return NewRetentionError("无法在运行时更新配置", nil)
	}
	
	j.config = config
	return nil
}

// RetentionResult 保留策略执行结果
type RetentionResult struct {
	StartTime       time.Time     `json:"start_time"`
	Duration        time.Duration `json:"duration"`
	MaxDays         int           `json:"max_days"`
	CutoffTime      time.Time     `json:"cutoff_time"`
	DeletedMessages int64         `json:"deleted_messages"`
	DeletedSessions int64         `json:"deleted_sessions"`
	Success         bool          `json:"success"`
	Error           error         `json:"error,omitempty"`
}

// RetentionStats 保留策略统计信息
type RetentionStats struct {
	Enabled       bool          `json:"enabled"`
	MaxDays       int           `json:"max_days"`
	Cron          string        `json:"cron"`
	Running       bool          `json:"running"`
	LastRun       time.Time     `json:"last_run"`
	LastDuration  time.Duration `json:"last_duration"`
	TotalRuns     int64         `json:"total_runs"`
	TotalDeleted  int64         `json:"total_deleted"`
	LastError     error         `json:"last_error,omitempty"`
}

// RetentionManager 保留策略管理器
type RetentionManager struct {
	jobs map[string]*RetentionJob
	mu   sync.RWMutex
}

// NewRetentionManager 创建保留策略管理器
func NewRetentionManager() *RetentionManager {
	return &RetentionManager{
		jobs: make(map[string]*RetentionJob),
	}
}

// AddJob 添加保留策略任务
func (m *RetentionManager) AddJob(name string, job *RetentionJob) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.jobs[name] = job
}

// RemoveJob 移除保留策略任务
func (m *RetentionManager) RemoveJob(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if job, exists := m.jobs[name]; exists {
		if err := job.Stop(); err != nil {
			return err
		}
		delete(m.jobs, name)
	}
	
	return nil
}

// GetJob 获取保留策略任务
func (m *RetentionManager) GetJob(name string) (*RetentionJob, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	job, exists := m.jobs[name]
	return job, exists
}

// StartAll 启动所有任务
func (m *RetentionManager) StartAll(ctx context.Context) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	for name, job := range m.jobs {
		if err := job.Start(ctx); err != nil {
			return fmt.Errorf("启动保留策略任务 %s 失败: %w", name, err)
		}
	}
	
	return nil
}

// StopAll 停止所有任务
func (m *RetentionManager) StopAll() error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	for name, job := range m.jobs {
		if err := job.Stop(); err != nil {
			return fmt.Errorf("停止保留策略任务 %s 失败: %w", name, err)
		}
	}
	
	return nil
}

// GetAllStats 获取所有任务统计信息
func (m *RetentionManager) GetAllStats() map[string]*RetentionStats {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	stats := make(map[string]*RetentionStats)
	for name, job := range m.jobs {
		stats[name] = job.GetStats()
	}
	
	return stats
}
