package event

import (
	"encoding/json"
	"errors"
	"testing"
	"time"
)

func TestEventTypes(t *testing.T) {
	tests := []struct {
		name      string
		eventType Type
		expected  string
	}{
		{"Token事件类型", EventToken, "token"},
		{"Thought事件类型", EventThought, "thought"},
		{"ToolCall事件类型", EventToolCall, "tool_call"},
		{"ToolResult事件类型", EventToolResult, "tool_result"},
		{"Transfer事件类型", EventTransfer, "transfer"},
		{"Checkpoint事件类型", EventCheckpoint, "checkpoint"},
		{"Final事件类型", EventFinal, "final"},
		{"Error事件类型", EventError, "error"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.eventType) != tt.expected {
				t.Errorf("事件类型不匹配: got %s, want %s", tt.eventType, tt.expected)
			}
		})
	}
}

func TestNewTokenEvent(t *testing.T) {
	content := "Hello"
	delta := true

	event := NewTokenEvent(content, delta)

	if event.Type != EventToken {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventToken)
	}

	data, ok := event.Data.(*TokenData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.Content != content {
		t.Errorf("内容不匹配: got %s, want %s", data.Content, content)
	}

	if data.Delta != delta {
		t.Errorf("Delta标志不匹配: got %v, want %v", data.Delta, delta)
	}

	if event.At.IsZero() {
		t.Error("时间戳不应为零值")
	}
}

func TestNewThoughtEvent(t *testing.T) {
	content := "我需要思考这个问题"
	reasoning := "基于用户输入分析"

	event := NewThoughtEvent(content, reasoning)

	if event.Type != EventThought {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventThought)
	}

	data, ok := event.Data.(*ThoughtData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.Content != content {
		t.Errorf("内容不匹配: got %s, want %s", data.Content, content)
	}

	if data.Reasoning != reasoning {
		t.Errorf("推理过程不匹配: got %s, want %s", data.Reasoning, reasoning)
	}
}

func TestNewToolCallEvent(t *testing.T) {
	id := "call_123"
	name := "http_request"
	args := map[string]any{
		"url":    "https://api.example.com",
		"method": "GET",
	}

	event := NewToolCallEvent(id, name, args)

	if event.Type != EventToolCall {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventToolCall)
	}

	data, ok := event.Data.(*ToolCallData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.ID != id {
		t.Errorf("ID不匹配: got %s, want %s", data.ID, id)
	}

	if data.Name != name {
		t.Errorf("工具名称不匹配: got %s, want %s", data.Name, name)
	}

	if len(data.Args) != len(args) {
		t.Errorf("参数数量不匹配: got %d, want %d", len(data.Args), len(args))
	}
}

func TestNewToolResultEvent(t *testing.T) {
	t.Run("成功结果", func(t *testing.T) {
		id := "call_123"
		name := "http_request"
		result := map[string]any{
			"status": 200,
			"body":   "success",
		}

		event := NewToolResultEvent(id, name, result, nil)

		if event.Type != EventToolResult {
			t.Errorf("事件类型错误: got %s, want %s", event.Type, EventToolResult)
		}

		data, ok := event.Data.(*ToolResultData)
		if !ok {
			t.Fatal("事件数据类型错误")
		}

		if !data.Success {
			t.Error("应该标记为成功")
		}

		if data.Error != "" {
			t.Errorf("不应该有错误信息: got %s", data.Error)
		}

		if len(data.Result) != len(result) {
			t.Errorf("结果数量不匹配: got %d, want %d", len(data.Result), len(result))
		}
	})

	t.Run("错误结果", func(t *testing.T) {
		id := "call_123"
		name := "http_request"
		err := errors.New("网络连接失败")

		event := NewToolResultEvent(id, name, nil, err)

		data, ok := event.Data.(*ToolResultData)
		if !ok {
			t.Fatal("事件数据类型错误")
		}

		if data.Success {
			t.Error("不应该标记为成功")
		}

		if data.Error != err.Error() {
			t.Errorf("错误信息不匹配: got %s, want %s", data.Error, err.Error())
		}

		if data.Result != nil {
			t.Error("错误情况下结果应为nil")
		}
	})
}

func TestNewTransferEvent(t *testing.T) {
	fromAgent := "planner"
	toAgent := "executor"
	reason := "计划完成，开始执行"

	event := NewTransferEvent(fromAgent, toAgent, reason)

	if event.Type != EventTransfer {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventTransfer)
	}

	data, ok := event.Data.(*TransferData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.FromAgent != fromAgent {
		t.Errorf("源Agent不匹配: got %s, want %s", data.FromAgent, fromAgent)
	}

	if data.ToAgent != toAgent {
		t.Errorf("目标Agent不匹配: got %s, want %s", data.ToAgent, toAgent)
	}

	if data.Reason != reason {
		t.Errorf("转移原因不匹配: got %s, want %s", data.Reason, reason)
	}
}

func TestNewCheckpointEvent(t *testing.T) {
	sessionID := "session_123"
	action := "save"
	success := true

	event := NewCheckpointEvent(sessionID, action, success)

	if event.Type != EventCheckpoint {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventCheckpoint)
	}

	data, ok := event.Data.(*CheckpointData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.SessionID != sessionID {
		t.Errorf("会话ID不匹配: got %s, want %s", data.SessionID, sessionID)
	}

	if data.Action != action {
		t.Errorf("操作类型不匹配: got %s, want %s", data.Action, action)
	}

	if data.Success != success {
		t.Errorf("成功标志不匹配: got %v, want %v", data.Success, success)
	}
}

func TestNewFinalEvent(t *testing.T) {
	content := "任务完成"
	summary := "成功处理用户请求"
	usage := map[string]any{
		"tokens": 150,
		"time":   "2.5s",
	}

	event := NewFinalEvent(content, summary, usage)

	if event.Type != EventFinal {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventFinal)
	}

	data, ok := event.Data.(*FinalData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.Content != content {
		t.Errorf("内容不匹配: got %s, want %s", data.Content, content)
	}

	if data.Summary != summary {
		t.Errorf("摘要不匹配: got %s, want %s", data.Summary, summary)
	}

	if len(data.Usage) != len(usage) {
		t.Errorf("使用情况数量不匹配: got %d, want %d", len(data.Usage), len(usage))
	}
}

func TestNewErrorEvent(t *testing.T) {
	err := errors.New("执行失败")
	code := "EXECUTION_ERROR"
	details := map[string]any{
		"step":   "tool_execution",
		"reason": "timeout",
	}

	event := NewErrorEvent(err, code, details)

	if event.Type != EventError {
		t.Errorf("事件类型错误: got %s, want %s", event.Type, EventError)
	}

	if event.Err != err {
		t.Errorf("错误对象不匹配: got %v, want %v", event.Err, err)
	}

	data, ok := event.Data.(*ErrorData)
	if !ok {
		t.Fatal("事件数据类型错误")
	}

	if data.Message != err.Error() {
		t.Errorf("错误消息不匹配: got %s, want %s", data.Message, err.Error())
	}

	if data.Code != code {
		t.Errorf("错误代码不匹配: got %s, want %s", data.Code, code)
	}

	if len(data.Details) != len(details) {
		t.Errorf("错误详情数量不匹配: got %d, want %d", len(data.Details), len(details))
	}
}

func TestEventMethods(t *testing.T) {
	t.Run("IsError方法", func(t *testing.T) {
		errorEvent := NewErrorEvent(errors.New("test"), "TEST", nil)
		if !errorEvent.IsError() {
			t.Error("错误事件应该返回true")
		}

		tokenEvent := NewTokenEvent("test", false)
		if tokenEvent.IsError() {
			t.Error("非错误事件应该返回false")
		}
	})

	t.Run("IsFinal方法", func(t *testing.T) {
		finalEvent := NewFinalEvent("done", "", nil)
		if !finalEvent.IsFinal() {
			t.Error("最终事件应该返回true")
		}

		tokenEvent := NewTokenEvent("test", false)
		if tokenEvent.IsFinal() {
			t.Error("非最终事件应该返回false")
		}
	})

	t.Run("WithMetadata和GetMetadata方法", func(t *testing.T) {
		event := NewTokenEvent("test", false)

		// 添加元数据
		event.WithMetadata("trace_id", "123456")
		event.WithMetadata("span_id", "789")

		// 获取元数据
		traceID, exists := event.GetMetadata("trace_id")
		if !exists {
			t.Error("应该存在trace_id元数据")
		}
		if traceID != "123456" {
			t.Errorf("trace_id不匹配: got %v, want %s", traceID, "123456")
		}

		// 获取不存在的元数据
		_, exists = event.GetMetadata("not_exist")
		if exists {
			t.Error("不存在的元数据应该返回false")
		}
	})
}

func TestEventSerialization(t *testing.T) {
	event := NewTokenEvent("Hello", true)
	event.WithMetadata("test", "value")

	// 测试String方法
	str := event.String()
	if str == "" {
		t.Error("String方法不应返回空字符串")
	}

	// 测试JSON序列化
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("JSON序列化失败: %v", err)
	}

	// 测试JSON反序列化
	var decoded Event
	err = json.Unmarshal(data, &decoded)
	if err != nil {
		t.Fatalf("JSON反序列化失败: %v", err)
	}

	if decoded.Type != event.Type {
		t.Errorf("反序列化后类型不匹配: got %s, want %s", decoded.Type, event.Type)
	}
}

func TestEventTimestamp(t *testing.T) {
	before := time.Now()
	event := NewTokenEvent("test", false)
	after := time.Now()

	if event.At.Before(before) || event.At.After(after) {
		t.Error("事件时间戳应该在创建时间范围内")
	}
}
