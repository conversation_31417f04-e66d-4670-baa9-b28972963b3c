package memory

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/agentscope/agentscope-golang/pkg/message"
)

// TestPostgresIntegration 测试Postgres集成
func TestPostgresIntegration(t *testing.T) {
	// 检查是否启用Postgres集成测试
	if os.Getenv("POSTGRES_INTEGRATION_TEST") != "true" {
		t.<PERSON><PERSON>("跳过Postgres集成测试，设置 POSTGRES_INTEGRATION_TEST=true 来启用")
	}

	// 检查必要的环境变量
	requiredEnvs := []string{"POSTGRES_HOST", "POSTGRES_DB", "POSTGRES_USER"}
	for _, env := range requiredEnvs {
		if os.Getenv(env) == "" {
			t.Skipf("跳过Postgres集成测试，缺少环境变量: %s", env)
		}
	}

	store, err := NewPostgresStore(nil)
	if err != nil {
		t.Fatalf("创建Postgres存储失败: %v", err)
	}
	defer store.Close()

	ctx := context.Background()
	sessionID := "integration-test-session"

	// 清理测试数据
	defer func() {
		store.Delete(ctx, sessionID)
	}()

	// 测试保存消息
	msg := message.NewUserMessage("集成测试消息")
	err = store.Save(ctx, sessionID, msg)
	if err != nil {
		t.Errorf("保存消息失败: %v", err)
	}

	// 测试加载消息
	messages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}

	if len(messages) != 1 {
		t.Errorf("消息数量不匹配: got %d, want 1", len(messages))
	}

	if messages[0].GetContentString() != "集成测试消息" {
		t.Errorf("消息内容不匹配: got %s, want 集成测试消息", messages[0].GetContentString())
	}

	// 测试搜索
	results, err := store.Search(ctx, sessionID, "集成", 0)
	if err != nil {
		t.Errorf("搜索失败: %v", err)
	}

	if len(results) != 1 {
		t.Errorf("搜索结果数量不匹配: got %d, want 1", len(results))
	}

	// 测试删除
	err = store.Delete(ctx, sessionID)
	if err != nil {
		t.Errorf("删除会话失败: %v", err)
	}

	messages, err = store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载消息失败: %v", err)
	}

	if len(messages) != 0 {
		t.Errorf("删除后应该没有消息: got %d", len(messages))
	}
}

// TestSQLiteIntegration 测试SQLite集成（仅在非Windows系统）
func TestSQLiteIntegration(t *testing.T) {
	// 检查是否启用SQLite集成测试
	if os.Getenv("SQLITE_INTEGRATION_TEST") != "true" {
		t.Skip("跳过SQLite集成测试，设置 SQLITE_INTEGRATION_TEST=true 来启用")
	}

	// SQLite在Windows上可能不可用，跳过测试
	t.Skip("SQLite集成测试在当前环境中不可用")
}

// TestMemoryStorePerformance 测试内存存储性能
func TestMemoryStorePerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过性能测试")
	}

	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "performance-test-session"

	// 测试大量消息的保存和加载
	messageCount := 1000
	for i := 0; i < messageCount; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("性能测试消息 %d", i))
		err := store.Save(ctx, sessionID, msg)
		if err != nil {
			t.Errorf("保存消息 %d 失败: %v", i, err)
		}
	}

	// 测试加载所有消息
	messages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		t.Errorf("加载所有消息失败: %v", err)
	}

	if len(messages) != messageCount {
		t.Errorf("消息数量不匹配: got %d, want %d", len(messages), messageCount)
	}

	// 测试限制加载
	limitedMessages, err := store.Load(ctx, sessionID, 100)
	if err != nil {
		t.Errorf("限制加载失败: %v", err)
	}

	if len(limitedMessages) != 100 {
		t.Errorf("限制加载消息数量不匹配: got %d, want 100", len(limitedMessages))
	}

	// 测试搜索性能
	results, err := store.Search(ctx, sessionID, "性能测试", 50)
	if err != nil {
		t.Errorf("搜索失败: %v", err)
	}

	if len(results) != 50 {
		t.Errorf("搜索结果数量不匹配: got %d, want 50", len(results))
	}
}

// BenchmarkMemoryStoreSave 基准测试：保存消息
func BenchmarkMemoryStoreSave(b *testing.B) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "benchmark-session"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("基准测试消息 %d", i))
		err := store.Save(ctx, sessionID, msg)
		if err != nil {
			b.Errorf("保存消息失败: %v", err)
		}
	}
}

// BenchmarkMemoryStoreLoad 基准测试：加载消息
func BenchmarkMemoryStoreLoad(b *testing.B) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "benchmark-session"

	// 预先保存一些消息
	for i := 0; i < 1000; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("基准测试消息 %d", i))
		store.Save(ctx, sessionID, msg)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := store.LoadAll(ctx, sessionID)
		if err != nil {
			b.Errorf("加载消息失败: %v", err)
		}
	}
}

// BenchmarkMemoryStoreSearch 基准测试：搜索消息
func BenchmarkMemoryStoreSearch(b *testing.B) {
	store := NewMemoryStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "benchmark-session"

	// 预先保存一些消息
	for i := 0; i < 1000; i++ {
		msg := message.NewUserMessage(fmt.Sprintf("基准测试消息 %d", i))
		store.Save(ctx, sessionID, msg)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := store.Search(ctx, sessionID, "基准测试", 10)
		if err != nil {
			b.Errorf("搜索消息失败: %v", err)
		}
	}
}
