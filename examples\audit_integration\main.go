package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/audit"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// SimpleAgent 简单的测试Agent
type SimpleAgent struct {
	name        string
	description string
}

// NewSimpleAgent 创建简单Agent
func NewSimpleAgent(name, description string) *SimpleAgent {
	return &SimpleAgent{
		name:        name,
		description: description,
	}
}

// Name 返回Agent名称
func (a *SimpleAgent) Name(ctx context.Context) string {
	return a.name
}

// Description 返回Agent描述
func (a *SimpleAgent) Description(ctx context.Context) string {
	return a.description
}

// Run 运行Agent
func (a *SimpleAgent) Run(ctx context.Context, in *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 发送思考事件
		pair.Generator.Send(event.NewThoughtEvent("正在处理用户输入", "分析消息内容"))

		// 模拟处理时间
		time.Sleep(100 * time.Millisecond)

		// 发送Token事件
		for _, msg := range in.Messages {
			content := msg.GetContentString()
			pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("处理消息: %s", content), false))
		}

		// 发送最终事件
		pair.Generator.Send(event.NewFinalEvent("处理完成", "成功处理所有消息", map[string]any{
			"processed_messages": len(in.Messages),
			"agent":              a.name,
		}))
	}()

	return pair.Iterator
}

func main() {
	ctx := context.Background()

	// 创建临时目录用于存储审计数据
	tempDir, err := os.MkdirTemp("", "audit_example")
	if err != nil {
		log.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	dbPath := filepath.Join(tempDir, "audit.db")
	fmt.Printf("审计数据库路径: %s\n", dbPath)

	// 创建审计存储
	auditConfig := audit.DefaultConfig()
	store, err := audit.NewSQLiteStore(dbPath, auditConfig)
	if err != nil {
		log.Fatalf("创建审计存储失败: %v", err)
	}
	defer store.Close()

	// 创建脱敏器
	sanitizer, err := audit.NewSanitizer(&audit.PrivacyConfig{})
	if err != nil {
		log.Fatalf("创建脱敏器失败: %v", err)
	}

	// 创建审计写入器
	batchConfig := &audit.BatchConfig{
		Async:         false, // 使用同步模式确保数据立即写入
		ChanBuffer:    1000,
		FlushInterval: time.Second,
	}
	writer := audit.NewAuditWriter(store, sanitizer, batchConfig)
	defer writer.Close()

	// 创建审计拦截器
	auditInterceptor := audit.NewAuditInterceptor(writer, auditConfig)
	auditInterceptor.SetSessionID("example-session-001")

	// 创建运行时拦截器适配器
	runtimeAdapter := audit.NewRuntimeInterceptorAdapter(auditInterceptor, "audit-interceptor")
	fmt.Printf("创建了审计拦截器: %s\n", runtimeAdapter.Name())

	// 创建Runner配置
	runnerConfig := runtime.DefaultRunnerConfig()
	runnerConfig.BufferSize = 100
	runnerConfig.Timeout = 30 * time.Second

	// 创建Runner并添加审计拦截器
	runner := runtime.NewRunner(ctx, runnerConfig)
	runner.AddInterceptor(runtimeAdapter)
	fmt.Printf("添加了拦截器，当前拦截器列表: %v\n", runner.ListInterceptors())

	// 创建测试Agent
	testAgent := NewSimpleAgent("test-agent", "用于演示审计功能的测试Agent")

	// 创建输入
	input := runtime.NewInput()
	input.AddMessage(message.NewUserMessage("你好，这是一条测试消息"))
	input.AddMessage(message.NewUserMessage("请处理这些数据"))
	input.SetOption("temperature", 0.7)
	input.SetOption("max_tokens", 100)

	fmt.Println("开始执行Agent...")

	// 运行Agent
	iterator := runner.Run(ctx, testAgent, input)

	// 收集并显示事件
	events := iterator.Collect()
	fmt.Printf("收到 %d 个事件:\n", len(events))
	for i, ev := range events {
		fmt.Printf("  事件 %d: 类型=%s, 时间=%s\n", i+1, ev.Type, ev.At.Format("15:04:05.000"))
		if ev.Data != nil {
			fmt.Printf("    数据: %+v\n", ev.Data)
		}
	}

	// 等待审计数据写入完成
	fmt.Println("等待审计数据写入...")
	time.Sleep(500 * time.Millisecond)

	// 查询审计记录
	sessionID := "example-session-001"
	query := audit.Query{
		SessionID: &sessionID,
		Limit:     50,
	}

	result, err := store.QueryMessages(ctx, query)
	if err != nil {
		log.Fatalf("查询审计记录失败: %v", err)
	}

	fmt.Printf("\n审计记录 (共 %d 条):\n", len(result.Records))
	for i, record := range result.Records {
		fmt.Printf("  记录 %d:\n", i+1)
		fmt.Printf("    ID: %s\n", record.ID)
		fmt.Printf("    角色: %s\n", record.Role)
		fmt.Printf("    类型: %s\n", record.MsgType)
		fmt.Printf("    内容: %s\n", record.Content)
		fmt.Printf("    事件类型: %s\n", record.EventType)
		fmt.Printf("    时间: %s\n", record.CreatedAt.Format("2006-01-02 15:04:05"))
		if record.AgentID != "" {
			fmt.Printf("    Agent ID: %s\n", record.AgentID)
		}
		if record.ErrorCode != "" {
			fmt.Printf("    错误代码: %s\n", record.ErrorCode)
		}
		fmt.Println()
	}

	fmt.Println("审计集成示例完成！")
}
