package capabilities

import (
	"context"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/message"
)

// Classifier 通用分类器接口
type Classifier interface {
	// Classify 对输入内容进行分类
	Classify(ctx context.Context, input string, categories []Category) (*ClassificationResult, error)
}

// Category 分类类别定义
type Category struct {
	Name        string   `json:"name" yaml:"name"`               // 类别名称
	Description string   `json:"description" yaml:"description"` // 类别描述
	Keywords    []string `json:"keywords" yaml:"keywords"`       // 关键词列表
	Priority    string   `json:"priority" yaml:"priority"`       // 优先级：high, medium, low
	Examples    []string `json:"examples" yaml:"examples"`       // 示例文本
}

// ClassificationResult 分类结果
type ClassificationResult struct {
	Category   string  `json:"category"`   // 分类结果
	Confidence float64 `json:"confidence"` // 置信度 0-1
	Reasoning  string  `json:"reasoning"`  // 分类理由
	Timestamp  time.Time `json:"timestamp"` // 分类时间
}

// EmotionAnalyzer 情绪分析器接口
type EmotionAnalyzer interface {
	// Analyze 分析文本情绪
	Analyze(ctx context.Context, text string) (*EmotionResult, error)
}

// EmotionResult 情绪分析结果
type EmotionResult struct {
	Type       string    `json:"type"`       // 情绪类型：positive, negative, neutral
	Score      float64   `json:"score"`      // 情绪强度 0-1
	Confidence float64   `json:"confidence"` // 置信度 0-1
	Details    map[string]float64 `json:"details"` // 详细情绪分析（如愤怒、喜悦等）
	Reasoning  string    `json:"reasoning"`  // 分析理由
	Timestamp  time.Time `json:"timestamp"`  // 分析时间
}

// QualityAssessor 质量评估器接口
type QualityAssessor interface {
	// Assess 评估对话质量
	Assess(ctx context.Context, userMessage, agentResponse *message.Message) (*QualityResult, error)
}

// QualityResult 质量评估结果
type QualityResult struct {
	OverallScore float64                `json:"overall_score"` // 总体评分 0-100
	Metrics      map[string]float64     `json:"metrics"`       // 详细指标
	Feedback     string                 `json:"feedback"`      // 改进建议
	Strengths    []string               `json:"strengths"`     // 优点
	Weaknesses   []string               `json:"weaknesses"`    // 不足
	Timestamp    time.Time              `json:"timestamp"`     // 评估时间
}

// AutoReplier 自动回复器接口
type AutoReplier interface {
	// GetReply 获取自动回复
	GetReply(ctx context.Context, input string) (*AutoReplyResult, error)
}

// AutoReplyResult 自动回复结果
type AutoReplyResult struct {
	HasReply   bool      `json:"has_reply"`   // 是否有匹配的回复
	Content    string    `json:"content"`     // 回复内容
	Confidence float64   `json:"confidence"`  // 匹配置信度
	Source     string    `json:"source"`      // 回复来源（如FAQ、知识库等）
	Timestamp  time.Time `json:"timestamp"`   // 回复时间
}

// CapabilityConfig 能力配置
type CapabilityConfig struct {
	// LLM配置
	LLMProvider string                 `json:"llm_provider" yaml:"llm_provider"`
	Model       string                 `json:"model" yaml:"model"`
	Temperature float64                `json:"temperature" yaml:"temperature"`
	MaxTokens   int                    `json:"max_tokens" yaml:"max_tokens"`
	
	// 提示词配置
	PromptTemplate string                 `json:"prompt_template" yaml:"prompt_template"`
	SystemPrompt   string                 `json:"system_prompt" yaml:"system_prompt"`
	
	// 缓存配置
	CacheEnabled bool          `json:"cache_enabled" yaml:"cache_enabled"`
	CacheTTL     time.Duration `json:"cache_ttl" yaml:"cache_ttl"`
	
	// 其他参数
	Parameters map[string]interface{} `json:"parameters" yaml:"parameters"`
}

// CapabilityManager 能力管理器接口
type CapabilityManager interface {
	// GetClassifier 获取分类器
	GetClassifier(name string) (Classifier, error)
	
	// GetEmotionAnalyzer 获取情绪分析器
	GetEmotionAnalyzer(name string) (EmotionAnalyzer, error)
	
	// GetQualityAssessor 获取质量评估器
	GetQualityAssessor(name string) (QualityAssessor, error)
	
	// GetAutoReplier 获取自动回复器
	GetAutoReplier(name string) (AutoReplier, error)
	
	// RegisterCapability 注册能力
	RegisterCapability(name string, capability interface{}) error
}
