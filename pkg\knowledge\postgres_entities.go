package knowledge

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"
)

// --- 实体管理 ---

func (kb *PostgresKnowledgeBase) AddEntity(ctx context.Context, e *Entity) error {
	var props []byte
	if e.Properties != nil {
		props, _ = json.Marshal(e.Properties)
	}
	aliases := toPGArray(e.<PERSON>)
	_, err := kb.db.ExecContext(ctx, `INSERT INTO kb_entities(id,name,type,description,properties,aliases,created_at,updated_at)
VALUES($1,$2,$3,$4,$5,$6,$7,$8)
ON CONFLICT (id) DO UPDATE SET name=EXCLUDED.name,type=EXCLUDED.type,description=EXCLUDED.description,properties=EXCLUDED.properties,aliases=EXCLUDED.aliases,updated_at=EXCLUDED.updated_at`,
		e.ID, e.Name, e.Type, e.Description, jsonRawOrNull(props), aliases, e.CreatedAt, e.UpdatedAt)
	return err
}

func (kb *PostgresKnowledgeBase) GetEntity(ctx context.Context, id string) (*Entity, error) {
	row := kb.db.QueryRowContext(ctx, `SELECT id,name,type,description,properties,aliases,created_at,updated_at FROM kb_entities WHERE id=$1`, id)
	var e Entity
	var props, aliases sql.NullString
	if err := row.Scan(&e.ID, &e.Name, &e.Type, &e.Description, &props, &aliases, &e.CreatedAt, &e.UpdatedAt); err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("entity not found")
		}
		return nil, err
	}
	if props.Valid && props.String != "" {
		_ = json.Unmarshal([]byte(props.String), &e.Properties)
	}
	e.Aliases = parsePGArray(aliases.String)
	// 加载关联文档
	rows, err := kb.db.QueryContext(ctx, `SELECT document_id FROM kb_document_entities WHERE entity_id=$1`, id)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var did string
			_ = rows.Scan(&did)
			e.DocumentIDs = append(e.DocumentIDs, did)
		}
	}
	return &e, nil
}

func (kb *PostgresKnowledgeBase) UpdateEntity(ctx context.Context, e *Entity) error {
	var props []byte
	if e.Properties != nil {
		props, _ = json.Marshal(e.Properties)
	}
	aliases := toPGArray(e.Aliases)
	res, err := kb.db.ExecContext(ctx, `UPDATE kb_entities SET name=$2,type=$3,description=$4,properties=$5,aliases=$6,updated_at=$7 WHERE id=$1`,
		e.ID, e.Name, e.Type, e.Description, jsonRawOrNull(props), aliases, e.UpdatedAt)
	if err != nil {
		return err
	}
	if n, _ := res.RowsAffected(); n == 0 {
		return fmt.Errorf("entity not found")
	}
	return nil
}

func (kb *PostgresKnowledgeBase) DeleteEntity(ctx context.Context, id string) error {
	_, err := kb.db.ExecContext(ctx, `DELETE FROM kb_entities WHERE id=$1`, id)
	return err
}

func (kb *PostgresKnowledgeBase) SearchEntities(ctx context.Context, q *EntityQuery) ([]*Entity, error) {
	if q == nil {
		q = &EntityQuery{}
	}
	sqlQ := `SELECT id,name,type,description,properties,aliases,created_at,updated_at FROM kb_entities WHERE 1=1`
	args := []any{}
	if q.Name != "" {
		sqlQ += ` AND name ILIKE $1`
		args = append(args, "%"+q.Name+"%")
	}
	if q.Type != "" {
		sqlQ += fmt.Sprintf(" AND type = $%d", len(args)+1)
		args = append(args, q.Type)
	}
	if len(q.DocumentIDs) > 0 {
		sqlQ += fmt.Sprintf(" AND id IN (SELECT entity_id FROM kb_document_entities WHERE document_id = ANY($%d::text[]))", len(args)+1)
		args = append(args, toPGArray(q.DocumentIDs))
	}
	limit := q.Limit
	if limit <= 0 {
		limit = 50
	}
	offset := q.Offset
	sqlQ += fmt.Sprintf(" ORDER BY updated_at DESC LIMIT $%d OFFSET $%d", len(args)+1, len(args)+2)
	args = append(args, limit, offset)
	rows, err := kb.db.QueryContext(ctx, sqlQ, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []*Entity
	for rows.Next() {
		var e Entity
		var props, aliases sql.NullString
		if err := rows.Scan(&e.ID, &e.Name, &e.Type, &e.Description, &props, &aliases, &e.CreatedAt, &e.UpdatedAt); err != nil {
			return nil, err
		}
		if props.Valid && props.String != "" {
			_ = json.Unmarshal([]byte(props.String), &e.Properties)
		}
		e.Aliases = parsePGArray(aliases.String)
		out = append(out, &e)
	}
	return out, nil
}

// --- 关系管理 ---

func (kb *PostgresKnowledgeBase) AddRelation(ctx context.Context, r *Relation) error {
	var props []byte
	if r.Properties != nil {
		props, _ = json.Marshal(r.Properties)
	}
	_, err := kb.db.ExecContext(ctx, `INSERT INTO kb_relations(id,from_entity,to_entity,type,description,properties,confidence,created_at,updated_at)
VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)
ON CONFLICT (id) DO UPDATE SET from_entity=EXCLUDED.from_entity,to_entity=EXCLUDED.to_entity,type=EXCLUDED.type,description=EXCLUDED.description,properties=EXCLUDED.properties,confidence=EXCLUDED.confidence,updated_at=EXCLUDED.updated_at`,
		r.ID, r.FromEntity, r.ToEntity, r.Type, r.Description, jsonRawOrNull(props), r.Confidence, r.CreatedAt, r.UpdatedAt)
	if err != nil {
		return err
	}
	// 关联文档
	for _, did := range r.DocumentIDs {
		_, _ = kb.db.ExecContext(ctx, `INSERT INTO kb_relation_documents(relation_id,document_id) VALUES($1,$2) ON CONFLICT DO NOTHING`, r.ID, did)
	}
	return nil
}

func (kb *PostgresKnowledgeBase) GetRelation(ctx context.Context, id string) (*Relation, error) {
	row := kb.db.QueryRowContext(ctx, `SELECT id,from_entity,to_entity,type,description,properties,confidence,created_at,updated_at FROM kb_relations WHERE id=$1`, id)
	var r Relation
	var props sql.NullString
	if err := row.Scan(&r.ID, &r.FromEntity, &r.ToEntity, &r.Type, &r.Description, &props, &r.Confidence, &r.CreatedAt, &r.UpdatedAt); err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("relation not found")
		}
		return nil, err
	}
	if props.Valid && props.String != "" {
		_ = json.Unmarshal([]byte(props.String), &r.Properties)
	}
	// 文档关联
	rows, err := kb.db.QueryContext(ctx, `SELECT document_id FROM kb_relation_documents WHERE relation_id=$1`, id)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var did string
			_ = rows.Scan(&did)
			r.DocumentIDs = append(r.DocumentIDs, did)
		}
	}
	return &r, nil
}

func (kb *PostgresKnowledgeBase) UpdateRelation(ctx context.Context, r *Relation) error {
	var props []byte
	if r.Properties != nil {
		props, _ = json.Marshal(r.Properties)
	}
	res, err := kb.db.ExecContext(ctx, `UPDATE kb_relations SET from_entity=$2,to_entity=$3,type=$4,description=$5,properties=$6,confidence=$7,updated_at=$8 WHERE id=$1`,
		r.ID, r.FromEntity, r.ToEntity, r.Type, r.Description, jsonRawOrNull(props), r.Confidence, r.UpdatedAt)
	if err != nil {
		return err
	}
	if n, _ := res.RowsAffected(); n == 0 {
		return fmt.Errorf("relation not found")
	}
	return nil
}

func (kb *PostgresKnowledgeBase) DeleteRelation(ctx context.Context, id string) error {
	_, err := kb.db.ExecContext(ctx, `DELETE FROM kb_relations WHERE id=$1`, id)
	return err
}

func (kb *PostgresKnowledgeBase) SearchRelations(ctx context.Context, q *RelationQuery) ([]*Relation, error) {
	if q == nil {
		q = &RelationQuery{}
	}
	s := `SELECT id,from_entity,to_entity,type,description,properties,confidence,created_at,updated_at FROM kb_relations WHERE 1=1`
	args := []any{}
	if q.FromEntity != "" {
		s += ` AND from_entity = $1`
		args = append(args, q.FromEntity)
	}
	if q.ToEntity != "" {
		s += fmt.Sprintf(" AND to_entity = $%d", len(args)+1)
		args = append(args, q.ToEntity)
	}
	if q.Type != "" {
		s += fmt.Sprintf(" AND type = $%d", len(args)+1)
		args = append(args, q.Type)
	}
	if q.MinConfidence > 0 {
		s += fmt.Sprintf(" AND confidence >= $%d", len(args)+1)
		args = append(args, q.MinConfidence)
	}
	if len(q.DocumentIDs) > 0 {
		s += fmt.Sprintf(" AND id IN (SELECT relation_id FROM kb_relation_documents WHERE document_id = ANY($%d::text[]))", len(args)+1)
		args = append(args, toPGArray(q.DocumentIDs))
	}
	limit := q.Limit
	if limit <= 0 {
		limit = 50
	}
	offset := q.Offset
	s += fmt.Sprintf(" ORDER BY updated_at DESC LIMIT $%d OFFSET $%d", len(args)+1, len(args)+2)
	args = append(args, limit, offset)
	rows, err := kb.db.QueryContext(ctx, s, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []*Relation
	for rows.Next() {
		var r Relation
		var props sql.NullString
		if err := rows.Scan(&r.ID, &r.FromEntity, &r.ToEntity, &r.Type, &r.Description, &props, &r.Confidence, &r.CreatedAt, &r.UpdatedAt); err != nil {
			return nil, err
		}
		if props.Valid && props.String != "" {
			_ = json.Unmarshal([]byte(props.String), &r.Properties)
		}
		out = append(out, &r)
	}
	return out, nil
}

// --- 图查询 ---

func (kb *PostgresKnowledgeBase) GetEntityRelations(ctx context.Context, entityID string) ([]*Relation, error) {
	rows, err := kb.db.QueryContext(ctx, `SELECT id,from_entity,to_entity,type,description,properties,confidence,created_at,updated_at FROM kb_relations WHERE from_entity=$1 OR to_entity=$1`, entityID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []*Relation
	for rows.Next() {
		var r Relation
		var props sql.NullString
		if err := rows.Scan(&r.ID, &r.FromEntity, &r.ToEntity, &r.Type, &r.Description, &props, &r.Confidence, &r.CreatedAt, &r.UpdatedAt); err != nil {
			return nil, err
		}
		if props.Valid && props.String != "" {
			_ = json.Unmarshal([]byte(props.String), &r.Properties)
		}
		out = append(out, &r)
	}
	return out, nil
}

func (kb *PostgresKnowledgeBase) GetRelatedEntities(ctx context.Context, entityID string, relationTypes []string) ([]*Entity, error) {
	q := `SELECT e.id,e.name,e.type,e.description,e.properties,e.aliases,e.created_at,e.updated_at
FROM kb_entities e JOIN kb_relations r ON (e.id=r.from_entity OR e.id=r.to_entity)
WHERE ($1 = r.from_entity OR $1 = r.to_entity) AND e.id <> $1`
	args := []any{entityID}
	if len(relationTypes) > 0 {
		q += ` AND r.type = ANY($2::text[])`
		args = append(args, toPGArray(relationTypes))
	}
	rows, err := kb.db.QueryContext(ctx, q, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	seen := map[string]struct{}{}
	var out []*Entity
	for rows.Next() {
		var e Entity
		var props, aliases sql.NullString
		if err := rows.Scan(&e.ID, &e.Name, &e.Type, &e.Description, &props, &aliases, &e.CreatedAt, &e.UpdatedAt); err != nil {
			return nil, err
		}
		if _, ok := seen[e.ID]; ok {
			continue
		}
		seen[e.ID] = struct{}{}
		if props.Valid && props.String != "" {
			_ = json.Unmarshal([]byte(props.String), &e.Properties)
		}
		e.Aliases = parsePGArray(aliases.String)
		out = append(out, &e)
	}
	return out, nil
}

// FindPath 使用简单BFS查找从from到to的关系路径，最大深度maxDepth
func (kb *PostgresKnowledgeBase) FindPath(ctx context.Context, fromEntityID, toEntityID string, maxDepth int) ([][]*Relation, error) {
	if fromEntityID == toEntityID {
		return [][]*Relation{}, nil
	}
	if maxDepth <= 0 {
		maxDepth = 3
	}
	type path struct {
		seq  []*Relation
		last string
	}
	// 初始化：取与from相连的边
	neighbors, err := kb.GetEntityRelations(ctx, fromEntityID)
	if err != nil {
		return nil, err
	}
	queue := []path{}
	for _, r := range neighbors {
		last := r.ToEntity
		if last == fromEntityID {
			last = r.FromEntity
		}
		queue = append(queue, path{seq: []*Relation{r}, last: last})
	}
	visited := map[string]bool{fromEntityID: true}
	var solutions [][]*Relation
	for depth := 1; depth <= maxDepth && len(queue) > 0; depth++ {
		next := []path{}
		for _, p := range queue {
			if p.last == toEntityID {
				solutions = append(solutions, p.seq)
				continue
			}
			if visited[p.last] {
				continue
			}
			visited[p.last] = true
			rels, err := kb.GetEntityRelations(ctx, p.last)
			if err != nil {
				return nil, err
			}
			for _, r := range rels {
				nextNode := r.ToEntity
				if nextNode == p.last {
					nextNode = r.FromEntity
				}
				// 避免回到起点
				if nextNode == fromEntityID {
					continue
				}
				next = append(next, path{seq: append(append([]*Relation{}, p.seq...), r), last: nextNode})
			}
		}
		queue = next
	}
	return solutions, nil
}

// 可选：维护最近更新时间，便于指标采集
func (kb *PostgresKnowledgeBase) touch(ctx context.Context) {
	_, _ = kb.db.ExecContext(ctx, `SELECT 1`)
	_ = time.Now()
}
