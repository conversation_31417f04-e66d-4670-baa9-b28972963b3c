package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/audit"
	"github.com/gorilla/mux"
)

// TestAuditIntegration 集成测试：验证完整的审计功能流程
func TestAuditIntegration(t *testing.T) {
	// 跳过集成测试，除非设置了环境变量
	if os.Getenv("RUN_INTEGRATION_TESTS") == "" {
		t.Skip("跳过集成测试，设置 RUN_INTEGRATION_TESTS=1 来运行")
	}

	// 创建临时数据库文件
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_audit.db")

	// 创建测试配置
	auditConfig := &audit.Config{
		Enabled: true,
		Driver:  "sqlite",
		DSN:     dbPath,
		Privacy: audit.PrivacyConfig{
			RedactPII:     true,
			PIIPatterns:   []string{"email", "phone"},
			HashContent:   false,
			EncryptAtRest: false,
		},
		Batch: audit.BatchConfig{
			Async:         false, // 使用同步模式便于测试
			ChanBuffer:    10,
			FlushInterval: time.Millisecond * 100,
		},
		Retention: audit.RetentionConfig{
			Enabled: false, // 测试期间禁用自动清理
		},
		Web: audit.WebConfig{
			AllowReadAPI:   true,
			AllowDeleteAPI: false,
		},
	}

	// 创建审计存储
	store, err := audit.NewSQLiteStore(dbPath, auditConfig)
	if err != nil {
		t.Fatalf("创建审计存储失败: %v", err)
	}
	defer store.Close()

	// 创建数据脱敏器
	sanitizer, err := audit.NewSanitizer(&auditConfig.Privacy)
	if err != nil {
		t.Fatalf("创建数据脱敏器失败: %v", err)
	}

	// 创建审计写入器
	writer := audit.NewAuditWriter(store, sanitizer, &auditConfig.Batch)
	err = writer.Start(context.Background())
	if err != nil {
		t.Fatalf("启动审计写入器失败: %v", err)
	}
	defer writer.Close()

	// 创建审计查询器
	reader := audit.NewAuditReader(store, sanitizer)

	// 测试1：基础存储功能
	t.Run("基础存储功能", func(t *testing.T) {
		testBasicStorage(t, store)
	})

	// 测试2：审计写入器功能
	t.Run("审计写入器功能", func(t *testing.T) {
		testAuditWriter(t, writer)
	})

	// 测试3：审计查询器功能
	t.Run("审计查询器功能", func(t *testing.T) {
		testAuditReader(t, reader, store)
	})

	// 测试4：Web API集成
	t.Run("Web API集成", func(t *testing.T) {
		testWebAPIIntegration(t, reader, store)
	})

	// 测试5：数据隐私保护
	t.Run("数据隐私保护", func(t *testing.T) {
		testPrivacyProtection(t, writer, store)
	})
}

// testBasicStorage 测试基础存储功能
func testBasicStorage(t *testing.T, store audit.Store) {
	ctx := context.Background()

	// 创建测试记录
	record := &audit.Record{
		ID:        audit.GenerateID(),
		SessionID: "test-session-1",
		UserID:    "test-user-1",
		Role:      "user",
		MsgType:   "text",
		Content:   "测试消息内容",
		CreatedAt: time.Now(),
	}

	// 保存记录
	err := store.SaveMessage(ctx, record)
	if err != nil {
		t.Fatalf("保存消息失败: %v", err)
	}

	// 查询记录
	query := audit.Query{
		SessionID: &record.SessionID,
		Limit:     10,
		Offset:    0,
	}

	result, err := store.QueryMessages(ctx, query)
	if err != nil {
		t.Fatalf("查询消息失败: %v", err)
	}

	if len(result.Records) != 1 {
		t.Fatalf("期望查询到1条记录，实际查询到%d条", len(result.Records))
	}

	if result.Records[0].Content != record.Content {
		t.Errorf("记录内容不匹配，期望: %s，实际: %s", record.Content, result.Records[0].Content)
	}
}

// testAuditWriter 测试审计写入器功能
func testAuditWriter(t *testing.T, writer *audit.AuditWriter) {
	ctx := context.Background()

	// 写入多条记录
	sessionID := "test-session-writer"
	for i := 0; i < 5; i++ {
		record := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: sessionID,
			UserID:    "test-user-writer",
			Role:      "user",
			MsgType:   "text",
			Content:   fmt.Sprintf("测试消息 %d", i+1),
			CreatedAt: time.Now(),
		}

		err := writer.WriteRecord(ctx, record)
		if err != nil {
			t.Fatalf("写入记录失败: %v", err)
		}
	}

	// 等待写入完成
	time.Sleep(time.Millisecond * 200)

	// 检查统计信息
	stats := writer.GetStats()
	if stats.IsClosed {
		t.Error("写入器不应该已关闭")
	}
}

// testAuditReader 测试审计查询器功能
func testAuditReader(t *testing.T, reader *audit.AuditReader, store audit.Store) {
	ctx := context.Background()

	// 先插入一些测试数据
	sessionID := "test-session-reader"
	userID := "test-user-reader"

	for i := 0; i < 3; i++ {
		record := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: sessionID,
			UserID:    userID,
			Role:      "user",
			MsgType:   "text",
			Content:   fmt.Sprintf("用户消息 %d", i+1),
			CreatedAt: time.Now().Add(time.Duration(i) * time.Minute),
		}
		store.SaveMessage(ctx, record)

		// 添加对应的助手响应
		assistantRecord := &audit.Record{
			ID:        audit.GenerateID(),
			SessionID: sessionID,
			AgentID:   "assistant-001",
			Role:      "assistant",
			MsgType:   "final",
			Content:   fmt.Sprintf("助手响应 %d", i+1),
			CreatedAt: time.Now().Add(time.Duration(i)*time.Minute + time.Second*30),
		}
		store.SaveMessage(ctx, assistantRecord)
	}

	// 测试按会话查询
	query := audit.Query{
		SessionID: &sessionID,
		Limit:     10,
		Offset:    0,
	}

	result, err := reader.QueryMessages(ctx, query)
	if err != nil {
		t.Fatalf("查询消息失败: %v", err)
	}

	if len(result.Records) != 6 { // 3条用户消息 + 3条助手响应
		t.Errorf("期望查询到6条记录，实际查询到%d条", len(result.Records))
	}

	// 测试按用户查询
	userResult, err := reader.QueryMessagesByUser(ctx, userID, 10, 0)
	if err != nil {
		t.Fatalf("按用户查询失败: %v", err)
	}

	if len(userResult.Records) < 3 {
		t.Errorf("期望至少查询到3条用户记录，实际查询到%d条", len(userResult.Records))
	}

	// 测试会话摘要
	summary, err := reader.GetSessionSummary(ctx, sessionID)
	if err != nil {
		t.Fatalf("获取会话摘要失败: %v", err)
	}

	if summary.SessionID != sessionID {
		t.Errorf("会话摘要ID不匹配，期望: %s，实际: %s", sessionID, summary.SessionID)
	}

	if summary.MessageCount < 6 {
		t.Errorf("会话摘要消息数量不正确，期望至少6条，实际: %d", summary.MessageCount)
	}
}

// testWebAPIIntegration 测试Web API集成
func testWebAPIIntegration(t *testing.T, reader *audit.AuditReader, store audit.Store) {
	// 创建API处理器
	webConfig := &audit.WebConfig{
		AllowReadAPI:   true,
		AllowDeleteAPI: false,
	}

	apiHandler := audit.NewAPIHandler(reader, nil, webConfig)

	// 创建路由器
	router := mux.NewRouter()
	apiHandler.RegisterRoutes(router)

	// 先插入测试数据
	ctx := context.Background()
	sessionID := "test-session-api"

	record := &audit.Record{
		ID:        audit.GenerateID(),
		SessionID: sessionID,
		UserID:    "test-user-api",
		Role:      "user",
		MsgType:   "text",
		Content:   "API测试消息",
		CreatedAt: time.Now(),
	}
	store.SaveMessage(ctx, record)

	// 测试获取会话消息API
	req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/audit/sessions/%s/messages", sessionID), nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("API请求失败，状态码: %d，响应: %s", w.Code, w.Body.String())
	}

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("解析API响应失败: %v", err)
	}

	messages, ok := response["messages"].([]interface{})
	if !ok {
		t.Fatal("API响应中没有messages字段")
	}

	if len(messages) == 0 {
		t.Error("API应该返回至少一条消息")
	}
}

// testPrivacyProtection 测试数据隐私保护
func testPrivacyProtection(t *testing.T, writer *audit.AuditWriter, store audit.Store) {
	ctx := context.Background()

	// 创建包含敏感信息的记录
	originalContent := "我的邮箱是 <EMAIL>，电话是 13812345678"
	record := &audit.Record{
		ID:        audit.GenerateID(),
		SessionID: "test-session-privacy",
		UserID:    "test-user-privacy",
		Role:      "user",
		MsgType:   "text",
		Content:   originalContent,
		CreatedAt: time.Now(),
	}

	// 写入记录（应该自动脱敏）
	err := writer.WriteRecord(ctx, record)
	if err != nil {
		t.Fatalf("写入记录失败: %v", err)
	}

	// 等待写入完成
	time.Sleep(time.Millisecond * 200)

	// 查询记录验证脱敏效果
	query := audit.Query{
		SessionID: &record.SessionID,
		Limit:     1,
		Offset:    0,
	}

	result, err := store.QueryMessages(ctx, query)
	if err != nil {
		t.Fatalf("查询记录失败: %v", err)
	}

	if len(result.Records) != 1 {
		t.Fatalf("期望查询到1条记录，实际查询到%d条", len(result.Records))
	}

	storedContent := result.Records[0].Content
	if storedContent == originalContent {
		t.Error("敏感信息应该被脱敏，但内容未发生变化")
	}

	// 验证邮箱和电话号码被脱敏
	if storedContent == originalContent {
		t.Error("PII信息应该被脱敏")
	}
}
