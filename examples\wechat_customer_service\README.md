# 企业微信客服Agent示例 - 配置驱动架构

这是一个基于AgentScope-Golang框架的企业微信客服Agent示例，展示了全新的**配置驱动架构**设计理念。

## 🎯 架构理念

### 传统架构 vs 配置驱动架构

**传统架构问题**：
- 每个Agent都重复实现基础AI能力（分类、情绪分析、质量评估等）
- 代码复杂度高，维护困难
- 业务逻辑与AI能力耦合严重
- 扩展性差，难以复用

**配置驱动架构优势**：
- **框架能力抽象**：AI能力由框架统一提供，避免重复实现
- **角色驱动设计**：通过配置文件定义Agent角色和行为
- **工具化扩展**：业务功能通过Tool和MCP Server实现
- **配置即代码**：Agent行为主要通过YAML配置定义

## 🏗️ 新架构设计

### 核心组件

1. **框架能力层** (`pkg/agent/capabilities/`)
   - `Classifier`: 通用问题分类能力
   - `EmotionAnalyzer`: 情绪分析能力  
   - `QualityAssessor`: 质量评估能力
   - `AutoReplier`: 自动回复能力

2. **配置驱动Agent** (`pkg/agent/config_driven_agent.go`)
   - 基于配置文件创建Agent
   - 自动注册框架能力和工具
   - 事件驱动的处理流程

3. **工具系统** (`pkg/tool/builtin/`)
   - `TicketTool`: 工单管理工具
   - 可扩展的业务功能工具

4. **提示词管理** (`pkg/llm/prompts/`)
   - 结构化提示词模板
   - 变量替换和JSON响应解析

## 🚀 功能特性

- **智能问题分类**：基于LLM+提示词的智能分类，支持自定义类别
- **实时情绪分析**：多维度情绪识别，自动升级负面情绪
- **智能自动回复**：基于FAQ库的智能匹配，支持置信度评估
- **工单管理系统**：完整的CRUD操作，支持优先级和状态管理
- **多维质量评估**：准确性、完整性、专业性、友好性、效率性评估
- **事件驱动架构**：完整的事件流处理和异步迭代器

## 📋 配置文件说明

### Agent配置结构

```yaml
agents:
  wechat_customer_service:
    name: "企业微信客服助手"
    description: "专业的企业微信客服Agent"
    
    # 角色配置 - 定义Agent的基本行为
    role:
      system_prompt: |
        你是一个专业的企业微信客服助手...
      personality: "友好、专业、耐心、细致"
      expertise: ["客户服务", "问题解答", "产品咨询"]
      constraints: ["始终保持礼貌", "保护用户隐私"]
      examples:
        - user: "你好，我想咨询产品功能"
          assistant: "您好！很高兴为您服务..."
    
    # 工具配置 - 注册业务功能工具
    tools:
      - "ticket_manager"
    
    # 能力配置 - 启用框架AI能力
    capabilities:
      classification:
        enabled: true
        parameters:
          categories:
            - name: "技术支持"
              keywords: ["bug", "错误", "故障"]
              priority: "high"
      
      emotion_analysis:
        enabled: true
        parameters:
          escalation_threshold: 0.7
      
      auto_reply:
        enabled: true
        faqs:
          - question: "营业时间"
            answer: "我们的营业时间是..."
            keywords: ["营业时间", "工作时间"]
```

## 🛠️ 快速开始

### 1. 环境准备

```bash
export DEEPSEEK_API_KEY="your_deepseek_api_key"
```

### 2. 运行示例

```bash
cd examples/wechat_customer_service
go run main.go
```

### 3. 观察事件流

程序会输出完整的事件处理流程：

```
=== 企业微信客服Agent示例 - 配置驱动架构 ===
新架构特性：
- 框架提供通用AI能力（分类、情绪分析、质量评估）
- 配置驱动的Agent行为定义
- 工具化的业务功能扩展
- 基于LLM+提示词的智能能力

收到事件: thought
收到事件: classification
收到事件: emotion_analysis  
收到事件: auto_reply
收到事件: quality_assessment
收到事件: final
```

## 🧪 测试

运行完整测试套件：

```bash
go test -cover ./pkg/agent/
# coverage: 59.3% of statements
```

测试包含：
- ConfigDrivenAgent创建和配置
- 事件流处理验证
- 能力启用/禁用测试
- 工具注册验证
- 系统提示词构建测试

## 🔧 扩展开发

### 添加新的AI能力

1. 在 `pkg/agent/capabilities/types.go` 中定义接口
2. 在 `pkg/agent/capabilities/` 中实现LLM版本
3. 在 `pkg/llm/prompts/templates.go` 中添加提示词模板
4. 在配置文件中启用新能力

### 添加新的工具

1. 在 `pkg/tool/builtin/` 中实现Tool接口
2. 在 `config_driven_agent.go` 中注册工具
3. 在配置文件的tools列表中添加

### 创建新的Agent角色

复制并修改配置文件中的agent定义：

```yaml
agents:
  sales_agent:
    name: "销售助手"
    role:
      system_prompt: "你是一个专业的销售助手..."
      expertise: ["产品销售", "客户关系", "商务谈判"]
    capabilities:
      # 根据销售场景配置不同的能力
```

## 📊 架构优势验证

### 代码复用性
- **传统架构**：每个Agent ~500行重复代码
- **配置驱动**：Agent主逻辑 <100行，能力复用100%

### 配置灵活性
- **传统架构**：修改行为需要改代码
- **配置驱动**：修改YAML即可调整行为

### 测试覆盖率
- **框架能力**：独立测试，覆盖率>80%
- **Agent逻辑**：配置驱动，测试简化
- **整体覆盖**：59.3%（持续提升中）

## 🎉 总结

这个示例展示了如何通过**配置驱动架构**构建高质量、可维护的Agent系统：

1. **框架能力抽象**：避免重复实现，提高代码质量
2. **配置驱动设计**：降低复杂度，提高灵活性
3. **工具化扩展**：业务功能模块化，易于扩展
4. **真实API集成**：遵循"禁止简化实现"规范

这种架构为构建企业级Agent系统提供了最佳实践参考。
