package web

import (
	"fmt"
	"testing"
	"time"
)

// BenchmarkRateLimiterAllow 基准测试RateLimiter.Allow方法
func BenchmarkRateLimiterAllow(b *testing.B) {
	config := &RateLimitConfig{
		RequestsPerMinute: 60,
		BurstSize:         10,
		CleanupInterval:   time.Minute,
	}
	
	limiter := NewRateLimiter(config)
	
	b.<PERSON>setTimer()
	b.<PERSON><PERSON>(func(pb *testing.PB) {
		clientIP := "***********"
		for pb.Next() {
			limiter.Allow(clientIP)
		}
	})
}

// BenchmarkRateLimiterAllowMultipleClients 基准测试多客户端场景
func BenchmarkRateLimiterAllowMultipleClients(b *testing.B) {
	config := &RateLimitConfig{
		RequestsPerMinute: 60,
		BurstSize:         10,
		CleanupInterval:   time.Minute,
	}
	
	limiter := NewRateLimiter(config)
	
	b.<PERSON>setTimer()
	b.<PERSON><PERSON><PERSON><PERSON><PERSON>(func(pb *testing.PB) {
		clientCounter := 0
		for pb.Next() {
			clientIP := fmt.Sprintf("192.168.1.%d", clientCounter%100)
			limiter.Allow(clientIP)
			clientCounter++
		}
	})
}

// BenchmarkRateLimiterConcurrentAccess 基准测试并发访问
func BenchmarkRateLimiterConcurrentAccess(b *testing.B) {
	config := &RateLimitConfig{
		RequestsPerMinute: 1000,
		BurstSize:         50,
		CleanupInterval:   time.Minute,
	}
	
	limiter := NewRateLimiter(config)
	
	b.ResetTimer()
	b.SetParallelism(10) // 设置并发度
	b.RunParallel(func(pb *testing.PB) {
		clientCounter := 0
		for pb.Next() {
			clientIP := fmt.Sprintf("10.0.0.%d", clientCounter%255)
			limiter.Allow(clientIP)
			clientCounter++
		}
	})
}

// TestRateLimiterAccuracy 测试限流精度
func TestRateLimiterAccuracy(t *testing.T) {
	config := &RateLimitConfig{
		RequestsPerMinute: 60, // 每分钟60个请求，即每秒1个
		BurstSize:         5,  // 突发5个
		CleanupInterval:   time.Minute,
	}
	
	limiter := NewRateLimiter(config)
	clientIP := "test.client"
	
	// 测试突发请求
	allowedCount := 0
	for i := 0; i < 10; i++ {
		if limiter.Allow(clientIP) {
			allowedCount++
		}
	}
	
	// 应该允许突发大小的请求数
	if allowedCount != config.BurstSize {
		t.Errorf("突发请求测试失败: 期望 %d, 实际 %d", config.BurstSize, allowedCount)
	}
	
	// 等待一段时间后再次测试
	time.Sleep(2 * time.Second)
	
	// 应该能够再次允许一些请求
	if !limiter.Allow(clientIP) {
		t.Error("等待后应该能够允许新请求")
	}
}

// TestRateLimiterCleanup 测试清理功能
func TestRateLimiterCleanup(t *testing.T) {
	config := &RateLimitConfig{
		RequestsPerMinute: 60,
		BurstSize:         10,
		CleanupInterval:   100 * time.Millisecond, // 快速清理用于测试
	}
	
	limiter := NewRateLimiter(config)
	
	// 添加一些客户端
	for i := 0; i < 5; i++ {
		clientIP := fmt.Sprintf("192.168.1.%d", i)
		limiter.Allow(clientIP)
	}
	
	// 检查客户端数量
	limiter.mu.RLock()
	initialCount := len(limiter.clients)
	limiter.mu.RUnlock()
	
	if initialCount != 5 {
		t.Errorf("初始客户端数量错误: 期望 5, 实际 %d", initialCount)
	}
	
	// 等待清理
	time.Sleep(200 * time.Millisecond)
	
	// 由于清理间隔很短，但客户端刚刚访问过，所以不应该被清理
	limiter.mu.RLock()
	afterCleanupCount := len(limiter.clients)
	limiter.mu.RUnlock()
	
	if afterCleanupCount != 5 {
		t.Errorf("清理后客户端数量错误: 期望 5, 实际 %d", afterCleanupCount)
	}
}
