package runtime

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
)

// Interceptor 表示拦截器接口，用于在 Agent 执行过程中进行切面处理
type Interceptor interface {
	// Name 返回拦截器的名称
	Name() string
	// BeforeRun 在 Agent 运行前调用
	BeforeRun(ctx context.Context, agentName string, input any) error
	// AfterRun 在 Agent 运行后调用
	AfterRun(ctx context.Context, agentName string, input any, err error) error
	// OnEvent 在事件产生时调用
	OnEvent(ctx context.Context, agentName string, ev *event.Event) error
	// OnError 在发生错误时调用
	OnError(ctx context.Context, agentName string, err error) error
}

// InterceptorChain 表示拦截器链，按顺序执行多个拦截器
type InterceptorChain struct {
	interceptors []Interceptor
	mu           sync.RWMutex
}

// NewInterceptorChain 创建新的拦截器链
func NewInterceptorChain() *InterceptorChain {
	return &InterceptorChain{
		interceptors: make([]Interceptor, 0),
	}
}

// Add 添加拦截器到链中
func (ic *InterceptorChain) Add(interceptor Interceptor) {
	ic.mu.Lock()
	defer ic.mu.Unlock()
	ic.interceptors = append(ic.interceptors, interceptor)
}

// Remove 从链中移除指定名称的拦截器
func (ic *InterceptorChain) Remove(name string) bool {
	ic.mu.Lock()
	defer ic.mu.Unlock()

	for i, interceptor := range ic.interceptors {
		if interceptor.Name() == name {
			ic.interceptors = append(ic.interceptors[:i], ic.interceptors[i+1:]...)
			return true
		}
	}
	return false
}

// Clear 清空拦截器链
func (ic *InterceptorChain) Clear() {
	ic.mu.Lock()
	defer ic.mu.Unlock()
	ic.interceptors = ic.interceptors[:0]
}

// List 返回所有拦截器的名称列表
func (ic *InterceptorChain) List() []string {
	ic.mu.RLock()
	defer ic.mu.RUnlock()

	names := make([]string, len(ic.interceptors))
	for i, interceptor := range ic.interceptors {
		names[i] = interceptor.Name()
	}
	return names
}

// BeforeRun 执行所有拦截器的 BeforeRun 方法
func (ic *InterceptorChain) BeforeRun(ctx context.Context, agentName string, input any) error {
	ic.mu.RLock()
	defer ic.mu.RUnlock()

	for _, interceptor := range ic.interceptors {
		if err := interceptor.BeforeRun(ctx, agentName, input); err != nil {
			return fmt.Errorf("拦截器 %s BeforeRun 失败: %w", interceptor.Name(), err)
		}
	}
	return nil
}

// AfterRun 执行所有拦截器的 AfterRun 方法（逆序执行）
func (ic *InterceptorChain) AfterRun(ctx context.Context, agentName string, input any, err error) error {
	ic.mu.RLock()
	defer ic.mu.RUnlock()

	// 逆序执行 AfterRun
	for i := len(ic.interceptors) - 1; i >= 0; i-- {
		if afterErr := ic.interceptors[i].AfterRun(ctx, agentName, input, err); afterErr != nil {
			return fmt.Errorf("拦截器 %s AfterRun 失败: %w", ic.interceptors[i].Name(), afterErr)
		}
	}
	return nil
}

// OnEvent 执行所有拦截器的 OnEvent 方法
func (ic *InterceptorChain) OnEvent(ctx context.Context, agentName string, ev *event.Event) error {
	ic.mu.RLock()
	defer ic.mu.RUnlock()

	for _, interceptor := range ic.interceptors {
		if err := interceptor.OnEvent(ctx, agentName, ev); err != nil {
			return fmt.Errorf("拦截器 %s OnEvent 失败: %w", interceptor.Name(), err)
		}
	}
	return nil
}

// OnError 执行所有拦截器的 OnError 方法
func (ic *InterceptorChain) OnError(ctx context.Context, agentName string, err error) error {
	ic.mu.RLock()
	defer ic.mu.RUnlock()

	for _, interceptor := range ic.interceptors {
		if onErr := interceptor.OnError(ctx, agentName, err); onErr != nil {
			return fmt.Errorf("拦截器 %s OnError 失败: %w", interceptor.Name(), onErr)
		}
	}
	return nil
}

// LoggingInterceptor 日志拦截器，记录 Agent 执行过程
type LoggingInterceptor struct {
	name   string
	logger *log.Logger
}

// NewLoggingInterceptor 创建新的日志拦截器
func NewLoggingInterceptor(name string, logger *log.Logger) *LoggingInterceptor {
	if logger == nil {
		logger = log.Default()
	}
	return &LoggingInterceptor{
		name:   name,
		logger: logger,
	}
}

// Name 返回拦截器名称
func (li *LoggingInterceptor) Name() string {
	return li.name
}

// BeforeRun 记录 Agent 开始运行
func (li *LoggingInterceptor) BeforeRun(ctx context.Context, agentName string, input any) error {
	li.logger.Printf("[%s] Agent %s 开始运行，输入: %+v", li.name, agentName, input)
	return nil
}

// AfterRun 记录 Agent 运行结束
func (li *LoggingInterceptor) AfterRun(ctx context.Context, agentName string, input any, err error) error {
	if err != nil {
		li.logger.Printf("[%s] Agent %s 运行失败: %v", li.name, agentName, err)
	} else {
		li.logger.Printf("[%s] Agent %s 运行成功", li.name, agentName)
	}
	return nil
}

// OnEvent 记录事件
func (li *LoggingInterceptor) OnEvent(ctx context.Context, agentName string, ev *event.Event) error {
	li.logger.Printf("[%s] Agent %s 产生事件: %s", li.name, agentName, ev.Type)
	return nil
}

// OnError 记录错误
func (li *LoggingInterceptor) OnError(ctx context.Context, agentName string, err error) error {
	li.logger.Printf("[%s] Agent %s 发生错误: %v", li.name, agentName, err)
	return nil
}

// MetricsInterceptor 指标拦截器，收集 Agent 执行指标
type MetricsInterceptor struct {
	name      string
	startTime map[string]time.Time
	mu        sync.RWMutex
	// 指标数据
	RunCount   map[string]int64
	ErrorCount map[string]int64
	EventCount map[string]map[event.Type]int64
	Duration   map[string]time.Duration
}

// NewMetricsInterceptor 创建新的指标拦截器
func NewMetricsInterceptor(name string) *MetricsInterceptor {
	return &MetricsInterceptor{
		name:       name,
		startTime:  make(map[string]time.Time),
		RunCount:   make(map[string]int64),
		ErrorCount: make(map[string]int64),
		EventCount: make(map[string]map[event.Type]int64),
		Duration:   make(map[string]time.Duration),
	}
}

// Name 返回拦截器名称
func (mi *MetricsInterceptor) Name() string {
	return mi.name
}

// BeforeRun 记录开始时间和运行次数
func (mi *MetricsInterceptor) BeforeRun(ctx context.Context, agentName string, input any) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	mi.startTime[agentName] = time.Now()
	mi.RunCount[agentName]++

	return nil
}

// AfterRun 记录结束时间和错误次数
func (mi *MetricsInterceptor) AfterRun(ctx context.Context, agentName string, input any, err error) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	if startTime, exists := mi.startTime[agentName]; exists {
		duration := time.Since(startTime)
		mi.Duration[agentName] += duration
		delete(mi.startTime, agentName)
	}

	if err != nil {
		mi.ErrorCount[agentName]++
	}

	return nil
}

// OnEvent 记录事件次数
func (mi *MetricsInterceptor) OnEvent(ctx context.Context, agentName string, ev *event.Event) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	if mi.EventCount[agentName] == nil {
		mi.EventCount[agentName] = make(map[event.Type]int64)
	}
	mi.EventCount[agentName][ev.Type]++

	return nil
}

// OnError 记录错误次数
func (mi *MetricsInterceptor) OnError(ctx context.Context, agentName string, err error) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	mi.ErrorCount[agentName]++

	return nil
}

// GetMetrics 获取指定 Agent 的指标数据
func (mi *MetricsInterceptor) GetMetrics(agentName string) map[string]any {
	mi.mu.RLock()
	defer mi.mu.RUnlock()

	metrics := make(map[string]any)
	metrics["run_count"] = mi.RunCount[agentName]
	metrics["error_count"] = mi.ErrorCount[agentName]
	metrics["duration"] = mi.Duration[agentName]
	metrics["event_count"] = mi.EventCount[agentName]

	return metrics
}

// GetAllMetrics 获取所有 Agent 的指标数据
func (mi *MetricsInterceptor) GetAllMetrics() map[string]map[string]any {
	mi.mu.RLock()
	defer mi.mu.RUnlock()

	allMetrics := make(map[string]map[string]any)

	// 收集所有 Agent 名称
	agentNames := make(map[string]bool)
	for name := range mi.RunCount {
		agentNames[name] = true
	}
	for name := range mi.ErrorCount {
		agentNames[name] = true
	}
	for name := range mi.Duration {
		agentNames[name] = true
	}
	for name := range mi.EventCount {
		agentNames[name] = true
	}

	// 为每个 Agent 生成指标
	for agentName := range agentNames {
		allMetrics[agentName] = mi.GetMetrics(agentName)
	}

	return allMetrics
}

// Reset 重置指标数据
func (mi *MetricsInterceptor) Reset() {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	mi.startTime = make(map[string]time.Time)
	mi.RunCount = make(map[string]int64)
	mi.ErrorCount = make(map[string]int64)
	mi.EventCount = make(map[string]map[event.Type]int64)
	mi.Duration = make(map[string]time.Duration)
}
