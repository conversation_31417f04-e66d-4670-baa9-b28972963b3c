package httprequest

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// HTTPRequestTool HTTP 请求工具
type HTTPRequestTool struct {
	client  *http.Client
	timeout time.Duration
	retries int
}

// RequestParams HTTP 请求参数
type RequestParams struct {
	URL     string            `json:"url"`
	Method  string            `json:"method"`
	Headers map[string]string `json:"headers,omitempty"`
	Body    string            `json:"body,omitempty"`
	Timeout int               `json:"timeout,omitempty"` // 超时时间（秒）
}

// ResponseData HTTP 响应数据
type ResponseData struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       string            `json:"body"`
	Success    bool              `json:"success"`
	Error      string            `json:"error,omitempty"`
}

// NewHTTPRequestTool 创建新的 HTTP 请求工具
func NewHTTPRequestTool() *HTTPRequestTool {
	return &HTTPRequestTool{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		timeout: 30 * time.Second,
		retries: 3,
	}
}

// NewHTTPRequestToolWithConfig 使用配置创建 HTTP 请求工具
func NewHTTPRequestToolWithConfig(timeout time.Duration, retries int) *HTTPRequestTool {
	return &HTTPRequestTool{
		client: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
		retries: retries,
	}
}

// Name 返回工具名称
func (h *HTTPRequestTool) Name() string {
	return "httprequest"
}

// Description 返回工具描述
func (h *HTTPRequestTool) Description() string {
	return "发送 HTTP 请求并返回响应结果。支持 GET、POST、PUT、DELETE 等方法，可以设置请求头和请求体。"
}

// Schema 返回工具参数的 JSON Schema
func (h *HTTPRequestTool) Schema() *tool.JSONSchema {
	return &tool.JSONSchema{
		Type: "object",
		Properties: map[string]*tool.JSONSchema{
			"url": {
				Type:        "string",
				Description: "请求的 URL 地址",
			},
			"method": {
				Type:        "string",
				Description: "HTTP 请求方法",
				Enum:        []any{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"},
			},
			"headers": {
				Type:        "object",
				Description: "请求头，键值对形式",
				Properties: map[string]*tool.JSONSchema{
					"Content-Type":  {Type: "string"},
					"Authorization": {Type: "string"},
					"User-Agent":    {Type: "string"},
				},
			},
			"body": {
				Type:        "string",
				Description: "请求体内容（JSON 字符串或普通文本）",
			},
			"timeout": {
				Type:        "integer",
				Description: "请求超时时间（秒），默认 30 秒",
				Minimum:     func() *float64 { v := 1.0; return &v }(),
				Maximum:     func() *float64 { v := 300.0; return &v }(),
			},
		},
		Required: []string{"url", "method"},
	}
}

// Execute 执行 HTTP 请求
func (h *HTTPRequestTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	// 解析输入参数
	var requestParams RequestParams

	// 从map中提取参数
	if url, ok := params["url"].(string); ok {
		requestParams.URL = url
	} else {
		return nil, fmt.Errorf("缺少必需参数: url")
	}

	if method, ok := params["method"].(string); ok {
		requestParams.Method = method
	} else {
		return nil, fmt.Errorf("缺少必需参数: method")
	}

	if headers, ok := params["headers"].(map[string]any); ok {
		requestParams.Headers = make(map[string]string)
		for k, v := range headers {
			if str, ok := v.(string); ok {
				requestParams.Headers[k] = str
			}
		}
	}

	if body, ok := params["body"].(string); ok {
		requestParams.Body = body
	}

	if timeout, ok := params["timeout"]; ok {
		switch v := timeout.(type) {
		case int:
			requestParams.Timeout = v
		case float64:
			requestParams.Timeout = int(v)
		}
	}

	// 验证参数
	if err := h.validateParams(&requestParams); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 设置超时
	requestCtx := ctx
	if requestParams.Timeout > 0 {
		var cancel context.CancelFunc
		requestCtx, cancel = context.WithTimeout(ctx, time.Duration(requestParams.Timeout)*time.Second)
		defer cancel()
	}

	// 执行请求（带重试）
	var responseData *ResponseData
	var lastErr error

	for attempt := 0; attempt <= h.retries; attempt++ {
		responseData, lastErr = h.doRequest(requestCtx, &requestParams)
		if lastErr == nil {
			break
		}

		// 如果是上下文取消，不重试
		if requestCtx.Err() != nil {
			break
		}

		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < h.retries {
			select {
			case <-requestCtx.Done():
				return nil, requestCtx.Err()
			case <-time.After(time.Duration(attempt+1) * time.Second):
				// 指数退避
			}
		}
	}

	if lastErr != nil {
		return map[string]any{
			"success": false,
			"error":   lastErr.Error(),
		}, nil
	}

	// 返回结构化响应
	return map[string]any{
		"success":     responseData.Success,
		"status_code": responseData.StatusCode,
		"headers":     responseData.Headers,
		"body":        responseData.Body,
	}, nil
}

// validateParams 验证请求参数
func (h *HTTPRequestTool) validateParams(params *RequestParams) error {
	if params.URL == "" {
		return fmt.Errorf("URL 不能为空")
	}

	if params.Method == "" {
		return fmt.Errorf("HTTP 方法不能为空")
	}

	// 验证 HTTP 方法
	validMethods := map[string]bool{
		"GET": true, "POST": true, "PUT": true, "DELETE": true,
		"PATCH": true, "HEAD": true, "OPTIONS": true,
	}

	if !validMethods[strings.ToUpper(params.Method)] {
		return fmt.Errorf("不支持的 HTTP 方法: %s", params.Method)
	}

	// 验证超时时间
	if params.Timeout < 0 || params.Timeout > 300 {
		return fmt.Errorf("超时时间必须在 1-300 秒之间")
	}

	return nil
}

// doRequest 执行单次 HTTP 请求
func (h *HTTPRequestTool) doRequest(ctx context.Context, params *RequestParams) (*ResponseData, error) {
	// 创建请求体
	var bodyReader io.Reader
	if params.Body != "" {
		bodyReader = strings.NewReader(params.Body)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, strings.ToUpper(params.Method), params.URL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	if params.Headers != nil {
		for key, value := range params.Headers {
			req.Header.Set(key, value)
		}
	}

	// 如果没有设置 Content-Type 且有请求体，尝试自动设置
	if params.Body != "" && req.Header.Get("Content-Type") == "" {
		if h.isJSON(params.Body) {
			req.Header.Set("Content-Type", "application/json")
		} else {
			req.Header.Set("Content-Type", "text/plain")
		}
	}

	// 设置默认 User-Agent
	if req.Header.Get("User-Agent") == "" {
		req.Header.Set("User-Agent", "AgentScope-HTTPRequestTool/1.0")
	}

	// 发送请求
	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 构建响应头映射
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0] // 只取第一个值
		}
	}

	// 构建响应数据
	response := &ResponseData{
		StatusCode: resp.StatusCode,
		Headers:    headers,
		Body:       string(bodyBytes),
		Success:    resp.StatusCode >= 200 && resp.StatusCode < 300,
	}

	return response, nil
}

// isJSON 检查字符串是否为有效的 JSON
func (h *HTTPRequestTool) isJSON(str string) bool {
	str = strings.TrimSpace(str)
	return (strings.HasPrefix(str, "{") && strings.HasSuffix(str, "}")) ||
		(strings.HasPrefix(str, "[") && strings.HasSuffix(str, "]"))
}

// SetTimeout 设置超时时间
func (h *HTTPRequestTool) SetTimeout(timeout time.Duration) {
	h.timeout = timeout
	h.client.Timeout = timeout
}

// SetRetries 设置重试次数
func (h *HTTPRequestTool) SetRetries(retries int) {
	h.retries = retries
}

// GetClient 获取 HTTP 客户端（用于测试）
func (h *HTTPRequestTool) GetClient() *http.Client {
	return h.client
}

// SetClient 设置 HTTP 客户端（用于测试）
func (h *HTTPRequestTool) SetClient(client *http.Client) {
	h.client = client
}
