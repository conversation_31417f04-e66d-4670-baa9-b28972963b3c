id: "customer_service"
name: "客服专用知识库"
description: "客服人员专用的FAQ和处理流程"
level: "role"
access_level: "role"
roles: ["customer_service", "wechat_customer_service"]
version: "1.0.0"
created_at: "2025-09-10T08:35:00Z"
updated_at: "2025-09-10T08:35:00Z"

items:
  - id: "cs_001"
    question: "忘记密码怎么办？"
    answer: "请按以下步骤重置密码：\n1. 访问登录页面，点击'忘记密码'\n2. 输入您的注册邮箱或手机号\n3. 查收验证码并按提示设置新密码\n如仍有问题，请联系客服协助处理。"
    keywords: ["忘记密码", "重置密码", "找回密码", "密码重置", "登录问题"]
    tags: ["账户问题", "密码"]
    category: "账户管理"
    priority: 9
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

  - id: "cs_002"
    question: "如何退款？"
    answer: "退款流程如下：\n1. 登录账户，进入订单管理\n2. 找到需要退款的订单，点击'申请退款'\n3. 填写退款原因并提交\n4. 客服会在1-2个工作日内处理\n退款将原路返回，到账时间3-7个工作日。"
    keywords: ["退款", "退钱", "申请退款", "退款流程", "订单退款"]
    tags: ["订单管理", "退款"]
    category: "订单处理"
    priority: 8
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

  - id: "cs_003"
    question: "账户被锁定怎么办？"
    answer: "账户锁定处理步骤：\n1. 确认锁定原因（多次错误登录、安全风险等）\n2. 联系客服提供身份验证信息\n3. 客服验证后会在30分钟内解锁\n4. 建议修改密码并开启二次验证\n预防措施：避免在公共网络登录，定期更换密码。"
    keywords: ["账户锁定", "账号锁定", "解锁账户", "登录失败", "账户安全"]
    tags: ["账户问题", "安全"]
    category: "账户管理"
    priority: 8
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

  - id: "cs_004"
    question: "如何处理投诉？"
    answer: "投诉处理标准流程：\n1. 耐心倾听客户问题，表示理解和歉意\n2. 详细记录投诉内容和客户联系方式\n3. 承诺处理时间（一般24小时内回复）\n4. 立即上报主管，启动投诉处理流程\n5. 跟进处理结果，确保客户满意\n注意：保持专业态度，不与客户争辩。"
    keywords: ["投诉处理", "客户投诉", "投诉流程", "处理投诉", "客户不满"]
    tags: ["投诉处理", "流程"]
    category: "投诉管理"
    priority: 10
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

  - id: "cs_005"
    question: "如何处理愤怒客户？"
    answer: "愤怒客户处理技巧：\n1. 保持冷静，不要被情绪感染\n2. 主动倾听，让客户充分表达\n3. 表示理解：'我理解您的感受'\n4. 道歉：'非常抱歉给您带来困扰'\n5. 提供解决方案，询问客户意见\n6. 如无法解决，及时转接主管\n记住：客户的愤怒通常针对问题，不是针对您个人。"
    keywords: ["愤怒客户", "情绪处理", "客户情绪", "安抚客户", "冲突处理"]
    tags: ["情绪处理", "技巧"]
    category: "沟通技巧"
    priority: 9
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

  - id: "cs_006"
    question: "订单状态查询"
    answer: "订单状态查询方法：\n1. 请客户提供订单号或注册手机号\n2. 在系统中查询订单状态\n3. 详细说明当前状态和预计时间\n常见状态：\n- 待支付：等待客户付款\n- 已支付：订单确认，准备发货\n- 已发货：商品在途，提供物流单号\n- 已完成：订单完成\n- 已取消：订单已取消"
    keywords: ["订单查询", "订单状态", "物流查询", "发货状态", "订单跟踪"]
    tags: ["订单管理", "查询"]
    category: "订单处理"
    priority: 8
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

  - id: "cs_007"
    question: "如何升级服务？"
    answer: "服务升级流程：\n1. 了解客户当前服务套餐\n2. 分析客户需求和使用情况\n3. 推荐合适的升级方案\n4. 详细说明升级后的功能和价格\n5. 协助客户完成升级操作\n6. 确认升级成功并提供使用指导\n注意：升级费用按比例计算，立即生效。"
    keywords: ["服务升级", "套餐升级", "功能升级", "升级流程", "增值服务"]
    tags: ["服务管理", "升级"]
    category: "服务管理"
    priority: 7
    level: "role"
    access_level: "role"
    roles: ["customer_service", "wechat_customer_service"]
    version: "1.0.0"
    created_at: "2025-09-10T08:35:00Z"
    updated_at: "2025-09-10T08:35:00Z"

metadata:
  source: "customer_service_manual"
  maintainer: "customer_service_team"
  review_cycle: "monthly"
  last_review: "2025-09-10"
  training_required: true
