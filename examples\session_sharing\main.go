package main

import (
	"context"
	"fmt"
	"log"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/session"
)

// DataCollectorAgent 数据收集智能体
type DataCollectorAgent struct {
	name string
}

func NewDataCollectorAgent(name string) *DataCollectorAgent {
	return &DataCollectorAgent{name: name}
}

func (a *DataCollectorAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *DataCollectorAgent) Description(ctx context.Context) string {
	return "收集和存储数据到共享会话中"
}

func (a *DataCollectorAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 发送开始事件
		pair.Generator.Send(event.NewThoughtEvent("开始收集数据", "数据收集阶段"))

		// 模拟收集数据并存储到Session
		if input.Session != nil {
			// 收集用户信息
			input.Session.Set("user_profile", map[string]any{
				"name":     "张三",
				"age":      30,
				"location": "北京",
				"interests": []string{"技术", "阅读", "旅行"},
			})

			// 收集系统状态
			input.Session.Set("system_status", map[string]any{
				"cpu_usage":    "45%",
				"memory_usage": "60%",
				"disk_space":   "80GB",
			})

			// 收集任务信息
			input.Session.Set("current_task", "多智能体协作演示")
			input.Session.Set("task_priority", "高")
			input.Session.Set("data_collected_by", a.name)
		}

		pair.Generator.Send(event.NewTokenEvent("✓ 用户信息已收集\n", true))
		pair.Generator.Send(event.NewTokenEvent("✓ 系统状态已收集\n", true))
		pair.Generator.Send(event.NewTokenEvent("✓ 任务信息已收集\n", true))

		// 发送完成事件
		pair.Generator.Send(event.NewFinalEvent("数据收集完成", "所有必要数据已存储到共享会话", nil))
	}()

	return pair.Iterator
}

// DataAnalyzerAgent 数据分析智能体
type DataAnalyzerAgent struct {
	name string
}

func NewDataAnalyzerAgent(name string) *DataAnalyzerAgent {
	return &DataAnalyzerAgent{name: name}
}

func (a *DataAnalyzerAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *DataAnalyzerAgent) Description(ctx context.Context) string {
	return "分析共享会话中的数据并生成洞察"
}

func (a *DataAnalyzerAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 发送开始事件
		pair.Generator.Send(event.NewThoughtEvent("开始分析数据", "数据分析阶段"))

		if input.Session != nil {
			// 读取并分析用户信息
			if userProfile, exists := input.Session.Get("user_profile"); exists {
				if profile, ok := userProfile.(map[string]any); ok {
					pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("📊 分析用户: %v\n", profile["name"]), true))
					
					// 生成用户洞察
					insights := []string{
						"用户属于技术导向型",
						"具有持续学习意愿",
						"地理位置适合线下活动",
					}
					input.Session.Set("user_insights", insights)
				}
			}

			// 读取并分析系统状态
			if systemStatus, exists := input.Session.Get("system_status"); exists {
				if status, ok := systemStatus.(map[string]any); ok {
					pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("🖥️ 系统CPU使用率: %v\n", status["cpu_usage"]), true))
					
					// 生成系统建议
					recommendations := []string{
						"CPU使用率正常，可以处理更多任务",
						"内存使用率偏高，建议优化",
						"磁盘空间充足",
					}
					input.Session.Set("system_recommendations", recommendations)
				}
			}

			// 生成综合分析结果
			input.Session.Set("analysis_completed_by", a.name)
			input.Session.Set("analysis_timestamp", "2025-09-09 12:30:00")
		}

		pair.Generator.Send(event.NewTokenEvent("✓ 用户洞察已生成\n", true))
		pair.Generator.Send(event.NewTokenEvent("✓ 系统建议已生成\n", true))

		// 发送完成事件
		pair.Generator.Send(event.NewFinalEvent("数据分析完成", "分析结果已存储到共享会话", nil))
	}()

	return pair.Iterator
}

// ReportGeneratorAgent 报告生成智能体
type ReportGeneratorAgent struct {
	name string
}

func NewReportGeneratorAgent(name string) *ReportGeneratorAgent {
	return &ReportGeneratorAgent{name: name}
}

func (a *ReportGeneratorAgent) Name(ctx context.Context) string {
	return a.name
}

func (a *ReportGeneratorAgent) Description(ctx context.Context) string {
	return "基于共享会话数据生成综合报告"
}

func (a *ReportGeneratorAgent) Run(ctx context.Context, input *runtime.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 发送开始事件
		pair.Generator.Send(event.NewThoughtEvent("开始生成报告", "报告生成阶段"))

		if input.Session != nil {
			// 读取所有数据并生成报告
			pair.Generator.Send(event.NewTokenEvent("📋 生成综合报告...\n\n", true))

			// 用户信息部分
			if userProfile, exists := input.Session.Get("user_profile"); exists {
				if profile, ok := userProfile.(map[string]any); ok {
					pair.Generator.Send(event.NewTokenEvent("=== 用户信息 ===\n", true))
					pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("姓名: %v\n", profile["name"]), true))
					pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("年龄: %v\n", profile["age"]), true))
					pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("位置: %v\n", profile["location"]), true))
					pair.Generator.Send(event.NewTokenEvent("\n", true))
				}
			}

			// 用户洞察部分
			if insights, exists := input.Session.Get("user_insights"); exists {
				if insightList, ok := insights.([]string); ok {
					pair.Generator.Send(event.NewTokenEvent("=== 用户洞察 ===\n", true))
					for _, insight := range insightList {
						pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("• %s\n", insight), true))
					}
					pair.Generator.Send(event.NewTokenEvent("\n", true))
				}
			}

			// 系统建议部分
			if recommendations, exists := input.Session.Get("system_recommendations"); exists {
				if recList, ok := recommendations.([]string); ok {
					pair.Generator.Send(event.NewTokenEvent("=== 系统建议 ===\n", true))
					for _, rec := range recList {
						pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("• %s\n", rec), true))
					}
					pair.Generator.Send(event.NewTokenEvent("\n", true))
				}
			}

			// 生成报告元数据
			reportMetadata := map[string]any{
				"generated_by":    a.name,
				"generation_time": "2025-09-09 12:30:00",
				"data_sources":    []string{"DataCollector", "DataAnalyzer"},
				"report_version":  "1.0",
			}
			input.Session.Set("report_metadata", reportMetadata)

			pair.Generator.Send(event.NewTokenEvent("=== 报告元数据 ===\n", true))
			pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("生成者: %v\n", reportMetadata["generated_by"]), true))
			pair.Generator.Send(event.NewTokenEvent(fmt.Sprintf("生成时间: %v\n", reportMetadata["generation_time"]), true))
		}

		// 发送完成事件
		pair.Generator.Send(event.NewFinalEvent("报告生成完成", "综合报告已生成并存储", nil))
	}()

	return pair.Iterator
}

func main() {
	ctx := context.Background()

	// 创建Runner
	config := runtime.DefaultRunnerConfig()
	runner := runtime.NewRunner(ctx, config)

	fmt.Println("=== 多智能体Session共享演示 ===")
	fmt.Println("场景: 数据收集 → 数据分析 → 报告生成")
	fmt.Println()

	// 创建共享Session
	sharedSession := session.New()
	sharedSession.Set("workflow_id", "demo_workflow_001")
	sharedSession.Set("start_time", "2025-09-09 12:30:00")

	// 阶段1: 数据收集
	fmt.Println("阶段1: 数据收集")
	fmt.Println("================")

	collector := NewDataCollectorAgent("DataCollector")
	input1 := runtime.NewInput()
	input1.AddMessage(message.NewUserMessage("请收集用户和系统数据"))
	input1.SetSession(sharedSession)

	events1, err := runner.RunAndCollect(ctx, collector, input1)
	if err != nil {
		log.Fatalf("数据收集失败: %v", err)
	}

	// 显示收集阶段的输出
	for _, ev := range events1 {
		if ev.Type == event.EventToken {
			if data, ok := ev.Data.(*event.TokenData); ok {
				fmt.Print(data.Content)
			}
		}
	}
	fmt.Println()

	// 阶段2: 数据分析
	fmt.Println("阶段2: 数据分析")
	fmt.Println("================")

	analyzer := NewDataAnalyzerAgent("DataAnalyzer")
	input2 := runtime.NewInput()
	input2.AddMessage(message.NewUserMessage("请分析收集的数据"))
	input2.SetSession(sharedSession) // 使用相同的Session

	events2, err := runner.RunAndCollect(ctx, analyzer, input2)
	if err != nil {
		log.Fatalf("数据分析失败: %v", err)
	}

	// 显示分析阶段的输出
	for _, ev := range events2 {
		if ev.Type == event.EventToken {
			if data, ok := ev.Data.(*event.TokenData); ok {
				fmt.Print(data.Content)
			}
		}
	}
	fmt.Println()

	// 阶段3: 报告生成
	fmt.Println("阶段3: 报告生成")
	fmt.Println("================")

	generator := NewReportGeneratorAgent("ReportGenerator")
	input3 := runtime.NewInput()
	input3.AddMessage(message.NewUserMessage("请生成综合报告"))
	input3.SetSession(sharedSession) // 使用相同的Session

	events3, err := runner.RunAndCollect(ctx, generator, input3)
	if err != nil {
		log.Fatalf("报告生成失败: %v", err)
	}

	// 显示报告生成阶段的输出
	for _, ev := range events3 {
		if ev.Type == event.EventToken {
			if data, ok := ev.Data.(*event.TokenData); ok {
				fmt.Print(data.Content)
			}
		}
	}

	// 显示最终的Session状态
	fmt.Println("\n=== 最终Session状态 ===")
	fmt.Printf("工作流ID: %v\n", getSessionValue(sharedSession, "workflow_id"))
	fmt.Printf("开始时间: %v\n", getSessionValue(sharedSession, "start_time"))
	fmt.Printf("数据收集者: %v\n", getSessionValue(sharedSession, "data_collected_by"))
	fmt.Printf("数据分析者: %v\n", getSessionValue(sharedSession, "analysis_completed_by"))
	
	if metadata, exists := sharedSession.Get("report_metadata"); exists {
		if meta, ok := metadata.(map[string]any); ok {
			fmt.Printf("报告生成者: %v\n", meta["generated_by"])
			fmt.Printf("报告版本: %v\n", meta["report_version"])
		}
	}

	fmt.Printf("\nSession中共有 %d 个键值对\n", sharedSession.Size())
	fmt.Println("\n=== 演示完成 ===")
}

// getSessionValue 安全地获取Session值
func getSessionValue(session session.Map, key string) any {
	if value, exists := session.Get(key); exists {
		return value
	}
	return "未设置"
}
