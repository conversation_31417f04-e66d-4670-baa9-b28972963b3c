package runtime

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"
)

func TestNewAsyncIterator(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 10)

	if pair.Generator == nil {
		t.<PERSON><PERSON>r("生成器不应该为 nil")
	}

	if pair.Iterator == nil {
		t.<PERSON><PERSON>("迭代器不应该为 nil")
	}

	if pair.Generator.IsClosed() {
		t.<PERSON><PERSON><PERSON>("新创建的生成器不应该是关闭状态")
	}

	if pair.Iterator.IsClosed() {
		t.<PERSON><PERSON>("新创建的迭代器不应该是关闭状态")
	}
}

func TestBasicSendAndReceive(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 5)

	// 发送事件
	err := pair.Generator.Send("event1")
	if err != nil {
		t.<PERSON>rrorf("发送事件失败: %v", err)
	}

	err = pair.Generator.Send("event2")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("发送事件失败: %v", err)
	}

	// 关闭生成器
	pair.Generator.Close()

	// 接收事件
	value1, ok1 := pair.Iterator.Next()
	if !ok1 {
		t.Error("应该能接收到第一个事件")
	}
	if value1 != "event1" {
		t.Errorf("第一个事件不匹配: got %s, want %s", value1, "event1")
	}

	value2, ok2 := pair.Iterator.Next()
	if !ok2 {
		t.Error("应该能接收到第二个事件")
	}
	if value2 != "event2" {
		t.Errorf("第二个事件不匹配: got %s, want %s", value2, "event2")
	}

	// 应该没有更多事件
	_, ok3 := pair.Iterator.Next()
	if ok3 {
		t.Error("不应该有更多事件")
	}
}

func TestTrySend(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[int](ctx, 2) // 小缓冲区

	// 填满缓冲区
	success1 := pair.Generator.TrySend(1)
	if !success1 {
		t.Error("第一次 TrySend 应该成功")
	}

	success2 := pair.Generator.TrySend(2)
	if !success2 {
		t.Error("第二次 TrySend 应该成功")
	}

	// 缓冲区满了，应该失败
	success3 := pair.Generator.TrySend(3)
	if success3 {
		t.Error("缓冲区满时 TrySend 应该失败")
	}

	// 消费一个事件
	value, ok := pair.Iterator.Next()
	if !ok || value != 1 {
		t.Error("应该能消费第一个事件")
	}

	// 现在应该能再发送一个
	success4 := pair.Generator.TrySend(4)
	if !success4 {
		t.Error("消费后 TrySend 应该成功")
	}
}

func TestContextCancellation(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	pair := NewAsyncIterator[string](ctx, 1) // 使用小缓冲区

	// 填满缓冲区
	err := pair.Generator.Send("event1")
	if err != nil {
		t.Errorf("发送事件失败: %v", err)
	}

	// 取消上下文
	cancel()

	// 尝试发送另一个事件，应该失败（因为缓冲区满且上下文取消）
	err = pair.Generator.Send("event2")
	if err == nil {
		t.Error("上下文取消后发送应该失败")
	}

	// 测试通过，上下文取消功能正常工作
}

func TestGeneratorClose(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 5)

	// 发送事件
	err := pair.Generator.Send("event1")
	if err != nil {
		t.Errorf("发送事件失败: %v", err)
	}

	// 关闭生成器
	pair.Generator.Close()

	if !pair.Generator.IsClosed() {
		t.Error("生成器应该是关闭状态")
	}

	// 尝试再次发送应该失败
	err = pair.Generator.Send("event2")
	if err == nil {
		t.Error("关闭后发送应该失败")
	}

	// 应该能接收到关闭前发送的事件
	value, ok := pair.Iterator.Next()
	if !ok || value != "event1" {
		t.Error("应该能接收到关闭前发送的事件")
	}

	// 下一次接收应该失败
	_, ok = pair.Iterator.Next()
	if ok {
		t.Error("生成器关闭后不应该有更多事件")
	}
}

func TestIteratorClose(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 5)

	// 关闭迭代器
	pair.Iterator.Close()

	if !pair.Iterator.IsClosed() {
		t.Error("迭代器应该是关闭状态")
	}

	// 尝试接收应该失败
	_, ok := pair.Iterator.Next()
	if ok {
		t.Error("关闭后接收应该失败")
	}
}

func TestConcurrentSendAndReceive(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[int](ctx, 100)

	const numSenders = 5
	const numMessages = 100

	var wg sync.WaitGroup

	// 启动多个发送者
	for i := 0; i < numSenders; i++ {
		wg.Add(1)
		go func(senderID int) {
			defer wg.Done()
			for j := 0; j < numMessages; j++ {
				value := senderID*numMessages + j
				err := pair.Generator.Send(value)
				if err != nil {
					t.Errorf("发送失败: %v", err)
					return
				}
			}
		}(i)
	}

	// 等待所有发送完成
	go func() {
		wg.Wait()
		pair.Generator.Close()
	}()

	// 接收所有事件
	received := make(map[int]bool)
	for value, ok := pair.Iterator.Next(); ok; value, ok = pair.Iterator.Next() {
		if received[value] {
			t.Errorf("重复接收到值: %d", value)
		}
		received[value] = true
	}

	// 验证接收到的事件数量
	expectedCount := numSenders * numMessages
	if len(received) != expectedCount {
		t.Errorf("接收到的事件数量不匹配: got %d, want %d", len(received), expectedCount)
	}
}

func TestSendWithRecovery(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 5)

	// 正常发送
	err := pair.Generator.SendWithRecovery("normal")
	if err != nil {
		t.Errorf("正常发送不应该失败: %v", err)
	}

	// 关闭生成器后发送（会导致 panic，但应该被捕获）
	pair.Generator.Close()
	err = pair.Generator.SendWithRecovery("after_close")
	if err == nil {
		t.Error("关闭后发送应该返回错误")
	}
}

func TestCollect(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 5)

	// 发送多个事件
	events := []string{"event1", "event2", "event3"}
	for _, event := range events {
		err := pair.Generator.Send(event)
		if err != nil {
			t.Errorf("发送事件失败: %v", err)
		}
	}
	pair.Generator.Close()

	// 收集所有事件
	collected := pair.Iterator.Collect()

	if len(collected) != len(events) {
		t.Errorf("收集的事件数量不匹配: got %d, want %d", len(collected), len(events))
	}

	for i, event := range events {
		if collected[i] != event {
			t.Errorf("事件 %d 不匹配: got %s, want %s", i, collected[i], event)
		}
	}
}

func TestCollectWithTimeout(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[string](ctx, 5)

	// 发送一些事件
	go func() {
		pair.Generator.Send("event1")
		pair.Generator.Send("event2")
		// 不立即关闭，让超时生效
		time.Sleep(200 * time.Millisecond)
		pair.Generator.Send("event3")
		pair.Generator.Close()
	}()

	// 设置较短的超时时间
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	collected := pair.Iterator.CollectWithTimeout(timeoutCtx)

	// 应该只收集到前两个事件（第三个事件在超时后才发送）
	if len(collected) != 2 {
		t.Errorf("超时收集的事件数量不匹配: got %d, want %d", len(collected), 2)
	}
}

func TestForEach(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[int](ctx, 5)

	// 发送事件
	for i := 1; i <= 5; i++ {
		pair.Generator.Send(i)
	}
	pair.Generator.Close()

	// 使用 ForEach 处理事件
	var sum int
	var count int
	pair.Iterator.ForEach(func(value int) bool {
		sum += value
		count++
		return value < 3 // 只处理前3个
	})

	if count != 3 {
		t.Errorf("处理的事件数量不匹配: got %d, want %d", count, 3)
	}

	expectedSum := 1 + 2 + 3
	if sum != expectedSum {
		t.Errorf("事件和不匹配: got %d, want %d", sum, expectedSum)
	}
}

func TestFilter(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[int](ctx, 10)

	// 发送事件
	for i := 1; i <= 10; i++ {
		pair.Generator.Send(i)
	}
	pair.Generator.Close()

	// 过滤偶数
	filtered := pair.Iterator.Filter(ctx, func(value int) bool {
		return value%2 == 0
	}, 5)

	// 收集过滤后的结果
	results := filtered.Collect()

	expected := []int{2, 4, 6, 8, 10}
	if len(results) != len(expected) {
		t.Errorf("过滤结果数量不匹配: got %d, want %d", len(results), len(expected))
	}

	for i, expected := range expected {
		if results[i] != expected {
			t.Errorf("过滤结果 %d 不匹配: got %d, want %d", i, results[i], expected)
		}
	}
}

func TestMap(t *testing.T) {
	ctx := context.Background()
	pair := NewAsyncIterator[int](ctx, 5)

	// 发送事件
	for i := 1; i <= 5; i++ {
		pair.Generator.Send(i)
	}
	pair.Generator.Close()

	// 映射为字符串
	mapped := Map(pair.Iterator, ctx, func(value int) string {
		return fmt.Sprintf("value_%d", value)
	}, 5)

	// 收集映射后的结果
	results := mapped.Collect()

	if len(results) != 5 {
		t.Errorf("映射结果数量不匹配: got %d, want %d", len(results), 5)
	}

	for i := 0; i < 5; i++ {
		expected := fmt.Sprintf("value_%d", i+1)
		if results[i] != expected {
			t.Errorf("映射结果 %d 不匹配: got %s, want %s", i, results[i], expected)
		}
	}
}

func TestMerge(t *testing.T) {
	ctx := context.Background()

	// 创建多个迭代器
	var iterators []*AsyncIterator[int]
	for i := 0; i < 3; i++ {
		pair := NewAsyncIterator[int](ctx, 5)

		// 每个迭代器发送不同的事件
		go func(id int, gen *Generator[int]) {
			for j := 0; j < 3; j++ {
				gen.Send(id*10 + j)
			}
			gen.Close()
		}(i, pair.Generator)

		iterators = append(iterators, pair.Iterator)
	}

	// 合并所有迭代器
	merged := Merge(ctx, iterators, 15)

	// 收集合并后的结果
	results := merged.Collect()

	// 应该收到所有事件
	if len(results) != 9 {
		t.Errorf("合并结果数量不匹配: got %d, want %d", len(results), 9)
	}

	// 验证所有预期的值都存在
	expected := make(map[int]bool)
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			expected[i*10+j] = true
		}
	}

	for _, result := range results {
		if !expected[result] {
			t.Errorf("意外的合并结果: %d", result)
		}
		delete(expected, result)
	}

	if len(expected) > 0 {
		t.Errorf("缺少预期的合并结果: %v", expected)
	}
}
