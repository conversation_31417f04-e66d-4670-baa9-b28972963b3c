package builtin

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// FileTool 文件操作工具
type FileTool struct {
	allowedPaths []string
}

// NewFileTool 创建文件工具
func NewFileTool(allowedPaths []string) *FileTool {
	if allowedPaths == nil {
		allowedPaths = []string{"./"}
	}
	return &FileTool{
		allowedPaths: allowedPaths,
	}
}

// Name 返回工具名称
func (f *FileTool) Name() string {
	return "file"
}

// Description 返回工具描述
func (f *FileTool) Description() string {
	return "File operations: read, write, list directory contents"
}

// Schema 返回工具参数模式
func (f *FileTool) Schema() *tool.JSONSchema {
	return &tool.JSONSchema{
		Type: "object",
		Properties: map[string]*tool.JSONSchema{
			"operation": {
				Type:        "string",
				Description: "File operation to perform",
				Enum:        []any{"read", "write", "list", "exists", "stat"},
			},
			"path": {
				Type:        "string",
				Description: "File or directory path",
			},
			"content": {
				Type:        "string",
				Description: "Content to write (for write operation)",
			},
		},
		Required:    []string{"operation", "path"},
		Description: "Perform file operations with security restrictions",
	}
}

// Execute 执行文件操作
func (f *FileTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	operation, ok := params["operation"].(string)
	if !ok {
		return nil, fmt.Errorf("operation parameter must be a string")
	}

	path, ok := params["path"].(string)
	if !ok {
		return nil, fmt.Errorf("path parameter must be a string")
	}

	// 安全检查
	if !f.isPathAllowed(path) {
		return nil, fmt.Errorf("path not allowed for security reasons")
	}

	var result interface{}
	var err error

	switch operation {
	case "read":
		result, err = f.readFile(path)
	case "write":
		content, _ := params["content"].(string)
		result, err = f.writeFile(path, content)
	case "list":
		result, err = f.listDirectory(path)
	case "exists":
		result, err = f.fileExists(path)
	case "stat":
		result, err = f.fileStat(path)
	default:
		err = fmt.Errorf("unsupported operation: %s", operation)
	}

	if err != nil {
		return nil, err
	}

	return map[string]any{
		"operation": operation,
		"path":      path,
		"result":    result,
	}, nil
}

// isPathAllowed 检查路径是否被允许
func (f *FileTool) isPathAllowed(path string) bool {
	absPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}

	for _, allowedPath := range f.allowedPaths {
		allowedAbs, err := filepath.Abs(allowedPath)
		if err != nil {
			continue
		}

		if strings.HasPrefix(absPath, allowedAbs) {
			return true
		}
	}

	return false
}

// readFile 读取文件
func (f *FileTool) readFile(path string) (string, error) {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

// writeFile 写入文件
func (f *FileTool) writeFile(path, content string) (string, error) {
	err := ioutil.WriteFile(path, []byte(content), 0644)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("Successfully wrote %d bytes to %s", len(content), path), nil
}

// listDirectory 列出目录内容
func (f *FileTool) listDirectory(path string) ([]map[string]interface{}, error) {
	entries, err := ioutil.ReadDir(path)
	if err != nil {
		return nil, err
	}

	var result []map[string]interface{}
	for _, entry := range entries {
		result = append(result, map[string]interface{}{
			"name":     entry.Name(),
			"is_dir":   entry.IsDir(),
			"size":     entry.Size(),
			"mod_time": entry.ModTime(),
		})
	}

	return result, nil
}

// fileExists 检查文件是否存在
func (f *FileTool) fileExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// fileStat 获取文件信息
func (f *FileTool) fileStat(path string) (map[string]interface{}, error) {
	info, err := os.Stat(path)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"name":     info.Name(),
		"size":     info.Size(),
		"mode":     info.Mode().String(),
		"mod_time": info.ModTime(),
		"is_dir":   info.IsDir(),
	}, nil
}
