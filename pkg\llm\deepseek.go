package llm

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// Import error codes for convenience
const (
	CodeAPIKeyMissing      = errors.CodeAPIKeyMissing
	CodeInvalidInput       = errors.CodeInvalidInput
	CodeLLMRequestFailed   = errors.CodeLLMRequestFailed
	CodeLLMResponseInvalid = errors.CodeLLMResponseInvalid
	CodeInvalidCredentials = errors.CodeInvalidCredentials
	CodeLLMQuotaExceeded   = errors.CodeLLMQuotaExceeded
	CodeConnectionFailed   = errors.CodeConnectionFailed
	CodeResourceExhausted  = errors.CodeResourceExhausted
	CodeRequestTimeout     = errors.CodeRequestTimeout
	CodeServiceUnavailable = errors.CodeServiceUnavailable
	CodeRequestFailed      = errors.CodeRequestFailed
)

// DeepseekClient implements the LLMClient interface for Deepseek API
type DeepseekClient struct {
	config     *Config
	httpClient *http.Client
	logger     logger.Logger
	modelInfo  *ModelInfo
}

// NewDeepseekClient creates a new Deepseek client
func NewDeepseekClient(config *Config) (*DeepseekClient, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// Set defaults for Deepseek
	if config.Provider == "" {
		config.Provider = "deepseek"
	}
	if config.BaseURL == "" {
		config.BaseURL = "https://api.deepseek.com/v1"
	}
	if config.Model == "" {
		config.Model = "deepseek-chat"
	}

	// Get API key from config or environment
	apiKey := config.APIKey
	if apiKey == "" {
		apiKey = os.Getenv("DEEPSEEK_API_KEY")
	}
	if apiKey == "" {
		return nil, errors.NewAuthError(CodeAPIKeyMissing,
			"Deepseek API key is required (set DEEPSEEK_API_KEY environment variable)")
	}
	config.APIKey = apiKey

	if err := config.Validate(); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, CodeInvalidInput,
			"invalid Deepseek client configuration")
	}

	client := &DeepseekClient{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		logger: logger.GetGlobalLogger(),
		modelInfo: &ModelInfo{
			ID:           config.Model,
			Object:       "model",
			Created:      time.Now().Unix(),
			OwnedBy:      "deepseek",
			Description:  "Deepseek Chat Model",
			Capabilities: []string{"chat", "completion"},
		},
	}

	client.logger.WithField("component", "deepseek_client").
		WithField("model", config.Model).
		WithField("base_url", config.BaseURL).
		Info("Deepseek client initialized")

	return client, nil
}

// Generate generates a response for the given request
func (dc *DeepseekClient) Generate(ctx context.Context, request *GenerateRequest) (*GenerateResponse, error) {
	if request == nil {
		return nil, errors.NewValidationError("INVALID_INPUT", "request cannot be nil")
	}

	// Prepare the request
	apiRequest := dc.prepareAPIRequest(request)
	apiRequest.Stream = false

	// Make the API call
	response, err := dc.makeAPICall(ctx, apiRequest)
	if err != nil {
		return nil, err
	}

	dc.logger.WithField("component", "deepseek_client").
		WithField("model", apiRequest.Model).
		WithField("messages_count", len(apiRequest.Messages)).
		Debug("Generated response successfully")

	return response, nil
}

// GenerateStream generates a streaming response for the given request
func (dc *DeepseekClient) GenerateStream(ctx context.Context, request *GenerateRequest) (<-chan *StreamResponse, error) {
	if request == nil {
		return nil, errors.NewValidationError("INVALID_INPUT", "request cannot be nil")
	}

	// Prepare the request
	apiRequest := dc.prepareAPIRequest(request)
	apiRequest.Stream = true

	// Create response channel
	responseChan := make(chan *StreamResponse, 10)

	// Start streaming in a goroutine
	go func() {
		defer close(responseChan)

		if err := dc.makeStreamingAPICall(ctx, apiRequest, responseChan); err != nil {
			// Send error response
			responseChan <- &StreamResponse{
				Error: &ErrorInfo{
					Message: err.Error(),
					Type:    "stream_error",
				},
				Done: true,
			}
		}
	}()

	return responseChan, nil
}

// GetModelInfo returns information about the model
func (dc *DeepseekClient) GetModelInfo() *ModelInfo {
	return dc.modelInfo
}

// Close closes the client and releases resources
func (dc *DeepseekClient) Close() error {
	dc.logger.WithField("component", "deepseek_client").Info("Deepseek client closed")
	return nil
}

// prepareAPIRequest prepares the API request from the generate request
func (dc *DeepseekClient) prepareAPIRequest(request *GenerateRequest) *GenerateRequest {
	apiRequest := &GenerateRequest{
		Messages: request.Messages,
		Model:    dc.config.Model,
		Stream:   request.Stream,
	}

	// Override model if specified in request
	if request.Model != "" {
		apiRequest.Model = request.Model
	}

	// Set parameters from config defaults
	if temp, exists := dc.config.Parameters["temperature"]; exists {
		if tempFloat, ok := temp.(float64); ok {
			apiRequest.Temperature = &tempFloat
		}
	}
	if maxTokens, exists := dc.config.Parameters["max_tokens"]; exists {
		if maxTokensInt, ok := maxTokens.(int); ok {
			apiRequest.MaxTokens = &maxTokensInt
		} else if maxTokensFloat, ok := maxTokens.(float64); ok {
			maxTokensInt := int(maxTokensFloat)
			apiRequest.MaxTokens = &maxTokensInt
		}
	}

	// Override with request parameters
	if request.Temperature != nil {
		apiRequest.Temperature = request.Temperature
	}
	if request.MaxTokens != nil {
		apiRequest.MaxTokens = request.MaxTokens
	}
	if request.TopP != nil {
		apiRequest.TopP = request.TopP
	}
	if request.TopK != nil {
		apiRequest.TopK = request.TopK
	}
	if request.Stop != nil {
		apiRequest.Stop = request.Stop
	}
	if request.Tools != nil {
		apiRequest.Tools = request.Tools
	}
	if request.ToolChoice != nil {
		apiRequest.ToolChoice = request.ToolChoice
	}

	return apiRequest
}

// makeAPICall makes a non-streaming API call
func (dc *DeepseekClient) makeAPICall(ctx context.Context, request *GenerateRequest) (*GenerateResponse, error) {
	// Serialize request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"failed to serialize request")
	}

	// Create HTTP request
	url := dc.config.BaseURL + "/chat/completions"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"failed to create HTTP request")
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+dc.config.APIKey)
	httpReq.Header.Set("User-Agent", "AgentScope-Golang/0.1.0")

	// Make the request
	httpResp, err := dc.httpClient.Do(httpReq)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeNetwork, "REQUEST_FAILED", "HTTP request failed")
	}
	defer httpResp.Body.Close()

	// Read response body
	responseBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMResponseInvalid,
			"failed to read response body")
	}

	// Check for HTTP errors
	if httpResp.StatusCode != http.StatusOK {
		return nil, dc.handleHTTPError(httpResp.StatusCode, responseBody)
	}

	// Parse response
	var response GenerateResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMResponseInvalid,
			"failed to parse response JSON")
	}

	// Check for API errors
	if response.Error != nil {
		return nil, errors.NewLLMError(CodeLLMRequestFailed,
			response.Error.Message)
	}

	return &response, nil
}

// makeStreamingAPICall makes a streaming API call
func (dc *DeepseekClient) makeStreamingAPICall(ctx context.Context, request *GenerateRequest, responseChan chan<- *StreamResponse) error {
	// Serialize request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"failed to serialize request")
	}

	// Create HTTP request
	url := dc.config.BaseURL + "/chat/completions"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"failed to create HTTP request")
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+dc.config.APIKey)
	httpReq.Header.Set("User-Agent", "AgentScope-Golang/0.1.0")
	httpReq.Header.Set("Accept", "text/event-stream")

	// Make the request
	httpResp, err := dc.httpClient.Do(httpReq)
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeNetwork, "REQUEST_FAILED", "HTTP request failed")
	}
	defer httpResp.Body.Close()

	// Check for HTTP errors
	if httpResp.StatusCode != http.StatusOK {
		responseBody, _ := io.ReadAll(httpResp.Body)
		return dc.handleHTTPError(httpResp.StatusCode, responseBody)
	}

	// Process streaming response
	scanner := bufio.NewScanner(httpResp.Body)
	for scanner.Scan() {
		line := scanner.Text()

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, ":") {
			continue
		}

		// Parse SSE data
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			// Check for end of stream
			if data == "[DONE]" {
				responseChan <- &StreamResponse{Done: true}
				break
			}

			// Parse JSON response
			var streamResp StreamResponse
			if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
				dc.logger.WithField("component", "deepseek_client").
					WithError(err).
					WithField("data", data).
					Warn("Failed to parse streaming response")
				continue
			}

			// Send response to channel
			select {
			case responseChan <- &streamResp:
			case <-ctx.Done():
				return ctx.Err()
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMResponseInvalid,
			"failed to read streaming response")
	}

	return nil
}

// handleHTTPError handles HTTP error responses
func (dc *DeepseekClient) handleHTTPError(statusCode int, responseBody []byte) error {
	var errorResp struct {
		Error *ErrorInfo `json:"error"`
	}

	// Try to parse error response
	if err := json.Unmarshal(responseBody, &errorResp); err == nil && errorResp.Error != nil {
		switch statusCode {
		case http.StatusUnauthorized:
			return errors.NewAuthError(CodeInvalidCredentials,
				errorResp.Error.Message)
		case http.StatusTooManyRequests:
			return errors.NewLLMError(CodeLLMQuotaExceeded,
				errorResp.Error.Message)
		case http.StatusBadRequest:
			return errors.NewValidationError("INVALID_INPUT", errorResp.Error.Message)
		default:
			return errors.NewLLMError(CodeLLMRequestFailed,
				errorResp.Error.Message)
		}
	}

	// Fallback error message
	return errors.NewLLMError(CodeLLMRequestFailed,
		fmt.Sprintf("HTTP %d: %s", statusCode, string(responseBody)))
}

// GetConfig returns the client configuration
func (dc *DeepseekClient) GetConfig() *Config {
	return dc.config.Clone()
}

// UpdateConfig updates the client configuration
func (dc *DeepseekClient) UpdateConfig(config *Config) error {
	if config == nil {
		return errors.NewValidationError(CodeInvalidInput, "config cannot be nil")
	}

	if err := config.Validate(); err != nil {
		return errors.Wrap(err, errors.ErrorTypeValidation, CodeInvalidInput,
			"invalid configuration")
	}

	dc.config = config.Clone()
	dc.httpClient.Timeout = config.Timeout

	// Update model info
	dc.modelInfo.ID = config.Model

	dc.logger.WithField("component", "deepseek_client").
		WithField("model", config.Model).
		Info("Client configuration updated")

	return nil
}

// TestConnection tests the connection to the Deepseek API
func (dc *DeepseekClient) TestConnection(ctx context.Context) error {
	// Create a simple test request
	request := NewGenerateRequest([]*ChatMessage{
		NewUserMessage("Hello"),
	})
	request.SetMaxTokens(1)

	// Make the request
	_, err := dc.Generate(ctx, request)
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeNetwork, CodeConnectionFailed,
			"connection test failed")
	}

	dc.logger.WithField("component", "deepseek_client").Info("Connection test successful")
	return nil
}

// GenerateMultiModal generates a response for multi-modal input
// 注意：DeepSeek API 当前仅支持文本内容，不支持图像、音频、视频或文件输入
// 此实现遵循 OpenAI 兼容格式，但会对不支持的内容类型返回明确错误
func (dc *DeepseekClient) GenerateMultiModal(ctx context.Context, request *MultiModalGenerateRequest) (*GenerateResponse, error) {
	// 验证输入内容类型，DeepSeek 当前仅支持纯文本
	for i, msg := range request.Messages {
		for j, content := range msg.Content {
			switch content.Type {
			case "text":
				// 文本内容支持，继续处理
				continue
			case "image_url":
				return nil, errors.Wrap(
					fmt.Errorf("DeepSeek API 当前不支持图像输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"DeepSeek 多模态限制：仅支持文本内容",
				)
			case "audio_url":
				return nil, errors.Wrap(
					fmt.Errorf("DeepSeek API 当前不支持音频输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"DeepSeek 多模态限制：仅支持文本内容",
				)
			case "video_url":
				return nil, errors.Wrap(
					fmt.Errorf("DeepSeek API 当前不支持视频输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"DeepSeek 多模态限制：仅支持文本内容",
				)
			case "file_url":
				return nil, errors.Wrap(
					fmt.Errorf("DeepSeek API 当前不支持文件输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"DeepSeek 多模态限制：仅支持文本内容",
				)
			default:
				return nil, errors.Wrap(
					fmt.Errorf("未知的内容类型: %s，消息 %d 内容 %d", content.Type, i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"DeepSeek 多模态限制：仅支持文本内容",
				)
			}
		}
	}

	// 构建纯文本消息格式（DeepSeek 当前仅支持文本）
	messages := make([]*ChatMessage, len(request.Messages))
	for i, msg := range request.Messages {
		// 提取所有文本内容并合并
		var textParts []string
		for _, content := range msg.Content {
			if content.Type == "text" && content.Text != "" {
				textParts = append(textParts, content.Text)
			}
		}

		if len(textParts) == 0 {
			return nil, errors.Wrap(
				fmt.Errorf("消息 %d 不包含任何文本内容", i),
				errors.ErrorTypeLLM,
				CodeLLMRequestFailed,
				"多模态消息必须包含至少一个文本内容",
			)
		}

		// 合并文本内容
		combinedText := ""
		if len(textParts) == 1 {
			combinedText = textParts[0]
		} else {
			combinedText = fmt.Sprintf("%s", textParts[0])
			for _, part := range textParts[1:] {
				combinedText += "\n" + part
			}
		}

		messages[i] = &ChatMessage{
			Role:    msg.Role,
			Content: combinedText,
		}
	}

	// Create standard generate request
	genRequest := &GenerateRequest{
		Messages:    messages,
		Model:       request.Model,
		Temperature: request.Temperature,
		MaxTokens:   request.MaxTokens,
		TopP:        request.TopP,
		TopK:        request.TopK,
		Stop:        request.Stop,
		Stream:      request.Stream,
		Parameters:  request.Parameters,
	}

	// Use the standard Generate method
	return dc.Generate(ctx, genRequest)
}

// GenerateWithTools generates a response with tool calling support
func (dc *DeepseekClient) GenerateWithTools(ctx context.Context, request *GenerateRequest, tools []ToolDefinition) (*GenerateResponse, error) {
	// Add tools to the request
	requestWithTools := *request
	requestWithTools.Tools = make([]*Tool, len(tools))

	for i, toolDef := range tools {
		requestWithTools.Tools[i] = &Tool{
			Type: toolDef.Type,
			Function: &ToolFunction{
				Name:        toolDef.Function.Name,
				Description: toolDef.Function.Description,
				Parameters:  toolDef.Function.Parameters,
			},
		}
	}

	// Use the standard Generate method
	return dc.Generate(ctx, &requestWithTools)
}
