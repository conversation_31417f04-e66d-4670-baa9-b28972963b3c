package audit

import (
	"context"
	"sync"
	"time"
)

// AuditReader 审计查询器
type AuditReader struct {
	store      Store
	sanitizer  *Sanitizer
	mu         sync.RWMutex
	closed     bool
}

// NewAuditReader 创建审计查询器
func NewAuditReader(store Store, sanitizer *Sanitizer) *AuditReader {
	return &AuditReader{
		store:     store,
		sanitizer: sanitizer,
	}
}

// QueryMessages 查询消息记录
func (r *AuditReader) QueryMessages(ctx context.Context, q Query) (*QueryResult, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if r.closed {
		return nil, NewStoreOperationError("查询器已关闭", nil)
	}
	
	// 执行查询
	result, err := r.store.QueryMessages(ctx, q)
	if err != nil {
		return nil, err
	}
	
	// 如果有加密，尝试解密内容
	if r.sanitizer != nil {
		for _, record := range result.Records {
			if record.Content != "" {
				decrypted, err := r.sanitizer.UnsanitizeContent(record.Content)
				if err == nil {
					record.Content = decrypted
				}
				// 如果解密失败，保持原始内容
			}
		}
	}
	
	return result, nil
}

// QueryMessagesBySession 按会话查询消息
func (r *AuditReader) QueryMessagesBySession(ctx context.Context, sessionID string, limit, offset int) (*QueryResult, error) {
	query := Query{
		SessionID: &sessionID,
		Limit:     limit,
		Offset:    offset,
	}
	return r.QueryMessages(ctx, query)
}

// QueryMessagesByUser 按用户查询消息
func (r *AuditReader) QueryMessagesByUser(ctx context.Context, userID string, limit, offset int) (*QueryResult, error) {
	query := Query{
		UserID: &userID,
		Limit:  limit,
		Offset: offset,
	}
	return r.QueryMessages(ctx, query)
}

// QueryMessagesByAgent 按Agent查询消息
func (r *AuditReader) QueryMessagesByAgent(ctx context.Context, agentID string, limit, offset int) (*QueryResult, error) {
	query := Query{
		AgentID: &agentID,
		Limit:   limit,
		Offset:  offset,
	}
	return r.QueryMessages(ctx, query)
}

// QueryMessagesByTimeRange 按时间范围查询消息
func (r *AuditReader) QueryMessagesByTimeRange(ctx context.Context, since, until *time.Time, limit, offset int) (*QueryResult, error) {
	query := Query{
		Since:  since,
		Until:  until,
		Limit:  limit,
		Offset: offset,
	}
	return r.QueryMessages(ctx, query)
}

// QueryMessagesByKeyword 按关键词查询消息
func (r *AuditReader) QueryMessagesByKeyword(ctx context.Context, keyword string, limit, offset int) (*QueryResult, error) {
	query := Query{
		Keyword: keyword,
		Limit:   limit,
		Offset:  offset,
	}
	return r.QueryMessages(ctx, query)
}

// QueryMessagesByType 按消息类型查询消息
func (r *AuditReader) QueryMessagesByType(ctx context.Context, msgTypes []string, limit, offset int) (*QueryResult, error) {
	query := Query{
		Types:  msgTypes,
		Limit:  limit,
		Offset: offset,
	}
	return r.QueryMessages(ctx, query)
}

// QuerySessions 查询会话列表
func (r *AuditReader) QuerySessions(ctx context.Context, userID string, limit, offset int) ([]*Session, int, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if r.closed {
		return nil, 0, NewStoreOperationError("查询器已关闭", nil)
	}
	
	return r.store.QuerySessions(ctx, userID, limit, offset)
}

// GetSessionSummary 获取会话摘要
func (r *AuditReader) GetSessionSummary(ctx context.Context, sessionID string) (*SessionSummary, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if r.closed {
		return nil, NewStoreOperationError("查询器已关闭", nil)
	}
	
	// 查询会话消息
	query := Query{
		SessionID: &sessionID,
		Limit:     1000, // 限制最大消息数
	}
	
	result, err := r.QueryMessages(ctx, query)
	if err != nil {
		return nil, err
	}
	
	if len(result.Records) == 0 {
		return nil, NewInvalidQueryError("会话不存在或无消息", nil)
	}
	
	// 计算摘要统计
	summary := &SessionSummary{
		SessionID:    sessionID,
		MessageCount: len(result.Records),
		UserMessages: 0,
		AgentMessages: 0,
		ErrorMessages: 0,
		ToolCalls:    0,
	}
	
	var firstMessage, lastMessage *Record
	userIDs := make(map[string]bool)
	agentIDs := make(map[string]bool)
	
	for _, record := range result.Records {
		// 统计消息类型
		switch record.Role {
		case "user":
			summary.UserMessages++
			userIDs[record.UserID] = true
		case "assistant":
			summary.AgentMessages++
			if record.AgentID != "" {
				agentIDs[record.AgentID] = true
			}
		case "tool":
			summary.ToolCalls++
		}
		
		if record.MsgType == "error" {
			summary.ErrorMessages++
		}
		
		// 记录首末消息
		if firstMessage == nil || record.CreatedAt.Before(firstMessage.CreatedAt) {
			firstMessage = record
		}
		if lastMessage == nil || record.CreatedAt.After(lastMessage.CreatedAt) {
			lastMessage = record
		}
	}
	
	if firstMessage != nil {
		summary.StartTime = firstMessage.CreatedAt
		summary.UserID = firstMessage.UserID
	}
	
	if lastMessage != nil {
		summary.EndTime = lastMessage.CreatedAt
		summary.Duration = lastMessage.CreatedAt.Sub(firstMessage.CreatedAt)
	}
	
	// 统计参与的用户和Agent数量
	summary.UniqueUsers = len(userIDs)
	summary.UniqueAgents = len(agentIDs)
	
	return summary, nil
}

// GetUserActivity 获取用户活动统计
func (r *AuditReader) GetUserActivity(ctx context.Context, userID string, since *time.Time) (*UserActivity, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	if r.closed {
		return nil, NewStoreOperationError("查询器已关闭", nil)
	}
	
	query := Query{
		UserID: &userID,
		Since:  since,
		Limit:  10000, // 限制最大查询数量
	}
	
	result, err := r.QueryMessages(ctx, query)
	if err != nil {
		return nil, err
	}
	
	activity := &UserActivity{
		UserID:       userID,
		MessageCount: len(result.Records),
		Sessions:     make(map[string]bool),
		Agents:       make(map[string]bool),
	}
	
	var firstMessage, lastMessage *Record
	
	for _, record := range result.Records {
		activity.Sessions[record.SessionID] = true
		if record.AgentID != "" {
			activity.Agents[record.AgentID] = true
		}
		
		if firstMessage == nil || record.CreatedAt.Before(firstMessage.CreatedAt) {
			firstMessage = record
		}
		if lastMessage == nil || record.CreatedAt.After(lastMessage.CreatedAt) {
			lastMessage = record
		}
	}
	
	if firstMessage != nil {
		activity.FirstActivity = firstMessage.CreatedAt
	}
	if lastMessage != nil {
		activity.LastActivity = lastMessage.CreatedAt
	}
	
	activity.SessionCount = len(activity.Sessions)
	activity.AgentCount = len(activity.Agents)
	
	return activity, nil
}

// Close 关闭查询器
func (r *AuditReader) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.closed = true
	return nil
}

// SessionSummary 会话摘要
type SessionSummary struct {
	SessionID     string        `json:"session_id"`
	UserID        string        `json:"user_id"`
	MessageCount  int           `json:"message_count"`
	UserMessages  int           `json:"user_messages"`
	AgentMessages int           `json:"agent_messages"`
	ErrorMessages int           `json:"error_messages"`
	ToolCalls     int           `json:"tool_calls"`
	UniqueUsers   int           `json:"unique_users"`
	UniqueAgents  int           `json:"unique_agents"`
	StartTime     time.Time     `json:"start_time"`
	EndTime       time.Time     `json:"end_time"`
	Duration      time.Duration `json:"duration"`
}

// UserActivity 用户活动统计
type UserActivity struct {
	UserID        string            `json:"user_id"`
	MessageCount  int               `json:"message_count"`
	SessionCount  int               `json:"session_count"`
	AgentCount    int               `json:"agent_count"`
	FirstActivity time.Time         `json:"first_activity"`
	LastActivity  time.Time         `json:"last_activity"`
	Sessions      map[string]bool   `json:"-"`
	Agents        map[string]bool   `json:"-"`
}
