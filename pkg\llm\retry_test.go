package llm

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
)

func TestDefaultRetryConfig(t *testing.T) {
	config := DefaultRetryConfig()
	if config == nil {
		t.<PERSON><PERSON>("DefaultRetryConfig 返回 nil")
	}

	if config.MaxRetries != 3 {
		t.<PERSON><PERSON>("期望 MaxRetries 为 3，得到 %d", config.MaxRetries)
	}

	if config.InitialDelay != 1*time.Second {
		t.Errorf("期望 InitialDelay 为 1s，得到 %v", config.InitialDelay)
	}

	if config.MaxDelay != 30*time.Second {
		t.Errorf("期望 MaxDelay 为 30s，得到 %v", config.MaxDelay)
	}

	if config.BackoffFactor != 2.0 {
		t.<PERSON>rrorf("期望 BackoffFactor 为 2.0，得到 %f", config.BackoffFactor)
	}

	if len(config.RetryableErrors) == 0 {
		t.Error("期望 RetryableErrors 不为空")
	}
}

func TestRetryConfigValidate(t *testing.T) {
	// 测试有效配置
	config := &RetryConfig{
		MaxRetries:    5,
		InitialDelay:  2 * time.Second,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 1.5,
	}
	err := config.Validate()
	if err != nil {
		t.Errorf("有效配置验证失败: %v", err)
	}

	// 测试无效配置会被修正
	invalidConfig := &RetryConfig{
		MaxRetries:    -1,
		InitialDelay:  0,
		MaxDelay:      0,
		BackoffFactor: 0,
	}
	err = invalidConfig.Validate()
	if err != nil {
		t.Errorf("配置验证失败: %v", err)
	}

	// 验证默认值被设置
	if invalidConfig.MaxRetries != 3 {
		t.Errorf("期望 MaxRetries 被设置为 3，得到 %d", invalidConfig.MaxRetries)
	}
	if invalidConfig.InitialDelay != 1*time.Second {
		t.Errorf("期望 InitialDelay 被设置为 1s，得到 %v", invalidConfig.InitialDelay)
	}
}

func TestIsRetryableError(t *testing.T) {
	// 测试可重试的错误
	retryableErr := errors.New(errors.ErrorTypeTimeout, "REQUEST_TIMEOUT", "请求超时")
	if !IsRetryableError(retryableErr) {
		t.Error("期望超时错误是可重试的")
	}

	// 测试普通错误
	normalErr := fmt.Errorf("普通错误")
	if IsRetryableError(normalErr) {
		t.Error("期望普通错误不可重试")
	}

	// 测试 nil 错误
	if IsRetryableError(nil) {
		t.Error("期望 nil 错误不可重试")
	}
}

func TestGetRetryDelay(t *testing.T) {
	config := DefaultRetryConfig()

	// 测试第一次重试
	delay1 := GetRetryDelay(1, config)
	if delay1 <= 0 {
		t.Error("重试延迟应该大于0")
	}

	// 测试第二次重试应该更长
	delay2 := GetRetryDelay(2, config)
	if delay2 <= delay1 {
		t.Error("第二次重试延迟应该比第一次更长")
	}

	// 测试nil配置使用默认值
	delay3 := GetRetryDelay(1, nil)
	if delay3 <= 0 {
		t.Error("使用默认配置的重试延迟应该大于0")
	}
}

func TestSimpleRetry(t *testing.T) {
	callCount := 0
	operation := func() (string, error) {
		callCount++
		if callCount < 3 {
			// 使用可重试的错误类型
			return "", errors.New(errors.ErrorTypeNetwork, "NETWORK_ERROR", "临时错误")
		}
		return "成功", nil
	}

	ctx := context.Background()
	result, err := SimpleRetry(ctx, 5, operation)
	if err != nil {
		t.Errorf("期望最终成功，得到错误: %v", err)
	}

	if result != "成功" {
		t.Errorf("期望结果为'成功'，得到: %s", result)
	}

	if callCount != 3 {
		t.Errorf("期望调用 3 次，实际调用 %d 次", callCount)
	}
}

func TestRetryWithBackoff(t *testing.T) {
	callCount := 0
	operation := func() (int, error) {
		callCount++
		if callCount == 1 {
			// 使用可重试的错误类型
			return 0, errors.New(errors.ErrorTypeTimeout, "REQUEST_TIMEOUT", "第一次失败")
		}
		return 42, nil
	}

	ctx := context.Background()
	result, err := RetryWithBackoff(ctx, 3, 10*time.Millisecond, 100*time.Millisecond, 2.0, operation)
	if err != nil {
		t.Errorf("期望成功，得到错误: %v", err)
	}

	if result != 42 {
		t.Errorf("期望结果为 42，得到: %d", result)
	}

	if callCount != 2 {
		t.Errorf("期望调用 2 次，实际调用 %d 次", callCount)
	}
}

func TestRetryOperation(t *testing.T) {
	config := &RetryConfig{
		MaxRetries:   2,
		InitialDelay: 10 * time.Millisecond,
		MaxDelay:     50 * time.Millisecond,
	}

	callCount := 0
	operation := func() (bool, error) {
		callCount++
		return true, nil
	}

	ctx := context.Background()
	result, err := RetryOperation(ctx, config, operation)
	if err != nil {
		t.Errorf("期望成功，得到错误: %v", err)
	}

	if !result {
		t.Error("期望结果为 true")
	}

	if callCount != 1 {
		t.Errorf("期望调用 1 次，实际调用 %d 次", callCount)
	}
}
