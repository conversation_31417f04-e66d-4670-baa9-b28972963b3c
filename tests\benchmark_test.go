//go:build integ

package tests

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/pipeline"
	"github.com/agentscope/agentscope-golang/pkg/tool"
	"github.com/agentscope/agentscope-golang/pkg/tool/builtin"
)

// BenchmarkAgentMessageProcessing benchmarks agent message processing performance
func BenchmarkAgentMessageProcessing(b *testing.B) {
	// Setup
	logger := logger.NewConsoleLogger(logger.ErrorLevel) // Reduce logging for benchmarks
	mockLLM := &MockLLMClient{}

	agentConfig := &agent.Config{
		ID:          "bench-agent",
		Name:        "Benchmark Agent",
		Type:        agent.AssistantType,
		Description: "Agent for benchmarking",
	}

	ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
	require.NoError(b, err)

	err = ag.Initialize()
	require.NoError(b, err)
	defer ag.Shutdown()

	ctx := context.Background()
	msg := message.NewTextMessage("user", "Benchmark test message")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := ag.Reply(ctx, msg)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkConcurrentAgentProcessing benchmarks concurrent agent processing
func BenchmarkConcurrentAgentProcessing(b *testing.B) {
	logger := logger.NewConsoleLogger(logger.ErrorLevel)
	mockLLM := &MockLLMClient{}

	// Create multiple agents
	agents := make([]agent.Agent, 10)
	for i := 0; i < 10; i++ {
		agentConfig := &agent.Config{
			ID:          fmt.Sprintf("concurrent-agent-%d", i),
			Name:        fmt.Sprintf("Concurrent Agent %d", i),
			Type:        agent.AssistantType,
			Description: "Agent for concurrent benchmarking",
		}

		ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
		require.NoError(b, err)

		err = ag.Initialize()
		require.NoError(b, err)
		defer ag.Shutdown()

		agents[i] = ag
	}

	ctx := context.Background()
	msg := message.NewTextMessage("user", "Concurrent benchmark test message")

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		agentIndex := 0
		for pb.Next() {
			ag := agents[agentIndex%len(agents)]
			_, err := ag.Reply(ctx, msg)
			if err != nil {
				b.Fatal(err)
			}
			agentIndex++
		}
	})
}

// BenchmarkMemoryOperations benchmarks memory service operations
func BenchmarkMemoryOperations(b *testing.B) {
	memoryConfig := &memory.Config{
		Type:        memory.InMemoryType,
		MaxMemories: 10000,
		TTL:         time.Hour,
	}

	memoryService, err := memory.NewMemoryService(memoryConfig)
	require.NoError(b, err)

	ctx := context.Background()

	b.Run("AddMemory", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			mem := &memory.Memory{
				ID:        fmt.Sprintf("bench-memory-%d", i),
				Content:   fmt.Sprintf("Benchmark memory content %d", i),
				Type:      memory.ShortTermType,
				Timestamp: time.Now(),
				Metadata: map[string]interface{}{
					"benchmark": true,
					"index":     i,
				},
			}
			err := memoryService.AddMemory(ctx, mem)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	// Add some memories for retrieval benchmarks
	for i := 0; i < 1000; i++ {
		mem := &memory.Memory{
			ID:        fmt.Sprintf("retrieval-memory-%d", i),
			Content:   fmt.Sprintf("Retrieval benchmark memory content %d", i),
			Type:      memory.ShortTermType,
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"category": "benchmark",
				"index":    i,
			},
		}
		memoryService.AddMemory(ctx, mem)
	}

	b.Run("RetrieveMemories", func(b *testing.B) {
		query := &memory.MemoryQuery{
			Keywords: []string{"benchmark"},
			Limit:    10,
		}

		for i := 0; i < b.N; i++ {
			_, err := memoryService.RetrieveMemories(ctx, query)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkToolExecution benchmarks tool execution performance
func BenchmarkToolExecution(b *testing.B) {
	registry := tool.NewDefaultRegistry()

	// Register built-in tools
	calcTool := builtin.NewCalculatorTool()
	err := registry.RegisterTool(calcTool)
	require.NoError(b, err)

	timeTool := builtin.NewTimeTool()
	err = registry.RegisterTool(timeTool)
	require.NoError(b, err)

	ctx := context.Background()

	b.Run("CalculatorTool", func(b *testing.B) {
		params := map[string]interface{}{
			"operation": "add",
			"a":         float64(10),
			"b":         float64(20),
		}

		for i := 0; i < b.N; i++ {
			_, err := calcTool.Execute(ctx, params)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("TimeTool", func(b *testing.B) {
		params := map[string]interface{}{
			"format": "2006-01-02 15:04:05",
		}

		for i := 0; i < b.N; i++ {
			_, err := timeTool.Execute(ctx, params)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("ToolRegistryLookup", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := registry.GetTool("calculator")
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkPipelineExecution benchmarks pipeline execution performance
func BenchmarkPipelineExecution(b *testing.B) {
	logger := logger.NewConsoleLogger(logger.ErrorLevel)
	mockLLM := &MockLLMClient{}

	// Create agents for pipeline
	agents := make([]agent.Agent, 5)
	for i := 0; i < 5; i++ {
		agentConfig := &agent.Config{
			ID:          fmt.Sprintf("pipeline-agent-%d", i),
			Name:        fmt.Sprintf("Pipeline Agent %d", i),
			Type:        agent.AssistantType,
			Description: "Agent for pipeline benchmarking",
		}

		ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
		require.NoError(b, err)

		err = ag.Initialize()
		require.NoError(b, err)
		defer ag.Shutdown()

		agents[i] = ag
	}

	ctx := context.Background()
	msg := message.NewTextMessage("user", "Pipeline benchmark test message")

	b.Run("SequentialPipeline", func(b *testing.B) {
		pipelineConfig := &pipeline.Config{
			Name:        "sequential-benchmark",
			Description: "Sequential pipeline for benchmarking",
		}
		seqPipeline := pipeline.NewSequentialPipeline(pipelineConfig, logger)

		for _, ag := range agents {
			seqPipeline.AddAgent(ag)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := seqPipeline.Execute(ctx, msg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("ParallelPipeline", func(b *testing.B) {
		pipelineConfig := &pipeline.Config{
			Name:        "parallel-benchmark",
			Description: "Parallel pipeline for benchmarking",
		}
		parallelPipeline := pipeline.NewParallelPipeline(pipelineConfig, logger)

		for _, ag := range agents {
			parallelPipeline.AddAgent(ag)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := parallelPipeline.Execute(ctx, msg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkMessageSerialization benchmarks message serialization performance
func BenchmarkMessageSerialization(b *testing.B) {
	msg := message.NewTextMessage("user", "This is a benchmark test message for serialization performance testing")

	b.Run("JSONSerialization", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := msg.ToJSON()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	// Serialize once for deserialization benchmark
	jsonData, err := msg.ToJSON()
	require.NoError(b, err)

	b.Run("JSONDeserialization", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := message.FromJSON(jsonData)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkMemoryUsage benchmarks memory usage patterns
func BenchmarkMemoryUsage(b *testing.B) {
	b.Run("AgentCreation", func(b *testing.B) {
		logger := logger.NewConsoleLogger(logger.ErrorLevel)
		mockLLM := &MockLLMClient{}

		var m1, m2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			agentConfig := &agent.Config{
				ID:          fmt.Sprintf("memory-agent-%d", i),
				Name:        fmt.Sprintf("Memory Agent %d", i),
				Type:        agent.AssistantType,
				Description: "Agent for memory benchmarking",
			}

			ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
			if err != nil {
				b.Fatal(err)
			}

			err = ag.Initialize()
			if err != nil {
				b.Fatal(err)
			}

			ag.Shutdown()
		}
		b.StopTimer()

		runtime.GC()
		runtime.ReadMemStats(&m2)

		b.ReportMetric(float64(m2.Alloc-m1.Alloc)/float64(b.N), "bytes/op")
	})

	b.Run("MessageCreation", func(b *testing.B) {
		var m1, m2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			msg := message.NewTextMessage("user", fmt.Sprintf("Memory benchmark message %d", i))
			_ = msg // Prevent optimization
		}
		b.StopTimer()

		runtime.GC()
		runtime.ReadMemStats(&m2)

		b.ReportMetric(float64(m2.Alloc-m1.Alloc)/float64(b.N), "bytes/op")
	})
}

// StressTestConcurrentAgents stress tests the system with many concurrent agents
func TestStressConcurrentAgents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping stress test in short mode")
	}

	logger := logger.NewConsoleLogger(logger.ErrorLevel)
	mockLLM := &MockLLMClient{}

	numAgents := 100
	numMessages := 10
	agents := make([]agent.Agent, numAgents)

	// Create agents
	for i := 0; i < numAgents; i++ {
		agentConfig := &agent.Config{
			ID:          fmt.Sprintf("stress-agent-%d", i),
			Name:        fmt.Sprintf("Stress Agent %d", i),
			Type:        agent.AssistantType,
			Description: "Agent for stress testing",
		}

		ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
		require.NoError(t, err)

		err = ag.Initialize()
		require.NoError(t, err)
		defer ag.Shutdown()

		agents[i] = ag
	}

	ctx := context.Background()
	var wg sync.WaitGroup
	errors := make(chan error, numAgents*numMessages)

	start := time.Now()

	// Launch concurrent operations
	for i := 0; i < numAgents; i++ {
		wg.Add(1)
		go func(agentIndex int) {
			defer wg.Done()
			ag := agents[agentIndex]

			for j := 0; j < numMessages; j++ {
				msg := message.NewTextMessage("user", fmt.Sprintf("Stress test message %d-%d", agentIndex, j))
				_, err := ag.Reply(ctx, msg)
				if err != nil {
					errors <- err
					return
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	duration := time.Since(start)

	// Check for errors
	for err := range errors {
		t.Errorf("Stress test error: %v", err)
	}

	totalMessages := numAgents * numMessages
	messagesPerSecond := float64(totalMessages) / duration.Seconds()

	t.Logf("Stress test completed:")
	t.Logf("  Agents: %d", numAgents)
	t.Logf("  Messages per agent: %d", numMessages)
	t.Logf("  Total messages: %d", totalMessages)
	t.Logf("  Duration: %v", duration)
	t.Logf("  Messages/second: %.2f", messagesPerSecond)

	// Performance assertions
	require.Less(t, duration, 30*time.Second, "Stress test should complete within 30 seconds")
	require.Greater(t, messagesPerSecond, 10.0, "Should process at least 10 messages per second")
}

// StressTestMemoryOperations stress tests memory operations
func TestStressMemoryOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping stress test in short mode")
	}

	memoryConfig := &memory.Config{
		Type:        memory.InMemoryType,
		MaxMemories: 50000,
		TTL:         time.Hour,
	}

	memoryService, err := memory.NewMemoryService(memoryConfig)
	require.NoError(t, err)

	ctx := context.Background()
	numOperations := 10000
	numConcurrent := 50

	var wg sync.WaitGroup
	errors := make(chan error, numOperations)

	start := time.Now()

	// Concurrent memory operations
	for i := 0; i < numConcurrent; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for j := 0; j < numOperations/numConcurrent; j++ {
				// Add memory
				mem := &memory.Memory{
					ID:        fmt.Sprintf("stress-memory-%d-%d", workerID, j),
					Content:   fmt.Sprintf("Stress test memory content %d-%d", workerID, j),
					Type:      memory.ShortTermType,
					Timestamp: time.Now(),
					Metadata: map[string]interface{}{
						"worker": workerID,
						"index":  j,
					},
				}

				err := memoryService.AddMemory(ctx, mem)
				if err != nil {
					errors <- err
					return
				}

				// Retrieve memories
				if j%10 == 0 {
					query := &memory.MemoryQuery{
						Keywords: []string{"stress"},
						Limit:    5,
					}
					_, err := memoryService.RetrieveMemories(ctx, query)
					if err != nil {
						errors <- err
						return
					}
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	duration := time.Since(start)

	// Check for errors
	for err := range errors {
		t.Errorf("Memory stress test error: %v", err)
	}

	operationsPerSecond := float64(numOperations) / duration.Seconds()

	t.Logf("Memory stress test completed:")
	t.Logf("  Operations: %d", numOperations)
	t.Logf("  Concurrent workers: %d", numConcurrent)
	t.Logf("  Duration: %v", duration)
	t.Logf("  Operations/second: %.2f", operationsPerSecond)

	// Performance assertions
	require.Less(t, duration, 60*time.Second, "Memory stress test should complete within 60 seconds")
	require.Greater(t, operationsPerSecond, 100.0, "Should process at least 100 operations per second")
}

// BenchmarkGoroutineScaling benchmarks goroutine scaling performance
func BenchmarkGoroutineScaling(b *testing.B) {
	logger := logger.NewConsoleLogger(logger.ErrorLevel)
	mockLLM := &MockLLMClient{}

	scales := []int{1, 10, 100, 1000}

	for _, scale := range scales {
		b.Run(fmt.Sprintf("Goroutines-%d", scale), func(b *testing.B) {
			agentConfig := &agent.Config{
				ID:          "scaling-agent",
				Name:        "Scaling Agent",
				Type:        agent.AssistantType,
				Description: "Agent for scaling benchmarking",
			}

			ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
			require.NoError(b, err)

			err = ag.Initialize()
			require.NoError(b, err)
			defer ag.Shutdown()

			ctx := context.Background()
			msg := message.NewTextMessage("user", "Scaling benchmark test message")

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				var wg sync.WaitGroup
				for j := 0; j < scale; j++ {
					wg.Add(1)
					go func() {
						defer wg.Done()
						_, err := ag.Reply(ctx, msg)
						if err != nil {
							b.Error(err)
						}
					}()
				}
				wg.Wait()
			}
		})
	}
}

// BenchmarkResourceCleanup benchmarks resource cleanup performance
func BenchmarkResourceCleanup(b *testing.B) {
	logger := logger.NewConsoleLogger(logger.ErrorLevel)
	mockLLM := &MockLLMClient{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Create agent
		agentConfig := &agent.Config{
			ID:          fmt.Sprintf("cleanup-agent-%d", i),
			Name:        fmt.Sprintf("Cleanup Agent %d", i),
			Type:        agent.AssistantType,
			Description: "Agent for cleanup benchmarking",
		}

		ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
		if err != nil {
			b.Fatal(err)
		}

		err = ag.Initialize()
		if err != nil {
			b.Fatal(err)
		}

		// Use agent briefly
		ctx := context.Background()
		msg := message.NewTextMessage("user", "Cleanup test message")
		_, err = ag.Reply(ctx, msg)
		if err != nil {
			b.Fatal(err)
		}

		// Cleanup
		ag.Shutdown()
	}
}
