-- 初始知识库表（PostgreSQL）
CREATE TABLE IF NOT EXISTS kb_documents (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT NOT NULL,
    source TEXT NULL,
    language TEXT NULL,
    tags TEXT[] NULL,
    metadata JSONB NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    version INT NOT NULL DEFAULT 1,
    hash TEXT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_kb_documents_type ON kb_documents(type);
CREATE INDEX IF NOT EXISTS idx_kb_documents_created_at ON kb_documents(created_at);

CREATE TABLE IF NOT EXISTS kb_chunks (
    id TEXT PRIMARY KEY,
    document_id TEXT NOT NULL REFERENCES kb_documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    start_pos INT NOT NULL,
    end_pos INT NOT NULL,
    chunk_index INT NOT NULL,
    metadata JSONB NULL,
    vector_json JSONB NULL,
    created_at TIMESTAMPTZ NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_kb_chunks_doc ON kb_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_kb_chunks_index ON kb_chunks(chunk_index);

CREATE TABLE IF NOT EXISTS kb_entities (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT NULL,
    properties JSONB NULL,
    aliases TEXT[] NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE IF NOT EXISTS kb_document_entities (
    document_id TEXT NOT NULL REFERENCES kb_documents(id) ON DELETE CASCADE,
    entity_id TEXT NOT NULL REFERENCES kb_entities(id) ON DELETE CASCADE,
    PRIMARY KEY(document_id, entity_id)
);

CREATE TABLE IF NOT EXISTS kb_relations (
    id TEXT PRIMARY KEY,
    from_entity TEXT NOT NULL REFERENCES kb_entities(id) ON DELETE CASCADE,
    to_entity TEXT NOT NULL REFERENCES kb_entities(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    description TEXT NULL,
    properties JSONB NULL,
    confidence DOUBLE PRECISION NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE IF NOT EXISTS kb_relation_documents (
    relation_id TEXT NOT NULL REFERENCES kb_relations(id) ON DELETE CASCADE,
    document_id TEXT NOT NULL REFERENCES kb_documents(id) ON DELETE CASCADE,
    PRIMARY KEY(relation_id, document_id)
);

