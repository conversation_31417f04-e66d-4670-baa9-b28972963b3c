package main

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/tool"
	"github.com/agentscope/agentscope-golang/pkg/tool/builtin/httprequest"
)

func main() {
	fmt.Println("=== AgentScope-Golang 简单聊天示例 ===")
	fmt.Println("这个示例展示了如何使用新的ADK架构创建一个具有工具和记忆功能的聊天智能体")
	fmt.Println("注意: 这是一个架构演示示例，展示了事件驱动、工具集成和记忆管理的核心概念")
	fmt.Println()

	ctx := context.Background()

	// 1. 创建记忆存储
	fmt.Println("1. 初始化记忆存储...")
	memoryStore := memory.NewMemoryStore()

	// 2. 创建工具
	fmt.Println("2. 注册工具...")
	tools := []tool.Tool{
		httprequest.NewHTTPRequestTool(),
	}

	fmt.Println("3. 架构组件初始化完成！")
	fmt.Println()
	fmt.Println("可用工具:")
	for _, tool := range tools {
		fmt.Printf("  - %s: %s\n", tool.Name(), tool.Description())
	}
	fmt.Println()
	fmt.Println("记忆存储: 内存存储 (支持会话管理)")
	fmt.Println("事件系统: 异步迭代器模式")
	fmt.Println()
	fmt.Println("开始演示记忆和工具功能 (输入 'quit' 或 'exit' 退出):")

	fmt.Println("5. 智能体初始化完成！")
	fmt.Println()
	fmt.Println("可用工具:")
	for _, tool := range tools {
		fmt.Printf("  - %s: %s\n", tool.Name(), tool.Description())
	}
	fmt.Println()
	fmt.Println("开始聊天 (输入 'quit' 或 'exit' 退出):")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 会话ID
	sessionID := "simple_chat_session"

	// 开始聊天循环
	scanner := bufio.NewScanner(os.Stdin)
	for {
		fmt.Print("\n用户: ")
		if !scanner.Scan() {
			break
		}

		userInput := strings.TrimSpace(scanner.Text())
		if userInput == "" {
			continue
		}

		// 检查退出命令
		if userInput == "quit" || userInput == "exit" {
			fmt.Println("\n再见！")
			break
		}

		// 特殊命令处理
		if userInput == "/help" {
			showHelp()
			continue
		}

		if userInput == "/memory" {
			showMemory(ctx, memoryStore, sessionID)
			continue
		}

		if userInput == "/clear" {
			err := memoryStore.Delete(ctx, sessionID)
			if err != nil {
				fmt.Printf("清空记忆失败: %v\n", err)
			} else {
				fmt.Println("记忆已清空")
			}
			continue
		}

		// 创建用户消息
		userMsg := message.NewUserMessage(userInput)

		// 保存用户消息到记忆
		err := memoryStore.Save(ctx, sessionID, userMsg)
		if err != nil {
			fmt.Printf("保存用户消息失败: %v\n", err)
			continue
		}

		// 加载对话历史（演示记忆功能）
		_, err = memoryStore.Load(ctx, sessionID, 10) // 最近10条消息
		if err != nil {
			fmt.Printf("加载对话历史失败: %v\n", err)
			continue
		}

		// 演示工具调用
		if strings.Contains(userInput, "http") || strings.Contains(userInput, "请求") {
			fmt.Print("系统: [演示工具调用] ")

			// 模拟工具调用
			httpTool := httprequest.NewHTTPRequestTool()
			fmt.Printf("使用工具: %s\n", httpTool.Name())
			fmt.Printf("工具描述: %s\n", httpTool.Description())

			// 这里可以实际调用工具，但为了简化示例，我们只是演示
			fmt.Println("工具调用完成 (演示模式)")
		} else {
			// 简单的回复演示
			fmt.Print("系统: 您说: \"" + userInput + "\"")
			fmt.Println(" (这是一个架构演示，展示了记忆存储和工具集成)")
		}

		// 保存系统回复到记忆
		systemMsg := message.NewAssistantMessage("系统回复: " + userInput)
		err = memoryStore.Save(ctx, sessionID, systemMsg)
		if err != nil {
			fmt.Printf("保存系统回复失败: %v\n", err)
		}

		fmt.Println() // 换行
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("读取输入时出错: %v\n", err)
	}
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println("\n可用命令:")
	fmt.Println("  /help   - 显示此帮助信息")
	fmt.Println("  /memory - 显示当前对话记忆")
	fmt.Println("  /clear  - 清空对话记忆")
	fmt.Println("  quit    - 退出程序")
	fmt.Println("  exit    - 退出程序")
	fmt.Println("\n可用工具:")
	fmt.Println("  - HTTP请求工具: 可以获取网络信息")
	fmt.Println("\n示例对话:")
	fmt.Println("  用户: 请帮我查询一下百度首页的内容")
	fmt.Println("  用户: 今天天气怎么样？")
	fmt.Println("  用户: 请总结一下我们刚才的对话")
}

// showMemory 显示当前记忆内容
func showMemory(ctx context.Context, store memory.Store, sessionID string) {
	messages, err := store.LoadAll(ctx, sessionID)
	if err != nil {
		fmt.Printf("加载记忆失败: %v\n", err)
		return
	}

	if len(messages) == 0 {
		fmt.Println("当前没有对话记忆")
		return
	}

	fmt.Printf("\n当前对话记忆 (共%d条消息):\n", len(messages))
	fmt.Println(strings.Repeat("-", 50))

	for i, msg := range messages {
		fmt.Printf("%d. [%s] %s\n", i+1, msg.Role, msg.GetContentString())
	}

	fmt.Println(strings.Repeat("-", 50))
}
