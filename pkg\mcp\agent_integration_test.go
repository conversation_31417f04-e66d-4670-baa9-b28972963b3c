package mcp

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestNewMCPAgentExtension 测试创建MCP Agent扩展
func TestNewMCPAgentExtension(t *testing.T) {
	config := &MCPAgentConfig{
		Servers: map[string]*MCPConfig{
			"test-server": {
				ServerCommand: []string{"node", "server.js"},
				Timeout:       30 * time.Second,
			},
		},
		AutoRefreshTools:  true,
		ResourceCacheSize: 1000,
		EnabledServers:    []string{"test-server"},
	}

	extension := NewMCPAgentExtension(config)

	assert.NotNil(t, extension)
	assert.NotNil(t, extension.toolManager)
	assert.NotNil(t, extension.config)
	assert.NotNil(t, extension.logger)
	assert.Equal(t, config, extension.config)
}

// TestNewMCPAgentExtensionWithNilConfig 测试使用空配置创建扩展
func TestNewMCPAgentExtensionWithNilConfig(t *testing.T) {
	extension := NewMCPAgentExtension(nil)

	assert.NotNil(t, extension)
	assert.NotNil(t, extension.toolManager)
	assert.NotNil(t, extension.config)
	assert.NotNil(t, extension.logger)

	// 验证默认配置
	assert.NotNil(t, extension.config.Servers)
	assert.True(t, extension.config.AutoRefreshTools)
	assert.Equal(t, 1000, extension.config.ResourceCacheSize)
	assert.Empty(t, extension.config.EnabledServers)
}

// TestMCPAgentConfig 测试MCP Agent配置结构
func TestMCPAgentConfig(t *testing.T) {
	config := &MCPAgentConfig{
		Servers: map[string]*MCPConfig{
			"server1": {
				ServerCommand: []string{"node", "server1.js"},
				Timeout:       30 * time.Second,
			},
			"server2": {
				ServerCommand: []string{"python", "server2.py"},
				Timeout:       60 * time.Second,
			},
		},
		AutoRefreshTools:  true,
		ResourceCacheSize: 2000,
		EnabledServers:    []string{"server1"},
	}

	assert.Len(t, config.Servers, 2)
	assert.True(t, config.AutoRefreshTools)
	assert.Equal(t, 2000, config.ResourceCacheSize)
	assert.Equal(t, []string{"server1"}, config.EnabledServers)

	// 验证服务器配置
	server1Config := config.Servers["server1"]
	assert.Equal(t, []string{"node", "server1.js"}, server1Config.ServerCommand)
	assert.Equal(t, 30*time.Second, server1Config.Timeout)
}

// TestMCPAgentExtensionInitialize 测试初始化（模拟）
func TestMCPAgentExtensionInitialize(t *testing.T) {
	config := &MCPAgentConfig{
		Servers: map[string]*MCPConfig{
			"test-server": {
				ServerCommand: []string{"node", "server.js"},
				Timeout:       30 * time.Second,
			},
		},
		AutoRefreshTools:  true,
		ResourceCacheSize: 1000,
		EnabledServers:    []string{"test-server"},
	}

	extension := NewMCPAgentExtension(config)
	ctx := context.Background()

	// 由于没有真实的MCP服务器，初始化会失败
	// 但我们可以测试它不会panic
	err := extension.Initialize(ctx)

	// 预期会有错误，因为无法连接到真实的MCP服务器
	// 这是正常的，因为我们没有运行真实的服务器
	if err != nil {
		assert.NotEmpty(t, err.Error())
	}
}

// TestMCPAgentExtensionGetAvailableTools 测试获取可用工具
func TestMCPAgentExtensionGetAvailableTools(t *testing.T) {
	extension := NewMCPAgentExtension(nil)
	ctx := context.Background()

	// 由于没有连接的服务器，应该返回空列表
	tools, err := extension.GetAvailableTools(ctx)
	assert.NoError(t, err)
	assert.Empty(t, tools)
}

// TestMCPAgentExtensionGetMCPResources 测试获取MCP资源
func TestMCPAgentExtensionGetMCPResources(t *testing.T) {
	extension := NewMCPAgentExtension(nil)
	ctx := context.Background()

	// 由于没有资源管理器，应该返回错误
	resources, err := extension.GetMCPResources(ctx, "test-server")
	assert.Error(t, err)
	assert.Nil(t, resources)
	assert.Contains(t, err.Error(), "no resource manager available")
}

// TestMCPAgentExtensionCallMCPTool 测试调用MCP工具
func TestMCPAgentExtensionCallMCPTool(t *testing.T) {
	extension := NewMCPAgentExtension(nil)
	ctx := context.Background()

	params := map[string]interface{}{
		"input": "test",
	}

	// 由于没有可用的工具，应该返回错误
	result, err := extension.CallMCPTool(ctx, "nonexistent-tool", params)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "tool not found")
}

// TestMCPAgentExtensionReadMCPResource 测试读取MCP资源
func TestMCPAgentExtensionReadMCPResource(t *testing.T) {
	extension := NewMCPAgentExtension(nil)
	ctx := context.Background()

	// 由于没有资源管理器，应该返回错误
	content, err := extension.ReadMCPResource(ctx, "file:///test.txt")
	assert.Error(t, err)
	assert.Nil(t, content)
	assert.Contains(t, err.Error(), "no resource manager available")
}

// TestMCPAgentExtensionRefreshTools 测试刷新工具
func TestMCPAgentExtensionRefreshTools(t *testing.T) {
	extension := NewMCPAgentExtension(nil)
	ctx := context.Background()

	// 由于没有连接的服务器，刷新工具应该快速完成
	err := extension.RefreshTools(ctx)

	// 可能没有错误，因为没有服务器需要刷新
	if err != nil {
		assert.NotEmpty(t, err.Error())
	}
}

// TestMCPAgentExtensionShutdown 测试关闭
func TestMCPAgentExtensionShutdown(t *testing.T) {
	extension := NewMCPAgentExtension(nil)

	// 关闭应该不会panic
	err := extension.Shutdown()

	// 关闭操作应该成功或者有合理的错误
	if err != nil {
		assert.NotEmpty(t, err.Error())
	}
}

// TestMCPAgentExtensionConcurrentAccess 测试并发访问
func TestMCPAgentExtensionConcurrentAccess(t *testing.T) {
	extension := NewMCPAgentExtension(nil)
	ctx := context.Background()

	// 并发访问GetAvailableTools
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func() {
			tools, err := extension.GetAvailableTools(ctx)
			assert.NoError(t, err)
			assert.Empty(t, tools)
			done <- true
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}
}

// TestMCPAgentExtensionConfigValidation 测试配置验证
func TestMCPAgentExtensionConfigValidation(t *testing.T) {
	// 测试有效配置
	validConfig := &MCPAgentConfig{
		Servers: map[string]*MCPConfig{
			"valid-server": {
				ServerCommand: []string{"node", "server.js"},
				Timeout:       30 * time.Second,
			},
		},
		AutoRefreshTools:  true,
		ResourceCacheSize: 1000,
		EnabledServers:    []string{"valid-server"},
	}

	extension := NewMCPAgentExtension(validConfig)
	assert.NotNil(t, extension)
	assert.Equal(t, validConfig, extension.config)

	// 测试空配置（应该使用默认值）
	extension2 := NewMCPAgentExtension(nil)
	assert.NotNil(t, extension2)
	assert.NotNil(t, extension2.config)
	assert.True(t, extension2.config.AutoRefreshTools)
	assert.Equal(t, 1000, extension2.config.ResourceCacheSize)
}
