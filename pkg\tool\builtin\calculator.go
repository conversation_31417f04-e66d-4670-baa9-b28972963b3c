package builtin

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// CalculatorTool 计算器工具
type CalculatorTool struct{}

// NewCalculatorTool 创建计算器工具
func NewCalculatorTool() *CalculatorTool {
	return &CalculatorTool{}
}

// Name 返回工具名称
func (c *CalculatorTool) Name() string {
	return "calculator"
}

// Description 返回工具描述
func (c *CalculatorTool) Description() string {
	return "计算器工具：可执行基础四则运算"
}

// Schema 返回工具参数模式
func (c *CalculatorTool) Schema() *tool.JSONSchema {
	return &tool.JSONSchema{
		Type: "object",
		Properties: map[string]*tool.JSONSchema{
			"expression": {
				Type:        "string",
				Description: "待计算的数学表达式（例如：'2 + 3 * 4'）",
			},
		},
		Required:    []string{"expression"},
		Description: "计算给定数学表达式的结果",
	}
}

// Execute 执行计算
func (c *CalculatorTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	expression, ok := params["expression"].(string)
	if !ok {
		return nil, fmt.Errorf("expression parameter must be a string")
	}

	result, err := c.evaluateExpression(expression)
	if err != nil {
		return nil, fmt.Errorf("calculation error: %v", err)
	}

	return map[string]any{
		"result":     result,
		"expression": expression,
	}, nil
}

// evaluateExpression 简单的表达式求值
func (c *CalculatorTool) evaluateExpression(expr string) (float64, error) {
	// 移除空格
	expr = strings.ReplaceAll(expr, " ", "")

	// 简单的四则运算解析
	return c.parseExpression(expr)
}

// parseExpression 解析表达式
func (c *CalculatorTool) parseExpression(expr string) (float64, error) {
	// 处理加减法
	for i := len(expr) - 1; i >= 0; i-- {
		if expr[i] == '+' || expr[i] == '-' {
			if i == 0 {
				continue // 负号
			}
			left, err := c.parseExpression(expr[:i])
			if err != nil {
				return 0, err
			}
			right, err := c.parseExpression(expr[i+1:])
			if err != nil {
				return 0, err
			}
			if expr[i] == '+' {
				return left + right, nil
			}
			return left - right, nil
		}
	}

	// 处理乘除法
	for i := len(expr) - 1; i >= 0; i-- {
		if expr[i] == '*' || expr[i] == '/' {
			left, err := c.parseExpression(expr[:i])
			if err != nil {
				return 0, err
			}
			right, err := c.parseExpression(expr[i+1:])
			if err != nil {
				return 0, err
			}
			if expr[i] == '*' {
				return left * right, nil
			}
			if right == 0 {
				return 0, fmt.Errorf("division by zero")
			}
			return left / right, nil
		}
	}

	// 处理括号
	if strings.HasPrefix(expr, "(") && strings.HasSuffix(expr, ")") {
		return c.parseExpression(expr[1 : len(expr)-1])
	}

	// 处理数字
	return strconv.ParseFloat(expr, 64)
}
