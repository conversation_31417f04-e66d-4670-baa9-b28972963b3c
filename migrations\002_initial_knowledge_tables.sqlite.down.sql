BEGIN TRANSACTION;

DROP TABLE IF EXISTS kb_relation_documents;
DROP INDEX IF EXISTS idx_kb_relations_type;
DROP INDEX IF EXISTS idx_kb_relations_to;
DROP INDEX IF EXISTS idx_kb_relations_from;
DROP TABLE IF EXISTS kb_relations;
DROP TABLE IF EXISTS kb_document_entities;
DROP INDEX IF EXISTS idx_kb_entities_name;
DROP TABLE IF EXISTS kb_entities;
DROP INDEX IF EXISTS idx_kb_chunks_document_id;
DROP TABLE IF EXISTS kb_chunks;
DROP INDEX IF EXISTS idx_kb_documents_title;
DROP TABLE IF EXISTS kb_documents;

COMMIT;

