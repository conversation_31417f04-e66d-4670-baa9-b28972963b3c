package distributed

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// ServiceDiscovery 服务发现接口
type ServiceDiscovery interface {
	// Register 注册服务
	Register(ctx context.Context, service *ServiceInfo) error

	// Unregister 注销服务
	Unregister(ctx context.Context, serviceID string) error

	// Discover 发现服务
	Discover(ctx context.Context, serviceName string) ([]*ServiceInfo, error)

	// Watch 监听服务变化
	Watch(ctx context.Context, serviceName string) (<-chan *ServiceEvent, error)

	// GetService 获取特定服务
	GetService(ctx context.Context, serviceID string) (*ServiceInfo, error)

	// ListServices 列出所有服务
	ListServices(ctx context.Context) ([]*ServiceInfo, error)

	// HealthCheck 健康检查
	HealthCheck(ctx context.Context, serviceID string) error
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	Address      string            `json:"address"`
	Port         int               `json:"port"`
	Protocol     string            `json:"protocol"`
	Tags         []string          `json:"tags"`
	Metadata     map[string]string `json:"metadata"`
	HealthCheck  *HealthCheckInfo  `json:"health_check,omitempty"`
	RegisteredAt time.Time         `json:"registered_at"`
	LastSeen     time.Time         `json:"last_seen"`
	Status       ServiceStatus     `json:"status"`
}

// HealthCheckInfo 健康检查信息
type HealthCheckInfo struct {
	Type     string        `json:"type"` // "http", "tcp", "grpc"
	URL      string        `json:"url,omitempty"`
	Interval time.Duration `json:"interval"`
	Timeout  time.Duration `json:"timeout"`
	Retries  int           `json:"retries"`
}

// ServiceStatus 服务状态
type ServiceStatus string

const (
	ServiceStatusHealthy   ServiceStatus = "healthy"
	ServiceStatusUnhealthy ServiceStatus = "unhealthy"
	ServiceStatusUnknown   ServiceStatus = "unknown"
)

// ServiceEvent 服务事件
type ServiceEvent struct {
	Type    ServiceEventType `json:"type"`
	Service *ServiceInfo     `json:"service"`
	Time    time.Time        `json:"time"`
}

// ServiceEventType 服务事件类型
type ServiceEventType string

const (
	ServiceEventRegistered   ServiceEventType = "registered"
	ServiceEventUnregistered ServiceEventType = "unregistered"
	ServiceEventHealthy      ServiceEventType = "healthy"
	ServiceEventUnhealthy    ServiceEventType = "unhealthy"
)

// InMemoryServiceDiscovery 内存服务发现实现
type InMemoryServiceDiscovery struct {
	services      map[string]*ServiceInfo
	watchers      map[string][]chan *ServiceEvent
	mu            sync.RWMutex
	logger        logger.Logger
	healthChecker *HealthChecker
}

// NewInMemoryServiceDiscovery 创建内存服务发现
func NewInMemoryServiceDiscovery() *InMemoryServiceDiscovery {
	sd := &InMemoryServiceDiscovery{
		services: make(map[string]*ServiceInfo),
		watchers: make(map[string][]chan *ServiceEvent),
		logger:   logger.GetGlobalLogger(),
	}

	sd.healthChecker = NewHealthChecker(sd)
	go sd.healthChecker.Start()

	return sd
}

// Register 注册服务
func (sd *InMemoryServiceDiscovery) Register(ctx context.Context, service *ServiceInfo) error {
	if service.ID == "" {
		service.ID = generateServiceID(service.Name)
	}

	service.RegisteredAt = time.Now()
	service.LastSeen = time.Now()
	service.Status = ServiceStatusUnknown

	sd.mu.Lock()
	sd.services[service.ID] = service
	sd.mu.Unlock()

	sd.logger.Infof("Service registered: %s (%s)", service.Name, service.ID)

	// 通知观察者
	event := &ServiceEvent{
		Type:    ServiceEventRegistered,
		Service: service,
		Time:    time.Now(),
	}
	sd.notifyWatchers(service.Name, event)

	// 启动健康检查
	if service.HealthCheck != nil {
		sd.healthChecker.AddService(service)
	}

	return nil
}

// Unregister 注销服务
func (sd *InMemoryServiceDiscovery) Unregister(ctx context.Context, serviceID string) error {
	sd.mu.Lock()
	service, exists := sd.services[serviceID]
	if !exists {
		sd.mu.Unlock()
		return errors.NewNotFoundError("service_not_found", fmt.Sprintf("service %s not found", serviceID))
	}

	delete(sd.services, serviceID)
	sd.mu.Unlock()

	sd.logger.Infof("Service unregistered: %s (%s)", service.Name, service.ID)

	// 通知观察者
	event := &ServiceEvent{
		Type:    ServiceEventUnregistered,
		Service: service,
		Time:    time.Now(),
	}
	sd.notifyWatchers(service.Name, event)

	// 停止健康检查
	sd.healthChecker.RemoveService(serviceID)

	return nil
}

// Discover 发现服务
func (sd *InMemoryServiceDiscovery) Discover(ctx context.Context, serviceName string) ([]*ServiceInfo, error) {
	sd.mu.RLock()
	defer sd.mu.RUnlock()

	var services []*ServiceInfo
	for _, service := range sd.services {
		if service.Name == serviceName && service.Status == ServiceStatusHealthy {
			services = append(services, service)
		}
	}

	return services, nil
}

// Watch 监听服务变化
func (sd *InMemoryServiceDiscovery) Watch(ctx context.Context, serviceName string) (<-chan *ServiceEvent, error) {
	ch := make(chan *ServiceEvent, 100)

	sd.mu.Lock()
	sd.watchers[serviceName] = append(sd.watchers[serviceName], ch)
	sd.mu.Unlock()

	// 启动清理协程
	go func() {
		<-ctx.Done()
		sd.removeWatcher(serviceName, ch)
		close(ch)
	}()

	return ch, nil
}

// GetService 获取特定服务
func (sd *InMemoryServiceDiscovery) GetService(ctx context.Context, serviceID string) (*ServiceInfo, error) {
	sd.mu.RLock()
	defer sd.mu.RUnlock()

	service, exists := sd.services[serviceID]
	if !exists {
		return nil, errors.NewNotFoundError("service_not_found", fmt.Sprintf("service %s not found", serviceID))
	}

	return service, nil
}

// ListServices 列出所有服务
func (sd *InMemoryServiceDiscovery) ListServices(ctx context.Context) ([]*ServiceInfo, error) {
	sd.mu.RLock()
	defer sd.mu.RUnlock()

	services := make([]*ServiceInfo, 0, len(sd.services))
	for _, service := range sd.services {
		services = append(services, service)
	}

	return services, nil
}

// HealthCheck 健康检查
func (sd *InMemoryServiceDiscovery) HealthCheck(ctx context.Context, serviceID string) error {
	return sd.healthChecker.CheckService(ctx, serviceID)
}

// UpdateServiceStatus 更新服务状态
func (sd *InMemoryServiceDiscovery) UpdateServiceStatus(serviceID string, status ServiceStatus) {
	sd.mu.Lock()
	service, exists := sd.services[serviceID]
	if !exists {
		sd.mu.Unlock()
		return
	}

	oldStatus := service.Status
	service.Status = status
	service.LastSeen = time.Now()
	sd.mu.Unlock()

	// 如果状态发生变化，通知观察者
	if oldStatus != status {
		var eventType ServiceEventType
		if status == ServiceStatusHealthy {
			eventType = ServiceEventHealthy
		} else {
			eventType = ServiceEventUnhealthy
		}

		event := &ServiceEvent{
			Type:    eventType,
			Service: service,
			Time:    time.Now(),
		}
		sd.notifyWatchers(service.Name, event)

		sd.logger.Debugf("Service %s status changed: %s -> %s", serviceID, oldStatus, status)
	}
}

// notifyWatchers 通知观察者
func (sd *InMemoryServiceDiscovery) notifyWatchers(serviceName string, event *ServiceEvent) {
	sd.mu.RLock()
	watchers := sd.watchers[serviceName]
	sd.mu.RUnlock()

	for _, watcher := range watchers {
		select {
		case watcher <- event:
		default:
			sd.logger.Warnf("Watcher channel full for service %s", serviceName)
		}
	}
}

// removeWatcher 移除观察者
func (sd *InMemoryServiceDiscovery) removeWatcher(serviceName string, ch chan *ServiceEvent) {
	sd.mu.Lock()
	defer sd.mu.Unlock()

	watchers := sd.watchers[serviceName]
	for i, watcher := range watchers {
		if watcher == ch {
			sd.watchers[serviceName] = append(watchers[:i], watchers[i+1:]...)
			break
		}
	}
}

// HealthChecker 健康检查器
type HealthChecker struct {
	discovery *InMemoryServiceDiscovery
	services  map[string]*ServiceInfo
	mu        sync.RWMutex
	logger    logger.Logger
	stopCh    chan struct{}
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(discovery *InMemoryServiceDiscovery) *HealthChecker {
	return &HealthChecker{
		discovery: discovery,
		services:  make(map[string]*ServiceInfo),
		logger:    logger.GetGlobalLogger(),
		stopCh:    make(chan struct{}),
	}
}

// Start 启动健康检查器
func (hc *HealthChecker) Start() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			hc.checkAllServices()
		case <-hc.stopCh:
			return
		}
	}
}

// Stop 停止健康检查器
func (hc *HealthChecker) Stop() {
	close(hc.stopCh)
}

// AddService 添加服务到健康检查
func (hc *HealthChecker) AddService(service *ServiceInfo) {
	hc.mu.Lock()
	defer hc.mu.Unlock()

	hc.services[service.ID] = service
}

// RemoveService 从健康检查中移除服务
func (hc *HealthChecker) RemoveService(serviceID string) {
	hc.mu.Lock()
	defer hc.mu.Unlock()

	delete(hc.services, serviceID)
}

// CheckService 检查特定服务
func (hc *HealthChecker) CheckService(ctx context.Context, serviceID string) error {
	hc.mu.RLock()
	service, exists := hc.services[serviceID]
	hc.mu.RUnlock()

	if !exists {
		return errors.NewNotFoundError("service_not_found", fmt.Sprintf("service %s not found", serviceID))
	}

	return hc.performHealthCheck(ctx, service)
}

// checkAllServices 检查所有服务
func (hc *HealthChecker) checkAllServices() {
	hc.mu.RLock()
	services := make([]*ServiceInfo, 0, len(hc.services))
	for _, service := range hc.services {
		services = append(services, service)
	}
	hc.mu.RUnlock()

	for _, service := range services {
		go func(s *ServiceInfo) {
			ctx, cancel := context.WithTimeout(context.Background(), s.HealthCheck.Timeout)
			defer cancel()

			err := hc.performHealthCheck(ctx, s)
			if err != nil {
				hc.discovery.UpdateServiceStatus(s.ID, ServiceStatusUnhealthy)
			} else {
				hc.discovery.UpdateServiceStatus(s.ID, ServiceStatusHealthy)
			}
		}(service)
	}
}

// performHealthCheck 执行健康检查
func (hc *HealthChecker) performHealthCheck(ctx context.Context, service *ServiceInfo) error {
	if service.HealthCheck == nil {
		return nil
	}

	switch service.HealthCheck.Type {
	case "http":
		return hc.httpHealthCheck(ctx, service)
	case "tcp":
		return hc.tcpHealthCheck(ctx, service)
	default:
		return fmt.Errorf("unsupported health check type: %s", service.HealthCheck.Type)
	}
}

// httpHealthCheck HTTP健康检查
func (hc *HealthChecker) httpHealthCheck(ctx context.Context, service *ServiceInfo) error {
	client := &http.Client{
		Timeout: service.HealthCheck.Timeout,
	}

	req, err := http.NewRequestWithContext(ctx, "GET", service.HealthCheck.URL, nil)
	if err != nil {
		return err
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("health check failed with status: %d", resp.StatusCode)
	}

	return nil
}

// tcpHealthCheck TCP健康检查
func (hc *HealthChecker) tcpHealthCheck(ctx context.Context, service *ServiceInfo) error {
	address := fmt.Sprintf("%s:%d", service.Address, service.Port)

	dialer := &net.Dialer{
		Timeout: service.HealthCheck.Timeout,
	}

	conn, err := dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		return err
	}
	defer conn.Close()

	return nil
}

// 辅助函数

// generateServiceID 生成服务ID
func generateServiceID(serviceName string) string {
	return fmt.Sprintf("%s-%d", serviceName, time.Now().UnixNano())
}

// ServiceRegistry 服务注册表
type ServiceRegistry struct {
	discovery    ServiceDiscovery
	localService *ServiceInfo
	logger       logger.Logger
}

// NewServiceRegistry 创建服务注册表
func NewServiceRegistry(discovery ServiceDiscovery) *ServiceRegistry {
	return &ServiceRegistry{
		discovery: discovery,
		logger:    logger.GetGlobalLogger(),
	}
}

// RegisterSelf 注册自身服务
func (sr *ServiceRegistry) RegisterSelf(ctx context.Context, service *ServiceInfo) error {
	sr.localService = service

	if err := sr.discovery.Register(ctx, service); err != nil {
		return fmt.Errorf("failed to register service: %w", err)
	}

	sr.logger.Info("Self-registered as service: %s", service.Name)
	return nil
}

// UnregisterSelf 注销自身服务
func (sr *ServiceRegistry) UnregisterSelf(ctx context.Context) error {
	if sr.localService == nil {
		return nil
	}

	if err := sr.discovery.Unregister(ctx, sr.localService.ID); err != nil {
		return fmt.Errorf("failed to unregister service: %w", err)
	}

	sr.logger.Info("Self-unregistered service: %s", sr.localService.Name)
	return nil
}

// GetLocalService 获取本地服务信息
func (sr *ServiceRegistry) GetLocalService() *ServiceInfo {
	return sr.localService
}
