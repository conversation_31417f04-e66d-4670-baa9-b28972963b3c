package audit

import (
	"fmt"
)

// AuditError 审计模块错误类型
type AuditError struct {
	Code    string
	Message string
	Cause   error
}

// Error 实现error接口
func (e *AuditError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("审计错误 [%s]: %s, 原因: %v", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("审计错误 [%s]: %s", e.Code, e.Message)
}

// Unwrap 支持错误链
func (e *AuditError) Unwrap() error {
	return e.Cause
}

// 错误代码常量
const (
	ErrCodeInvalidConfig    = "INVALID_CONFIG"
	ErrCodeStoreInit        = "STORE_INIT_FAILED"
	ErrCodeStoreOperation   = "STORE_OPERATION_FAILED"
	ErrCodeInvalidRecord    = "INVALID_RECORD"
	ErrCodeInvalidQuery     = "INVALID_QUERY"
	ErrCodeEncryption       = "ENCRYPTION_FAILED"
	ErrCodeDecryption       = "DECRYPTION_FAILED"
	ErrCodeSanitization     = "SANITIZATION_FAILED"
	ErrCodeRetention        = "RETENTION_FAILED"
	ErrCodeWriterClosed     = "WRITER_CLOSED"
	ErrCodeWriterFull       = "WRITER_BUFFER_FULL"
)

// NewAuditError 创建审计错误
func NewAuditError(code, message string, cause error) *AuditError {
	return &AuditError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// NewInvalidConfigError 创建无效配置错误
func NewInvalidConfigError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeInvalidConfig, message, cause)
}

// NewStoreInitError 创建存储初始化错误
func NewStoreInitError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeStoreInit, message, cause)
}

// NewStoreOperationError 创建存储操作错误
func NewStoreOperationError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeStoreOperation, message, cause)
}

// NewInvalidRecordError 创建无效记录错误
func NewInvalidRecordError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeInvalidRecord, message, cause)
}

// NewInvalidQueryError 创建无效查询错误
func NewInvalidQueryError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeInvalidQuery, message, cause)
}

// NewEncryptionError 创建加密错误
func NewEncryptionError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeEncryption, message, cause)
}

// NewDecryptionError 创建解密错误
func NewDecryptionError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeDecryption, message, cause)
}

// NewSanitizationError 创建脱敏错误
func NewSanitizationError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeSanitization, message, cause)
}

// NewRetentionError 创建保留策略错误
func NewRetentionError(message string, cause error) *AuditError {
	return NewAuditError(ErrCodeRetention, message, cause)
}

// NewWriterClosedError 创建写入器已关闭错误
func NewWriterClosedError() *AuditError {
	return NewAuditError(ErrCodeWriterClosed, "审计写入器已关闭", nil)
}

// NewWriterFullError 创建写入器缓冲区满错误
func NewWriterFullError() *AuditError {
	return NewAuditError(ErrCodeWriterFull, "审计写入器缓冲区已满", nil)
}
