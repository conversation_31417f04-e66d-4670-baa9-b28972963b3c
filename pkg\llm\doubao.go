package llm

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// DoubaoClient 实现豆包（字节跳动/火山引擎 Ark）OpenAI 兼容模式的 LLMClient 接口
type DoubaoClient struct {
	config     *Config
	httpClient *http.Client
	logger     logger.Logger
	modelInfo  *ModelInfo
}

// NewDoubaoClient 创建新的豆包客户端
func NewDoubaoClient(config *Config) (*DoubaoClient, error) {
	if config == nil {
		config = &Config{}
	}

	// 设置豆包默认值
	if config.Provider == "" {
		config.Provider = "doubao"
	}
	if config.BaseURL == "" {
		config.BaseURL = "https://ark.cn-beijing.volces.com/api/v3"
	}
	if config.Model == "" {
		config.Model = "ep-xxx" // Ark 常用 Endpoint ID
	}
	if config.Timeout <= 0 {
		config.Timeout = 30 * time.Second
	}
	if config.MaxRetries <= 0 {
		config.MaxRetries = 3
	}
	if config.Parameters == nil {
		config.Parameters = make(map[string]interface{})
	}

	// 获取 API Key，优先级：配置 > ARK_API_KEY > DOUBAO_API_KEY > VOLC_API_KEY
	apiKey := config.APIKey
	if apiKey == "" {
		apiKey = os.Getenv("ARK_API_KEY")
	}
	if apiKey == "" {
		apiKey = os.Getenv("DOUBAO_API_KEY")
	}
	if apiKey == "" {
		apiKey = os.Getenv("VOLC_API_KEY")
	}
	if apiKey == "" {
		return nil, errors.NewAuthError(CodeAPIKeyMissing,
			"豆包 API key 是必需的（请设置 ARK_API_KEY、DOUBAO_API_KEY 或 VOLC_API_KEY 环境变量）")
	}
	config.APIKey = apiKey

	if err := config.Validate(); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeValidation, CodeInvalidInput,
			"无效的豆包客户端配置")
	}

	client := &DoubaoClient{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		logger: logger.GetGlobalLogger(),
		modelInfo: &ModelInfo{
			ID:           config.Model,
			Object:       "model",
			Created:      time.Now().Unix(),
			OwnedBy:      "bytedance",
			Description:  "Doubao Chat Model via Ark",
			Capabilities: []string{"chat", "completion", "tools"},
		},
	}

	client.logger.WithField("component", "doubao_client").
		WithField("model", config.Model).
		WithField("base_url", config.BaseURL).
		Info("豆包客户端初始化完成")

	return client, nil
}

// Generate 生成响应
func (dc *DoubaoClient) Generate(ctx context.Context, request *GenerateRequest) (*GenerateResponse, error) {
	if request == nil {
		return nil, errors.NewValidationError("INVALID_INPUT", "请求不能为空")
	}

	// 准备 API 请求
	apiRequest := dc.prepareAPIRequest(request)
	apiRequest.Stream = false

	// 发起 API 调用
	response, err := dc.makeAPICall(ctx, apiRequest)
	if err != nil {
		return nil, err
	}

	dc.logger.WithField("component", "doubao_client").
		WithField("model", apiRequest.Model).
		WithField("messages_count", len(apiRequest.Messages)).
		Debug("成功生成响应")

	return response, nil
}

// GenerateStream 生成流式响应
func (dc *DoubaoClient) GenerateStream(ctx context.Context, request *GenerateRequest) (<-chan *StreamResponse, error) {
	if request == nil {
		return nil, errors.NewValidationError("INVALID_INPUT", "请求不能为空")
	}

	// 准备 API 请求
	apiRequest := dc.prepareAPIRequest(request)
	apiRequest.Stream = true

	// 创建响应通道
	responseChan := make(chan *StreamResponse, 10)

	// 在 goroutine 中开始流式处理
	go func() {
		defer close(responseChan)

		if err := dc.makeStreamingAPICall(ctx, apiRequest, responseChan); err != nil {
			// 发送错误响应
			responseChan <- &StreamResponse{
				Error: &ErrorInfo{
					Message: err.Error(),
					Type:    "stream_error",
				},
				Done: true,
			}
		}
	}()

	return responseChan, nil
}

// GenerateWithTools 生成带工具调用支持的响应
func (dc *DoubaoClient) GenerateWithTools(ctx context.Context, request *GenerateRequest, tools []ToolDefinition) (*GenerateResponse, error) {
	// 添加工具到请求中
	requestWithTools := *request
	requestWithTools.Tools = make([]*Tool, len(tools))

	for i, toolDef := range tools {
		requestWithTools.Tools[i] = &Tool{
			Type: toolDef.Type,
			Function: &ToolFunction{
				Name:        toolDef.Function.Name,
				Description: toolDef.Function.Description,
				Parameters:  toolDef.Function.Parameters,
			},
		}
	}

	// 使用标准 Generate 方法
	return dc.Generate(ctx, &requestWithTools)
}

// GenerateMultiModal 生成多模态响应
// 注意：Doubao API 支持文本和图像输入，但不支持音频、视频或文件输入
func (dc *DoubaoClient) GenerateMultiModal(ctx context.Context, request *MultiModalGenerateRequest) (*GenerateResponse, error) {
	// 验证输入内容类型，检查不支持的类型
	for i, msg := range request.Messages {
		for j, content := range msg.Content {
			switch content.Type {
			case "text", "image_url":
				// 文本和图像内容支持，继续处理
				continue
			case "audio_url":
				return nil, errors.Wrap(
					fmt.Errorf("Doubao API 当前不支持音频输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"Doubao 多模态限制：仅支持文本和图像内容",
				)
			case "video_url":
				return nil, errors.Wrap(
					fmt.Errorf("Doubao API 当前不支持视频输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"Doubao 多模态限制：仅支持文本和图像内容",
				)
			case "file_url":
				return nil, errors.Wrap(
					fmt.Errorf("Doubao API 当前不支持文件输入，消息 %d 内容 %d", i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"Doubao 多模态限制：仅支持文本和图像内容",
				)
			default:
				return nil, errors.Wrap(
					fmt.Errorf("未知的内容类型: %s，消息 %d 内容 %d", content.Type, i, j),
					errors.ErrorTypeLLM,
					CodeLLMRequestFailed,
					"Doubao 多模态限制：仅支持文本和图像内容",
				)
			}
		}
	}

	// 构建多模态消息格式（支持文本和图像）
	messages := make([]*ChatMessage, len(request.Messages))
	for i, msg := range request.Messages {
		// 对于多模态消息，需要构建特殊的content格式
		if len(msg.Content) == 1 && msg.Content[0].Type == "text" {
			// 纯文本消息，使用简单格式
			messages[i] = &ChatMessage{
				Role:    msg.Role,
				Content: msg.Content[0].Text,
			}
		} else {
			// 多模态消息，构建数组格式的content
			contentArray := make([]map[string]any, len(msg.Content))
			for j, content := range msg.Content {
				switch content.Type {
				case "text":
					contentArray[j] = map[string]any{
						"type": "text",
						"text": content.Text,
					}
				case "image_url":
					if content.ImageURL == nil {
						return nil, errors.Wrap(
							fmt.Errorf("image_url content missing ImageURL field"),
							errors.ErrorTypeLLM,
							CodeInvalidInput,
							"多模态消息格式错误",
						)
					}
					contentArray[j] = map[string]any{
						"type": "image_url",
						"image_url": map[string]any{
							"url": content.ImageURL.URL,
						},
					}
				}
			}

			messages[i] = &ChatMessage{
				Role:    msg.Role,
				Content: contentArray,
			}
		}
	}

	// 创建标准生成请求
	genRequest := &GenerateRequest{
		Messages:    messages,
		Model:       request.Model,
		Temperature: request.Temperature,
		MaxTokens:   request.MaxTokens,
		TopP:        request.TopP,
		TopK:        request.TopK,
		Stop:        request.Stop,
		Stream:      request.Stream,
		Parameters:  request.Parameters,
	}

	// 使用标准 Generate 方法
	return dc.Generate(ctx, genRequest)
}

// GetModelInfo 返回模型信息
func (dc *DoubaoClient) GetModelInfo() *ModelInfo {
	return dc.modelInfo
}

// Close 关闭客户端并释放资源
func (dc *DoubaoClient) Close() error {
	dc.logger.WithField("component", "doubao_client").Info("豆包客户端已关闭")
	return nil
}

// prepareAPIRequest 从生成请求准备 API 请求
func (dc *DoubaoClient) prepareAPIRequest(request *GenerateRequest) *GenerateRequest {
	apiRequest := &GenerateRequest{
		Messages: request.Messages,
		Model:    dc.config.Model,
		Stream:   request.Stream,
	}

	// 如果请求中指定了模型，则覆盖
	if request.Model != "" {
		apiRequest.Model = request.Model
	}

	// 从配置默认值设置参数
	if temp, exists := dc.config.Parameters["temperature"]; exists {
		if tempFloat, ok := temp.(float64); ok {
			apiRequest.Temperature = &tempFloat
		}
	}
	if maxTokens, exists := dc.config.Parameters["max_tokens"]; exists {
		if maxTokensInt, ok := maxTokens.(int); ok {
			apiRequest.MaxTokens = &maxTokensInt
		} else if maxTokensFloat, ok := maxTokens.(float64); ok {
			maxTokensInt := int(maxTokensFloat)
			apiRequest.MaxTokens = &maxTokensInt
		}
	}

	// 用请求参数覆盖
	if request.Temperature != nil {
		apiRequest.Temperature = request.Temperature
	}
	if request.MaxTokens != nil {
		apiRequest.MaxTokens = request.MaxTokens
	}
	if request.TopP != nil {
		apiRequest.TopP = request.TopP
	}
	if request.TopK != nil {
		apiRequest.TopK = request.TopK
	}
	if request.Stop != nil {
		apiRequest.Stop = request.Stop
	}
	if request.Tools != nil {
		apiRequest.Tools = request.Tools
	}
	if request.ToolChoice != nil {
		apiRequest.ToolChoice = request.ToolChoice
	}

	return apiRequest
}

// makeAPICall 发起非流式 API 调用
func (dc *DoubaoClient) makeAPICall(ctx context.Context, request *GenerateRequest) (*GenerateResponse, error) {
	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"序列化请求失败")
	}

	// 创建 HTTP 请求
	url := dc.config.BaseURL + "/chat/completions"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"创建 HTTP 请求失败")
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+dc.config.APIKey)
	httpReq.Header.Set("User-Agent", "AgentScope-Golang/0.1.0")

	// 发起请求
	httpResp, err := dc.httpClient.Do(httpReq)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeNetwork, "REQUEST_FAILED", "HTTP 请求失败")
	}
	defer httpResp.Body.Close()

	// 读取响应体
	responseBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMResponseInvalid,
			"读取响应体失败")
	}

	// 检查 HTTP 错误
	if httpResp.StatusCode != http.StatusOK {
		return nil, dc.handleHTTPError(httpResp.StatusCode, responseBody)
	}

	// 解析响应
	var response GenerateResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMResponseInvalid,
			"解析响应 JSON 失败")
	}

	// 检查 API 错误
	if response.Error != nil {
		return nil, errors.NewLLMError(CodeLLMRequestFailed,
			response.Error.Message)
	}

	return &response, nil
}

// makeStreamingAPICall 发起流式 API 调用
func (dc *DoubaoClient) makeStreamingAPICall(ctx context.Context, request *GenerateRequest, responseChan chan<- *StreamResponse) error {
	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"序列化请求失败")
	}

	// 创建 HTTP 请求
	url := dc.config.BaseURL + "/chat/completions"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMRequestFailed,
			"创建 HTTP 请求失败")
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+dc.config.APIKey)
	httpReq.Header.Set("User-Agent", "AgentScope-Golang/0.1.0")
	httpReq.Header.Set("Accept", "text/event-stream")

	// 发起请求
	httpResp, err := dc.httpClient.Do(httpReq)
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeNetwork, "REQUEST_FAILED", "HTTP 请求失败")
	}
	defer httpResp.Body.Close()

	// 检查 HTTP 错误
	if httpResp.StatusCode != http.StatusOK {
		responseBody, _ := io.ReadAll(httpResp.Body)
		return dc.handleHTTPError(httpResp.StatusCode, responseBody)
	}

	// 处理流式响应
	scanner := bufio.NewScanner(httpResp.Body)
	for scanner.Scan() {
		line := scanner.Text()

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, ":") {
			continue
		}

		// 解析 SSE 数据
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			// 检查流结束
			if data == "[DONE]" {
				responseChan <- &StreamResponse{Done: true}
				break
			}

			// 解析 JSON 响应
			var streamResp StreamResponse
			if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
				dc.logger.WithField("component", "doubao_client").
					WithError(err).
					WithField("data", data).
					Warn("解析流式响应失败")
				continue
			}

			// 发送响应到通道
			select {
			case responseChan <- &streamResp:
			case <-ctx.Done():
				return ctx.Err()
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return errors.Wrap(err, errors.ErrorTypeLLM, CodeLLMResponseInvalid,
			"读取流式响应失败")
	}

	return nil
}

// handleHTTPError 处理 HTTP 错误响应
func (dc *DoubaoClient) handleHTTPError(statusCode int, responseBody []byte) error {
	var errorResp struct {
		Error *ErrorInfo `json:"error"`
	}

	// 尝试解析错误响应
	if err := json.Unmarshal(responseBody, &errorResp); err == nil && errorResp.Error != nil {
		switch statusCode {
		case http.StatusUnauthorized:
			return errors.NewAuthError(CodeInvalidCredentials,
				errorResp.Error.Message)
		case http.StatusTooManyRequests:
			return errors.NewLLMError(CodeLLMQuotaExceeded,
				errorResp.Error.Message)
		case http.StatusBadRequest:
			return errors.NewValidationError("INVALID_INPUT", errorResp.Error.Message)
		default:
			return errors.NewLLMError(CodeLLMRequestFailed,
				errorResp.Error.Message)
		}
	}

	// 回退错误消息
	return errors.NewLLMError(CodeLLMRequestFailed,
		fmt.Sprintf("HTTP %d: %s", statusCode, string(responseBody)))
}

// GetConfig 返回客户端配置
func (dc *DoubaoClient) GetConfig() *Config {
	return dc.config.Clone()
}

// UpdateConfig 更新客户端配置
func (dc *DoubaoClient) UpdateConfig(config *Config) error {
	if config == nil {
		return errors.NewValidationError(CodeInvalidInput, "配置不能为空")
	}

	if err := config.Validate(); err != nil {
		return errors.Wrap(err, errors.ErrorTypeValidation, CodeInvalidInput,
			"无效的配置")
	}

	dc.config = config.Clone()
	dc.httpClient.Timeout = config.Timeout

	// 更新模型信息
	dc.modelInfo.ID = config.Model

	dc.logger.WithField("component", "doubao_client").
		WithField("model", config.Model).
		Info("客户端配置已更新")

	return nil
}

// TestConnection 测试与豆包 API 的连接
func (dc *DoubaoClient) TestConnection(ctx context.Context) error {
	// 创建简单的测试请求
	request := NewGenerateRequest([]*ChatMessage{
		NewUserMessage("Hello"),
	})
	request.SetMaxTokens(1)

	// 发起请求
	_, err := dc.Generate(ctx, request)
	if err != nil {
		return errors.Wrap(err, errors.ErrorTypeNetwork, CodeConnectionFailed,
			"连接测试失败")
	}

	dc.logger.WithField("component", "doubao_client").Info("连接测试成功")
	return nil
}
