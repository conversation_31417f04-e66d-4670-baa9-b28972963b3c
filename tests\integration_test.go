//go:build integ

package tests

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/pipeline"
	"github.com/agentscope/agentscope-golang/pkg/tool"
	"github.com/agentscope/agentscope-golang/pkg/tool/builtin"
)

// TestAgentLLMIntegration tests the integration between agents and LLM services
func TestAgentLLMIntegration(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("DEEPSEEK_API_KEY not set, skipping LLM integration test")
	}

	// Create LLM client
	llmConfig := &llm.DeepseekConfig{
		APIKey:  apiKey,
		Model:   "deepseek-chat",
		BaseURL: "https://api.deepseek.com/v1",
	}
	llmClient := llm.NewDeepseekClient(llmConfig)

	// Create agent configuration
	agentConfig := &agent.Config{
		ID:          "test-assistant",
		Name:        "Test Assistant",
		Type:        agent.AssistantType,
		Description: "A test assistant agent",
		LLMConfig: map[string]interface{}{
			"model":       "deepseek-chat",
			"temperature": 0.7,
		},
	}

	// Create logger
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create assistant agent
	assistantAgent, err := agent.NewAssistantAgent(agentConfig, llmClient, logger)
	require.NoError(t, err)

	// Initialize agent
	err = assistantAgent.Initialize()
	require.NoError(t, err)
	defer assistantAgent.Shutdown()

	// Create test message
	testMsg := message.NewTextMessage("user", "Hello! Can you tell me what 2+2 equals?")

	// Send message to agent
	ctx := context.Background()
	response, err := assistantAgent.Reply(ctx, testMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)

	// Verify response
	assert.Equal(t, assistantAgent.ID(), response.Sender())
	assert.NotEmpty(t, response.Content().String())

	t.Logf("Agent response: %s", response.Content().String())
}

// TestAgentToolIntegration tests the integration between agents and tools
func TestAgentToolIntegration(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("DEEPSEEK_API_KEY not set, skipping tool integration test")
	}

	// Create tool registry and register built-in tools
	toolRegistry := tool.NewDefaultRegistry()

	// Register calculator tool
	calcTool := builtin.NewCalculatorTool()
	err := toolRegistry.RegisterTool(calcTool)
	require.NoError(t, err)

	// Register time tool
	timeTool := builtin.NewTimeTool()
	err = toolRegistry.RegisterTool(timeTool)
	require.NoError(t, err)

	// Create LLM client with tool support
	llmConfig := &llm.DeepseekConfig{
		APIKey:  apiKey,
		Model:   "deepseek-chat",
		BaseURL: "https://api.deepseek.com/v1",
	}
	llmClient := llm.NewDeepseekClient(llmConfig)

	// Create agent configuration
	agentConfig := &agent.Config{
		ID:          "tool-assistant",
		Name:        "Tool Assistant",
		Type:        agent.AssistantType,
		Description: "An assistant agent with tool capabilities",
		LLMConfig: map[string]interface{}{
			"model":       "deepseek-chat",
			"temperature": 0.1,
		},
	}

	// Create logger
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create assistant agent with tools
	assistantAgent, err := agent.NewAssistantAgent(agentConfig, llmClient, logger)
	require.NoError(t, err)

	// Set tool registry for the agent
	assistantAgent.SetToolRegistry(toolRegistry)

	// Initialize agent
	err = assistantAgent.Initialize()
	require.NoError(t, err)
	defer assistantAgent.Shutdown()

	// Test calculator tool usage
	calcMsg := message.NewTextMessage("user", "Please calculate 15 * 23 using the calculator tool")

	ctx := context.Background()
	response, err := assistantAgent.Reply(ctx, calcMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)

	t.Logf("Calculator response: %s", response.Content().String())

	// Test time tool usage
	timeMsg := message.NewTextMessage("user", "What is the current time?")

	response, err = assistantAgent.Reply(ctx, timeMsg)
	require.NoError(t, err)
	assert.NotNil(t, response)

	t.Logf("Time response: %s", response.Content().String())
}

// TestAgentMemoryIntegration tests the integration between agents and memory systems
func TestAgentMemoryIntegration(t *testing.T) {
	// Create memory service
	memoryConfig := &memory.Config{
		Type:        memory.InMemoryType,
		MaxMemories: 100,
		TTL:         time.Hour,
	}
	memoryService, err := memory.NewMemoryService(memoryConfig)
	require.NoError(t, err)

	// Create agent configuration
	agentConfig := &agent.Config{
		ID:          "memory-assistant",
		Name:        "Memory Assistant",
		Type:        agent.AssistantType,
		Description: "An assistant agent with memory capabilities",
	}

	// Create logger
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create mock LLM client for testing
	mockLLM := &MockLLMClient{}

	// Create assistant agent with memory
	assistantAgent, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
	require.NoError(t, err)

	// Set memory service for the agent
	assistantAgent.SetMemoryService(memoryService)

	// Initialize agent
	err = assistantAgent.Initialize()
	require.NoError(t, err)
	defer assistantAgent.Shutdown()

	// Test memory storage and retrieval
	ctx := context.Background()

	// Send first message
	msg1 := message.NewTextMessage("user", "My name is Alice")
	response1, err := assistantAgent.Reply(ctx, msg1)
	require.NoError(t, err)
	assert.NotNil(t, response1)

	// Send second message that should reference the first
	msg2 := message.NewTextMessage("user", "What is my name?")
	response2, err := assistantAgent.Reply(ctx, msg2)
	require.NoError(t, err)
	assert.NotNil(t, response2)

	// Verify that memory was used (mock LLM should have received context)
	assert.True(t, mockLLM.receivedContext)
}

// TestPipelineIntegration tests the integration of multiple agents in a pipeline
func TestPipelineIntegration(t *testing.T) {
	// Create logger
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create mock LLM client
	mockLLM := &MockLLMClient{}

	// Create multiple agents
	agent1Config := &agent.Config{
		ID:          "agent-1",
		Name:        "Agent 1",
		Type:        agent.AssistantType,
		Description: "First agent in pipeline",
	}
	agent1, err := agent.NewAssistantAgent(agent1Config, mockLLM, logger)
	require.NoError(t, err)

	agent2Config := &agent.Config{
		ID:          "agent-2",
		Name:        "Agent 2",
		Type:        agent.AssistantType,
		Description: "Second agent in pipeline",
	}
	agent2, err := agent.NewAssistantAgent(agent2Config, mockLLM, logger)
	require.NoError(t, err)

	// Initialize agents
	err = agent1.Initialize()
	require.NoError(t, err)
	defer agent1.Shutdown()

	err = agent2.Initialize()
	require.NoError(t, err)
	defer agent2.Shutdown()

	// Create sequential pipeline
	pipelineConfig := &pipeline.Config{
		Name:        "test-pipeline",
		Description: "A test pipeline with two agents",
	}
	seqPipeline := pipeline.NewSequentialPipeline(pipelineConfig, logger)

	// Add agents to pipeline
	err = seqPipeline.AddAgent(agent1)
	require.NoError(t, err)

	err = seqPipeline.AddAgent(agent2)
	require.NoError(t, err)

	// Execute pipeline
	ctx := context.Background()
	initialMsg := message.NewTextMessage("user", "Process this message through the pipeline")

	result, err := seqPipeline.Execute(ctx, initialMsg)
	require.NoError(t, err)
	assert.NotNil(t, result)

	// Verify that both agents were called
	assert.Equal(t, 2, mockLLM.callCount)
}

// TestParallelPipelineIntegration tests parallel execution of agents
func TestParallelPipelineIntegration(t *testing.T) {
	// Create logger
	logger := logger.NewConsoleLogger(logger.InfoLevel)

	// Create mock LLM client with delay
	mockLLM := &MockLLMClientWithDelay{delay: 100 * time.Millisecond}

	// Create multiple agents
	agents := make([]agent.Agent, 3)
	for i := 0; i < 3; i++ {
		agentConfig := &agent.Config{
			ID:          fmt.Sprintf("parallel-agent-%d", i),
			Name:        fmt.Sprintf("Parallel Agent %d", i),
			Type:        agent.AssistantType,
			Description: fmt.Sprintf("Agent %d for parallel execution", i),
		}

		ag, err := agent.NewAssistantAgent(agentConfig, mockLLM, logger)
		require.NoError(t, err)

		err = ag.Initialize()
		require.NoError(t, err)
		defer ag.Shutdown()

		agents[i] = ag
	}

	// Create parallel pipeline
	pipelineConfig := &pipeline.Config{
		Name:        "parallel-test-pipeline",
		Description: "A test pipeline for parallel execution",
	}
	parallelPipeline := pipeline.NewParallelPipeline(pipelineConfig, logger)

	// Add agents to pipeline
	for _, ag := range agents {
		err := parallelPipeline.AddAgent(ag)
		require.NoError(t, err)
	}

	// Execute pipeline and measure time
	ctx := context.Background()
	initialMsg := message.NewTextMessage("user", "Process this message in parallel")

	start := time.Now()
	result, err := parallelPipeline.Execute(ctx, initialMsg)
	duration := time.Since(start)

	require.NoError(t, err)
	assert.NotNil(t, result)

	// Verify parallel execution (should be faster than sequential)
	// With 3 agents each taking 100ms, parallel should be ~100ms, sequential would be ~300ms
	assert.Less(t, duration, 200*time.Millisecond, "Parallel execution should be faster than sequential")

	// Verify all agents were called
	assert.Equal(t, 3, mockLLM.callCount)
}

// TestConfigIntegration tests configuration loading and agent creation
func TestConfigIntegration(t *testing.T) {
	// Create test configuration
	cfg := &config.Config{
		App: config.AppConfig{
			Name:    "test-app",
			Version: "1.0.0",
		},
		Agents: []config.AgentConfig{
			{
				ID:          "config-agent",
				Name:        "Config Agent",
				Type:        "assistant",
				Description: "Agent created from configuration",
				LLMConfig: map[string]interface{}{
					"model":       "deepseek-chat",
					"temperature": 0.7,
				},
			},
		},
		LLM: config.LLMConfig{
			Provider: "deepseek",
			Config: map[string]interface{}{
				"api_key":  "test-key",
				"base_url": "https://api.deepseek.com/v1",
			},
		},
	}

	// Create agent manager
	logger := logger.NewConsoleLogger(logger.InfoLevel)
	agentManager := agent.NewManager()

	// Create agents from configuration
	for _, agentConfig := range cfg.Agents {
		// Create mock LLM client
		mockLLM := &MockLLMClient{}

		// Convert config.AgentConfig to agent.Config
		agConfig := &agent.Config{
			ID:          agentConfig.ID,
			Name:        agentConfig.Name,
			Type:        agent.AgentType(agentConfig.Type),
			Description: agentConfig.Description,
			LLMConfig:   agentConfig.LLMConfig,
		}

		// Create agent
		ag, err := agent.NewAssistantAgent(agConfig, mockLLM, logger)
		require.NoError(t, err)

		// Initialize and add to manager
		err = ag.Initialize()
		require.NoError(t, err)
		defer ag.Shutdown()

		err = agentManager.AddAgent(ag)
		require.NoError(t, err)
	}

	// Verify agent was created and added
	agents := agentManager.ListAgents()
	assert.Len(t, agents, 1)
	assert.Equal(t, "config-agent", agents[0].ID())
}

// MockLLMClient for testing
type MockLLMClient struct {
	callCount       int
	receivedContext bool
}

func (m *MockLLMClient) Generate(ctx context.Context, req *llm.GenerateRequest) (*llm.GenerateResponse, error) {
	m.callCount++

	// Check if context was provided (indicating memory usage)
	if len(req.Messages) > 1 {
		m.receivedContext = true
	}

	return &llm.GenerateResponse{
		Content: "Mock response from LLM",
		Usage: &llm.Usage{
			PromptTokens:     10,
			CompletionTokens: 5,
			TotalTokens:      15,
		},
	}, nil
}

func (m *MockLLMClient) GenerateStream(ctx context.Context, req *llm.GenerateRequest) (<-chan *llm.StreamResponse, error) {
	ch := make(chan *llm.StreamResponse, 1)
	go func() {
		defer close(ch)
		ch <- &llm.StreamResponse{
			Content: "Mock streaming response",
			Done:    true,
		}
	}()
	return ch, nil
}

// MockLLMClientWithDelay for testing parallel execution
type MockLLMClientWithDelay struct {
	MockLLMClient
	delay time.Duration
}

func (m *MockLLMClientWithDelay) Generate(ctx context.Context, req *llm.GenerateRequest) (*llm.GenerateResponse, error) {
	time.Sleep(m.delay)
	return m.MockLLMClient.Generate(ctx, req)
}

func (m *MockLLMClientWithDelay) GenerateStream(ctx context.Context, req *llm.GenerateRequest) (<-chan *llm.StreamResponse, error) {
	time.Sleep(m.delay)
	return m.MockLLMClient.GenerateStream(ctx, req)
}
