package tool

import (
	"context"
	"fmt"
	"testing"
)

// 测试新的工具接口和注册表
func TestNewToolInterface(t *testing.T) {
	// 清空全局注册表以避免冲突
	Clear()

	tool := &mockTool{
		name:        "test-tool",
		description: "测试工具",
	}

	// 使用新的全局函数
	err := Register(tool)
	if err != nil {
		t.Fatalf("注册工具失败: %v", err)
	}

	if Size() != 1 {
		t.<PERSON><PERSON><PERSON>("注册后工具数量应该为 1: got %d", <PERSON><PERSON>())
	}

	retrieved, exists := Get("test-tool")
	if !exists {
		t.Error("应该能够获取已注册的工具")
	}

	if retrieved.Name() != "test-tool" {
		t.<PERSON><PERSON>("获取的工具名称不匹配: got %s, want %s", retrieved.Name(), "test-tool")
	}

	list := List()
	if len(list) != 1 || list[0] != "test-tool" {
		t.<PERSON>("工具列表不正确: got %v", list)
	}

	success := Unregister("test-tool")
	if !success {
		t.Error("注销工具应该成功")
	}

	if Size() != 0 {
		t.<PERSON><PERSON>("注销后工具数量应该为 0: got %d", Size())
	}
}

func TestNewRegistryInterface(t *testing.T) {
	registry := NewRegistry()

	tool := &mockTool{
		name:        "registry-tool",
		description: "注册表工具",
		schema: &JSONSchema{
			Type: "object",
			Properties: map[string]*JSONSchema{
				"message": {Type: "string"},
			},
			Required: []string{"message"},
		},
		executeFunc: func(ctx context.Context, params map[string]any) (any, error) {
			if message, ok := params["message"].(string); ok {
				return fmt.Sprintf("执行结果: %s", message), nil
			}
			return "执行结果: 无效参数", nil
		},
	}

	// 注册工具
	err := registry.Register(tool)
	if err != nil {
		t.Fatalf("注册工具失败: %v", err)
	}

	// 获取工具
	retrieved, exists := registry.Get("registry-tool")
	if !exists {
		t.Error("应该能够获取已注册的工具")
	}

	// 执行工具
	params := map[string]any{"message": "hello"}
	result, err := retrieved.Execute(context.Background(), params)
	if err != nil {
		t.Fatalf("执行工具失败: %v", err)
	}

	expected := "执行结果: hello"
	if result != expected {
		t.Errorf("执行结果不匹配: got %s, want %s", result, expected)
	}

	// 验证 schema
	schema := retrieved.Schema()
	if schema == nil {
		t.Error("工具应该有 schema")
	}

	if schema.Type != "object" {
		t.Errorf("schema 类型不匹配: got %s, want object", schema.Type)
	}

	// 清理
	registry.Clear()
	if registry.Size() != 0 {
		t.Errorf("清理后注册表应该为空: got %d", registry.Size())
	}
}

func TestJSONSchemaValidation(t *testing.T) {
	// 测试对象验证
	schema := &JSONSchema{
		Type: "object",
		Properties: map[string]*JSONSchema{
			"name": {Type: "string"},
			"age":  {Type: "integer"},
		},
		Required: []string{"name"},
	}

	// 有效输入
	err := ValidateInput(schema, `{"name": "张三", "age": 25}`)
	if err != nil {
		t.Errorf("有效输入应该通过验证: %v", err)
	}

	// 缺少必需字段
	err = ValidateInput(schema, `{"age": 25}`)
	if err == nil {
		t.Error("缺少必需字段应该验证失败")
	}

	// 类型错误
	err = ValidateInput(schema, `{"name": "张三", "age": "25"}`)
	if err == nil {
		t.Error("类型错误应该验证失败")
	}

	// 测试数组验证
	arraySchema := &JSONSchema{
		Type: "array",
		Items: &JSONSchema{
			Type: "string",
		},
	}

	err = ValidateInput(arraySchema, `["a", "b", "c"]`)
	if err != nil {
		t.Errorf("有效数组应该通过验证: %v", err)
	}

	err = ValidateInput(arraySchema, `["a", 123, "c"]`)
	if err == nil {
		t.Error("元素类型错误应该验证失败")
	}

	// 测试字符串长度验证
	minLen := 2
	maxLen := 10
	stringSchema := &JSONSchema{
		Type:      "string",
		MinLength: &minLen,
		MaxLength: &maxLen,
	}

	err = ValidateInput(stringSchema, `"hello"`)
	if err != nil {
		t.Errorf("有效字符串应该通过验证: %v", err)
	}

	err = ValidateInput(stringSchema, `"a"`)
	if err == nil {
		t.Error("太短的字符串应该验证失败")
	}

	err = ValidateInput(stringSchema, `"this is too long"`)
	if err == nil {
		t.Error("太长的字符串应该验证失败")
	}

	// 测试数字范围验证
	min := 0.0
	max := 100.0
	numberSchema := &JSONSchema{
		Type:    "number",
		Minimum: &min,
		Maximum: &max,
	}

	err = ValidateInput(numberSchema, `50.5`)
	if err != nil {
		t.Errorf("有效数字应该通过验证: %v", err)
	}

	err = ValidateInput(numberSchema, `-10`)
	if err == nil {
		t.Error("太小的数字应该验证失败")
	}

	err = ValidateInput(numberSchema, `150`)
	if err == nil {
		t.Error("太大的数字应该验证失败")
	}

	// 测试枚举验证
	enumSchema := &JSONSchema{
		Type: "string",
		Enum: []any{"red", "green", "blue"},
	}

	err = ValidateInput(enumSchema, `"red"`)
	if err != nil {
		t.Errorf("枚举值应该通过验证: %v", err)
	}

	err = ValidateInput(enumSchema, `"yellow"`)
	if err == nil {
		t.Error("无效枚举值应该验证失败")
	}
}

func TestRegistryErrorCases(t *testing.T) {
	registry := NewRegistry()

	// 注册空工具
	err := registry.Register(nil)
	if err == nil {
		t.Error("注册空工具应该失败")
	}

	// 注册空名称工具
	emptyNameTool := &mockTool{
		name:        "",
		description: "空名称工具",
	}

	err = registry.Register(emptyNameTool)
	if err == nil {
		t.Error("注册空名称工具应该失败")
	}

	// 重复注册
	tool := &mockTool{
		name:        "duplicate-tool",
		description: "重复工具",
	}

	err = registry.Register(tool)
	if err != nil {
		t.Fatalf("首次注册工具失败: %v", err)
	}

	err = registry.Register(tool)
	if err == nil {
		t.Error("重复注册应该失败")
	}

	// 获取不存在的工具
	_, exists := registry.Get("nonexistent")
	if exists {
		t.Error("不应该能够获取不存在的工具")
	}

	// 注销不存在的工具
	success := registry.Unregister("nonexistent")
	if success {
		t.Error("注销不存在的工具应该失败")
	}
}

func TestRegistryConcurrency(t *testing.T) {
	registry := NewRegistry()

	// 并发注册工具
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func(id int) {
			tool := &mockTool{
				name:        fmt.Sprintf("concurrent-tool-%d", id),
				description: "并发工具",
			}

			err := registry.Register(tool)
			if err != nil {
				t.Errorf("并发注册工具失败: %v", err)
			}

			done <- true
		}(i)
	}

	// 等待所有 goroutine 完成
	for i := 0; i < 10; i++ {
		<-done
	}

	// 验证所有工具都已注册
	if registry.Size() != 10 {
		t.Errorf("并发注册后工具数量应该为 10: got %d", registry.Size())
	}

	// 并发获取工具
	for i := 0; i < 10; i++ {
		go func(id int) {
			name := fmt.Sprintf("concurrent-tool-%d", id)
			tool, exists := registry.Get(name)
			if !exists {
				t.Errorf("应该能够获取工具 %s", name)
			}

			if tool != nil && tool.Name() != name {
				t.Errorf("工具名称不匹配: got %s, want %s", tool.Name(), name)
			}

			done <- true
		}(i)
	}

	// 等待所有 goroutine 完成
	for i := 0; i < 10; i++ {
		<-done
	}
}
