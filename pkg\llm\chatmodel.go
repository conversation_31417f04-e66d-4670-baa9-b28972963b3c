package llm

import (
	"context"
	"time"
)

// ChatModel 表示新的聊天模型接口
type ChatModel interface {
	// Chat 发送聊天请求并返回完整响应
	Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)

	// ChatStream 发送流式聊天请求并返回增量响应通道
	ChatStream(ctx context.Context, req *ChatRequest) (<-chan *ChatDelta, error)
}

// ChatRequest 表示新的聊天请求
type ChatRequest struct {
	Model            string           `json:"model"`                       // 模型名称
	Messages         []*ChatMessage   `json:"messages"`                    // 消息列表
	Temperature      *float64         `json:"temperature,omitempty"`       // 温度参数 (0.0-2.0)
	TopP             *float64         `json:"top_p,omitempty"`             // Top-p 参数 (0.0-1.0)
	MaxTokens        *int             `json:"max_tokens,omitempty"`        // 最大生成 token 数
	Stop             []string         `json:"stop,omitempty"`              // 停止词列表
	Stream           bool             `json:"stream,omitempty"`            // 是否流式输出
	Tools            []ToolDefinition `json:"tools,omitempty"`             // 可用工具列表
	ToolChoice       interface{}      `json:"tool_choice,omitempty"`       // 工具选择策略
	FrequencyPenalty *float64         `json:"frequency_penalty,omitempty"` // 频率惩罚 (-2.0-2.0)
	PresencePenalty  *float64         `json:"presence_penalty,omitempty"`  // 存在惩罚 (-2.0-2.0)
	User             string           `json:"user,omitempty"`              // 用户标识
	Seed             *int             `json:"seed,omitempty"`              // 随机种子
}

// ChatResponse 表示新的聊天响应
type ChatResponse struct {
	ID                string       `json:"id"`                           // 响应 ID
	Object            string       `json:"object"`                       // 对象类型
	Created           int64        `json:"created"`                      // 创建时间戳
	Model             string       `json:"model"`                        // 使用的模型
	SystemFingerprint string       `json:"system_fingerprint,omitempty"` // 系统指纹
	Choices           []ChatChoice `json:"choices"`                      // 选择列表
	Usage             *ChatUsage   `json:"usage,omitempty"`              // 使用统计
}

// ChatChoice 表示聊天响应选择
type ChatChoice struct {
	Index        int         `json:"index"`         // 选择索引
	Message      ChatMessage `json:"message"`       // 消息内容
	FinishReason string      `json:"finish_reason"` // 结束原因
}

// ChatUsage 表示聊天使用统计
type ChatUsage struct {
	PromptTokens     int `json:"prompt_tokens"`     // 提示 token 数
	CompletionTokens int `json:"completion_tokens"` // 完成 token 数
	TotalTokens      int `json:"total_tokens"`      // 总 token 数
}

// ChatDelta 表示流式响应增量
type ChatDelta struct {
	ID                string            `json:"id"`                           // 响应 ID
	Object            string            `json:"object"`                       // 对象类型
	Created           int64             `json:"created"`                      // 创建时间戳
	Model             string            `json:"model"`                        // 使用的模型
	SystemFingerprint string            `json:"system_fingerprint,omitempty"` // 系统指纹
	Choices           []ChatDeltaChoice `json:"choices"`                      // 增量选择列表
}

// ChatDeltaChoice 表示增量选择
type ChatDeltaChoice struct {
	Index        int              `json:"index"`         // 选择索引
	Delta        ChatDeltaContent `json:"delta"`         // 增量内容
	FinishReason *string          `json:"finish_reason"` // 结束原因（可能为空）
}

// ChatDeltaContent 表示增量内容
type ChatDeltaContent struct {
	Role      string      `json:"role,omitempty"`       // 角色（仅第一个增量）
	Content   string      `json:"content,omitempty"`    // 内容增量
	ToolCalls []*ToolCall `json:"tool_calls,omitempty"` // 工具调用增量
}

// ChatModelConfig 表示聊天模型配置
type ChatModelConfig struct {
	APIKey      string        `json:"api_key"`     // API 密钥
	BaseURL     string        `json:"base_url"`    // 基础 URL
	Timeout     time.Duration `json:"timeout"`     // 超时时间
	Retries     int           `json:"retries"`     // 重试次数
	MaxTokens   int           `json:"max_tokens"`  // 默认最大 token 数
	Temperature float64       `json:"temperature"` // 默认温度
	TopP        float64       `json:"top_p"`       // 默认 Top-p
}

// NewChatRequest 创建新的聊天请求
func NewChatRequest(model string, messages []*ChatMessage) *ChatRequest {
	return &ChatRequest{
		Model:    model,
		Messages: messages,
	}
}

// AddMessage 添加消息到请求
func (r *ChatRequest) AddMessage(role, content string) *ChatRequest {
	r.Messages = append(r.Messages, &ChatMessage{
		Role:    role,
		Content: content,
	})
	return r
}

// AddUserMessage 添加用户消息
func (r *ChatRequest) AddUserMessage(content string) *ChatRequest {
	return r.AddMessage("user", content)
}

// AddAssistantMessage 添加助手消息
func (r *ChatRequest) AddAssistantMessage(content string) *ChatRequest {
	return r.AddMessage("assistant", content)
}

// AddSystemMessage 添加系统消息
func (r *ChatRequest) AddSystemMessage(content string) *ChatRequest {
	return r.AddMessage("system", content)
}

// AddToolMessage 添加工具消息
func (r *ChatRequest) AddToolMessage(content, toolCallID string) *ChatRequest {
	r.Messages = append(r.Messages, &ChatMessage{
		Role:       "tool",
		Content:    content,
		ToolCallID: toolCallID,
	})
	return r
}

// WithTemperature 设置温度参数
func (r *ChatRequest) WithTemperature(temperature float64) *ChatRequest {
	r.Temperature = &temperature
	return r
}

// WithTopP 设置 Top-p 参数
func (r *ChatRequest) WithTopP(topP float64) *ChatRequest {
	r.TopP = &topP
	return r
}

// WithMaxTokens 设置最大 token 数
func (r *ChatRequest) WithMaxTokens(maxTokens int) *ChatRequest {
	r.MaxTokens = &maxTokens
	return r
}

// WithTools 设置可用工具
func (r *ChatRequest) WithTools(tools []ToolDefinition) *ChatRequest {
	r.Tools = tools
	return r
}

// WithToolChoice 设置工具选择策略
func (r *ChatRequest) WithToolChoice(toolChoice interface{}) *ChatRequest {
	r.ToolChoice = toolChoice
	return r
}

// WithStream 设置是否流式输出
func (r *ChatRequest) WithStream(stream bool) *ChatRequest {
	r.Stream = stream
	return r
}

// GetFirstChoice 获取第一个选择的消息
func (r *ChatResponse) GetFirstChoice() *ChatMessage {
	if len(r.Choices) > 0 {
		return &r.Choices[0].Message
	}
	return nil
}

// GetContent 获取第一个选择的内容
func (r *ChatResponse) GetContent() string {
	if msg := r.GetFirstChoice(); msg != nil {
		if content, ok := msg.Content.(string); ok {
			return content
		}
	}
	return ""
}

// HasToolCalls 检查是否包含工具调用
func (r *ChatResponse) HasToolCalls() bool {
	if msg := r.GetFirstChoice(); msg != nil {
		return len(msg.ToolCalls) > 0
	}
	return false
}

// GetToolCalls 获取工具调用列表
func (r *ChatResponse) GetToolCalls() []*ToolCall {
	if msg := r.GetFirstChoice(); msg != nil {
		return msg.ToolCalls
	}
	return nil
}

// IsFinished 检查响应是否已完成
func (r *ChatResponse) IsFinished() bool {
	if len(r.Choices) > 0 {
		reason := r.Choices[0].FinishReason
		return reason == "stop" || reason == "length" || reason == "tool_calls"
	}
	return false
}

// GetContent 获取增量内容
func (d *ChatDelta) GetContent() string {
	if len(d.Choices) > 0 {
		return d.Choices[0].Delta.Content
	}
	return ""
}

// HasToolCalls 检查增量是否包含工具调用
func (d *ChatDelta) HasToolCalls() bool {
	if len(d.Choices) > 0 {
		return len(d.Choices[0].Delta.ToolCalls) > 0
	}
	return false
}

// GetToolCalls 获取增量工具调用
func (d *ChatDelta) GetToolCalls() []*ToolCall {
	if len(d.Choices) > 0 {
		return d.Choices[0].Delta.ToolCalls
	}
	return nil
}

// IsFinished 检查增量是否表示完成
func (d *ChatDelta) IsFinished() bool {
	if len(d.Choices) > 0 && d.Choices[0].FinishReason != nil {
		reason := *d.Choices[0].FinishReason
		return reason == "stop" || reason == "length" || reason == "tool_calls"
	}
	return false
}
