package web

import (
	"net/http"
	"testing"

	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestCheckOrigin 测试Origin校验功能
func TestCheckOrigin(t *testing.T) {
	tests := []struct {
		name           string
		origin         string
		host           string
		allowedOrigins []string
		expected       bool
		description    string
	}{
		{
			name:           "无Origin头",
			origin:         "",
			host:           "localhost:8080",
			allowedOrigins: []string{},
			expected:       false,
			description:    "没有Origin头应该被拒绝",
		},
		{
			name:           "同源请求",
			origin:         "http://localhost:8080",
			host:           "localhost:8080",
			allowedOrigins: []string{},
			expected:       true,
			description:    "同源请求应该被允许",
		},
		{
			name:           "同源请求_不同端口",
			origin:         "http://localhost:3000",
			host:           "localhost:8080",
			allowedOrigins: []string{},
			expected:       false,
			description:    "不同端口的请求应该被拒绝",
		},
		{
			name:           "白名单匹配",
			origin:         "https://example.com",
			host:           "localhost:8080",
			allowedOrigins: []string{"https://example.com"},
			expected:       true,
			description:    "白名单中的Origin应该被允许",
		},
		{
			name:           "白名单不匹配",
			origin:         "https://evil.com",
			host:           "localhost:8080",
			allowedOrigins: []string{"https://example.com"},
			expected:       false,
			description:    "不在白名单中的Origin应该被拒绝",
		},
		{
			name:           "通配符允许所有",
			origin:         "https://any-domain.com",
			host:           "localhost:8080",
			allowedOrigins: []string{"*"},
			expected:       true,
			description:    "通配符应该允许所有Origin",
		},
		{
			name:           "子域名匹配",
			origin:         "https://api.example.com",
			host:           "localhost:8080",
			allowedOrigins: []string{"*.example.com"},
			expected:       true,
			description:    "子域名通配符应该匹配",
		},
		{
			name:           "子域名不匹配",
			origin:         "https://api.other.com",
			host:           "localhost:8080",
			allowedOrigins: []string{"*.example.com"},
			expected:       false,
			description:    "不匹配的子域名应该被拒绝",
		},
		{
			name:           "多个白名单",
			origin:         "https://app.example.com",
			host:           "localhost:8080",
			allowedOrigins: []string{"https://example.com", "https://app.example.com", "https://admin.example.com"},
			expected:       true,
			description:    "多个白名单中的任一匹配都应该被允许",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟的HTTP请求
			req, err := http.NewRequest("GET", "http://"+tt.host+"/ws", nil)
			assert.NoError(t, err)

			if tt.origin != "" {
				req.Header.Set("Origin", tt.origin)
			}
			req.Host = tt.host

			// 测试checkOrigin函数
			result := checkOrigin(req, tt.allowedOrigins)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestCreateUpgrader 测试WebSocket升级器创建
func TestCreateUpgrader(t *testing.T) {
	tests := []struct {
		name        string
		webConfig   *config.WebConfig
		origin      string
		host        string
		expected    bool
		description string
	}{
		{
			name:        "无配置",
			webConfig:   nil,
			origin:      "http://localhost:8080",
			host:        "localhost:8080",
			expected:    true,
			description: "无配置时应该允许同源请求",
		},
		{
			name: "CORS禁用",
			webConfig: &config.WebConfig{
				CORS: &config.CORSConfig{
					Enabled: false,
				},
			},
			origin:      "http://localhost:8080",
			host:        "localhost:8080",
			expected:    true,
			description: "CORS禁用时应该允许同源请求",
		},
		{
			name: "CORS启用_有白名单",
			webConfig: &config.WebConfig{
				CORS: &config.CORSConfig{
					Enabled:        true,
					AllowedOrigins: []string{"https://example.com"},
				},
			},
			origin:      "https://example.com",
			host:        "localhost:8080",
			expected:    true,
			description: "CORS启用且在白名单中应该被允许",
		},
		{
			name: "CORS启用_不在白名单",
			webConfig: &config.WebConfig{
				CORS: &config.CORSConfig{
					Enabled:        true,
					AllowedOrigins: []string{"https://example.com"},
				},
			},
			origin:      "https://evil.com",
			host:        "localhost:8080",
			expected:    false,
			description: "CORS启用但不在白名单中应该被拒绝",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			upgrader := createUpgrader(tt.webConfig)

			// 创建模拟的HTTP请求
			req, err := http.NewRequest("GET", "http://"+tt.host+"/ws", nil)
			assert.NoError(t, err)

			if tt.origin != "" {
				req.Header.Set("Origin", tt.origin)
			}
			req.Host = tt.host

			// 测试升级器的CheckOrigin函数
			result := upgrader.CheckOrigin(req)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestWebSocketManagerCreation 测试WebSocketManager创建
func TestWebSocketManagerCreation(t *testing.T) {
	webConfig := &config.WebConfig{
		Host: "localhost",
		Port: 8080,
		CORS: &config.CORSConfig{
			Enabled:        true,
			AllowedOrigins: []string{"https://example.com"},
		},
	}

	manager := NewWebSocketManager(nil, webConfig)
	assert.NotNil(t, manager)
	assert.Equal(t, webConfig, manager.webConfig)
	assert.NotNil(t, manager.clients)
	assert.NotNil(t, manager.broadcast)
	assert.NotNil(t, manager.register)
	assert.NotNil(t, manager.unregister)
}

// TestOriginValidationSecurity 测试Origin校验的安全性
func TestOriginValidationSecurity(t *testing.T) {
	// 测试常见的安全绕过尝试
	securityTests := []struct {
		name           string
		origin         string
		allowedOrigins []string
		expected       bool
		description    string
	}{
		{
			name:           "空Origin绕过尝试",
			origin:         "",
			allowedOrigins: []string{"https://example.com"},
			expected:       false,
			description:    "空Origin应该被拒绝",
		},
		{
			name:           "子域名欺骗",
			origin:         "https://evil.com.example.com",
			allowedOrigins: []string{"*.example.com"},
			expected:       true,
			description:    "合法的子域名应该被允许",
		},
		{
			name:           "域名后缀欺骗",
			origin:         "https://evilexample.com",
			allowedOrigins: []string{"https://example.com"},
			expected:       false,
			description:    "域名后缀欺骗应该被拒绝",
		},
		{
			name:           "协议不匹配",
			origin:         "http://example.com",
			allowedOrigins: []string{"https://example.com"},
			expected:       false,
			description:    "协议不匹配应该被拒绝",
		},
	}

	for _, tt := range securityTests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "http://localhost:8080/ws", nil)
			assert.NoError(t, err)

			if tt.origin != "" {
				req.Header.Set("Origin", tt.origin)
			}
			req.Host = "localhost:8080"

			result := checkOrigin(req, tt.allowedOrigins)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}
