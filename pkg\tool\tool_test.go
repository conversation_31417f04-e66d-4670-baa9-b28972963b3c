package tool

import (
	"context"
	"fmt"
	"testing"
)

// mockTool 模拟工具实现
type mockTool struct {
	name        string
	description string
	schema      *JSONSchema
	executeFunc func(ctx context.Context, params map[string]any) (any, error)
}

func (m *mockTool) Name() string {
	return m.name
}

func (m *mockTool) Description() string {
	return m.description
}

func (m *mockTool) Schema() *JSONSchema {
	return m.schema
}

func (m *mockTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	if m.executeFunc != nil {
		return m.executeFunc(ctx, params)
	}
	return "mock result", nil
}

func TestNewRegistry(t *testing.T) {
	registry := NewRegistry()
	if registry == nil {
		t.Fatal("创建注册表失败")
	}

	if registry.Size() != 0 {
		t.Errorf("新注册表应该为空: got %d", registry.Size())
	}
}

func TestRegistryRegister(t *testing.T) {
	registry := NewRegistry()

	tool := &mockTool{
		name:        "test-tool",
		description: "测试工具",
	}

	// 注册工具
	err := registry.Register(tool)
	if err != nil {
		t.Fatalf("注册工具失败: %v", err)
	}

	if registry.Size() != 1 {
		t.Errorf("注册后工具数量应该为 1: got %d", registry.Size())
	}

	// 重复注册应该失败
	err = registry.Register(tool)
	if err == nil {
		t.Error("重复注册应该失败")
	}
}

func TestRegistryRegisterNil(t *testing.T) {
	registry := NewRegistry()

	// 注册空工具应该失败
	err := registry.Register(nil)
	if err == nil {
		t.Error("注册空工具应该失败")
	}
}

func TestRegistryRegisterEmptyName(t *testing.T) {
	registry := NewRegistry()

	tool := &mockTool{
		name:        "",
		description: "测试工具",
	}

	// 注册空名称工具应该失败
	err := registry.Register(tool)
	if err == nil {
		t.Error("注册空名称工具应该失败")
	}
}

func TestRegistryGet(t *testing.T) {
	registry := NewRegistry()

	tool := &mockTool{
		name:        "test-tool",
		description: "测试工具",
	}

	// 注册工具
	err := registry.Register(tool)
	if err != nil {
		t.Fatalf("注册工具失败: %v", err)
	}

	// 获取工具
	retrieved, exists := registry.Get("test-tool")
	if !exists {
		t.Error("应该能够获取已注册的工具")
	}

	if retrieved.Name() != "test-tool" {
		t.Errorf("获取的工具名称不匹配: got %s, want %s", retrieved.Name(), "test-tool")
	}

	// 获取不存在的工具
	_, exists = registry.Get("nonexistent")
	if exists {
		t.Error("不应该能够获取不存在的工具")
	}
}

func TestRegistryList(t *testing.T) {
	registry := NewRegistry()

	// 空注册表
	list := registry.List()
	if len(list) != 0 {
		t.Errorf("空注册表应该返回空列表: got %d items", len(list))
	}

	// 注册多个工具
	tools := []*mockTool{
		{name: "tool1", description: "工具1"},
		{name: "tool2", description: "工具2"},
		{name: "tool3", description: "工具3"},
	}

	for _, tool := range tools {
		err := registry.Register(tool)
		if err != nil {
			t.Fatalf("注册工具 %s 失败: %v", tool.name, err)
		}
	}

	// 检查列表
	list = registry.List()
	if len(list) != 3 {
		t.Errorf("列表长度不匹配: got %d, want %d", len(list), 3)
	}

	// 验证所有工具都在列表中
	toolMap := make(map[string]bool)
	for _, name := range list {
		toolMap[name] = true
	}

	for _, tool := range tools {
		if !toolMap[tool.name] {
			t.Errorf("工具 %s 不在列表中", tool.name)
		}
	}
}

func TestRegistryUnregister(t *testing.T) {
	registry := NewRegistry()

	tool := &mockTool{
		name:        "test-tool",
		description: "测试工具",
	}

	// 注册工具
	err := registry.Register(tool)
	if err != nil {
		t.Fatalf("注册工具失败: %v", err)
	}

	// 注销工具
	success := registry.Unregister("test-tool")
	if !success {
		t.Error("注销工具应该成功")
	}

	if registry.Size() != 0 {
		t.Errorf("注销后工具数量应该为 0: got %d", registry.Size())
	}

	// 注销不存在的工具
	success = registry.Unregister("nonexistent")
	if success {
		t.Error("注销不存在的工具应该失败")
	}
}

func TestRegistryClear(t *testing.T) {
	registry := NewRegistry()

	// 注册多个工具
	for i := 0; i < 5; i++ {
		tool := &mockTool{
			name:        fmt.Sprintf("tool%d", i),
			description: "测试工具",
		}
		err := registry.Register(tool)
		if err != nil {
			t.Fatalf("注册工具失败: %v", err)
		}
	}

	if registry.Size() != 5 {
		t.Errorf("注册后工具数量应该为 5: got %d", registry.Size())
	}

	// 清空注册表
	registry.Clear()

	if registry.Size() != 0 {
		t.Errorf("清空后工具数量应该为 0: got %d", registry.Size())
	}

	list := registry.List()
	if len(list) != 0 {
		t.Errorf("清空后列表应该为空: got %d items", len(list))
	}
}

func TestRegistrySize(t *testing.T) {
	registry := NewRegistry()

	// 初始大小
	if registry.Size() != 0 {
		t.Errorf("初始大小应该为 0: got %d", registry.Size())
	}

	// 注册工具
	for i := 0; i < 3; i++ {
		tool := &mockTool{
			name:        fmt.Sprintf("tool%d", i),
			description: "测试工具",
		}
		err := registry.Register(tool)
		if err != nil {
			t.Fatalf("注册工具失败: %v", err)
		}

		expectedSize := i + 1
		if registry.Size() != expectedSize {
			t.Errorf("注册第 %d 个工具后大小应该为 %d: got %d", i+1, expectedSize, registry.Size())
		}
	}
}

func TestValidateInputNilSchema(t *testing.T) {
	err := ValidateInput(nil, `{"test": "value"}`)
	if err != nil {
		t.Errorf("空 schema 应该通过验证: %v", err)
	}
}

func TestValidateInputInvalidJSON(t *testing.T) {
	schema := &JSONSchema{Type: "object"}
	err := ValidateInput(schema, `{invalid json}`)
	if err == nil {
		t.Error("无效 JSON 应该验证失败")
	}
}

func TestValidateInputObject(t *testing.T) {
	schema := &JSONSchema{
		Type: "object",
		Properties: map[string]*JSONSchema{
			"name": {Type: "string"},
			"age":  {Type: "integer"},
		},
		Required: []string{"name"},
	}

	// 有效输入
	err := ValidateInput(schema, `{"name": "张三", "age": 25}`)
	if err != nil {
		t.Errorf("有效输入应该通过验证: %v", err)
	}

	// 缺少必需字段
	err = ValidateInput(schema, `{"age": 25}`)
	if err == nil {
		t.Error("缺少必需字段应该验证失败")
	}

	// 类型错误
	err = ValidateInput(schema, `{"name": "张三", "age": "25"}`)
	if err == nil {
		t.Error("类型错误应该验证失败")
	}
}

func TestValidateInputArray(t *testing.T) {
	schema := &JSONSchema{
		Type: "array",
		Items: &JSONSchema{
			Type: "string",
		},
	}

	// 有效输入
	err := ValidateInput(schema, `["a", "b", "c"]`)
	if err != nil {
		t.Errorf("有效数组应该通过验证: %v", err)
	}

	// 元素类型错误
	err = ValidateInput(schema, `["a", 123, "c"]`)
	if err == nil {
		t.Error("元素类型错误应该验证失败")
	}
}

func TestValidateInputString(t *testing.T) {
	minLen := 2
	maxLen := 10
	schema := &JSONSchema{
		Type:      "string",
		MinLength: &minLen,
		MaxLength: &maxLen,
	}

	// 有效输入
	err := ValidateInput(schema, `"hello"`)
	if err != nil {
		t.Errorf("有效字符串应该通过验证: %v", err)
	}

	// 太短
	err = ValidateInput(schema, `"a"`)
	if err == nil {
		t.Error("太短的字符串应该验证失败")
	}

	// 太长
	err = ValidateInput(schema, `"this is too long"`)
	if err == nil {
		t.Error("太长的字符串应该验证失败")
	}
}

func TestValidateInputNumber(t *testing.T) {
	min := 0.0
	max := 100.0
	schema := &JSONSchema{
		Type:    "number",
		Minimum: &min,
		Maximum: &max,
	}

	// 有效输入
	err := ValidateInput(schema, `50.5`)
	if err != nil {
		t.Errorf("有效数字应该通过验证: %v", err)
	}

	// 太小
	err = ValidateInput(schema, `-10`)
	if err == nil {
		t.Error("太小的数字应该验证失败")
	}

	// 太大
	err = ValidateInput(schema, `150`)
	if err == nil {
		t.Error("太大的数字应该验证失败")
	}
}

func TestValidateInputEnum(t *testing.T) {
	schema := &JSONSchema{
		Type: "string",
		Enum: []any{"red", "green", "blue"},
	}

	// 有效输入
	err := ValidateInput(schema, `"red"`)
	if err != nil {
		t.Errorf("枚举值应该通过验证: %v", err)
	}

	// 无效枚举值
	err = ValidateInput(schema, `"yellow"`)
	if err == nil {
		t.Error("无效枚举值应该验证失败")
	}
}

func TestGlobalRegistry(t *testing.T) {
	// 清空全局注册表
	Clear()

	tool := &mockTool{
		name:        "global-tool",
		description: "全局工具",
	}

	// 使用全局函数
	err := Register(tool)
	if err != nil {
		t.Fatalf("注册全局工具失败: %v", err)
	}

	if Size() != 1 {
		t.Errorf("全局注册表大小应该为 1: got %d", Size())
	}

	retrieved, exists := Get("global-tool")
	if !exists {
		t.Error("应该能够获取全局工具")
	}

	if retrieved.Name() != "global-tool" {
		t.Errorf("全局工具名称不匹配: got %s", retrieved.Name())
	}

	list := List()
	if len(list) != 1 || list[0] != "global-tool" {
		t.Errorf("全局工具列表不正确: got %v", list)
	}

	success := Unregister("global-tool")
	if !success {
		t.Error("注销全局工具应该成功")
	}

	if Size() != 0 {
		t.Errorf("注销后全局注册表应该为空: got %d", Size())
	}
}
