package knowledge

import (
	"context"
	"testing"
	"time"
)

func TestFAQManager(t *testing.T) {
	// 创建FAQ管理器
	manager := NewFAQManager()

	// 创建测试知识库
	kb := &FAQKnowledgeBase{
		ID:          "test_kb",
		Name:        "测试知识库",
		Description: "用于测试的FAQ知识库",
		Level:       KnowledgeLevelBase,
		AccessLevel: AccessLevelPublic,
		Version:     "1.0.0",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Items: []*FAQItem{
			{
				ID:          "faq_001",
				Question:    "营业时间是什么？",
				Answer:      "我们的营业时间是工作日9:00-18:00",
				Keywords:    []string{"营业时间", "工作时间", "上班时间"},
				Category:    "基础信息",
				Priority:    8,
				Level:       KnowledgeLevelBase,
				AccessLevel: AccessLevelPublic,
				Version:     "1.0.0",
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			{
				ID:          "faq_002",
				Question:    "如何联系客服？",
				Answer:      "您可以拨打400-xxx-xxxx联系客服",
				Keywords:    []string{"联系客服", "客服电话", "人工客服"},
				Category:    "客服服务",
				Priority:    9,
				Level:       KnowledgeLevelBase,
				AccessLevel: AccessLevelPublic,
				Version:     "1.0.0",
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
		},
	}

	// 加载知识库
	err := manager.LoadKnowledgeBase(context.Background(), kb)
	if err != nil {
		t.Fatalf("加载知识库失败: %v", err)
	}

	// 测试搜索功能
	t.Run("搜索营业时间", func(t *testing.T) {
		query := &FAQSearchQuery{
			Query:    "营业时间",
			Limit:    5,
			MinScore: 0.1,
		}

		results, err := manager.Search(context.Background(), query)
		if err != nil {
			t.Fatalf("搜索失败: %v", err)
		}

		if len(results) == 0 {
			t.Fatal("没有找到搜索结果")
		}

		// 验证第一个结果
		firstResult := results[0]
		if firstResult.Item.ID != "faq_001" {
			t.Errorf("期望第一个结果ID为faq_001，实际为%s", firstResult.Item.ID)
		}

		if firstResult.Score <= 0 {
			t.Errorf("期望分数大于0，实际为%f", firstResult.Score)
		}

		t.Logf("搜索结果: %s (分数: %.2f)", firstResult.Item.Question, firstResult.Score)
	})

	t.Run("搜索客服", func(t *testing.T) {
		query := &FAQSearchQuery{
			Query:    "客服",
			Limit:    5,
			MinScore: 0.1,
		}

		results, err := manager.Search(context.Background(), query)
		if err != nil {
			t.Fatalf("搜索失败: %v", err)
		}

		if len(results) == 0 {
			t.Fatal("没有找到搜索结果")
		}

		// 验证结果包含客服相关FAQ
		found := false
		for _, result := range results {
			if result.Item.ID == "faq_002" {
				found = true
				break
			}
		}

		if !found {
			t.Error("没有找到客服相关的FAQ")
		}
	})

	t.Run("根据ID获取FAQ", func(t *testing.T) {
		item, err := manager.GetByID(context.Background(), "faq_001")
		if err != nil {
			t.Fatalf("根据ID获取FAQ失败: %v", err)
		}

		if item.Question != "营业时间是什么？" {
			t.Errorf("期望问题为'营业时间是什么？'，实际为'%s'", item.Question)
		}
	})

	t.Run("根据分类获取FAQ", func(t *testing.T) {
		items, err := manager.GetByCategory(context.Background(), "基础信息", "")
		if err != nil {
			t.Fatalf("根据分类获取FAQ失败: %v", err)
		}

		if len(items) == 0 {
			t.Fatal("没有找到基础信息分类的FAQ")
		}

		if items[0].Category != "基础信息" {
			t.Errorf("期望分类为'基础信息'，实际为'%s'", items[0].Category)
		}
	})
}

func TestFileRepository(t *testing.T) {
	// 创建临时目录
	tempDir := t.TempDir()
	
	// 创建文件存储库
	repo := NewFileRepository(tempDir)

	// 创建测试知识库
	kb := &FAQKnowledgeBase{
		ID:          "test_file_kb",
		Name:        "文件测试知识库",
		Description: "用于测试文件存储的知识库",
		Level:       KnowledgeLevelBase,
		AccessLevel: AccessLevelPublic,
		Version:     "1.0.0",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Items: []*FAQItem{
			{
				ID:          "file_faq_001",
				Question:    "测试问题",
				Answer:      "测试答案",
				Keywords:    []string{"测试", "问题"},
				Category:    "测试分类",
				Priority:    5,
				Level:       KnowledgeLevelBase,
				AccessLevel: AccessLevelPublic,
				Version:     "1.0.0",
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
		},
	}

	// 测试保存知识库
	t.Run("保存知识库", func(t *testing.T) {
		err := repo.SaveKnowledgeBase(context.Background(), kb)
		if err != nil {
			t.Fatalf("保存知识库失败: %v", err)
		}

		// 验证文件是否存在
		if !repo.FileExists("test_file_kb") {
			t.Error("知识库文件不存在")
		}
	})

	// 测试加载知识库
	t.Run("加载知识库", func(t *testing.T) {
		loadedKB, err := repo.LoadKnowledgeBase(context.Background(), "test_file_kb")
		if err != nil {
			t.Fatalf("加载知识库失败: %v", err)
		}

		if loadedKB.ID != kb.ID {
			t.Errorf("期望ID为%s，实际为%s", kb.ID, loadedKB.ID)
		}

		if len(loadedKB.Items) != len(kb.Items) {
			t.Errorf("期望FAQ数量为%d，实际为%d", len(kb.Items), len(loadedKB.Items))
		}

		if loadedKB.Items[0].Question != kb.Items[0].Question {
			t.Errorf("期望问题为'%s'，实际为'%s'", kb.Items[0].Question, loadedKB.Items[0].Question)
		}
	})

	// 测试列出知识库
	t.Run("列出知识库", func(t *testing.T) {
		kbs, err := repo.ListKnowledgeBases(context.Background())
		if err != nil {
			t.Fatalf("列出知识库失败: %v", err)
		}

		if len(kbs) == 0 {
			t.Fatal("没有找到知识库")
		}

		found := false
		for _, loadedKB := range kbs {
			if loadedKB.ID == "test_file_kb" {
				found = true
				break
			}
		}

		if !found {
			t.Error("没有找到测试知识库")
		}
	})

	// 测试删除知识库
	t.Run("删除知识库", func(t *testing.T) {
		err := repo.DeleteKnowledgeBase(context.Background(), "test_file_kb")
		if err != nil {
			t.Fatalf("删除知识库失败: %v", err)
		}

		// 验证文件是否已删除
		if repo.FileExists("test_file_kb") {
			t.Error("知识库文件仍然存在")
		}
	})
}

func TestKnowledgeIntegration(t *testing.T) {
	// 创建临时目录
	tempDir := t.TempDir()
	
	// 创建文件存储库
	repo := NewFileRepository(tempDir)
	
	// 创建FAQ管理器
	manager := NewFAQManager()

	// 创建多个知识库
	baseKB := &FAQKnowledgeBase{
		ID:          "base_kb",
		Name:        "基础知识库",
		Level:       KnowledgeLevelBase,
		AccessLevel: AccessLevelPublic,
		Items: []*FAQItem{
			{
				ID:       "base_001",
				Question: "公司地址在哪里？",
				Answer:   "公司地址在北京市朝阳区",
				Keywords: []string{"公司地址", "地址", "位置"},
				Priority: 8,
				Level:    KnowledgeLevelBase,
			},
		},
	}

	roleKB := &FAQKnowledgeBase{
		ID:          "customer_service_kb",
		Name:        "客服知识库",
		Level:       KnowledgeLevelRole,
		AccessLevel: AccessLevelRole,
		Roles:       []string{"customer_service"},
		Items: []*FAQItem{
			{
				ID:       "cs_001",
				Question: "如何处理投诉？",
				Answer:   "投诉处理流程：1.倾听 2.记录 3.处理 4.回复",
				Keywords: []string{"投诉", "处理", "流程"},
				Priority: 9,
				Level:    KnowledgeLevelRole,
				Roles:    []string{"customer_service"},
			},
		},
	}

	// 保存知识库
	err := repo.SaveKnowledgeBase(context.Background(), baseKB)
	if err != nil {
		t.Fatalf("保存基础知识库失败: %v", err)
	}

	err = repo.SaveKnowledgeBase(context.Background(), roleKB)
	if err != nil {
		t.Fatalf("保存角色知识库失败: %v", err)
	}

	// 加载知识库到管理器
	err = manager.LoadKnowledgeBase(context.Background(), baseKB)
	if err != nil {
		t.Fatalf("加载基础知识库失败: %v", err)
	}

	err = manager.LoadKnowledgeBase(context.Background(), roleKB)
	if err != nil {
		t.Fatalf("加载角色知识库失败: %v", err)
	}

	// 测试综合搜索
	t.Run("综合搜索测试", func(t *testing.T) {
		// 搜索基础知识
		query := &FAQSearchQuery{
			Query:    "地址",
			Limit:    5,
			MinScore: 0.1,
		}

		results, err := manager.Search(context.Background(), query)
		if err != nil {
			t.Fatalf("搜索失败: %v", err)
		}

		if len(results) == 0 {
			t.Fatal("没有找到搜索结果")
		}

		// 搜索角色专用知识
		query.Query = "投诉"
		query.Role = "customer_service"

		results, err = manager.Search(context.Background(), query)
		if err != nil {
			t.Fatalf("搜索角色知识失败: %v", err)
		}

		if len(results) == 0 {
			t.Fatal("没有找到角色专用知识")
		}

		// 验证角色权限
		if results[0].Item.Level != KnowledgeLevelRole {
			t.Error("返回的不是角色专用知识")
		}
	})
}
