<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentScope-Golang Simple Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .chat-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #007bff;
            color: white;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            color: #333;
        }
        
        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
        }
        
        .input-area input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .input-area input:focus {
            border-color: #007bff;
        }
        
        .input-area button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .input-area button:hover {
            background: #0056b3;
        }
        
        .input-area button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .api-info {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .api-info h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .api-info a {
            color: #007bff;
            text-decoration: none;
        }
        
        .api-info a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AgentScope-Golang Simple Chat</h1>
            <p>与 AI 助手进行智能对话</p>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message assistant">
                <div class="message-content">
                    您好！我是 AgentScope-Golang 驱动的 AI 助手。有什么可以帮助您的吗？
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入您的消息..." maxlength="1000">
            <button id="sendButton" onclick="sendMessage()">发送</button>
        </div>
        
        <div class="status" id="status">
            正在连接到服务器...
        </div>
        
        <div class="api-info">
            <h3>API 端点信息</h3>
            <p><strong>健康检查:</strong> <a href="/api/v1/health" target="_blank">/api/v1/health</a></p>
            <p><strong>智能体列表:</strong> <a href="/api/v1/agents" target="_blank">/api/v1/agents</a></p>
            <p><strong>管道列表:</strong> <a href="/api/v1/pipelines" target="_blank">/api/v1/pipelines</a></p>
        </div>
    </div>

    <script>
        let isConnected = false;
        
        // 检查服务器连接状态
        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/health');
                if (response.ok) {
                    isConnected = true;
                    updateStatus('已连接到服务器', 'connected');
                } else {
                    throw new Error('服务器响应异常');
                }
            } catch (error) {
                isConnected = false;
                updateStatus('无法连接到服务器', 'error');
            }
        }
        
        // 更新状态显示
        function updateStatus(message, type = '') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
        }
        
        // 添加消息到聊天区域
        function addMessage(content, isUser = false) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + (isUser ? 'user' : 'assistant');
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message || !isConnected) return;
            
            // 禁用输入
            input.disabled = true;
            sendButton.disabled = true;
            
            // 添加用户消息
            addMessage(message, true);
            input.value = '';
            
            try {
                // 发送到管道执行端点
                const response = await fetch('/api/v1/pipelines/simple_chat/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: {
                            type: 'user',
                            sender: 'user_001',
                            content: message
                        }
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.message && result.message.content) {
                        addMessage(result.message.content);
                    } else {
                        addMessage('抱歉，我现在无法回复。');
                    }
                } else {
                    throw new Error('服务器响应错误');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('抱歉，发送消息时出现错误。请稍后重试。');
            } finally {
                // 重新启用输入
                input.disabled = false;
                sendButton.disabled = false;
                input.focus();
            }
        }
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面加载时检查连接
        window.addEventListener('load', function() {
            checkConnection();
            // 定期检查连接状态
            setInterval(checkConnection, 30000);
        });
    </script>
</body>
</html>
