package runtime

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/event"
)

// mockInterceptor 用于测试的模拟拦截器
type mockInterceptor struct {
	name   string
	calls  []string
	mu     sync.Mutex
	errors map[string]error
}

func newMockInterceptor(name string) *mockInterceptor {
	return &mockInterceptor{
		name:   name,
		calls:  make([]string, 0),
		errors: make(map[string]error),
	}
}

func (mi *mockInterceptor) Name() string {
	return mi.name
}

func (mi *mockInterceptor) BeforeRun(ctx context.Context, agentName string, input any) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()
	mi.calls = append(mi.calls, "BeforeRun")
	return mi.errors["BeforeRun"]
}

func (mi *mockInterceptor) AfterRun(ctx context.Context, agentName string, input any, err error) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()
	mi.calls = append(mi.calls, "AfterRun")
	return mi.errors["AfterRun"]
}

func (mi *mockInterceptor) OnEvent(ctx context.Context, agentName string, ev *event.Event) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()
	mi.calls = append(mi.calls, "OnEvent")
	return mi.errors["OnEvent"]
}

func (mi *mockInterceptor) OnError(ctx context.Context, agentName string, err error) error {
	mi.mu.Lock()
	defer mi.mu.Unlock()
	mi.calls = append(mi.calls, "OnError")
	return mi.errors["OnError"]
}

func (mi *mockInterceptor) getCalls() []string {
	mi.mu.Lock()
	defer mi.mu.Unlock()
	result := make([]string, len(mi.calls))
	copy(result, mi.calls)
	return result
}

func (mi *mockInterceptor) setError(method string, err error) {
	mi.mu.Lock()
	defer mi.mu.Unlock()
	mi.errors[method] = err
}

func TestInterceptorChain(t *testing.T) {
	chain := NewInterceptorChain()

	// 测试空链
	if len(chain.List()) != 0 {
		t.Error("新创建的链应该为空")
	}

	// 添加拦截器
	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")

	chain.Add(interceptor1)
	chain.Add(interceptor2)

	names := chain.List()
	if len(names) != 2 {
		t.Errorf("链中应该有 2 个拦截器，实际有 %d 个", len(names))
	}

	if names[0] != "interceptor1" || names[1] != "interceptor2" {
		t.Errorf("拦截器名称不匹配: %v", names)
	}
}

func TestInterceptorChainRemove(t *testing.T) {
	chain := NewInterceptorChain()

	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")
	interceptor3 := newMockInterceptor("interceptor3")

	chain.Add(interceptor1)
	chain.Add(interceptor2)
	chain.Add(interceptor3)

	// 移除中间的拦截器
	removed := chain.Remove("interceptor2")
	if !removed {
		t.Error("应该成功移除拦截器")
	}

	names := chain.List()
	if len(names) != 2 {
		t.Errorf("移除后应该有 2 个拦截器，实际有 %d 个", len(names))
	}

	if names[0] != "interceptor1" || names[1] != "interceptor3" {
		t.Errorf("移除后拦截器名称不匹配: %v", names)
	}

	// 尝试移除不存在的拦截器
	removed = chain.Remove("nonexistent")
	if removed {
		t.Error("移除不存在的拦截器应该返回 false")
	}
}

func TestInterceptorChainClear(t *testing.T) {
	chain := NewInterceptorChain()

	chain.Add(newMockInterceptor("interceptor1"))
	chain.Add(newMockInterceptor("interceptor2"))

	chain.Clear()

	if len(chain.List()) != 0 {
		t.Error("清空后链应该为空")
	}
}

func TestInterceptorChainBeforeRun(t *testing.T) {
	chain := NewInterceptorChain()

	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")

	chain.Add(interceptor1)
	chain.Add(interceptor2)

	ctx := context.Background()
	err := chain.BeforeRun(ctx, "testAgent", "testInput")

	if err != nil {
		t.Errorf("BeforeRun 不应该失败: %v", err)
	}

	// 检查调用顺序
	calls1 := interceptor1.getCalls()
	calls2 := interceptor2.getCalls()

	if len(calls1) != 1 || calls1[0] != "BeforeRun" {
		t.Errorf("interceptor1 调用不正确: %v", calls1)
	}

	if len(calls2) != 1 || calls2[0] != "BeforeRun" {
		t.Errorf("interceptor2 调用不正确: %v", calls2)
	}
}

func TestInterceptorChainBeforeRunError(t *testing.T) {
	chain := NewInterceptorChain()

	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")

	// 设置第一个拦截器返回错误
	interceptor1.setError("BeforeRun", errors.New("测试错误"))

	chain.Add(interceptor1)
	chain.Add(interceptor2)

	ctx := context.Background()
	err := chain.BeforeRun(ctx, "testAgent", "testInput")

	if err == nil {
		t.Error("BeforeRun 应该返回错误")
	}

	if !strings.Contains(err.Error(), "interceptor1") {
		t.Errorf("错误信息应该包含拦截器名称: %v", err)
	}

	// 第二个拦截器不应该被调用
	calls2 := interceptor2.getCalls()
	if len(calls2) != 0 {
		t.Errorf("第二个拦截器不应该被调用: %v", calls2)
	}
}

func TestInterceptorChainAfterRun(t *testing.T) {
	chain := NewInterceptorChain()

	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")

	chain.Add(interceptor1)
	chain.Add(interceptor2)

	ctx := context.Background()
	err := chain.AfterRun(ctx, "testAgent", "testInput", nil)

	if err != nil {
		t.Errorf("AfterRun 不应该失败: %v", err)
	}

	// 检查调用（AfterRun 应该逆序执行）
	calls1 := interceptor1.getCalls()
	calls2 := interceptor2.getCalls()

	if len(calls1) != 1 || calls1[0] != "AfterRun" {
		t.Errorf("interceptor1 调用不正确: %v", calls1)
	}

	if len(calls2) != 1 || calls2[0] != "AfterRun" {
		t.Errorf("interceptor2 调用不正确: %v", calls2)
	}
}

func TestInterceptorChainOnEvent(t *testing.T) {
	chain := NewInterceptorChain()

	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")

	chain.Add(interceptor1)
	chain.Add(interceptor2)

	ctx := context.Background()
	ev := event.NewTokenEvent("test", false)
	err := chain.OnEvent(ctx, "testAgent", ev)

	if err != nil {
		t.Errorf("OnEvent 不应该失败: %v", err)
	}

	// 检查调用
	calls1 := interceptor1.getCalls()
	calls2 := interceptor2.getCalls()

	if len(calls1) != 1 || calls1[0] != "OnEvent" {
		t.Errorf("interceptor1 调用不正确: %v", calls1)
	}

	if len(calls2) != 1 || calls2[0] != "OnEvent" {
		t.Errorf("interceptor2 调用不正确: %v", calls2)
	}
}

func TestInterceptorChainOnError(t *testing.T) {
	chain := NewInterceptorChain()

	interceptor1 := newMockInterceptor("interceptor1")
	interceptor2 := newMockInterceptor("interceptor2")

	chain.Add(interceptor1)
	chain.Add(interceptor2)

	ctx := context.Background()
	testErr := errors.New("测试错误")
	err := chain.OnError(ctx, "testAgent", testErr)

	if err != nil {
		t.Errorf("OnError 不应该失败: %v", err)
	}

	// 检查调用
	calls1 := interceptor1.getCalls()
	calls2 := interceptor2.getCalls()

	if len(calls1) != 1 || calls1[0] != "OnError" {
		t.Errorf("interceptor1 调用不正确: %v", calls1)
	}

	if len(calls2) != 1 || calls2[0] != "OnError" {
		t.Errorf("interceptor2 调用不正确: %v", calls2)
	}
}

func TestLoggingInterceptor(t *testing.T) {
	var buf bytes.Buffer
	logger := log.New(&buf, "", 0)

	interceptor := NewLoggingInterceptor("test-logger", logger)

	if interceptor.Name() != "test-logger" {
		t.Errorf("拦截器名称不匹配: got %s, want %s", interceptor.Name(), "test-logger")
	}

	ctx := context.Background()

	// 测试 BeforeRun
	err := interceptor.BeforeRun(ctx, "testAgent", "testInput")
	if err != nil {
		t.Errorf("BeforeRun 不应该失败: %v", err)
	}

	// 测试 AfterRun（成功）
	err = interceptor.AfterRun(ctx, "testAgent", "testInput", nil)
	if err != nil {
		t.Errorf("AfterRun 不应该失败: %v", err)
	}

	// 测试 AfterRun（失败）
	testErr := errors.New("测试错误")
	err = interceptor.AfterRun(ctx, "testAgent", "testInput", testErr)
	if err != nil {
		t.Errorf("AfterRun 不应该失败: %v", err)
	}

	// 测试 OnEvent
	ev := event.NewTokenEvent("test", false)
	err = interceptor.OnEvent(ctx, "testAgent", ev)
	if err != nil {
		t.Errorf("OnEvent 不应该失败: %v", err)
	}

	// 测试 OnError
	err = interceptor.OnError(ctx, "testAgent", testErr)
	if err != nil {
		t.Errorf("OnError 不应该失败: %v", err)
	}

	// 检查日志输出
	logOutput := buf.String()
	if !strings.Contains(logOutput, "开始运行") {
		t.Error("日志应该包含开始运行信息")
	}
	if !strings.Contains(logOutput, "运行成功") {
		t.Error("日志应该包含运行成功信息")
	}
	if !strings.Contains(logOutput, "运行失败") {
		t.Error("日志应该包含运行失败信息")
	}
	if !strings.Contains(logOutput, "产生事件") {
		t.Error("日志应该包含事件信息")
	}
	if !strings.Contains(logOutput, "发生错误") {
		t.Error("日志应该包含错误信息")
	}
}

func TestMetricsInterceptor(t *testing.T) {
	interceptor := NewMetricsInterceptor("test-metrics")

	if interceptor.Name() != "test-metrics" {
		t.Errorf("拦截器名称不匹配: got %s, want %s", interceptor.Name(), "test-metrics")
	}

	ctx := context.Background()
	agentName := "testAgent"

	// 测试 BeforeRun
	err := interceptor.BeforeRun(ctx, agentName, "testInput")
	if err != nil {
		t.Errorf("BeforeRun 不应该失败: %v", err)
	}

	// 等待一小段时间
	time.Sleep(10 * time.Millisecond)

	// 测试 AfterRun（成功）
	err = interceptor.AfterRun(ctx, agentName, "testInput", nil)
	if err != nil {
		t.Errorf("AfterRun 不应该失败: %v", err)
	}

	// 测试 OnEvent
	ev := event.NewTokenEvent("test", false)
	err = interceptor.OnEvent(ctx, agentName, ev)
	if err != nil {
		t.Errorf("OnEvent 不应该失败: %v", err)
	}

	// 测试 OnError
	testErr := errors.New("测试错误")
	err = interceptor.OnError(ctx, agentName, testErr)
	if err != nil {
		t.Errorf("OnError 不应该失败: %v", err)
	}

	// 检查指标
	metrics := interceptor.GetMetrics(agentName)

	if metrics["run_count"] != int64(1) {
		t.Errorf("运行次数不匹配: got %v, want %d", metrics["run_count"], 1)
	}

	if metrics["error_count"] != int64(1) {
		t.Errorf("错误次数不匹配: got %v, want %d", metrics["error_count"], 1)
	}

	duration, ok := metrics["duration"].(time.Duration)
	if !ok || duration <= 0 {
		t.Errorf("持续时间应该大于 0: got %v", duration)
	}

	eventCount, ok := metrics["event_count"].(map[event.Type]int64)
	if !ok {
		t.Error("事件计数应该是 map 类型")
	} else if eventCount[event.EventToken] != 1 {
		t.Errorf("Token 事件计数不匹配: got %d, want %d", eventCount[event.EventToken], 1)
	}
}

func TestMetricsInterceptorMultipleAgents(t *testing.T) {
	interceptor := NewMetricsInterceptor("test-metrics")

	ctx := context.Background()

	// 测试多个 Agent
	agents := []string{"agent1", "agent2", "agent3"}

	for _, agentName := range agents {
		err := interceptor.BeforeRun(ctx, agentName, "input")
		if err != nil {
			t.Errorf("BeforeRun 失败: %v", err)
		}

		err = interceptor.AfterRun(ctx, agentName, "input", nil)
		if err != nil {
			t.Errorf("AfterRun 失败: %v", err)
		}
	}

	// 检查所有指标
	allMetrics := interceptor.GetAllMetrics()

	if len(allMetrics) != len(agents) {
		t.Errorf("指标数量不匹配: got %d, want %d", len(allMetrics), len(agents))
	}

	for _, agentName := range agents {
		metrics, exists := allMetrics[agentName]
		if !exists {
			t.Errorf("Agent %s 的指标不存在", agentName)
			continue
		}

		if metrics["run_count"] != int64(1) {
			t.Errorf("Agent %s 运行次数不匹配: got %v, want %d", agentName, metrics["run_count"], 1)
		}
	}
}

func TestMetricsInterceptorReset(t *testing.T) {
	interceptor := NewMetricsInterceptor("test-metrics")

	ctx := context.Background()
	agentName := "testAgent"

	// 生成一些指标
	interceptor.BeforeRun(ctx, agentName, "input")
	interceptor.AfterRun(ctx, agentName, "input", nil)

	// 重置指标
	interceptor.Reset()

	// 检查指标是否被重置
	metrics := interceptor.GetMetrics(agentName)

	if metrics["run_count"] != int64(0) {
		t.Errorf("重置后运行次数应该为 0: got %v", metrics["run_count"])
	}

	if metrics["error_count"] != int64(0) {
		t.Errorf("重置后错误次数应该为 0: got %v", metrics["error_count"])
	}

	if metrics["duration"] != time.Duration(0) {
		t.Errorf("重置后持续时间应该为 0: got %v", metrics["duration"])
	}
}

func TestInterceptorChainConcurrency(t *testing.T) {
	chain := NewInterceptorChain()

	// 添加多个拦截器
	for i := 0; i < 10; i++ {
		interceptor := newMockInterceptor(fmt.Sprintf("interceptor%d", i))
		chain.Add(interceptor)
	}

	ctx := context.Background()
	var wg sync.WaitGroup

	// 并发执行拦截器方法
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			agentName := fmt.Sprintf("agent%d", id%5)

			chain.BeforeRun(ctx, agentName, "input")
			chain.OnEvent(ctx, agentName, event.NewTokenEvent("test", false))
			chain.AfterRun(ctx, agentName, "input", nil)
		}(i)
	}

	wg.Wait()

	// 验证没有发生竞态条件
	names := chain.List()
	if len(names) != 10 {
		t.Errorf("并发执行后拦截器数量不匹配: got %d, want %d", len(names), 10)
	}
}
