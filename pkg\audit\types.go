package audit

import (
	"context"
	"time"
)

// Record 表示单条审计记录
type Record struct {
	// ID 记录唯一标识符
	ID string `json:"id" db:"id"`
	// SessionID 会话标识符
	SessionID string `json:"session_id" db:"session_id"`
	// UserID 用户标识符
	UserID string `json:"user_id" db:"user_id"`
	// AgentID Agent标识符
	AgentID string `json:"agent_id" db:"agent_id"`
	// Role 消息角色：user/assistant/system/tool
	Role string `json:"role" db:"role"`
	// MsgType 消息类型：text/tool_result/error/token/final
	MsgType string `json:"msg_type" db:"msg_type"`
	// Content 消息内容（可能已脱敏/加密）
	Content string `json:"content" db:"content"`
	// ContentHash 内容哈希值，用于去重或敏感场景不存明文
	ContentHash string `json:"content_hash" db:"content_hash"`
	// EventType 对应event.Type（token/final/error等）
	EventType string `json:"event_type" db:"event_type"`
	// ToolName 工具名称（如果是工具调用相关）
	ToolName string `json:"tool_name" db:"tool_name"`
	// ErrorCode 错误代码（如果是错误事件）
	ErrorCode string `json:"error_code" db:"error_code"`
	// TraceID 链路追踪ID
	TraceID string `json:"trace_id" db:"trace_id"`
	// SpanID 链路追踪Span ID
	SpanID string `json:"span_id" db:"span_id"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// Session 表示会话信息
type Session struct {
	// SessionID 会话唯一标识符
	SessionID string `json:"session_id" db:"session_id"`
	// UserID 用户标识符
	UserID string `json:"user_id" db:"user_id"`
	// CreatedAt 会话创建时间
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	// LastActiveAt 最后活跃时间
	LastActiveAt time.Time `json:"last_active_at" db:"last_active_at"`
	// Metadata 会话元数据
	Metadata map[string]any `json:"metadata" db:"metadata"`
}

// Query 表示查询条件
type Query struct {
	// SessionID 按会话ID过滤
	SessionID *string `json:"session_id,omitempty"`
	// UserID 按用户ID过滤
	UserID *string `json:"user_id,omitempty"`
	// AgentID 按Agent ID过滤
	AgentID *string `json:"agent_id,omitempty"`
	// Types 按消息类型过滤
	Types []string `json:"types,omitempty"`
	// Since 开始时间
	Since *time.Time `json:"since,omitempty"`
	// Until 结束时间
	Until *time.Time `json:"until,omitempty"`
	// Keyword 关键词搜索（简单LIKE/ILIKE）
	Keyword string `json:"keyword,omitempty"`
	// Limit 限制返回数量
	Limit int `json:"limit,omitempty"`
	// Offset 偏移量
	Offset int `json:"offset,omitempty"`
}

// QueryResult 表示查询结果
type QueryResult struct {
	// Records 记录列表
	Records []*Record `json:"records"`
	// Total 总数量
	Total int `json:"total"`
	// HasMore 是否有更多数据
	HasMore bool `json:"has_more"`
}

// Store 审计存储接口
type Store interface {
	// SaveMessage 保存消息记录
	SaveMessage(ctx context.Context, r *Record) error
	
	// SaveSession 保存会话信息
	SaveSession(ctx context.Context, sessionID, userID string, meta map[string]any) error
	
	// TouchSession 更新会话最后活跃时间
	TouchSession(ctx context.Context, sessionID string, at time.Time) error
	
	// QueryMessages 查询消息记录
	QueryMessages(ctx context.Context, q Query) (*QueryResult, error)
	
	// QuerySessions 查询会话列表
	QuerySessions(ctx context.Context, userID string, limit, offset int) ([]*Session, int, error)
	
	// RunRetention 执行数据保留策略清理
	RunRetention(ctx context.Context) error
	
	// Close 关闭存储连接
	Close() error
}

// Config 审计配置
type Config struct {
	// Enabled 是否启用审计
	Enabled bool `yaml:"enabled" json:"enabled"`
	// Driver 数据库驱动：sqlite | postgres
	Driver string `yaml:"driver" json:"driver"`
	// DSN 数据库连接字符串
	DSN string `yaml:"dsn" json:"dsn"`
	// Retention 数据保留配置
	Retention RetentionConfig `yaml:"retention" json:"retention"`
	// Privacy 隐私保护配置
	Privacy PrivacyConfig `yaml:"privacy" json:"privacy"`
	// Batch 批处理配置
	Batch BatchConfig `yaml:"batch" json:"batch"`
	// Web Web API配置
	Web WebConfig `yaml:"web" json:"web"`
}

// RetentionConfig 数据保留配置
type RetentionConfig struct {
	// Enabled 是否启用数据保留策略
	Enabled bool `yaml:"enabled" json:"enabled"`
	// MaxDays 最大保留天数
	MaxDays int `yaml:"max_days" json:"max_days"`
	// Cron 执行计划（cron表达式）
	Cron string `yaml:"cron" json:"cron"`
}

// PrivacyConfig 隐私保护配置
type PrivacyConfig struct {
	// RedactPII 是否脱敏PII信息
	RedactPII bool `yaml:"redact_pii" json:"redact_pii"`
	// PIIPatterns PII模式列表
	PIIPatterns []string `yaml:"pii_patterns" json:"pii_patterns"`
	// HashContent 是否仅存储内容哈希
	HashContent bool `yaml:"hash_content" json:"hash_content"`
	// EncryptAtRest 是否启用静态加密
	EncryptAtRest bool `yaml:"encrypt_at_rest" json:"encrypt_at_rest"`
	// EncryptKeyEnv 加密密钥环境变量名
	EncryptKeyEnv string `yaml:"encrypt_key_env" json:"encrypt_key_env"`
}

// BatchConfig 批处理配置
type BatchConfig struct {
	// Async 是否异步处理
	Async bool `yaml:"async" json:"async"`
	// ChanBuffer 通道缓冲区大小
	ChanBuffer int `yaml:"chan_buffer" json:"chan_buffer"`
	// FlushInterval 刷新间隔
	FlushInterval time.Duration `yaml:"flush_interval" json:"flush_interval"`
}

// WebConfig Web API配置
type WebConfig struct {
	// AllowReadAPI 是否允许读取API
	AllowReadAPI bool `yaml:"allow_read_api" json:"allow_read_api"`
	// AllowDeleteAPI 是否允许删除API
	AllowDeleteAPI bool `yaml:"allow_delete_api" json:"allow_delete_api"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Enabled: false,
		Driver:  "sqlite",
		DSN:     "file:audit.db?_journal_mode=WAL",
		Retention: RetentionConfig{
			Enabled: true,
			MaxDays: 90,
			Cron:    "@daily",
		},
		Privacy: PrivacyConfig{
			RedactPII:     true,
			PIIPatterns:   []string{"email", "phone"},
			HashContent:   false,
			EncryptAtRest: false,
			EncryptKeyEnv: "AUDIT_AES_KEY",
		},
		Batch: BatchConfig{
			Async:         true,
			ChanBuffer:    1024,
			FlushInterval: time.Second,
		},
		Web: WebConfig{
			AllowReadAPI:   false,
			AllowDeleteAPI: false,
		},
	}
}
