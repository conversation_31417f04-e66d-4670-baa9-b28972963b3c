package agent

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/llm"
)

// MockPromptLLMClient 模拟LLM客户端用于提示词驱动Agent测试
type MockPromptLLMClient struct{}

func (m *MockPromptLLMClient) Generate(ctx context.Context, request *llm.GenerateRequest) (*llm.GenerateResponse, error) {
	// 根据请求内容返回不同的模拟响应
	userContent := ""
	for _, msg := range request.Messages {
		if msg.Role == "user" {
			if content, ok := msg.Content.(string); ok {
				userContent = content
			}
		}
		if msg.Role == "system" {
			// 系统消息处理
		}
	}

	var responseContent string

	// 根据用户输入和系统提示词生成合适的回复
	if strings.Contains(userContent, "你好") {
		responseContent = "您好！很高兴为您服务，我是您的专属客服助手。请问有什么可以帮助您的吗？"
	} else if strings.Contains(userContent, "登录") || strings.Contains(userContent, "密码") {
		responseContent = "我理解您遇到的登录问题。让我来帮您解决：\n1. 请确认用户名和密码是否正确\n2. 检查网络连接是否正常\n3. 尝试清除浏览器缓存\n如果问题仍然存在，我可以为您创建技术支持工单。"
	} else if strings.Contains(userContent, "价格") || strings.Contains(userContent, "产品") {
		responseContent = "关于产品价格咨询，我很乐意为您介绍。我们有多种产品套餐可供选择，具体价格会根据您的需求而定。请问您对哪个产品感兴趣？我可以为您提供详细的报价方案。"
	} else if strings.Contains(userContent, "投诉") || strings.Contains(userContent, "难用") {
		responseContent = "非常抱歉给您带来不便，我完全理解您的感受。您的反馈对我们非常重要。让我立即为您处理这个问题，并为您创建一个优先级工单，确保得到妥善解决。同时，我会将您的意见反馈给相关部门进行改进。"
	} else {
		responseContent = "我理解您的问题，让我来为您提供专业的帮助。请您详细描述一下具体情况，这样我能更好地为您解决问题。"
	}

	return &llm.GenerateResponse{
		Choices: []*llm.Choice{
			{
				Message: &llm.ChatMessage{
					Role:    "assistant",
					Content: responseContent,
				},
			},
		},
	}, nil
}

func (m *MockPromptLLMClient) GetModelInfo() *llm.ModelInfo {
	return &llm.ModelInfo{
		ID:      "mock-model",
		Object:  "model",
		OwnedBy: "mock",
	}
}

func (m *MockPromptLLMClient) Close() error {
	return nil
}

func (m *MockPromptLLMClient) GenerateMultiModal(ctx context.Context, request *llm.MultiModalGenerateRequest) (*llm.GenerateResponse, error) {
	return nil, fmt.Errorf("多模态生成未实现")
}

func (m *MockPromptLLMClient) GenerateStream(ctx context.Context, request *llm.GenerateRequest) (<-chan *llm.StreamResponse, error) {
	return nil, fmt.Errorf("流式生成未实现")
}

func (m *MockPromptLLMClient) GenerateWithTools(ctx context.Context, request *llm.GenerateRequest, tools []llm.ToolDefinition) (*llm.GenerateResponse, error) {
	return nil, fmt.Errorf("工具生成未实现")
}

// TestPromptDrivenAgentCreation 测试提示词驱动Agent的创建
func TestPromptDrivenAgentCreation(t *testing.T) {
	// 创建测试配置
	agentConfig := &config.PromptDrivenAgentConfig{
		Name:        "测试客服Agent",
		Description: "用于测试的客服Agent",
		BaseRole:    "你是一个专业的客服代表",
		AiCapabilities: []string{
			"emotion_analyst",
			"problem_classifier",
			"empathetic_responder",
		},
		Tools: []string{"ticket_manager"},
		PersonalityTraits: []string{
			"耐心细致",
			"专业可靠",
		},
		BehavioralConstraints: []string{
			"始终保持礼貌",
			"保护用户隐私",
		},
		BusinessConfig: &config.BusinessConfig{
			Classification: &config.ClassificationConfig{
				Categories: []*config.CategoryConfig{
					{
						Name:        "技术支持",
						Description: "技术问题和故障",
						Keywords:    []string{"登录", "密码", "故障"},
						Priority:    "high",
					},
				},
			},
			FAQs: []config.FAQ{
				{
					Question: "营业时间",
					Answer:   "工作日9:00-18:00",
					Keywords: []string{"营业时间", "工作时间"},
				},
			},
		},
	}

	// 创建Mock LLM客户端
	mockLLM := &MockPromptLLMClient{}

	// 创建Agent
	agent, err := NewPromptDrivenAgent("test_agent", agentConfig, mockLLM)
	if err != nil {
		t.Fatalf("创建Agent失败: %v", err)
	}

	// 验证Agent属性
	ctx := context.Background()
	if agent.Name(ctx) != "test_agent" {
		t.Errorf("Agent名称不正确，期望: test_agent, 实际: %s", agent.Name(ctx))
	}

	// 验证系统提示词是否包含预期内容
	systemPrompt := agent.GetSystemPrompt()
	if !strings.Contains(systemPrompt, "专业的客服代表") {
		t.Errorf("系统提示词不包含基础角色定义")
	}
	if !strings.Contains(systemPrompt, "情绪分析师") && !strings.Contains(systemPrompt, "emotion_analyst") {
		t.Errorf("系统提示词不包含情绪分析师能力，实际内容: %s", systemPrompt)
	}
	if !strings.Contains(systemPrompt, "耐心细致") {
		t.Errorf("系统提示词不包含个性特征")
	}
}

// TestPromptDrivenAgentRun 测试Agent运行
func TestPromptDrivenAgentRun(t *testing.T) {
	// 创建简化的测试配置
	agentConfig := &config.PromptDrivenAgentConfig{
		Name:        "测试Agent",
		Description: "测试用Agent",
		BaseRole:    "你是一个测试助手",
		AiCapabilities: []string{
			"professional_service",
			"empathetic_responder",
		},
		PersonalityTraits: []string{"友好", "专业"},
	}

	mockLLM := &MockPromptLLMClient{}
	agent, err := NewPromptDrivenAgent("test", agentConfig, mockLLM)
	if err != nil {
		t.Fatalf("创建Agent失败: %v", err)
	}

	// 测试不同的输入
	testCases := []struct {
		input    string
		expected []string // 期望在事件中出现的关键词
	}{
		{
			input:    "你好",
			expected: []string{"thought", "analysis", "response", "final"},
		},
		{
			input:    "我的账户登录不了",
			expected: []string{"thought", "analysis", "response", "final"},
		},
		{
			input:    "产品价格是多少",
			expected: []string{"thought", "analysis", "response", "final"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			ctx := context.Background()
			iterator := agent.Run(ctx, tc.input)

			// 收集所有事件
			var events []string
			for {
				event, ok := iterator.Next()
				if !ok {
					break
				}
				events = append(events, string(event.Type))
				if event.Type == "final" {
					break
				}
			}

			// 验证期望的事件类型
			for _, expectedEvent := range tc.expected {
				found := false
				for _, actualEvent := range events {
					if actualEvent == expectedEvent {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("输入 '%s' 缺少期望的事件: %s, 实际事件: %v", tc.input, expectedEvent, events)
				}
			}
		})
	}
}

// TestSystemPromptBuilding 测试系统提示词构建
func TestSystemPromptBuilding(t *testing.T) {
	agentConfig := &config.PromptDrivenAgentConfig{
		BaseRole: "你是一个专业客服",
		AiCapabilities: []string{
			"emotion_analyst",
			"problem_classifier",
		},
		PersonalityTraits:     []string{"耐心", "专业"},
		BehavioralConstraints: []string{"保持礼貌", "保护隐私"},
		BusinessConfig: &config.BusinessConfig{
			Classification: &config.ClassificationConfig{
				Categories: []*config.CategoryConfig{
					{
						Name:        "技术支持",
						Description: "技术相关问题",
						Keywords:    []string{"bug", "错误"},
						Priority:    "high",
					},
				},
			},
		},
	}

	mockLLM := &MockPromptLLMClient{}
	agent, err := NewPromptDrivenAgent("test", agentConfig, mockLLM)
	if err != nil {
		t.Fatalf("创建Agent失败: %v", err)
	}

	systemPrompt := agent.GetSystemPrompt()

	// 验证系统提示词包含各个组件
	expectedComponents := []string{
		"专业客服",   // 基础角色
		"情绪分析师",  // AI能力
		"问题分类专家", // AI能力
		"耐心",     // 个性特征
		"保持礼貌",   // 行为约束
		"技术支持",   // 业务配置
		"业务知识库",  // 业务上下文
	}

	for _, component := range expectedComponents {
		if !strings.Contains(systemPrompt, component) {
			t.Errorf("系统提示词缺少组件: %s", component)
		}
	}

	t.Logf("系统提示词长度: %d 字符", len(systemPrompt))
}
