package errors

import (
	"fmt"
	"time"
)

// New creates a new AgentScopeError
func New(errorType ErrorType, code, message string) *AgentScopeError {
	return &AgentScopeError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// Newf creates a new AgentScopeError with formatted message
func Newf(errorType ErrorType, code, format string, args ...interface{}) *AgentScopeError {
	return &AgentScopeError{
		Type:      errorType,
		Code:      code,
		Message:   fmt.Sprintf(format, args...),
		Timestamp: time.Now(),
	}
}

// Wrap wraps an existing error with AgentScope error information
func Wrap(err error, errorType ErrorType, code, message string) *AgentScopeError {
	return &AgentScopeError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		Cause:     err,
	}
}

// Wrapf wraps an existing error with formatted message
func Wrapf(err error, errorType ErrorType, code, format string, args ...interface{}) *AgentScopeError {
	return &AgentScopeError{
		Type:      errorType,
		Code:      code,
		Message:   fmt.Sprintf(format, args...),
		Timestamp: time.Now(),
		Cause:     err,
	}
}

// Common error constructors

// NewValidationError creates a validation error
func NewValidationError(code, message string) *AgentScopeError {
	return New(ErrorTypeValidation, code, message)
}

// NewNetworkError creates a network error
func NewNetworkError(code, message string) *AgentScopeError {
	return New(ErrorTypeNetwork, code, message)
}

// NewConnectionError creates a connection error
func NewConnectionError(code, message string) *AgentScopeError {
	return New(ErrorTypeNetwork, code, message)
}

// NewTimeoutError creates a timeout error
func NewTimeoutError(code, message string) *AgentScopeError {
	return New(ErrorTypeTimeout, code, message)
}

// NewAuthError creates an authentication error
func NewAuthError(code, message string) *AgentScopeError {
	return New(ErrorTypeAuth, code, message)
}

// NewInternalError creates an internal error
func NewInternalError(code, message string) *AgentScopeError {
	return New(ErrorTypeInternal, code, message)
}

// NewLLMError creates an LLM service error
func NewLLMError(code, message string) *AgentScopeError {
	return New(ErrorTypeLLM, code, message)
}

// NewToolError creates a tool execution error
func NewToolError(code, message string) *AgentScopeError {
	return New(ErrorTypeTool, code, message)
}

// NewAgentError creates an agent error
func NewAgentError(code, message string) *AgentScopeError {
	return New(ErrorTypeAgent, code, message)
}

// NewPipelineError creates a pipeline error
func NewPipelineError(code, message string) *AgentScopeError {
	return New(ErrorTypePipeline, code, message)
}

// NewNotFoundError creates a not found error
func NewNotFoundError(code, message string) *AgentScopeError {
	return New(ErrorTypeValidation, code, message)
}
