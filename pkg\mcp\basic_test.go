package mcp

import (
	"context"
	"encoding/json"
	"testing"
	"time"
)

// TestMCPConfig 测试MCP配置
func TestMCPConfig(t *testing.T) {
	config := &MCPConfig{
		ServerCommand: []string{"node", "server.js"},
		ServerArgs:    []string{"--port", "8080"},
		Timeout:       30 * time.Second,
		Environment: map[string]string{
			"NODE_ENV": "production",
		},
		WorkingDir: "/tmp/mcp",
		MaxRetries: 3,
	}

	if len(config.ServerCommand) != 2 || config.ServerCommand[0] != "node" {
		t.Errorf("Expected ServerCommand to be ['node', 'server.js'], got %v", config.ServerCommand)
	}

	if config.Timeout != 30*time.Second {
		t.Errorf("Expected Timeout to be 30s, got %v", config.Timeout)
	}

	if config.Environment["NODE_ENV"] != "production" {
		t.<PERSON><PERSON>("Expected NODE_ENV to be 'production', got %s", config.Environment["NODE_ENV"])
	}

	if config.MaxRetries != 3 {
		t.<PERSON><PERSON><PERSON>("Expected MaxRetries to be 3, got %d", config.MaxRetries)
	}
}

// TestMCPMessage 测试MCP消息结构
func TestMCPMessage(t *testing.T) {
	msg := &MCPMessage{
		ID:     "test-123",
		Method: "tools/list",
		Params: map[string]interface{}{
			"limit": 10,
			"type":  "function",
		},
	}

	// 测试JSON序列化
	data, err := json.Marshal(msg)
	if err != nil {
		t.Fatalf("Failed to marshal MCPMessage: %v", err)
	}

	// 测试JSON反序列化
	var unmarshaled MCPMessage
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MCPMessage: %v", err)
	}

	if unmarshaled.ID != msg.ID {
		t.Errorf("Expected ID to be %s, got %s", msg.ID, unmarshaled.ID)
	}

	if unmarshaled.Method != msg.Method {
		t.Errorf("Expected Method to be %s, got %s", msg.Method, unmarshaled.Method)
	}

	// 类型断言Params
	params, ok := unmarshaled.Params.(map[string]interface{})
	if !ok {
		t.Errorf("Expected Params to be map[string]interface{}")
		return
	}

	// JSON数字会变成float64
	if params["limit"] != float64(10) {
		t.Errorf("Expected limit to be 10, got %v", params["limit"])
	}

	if params["type"] != "function" {
		t.Errorf("Expected type to be 'function', got %v", params["type"])
	}
}

// TestMCPResponse 测试MCP响应结构
func TestMCPResponse(t *testing.T) {
	response := &MCPResponse{
		ID: "test-123",
		Result: map[string]interface{}{
			"tools": []interface{}{
				map[string]interface{}{
					"name":        "calculator",
					"description": "A simple calculator",
				},
			},
		},
	}

	// 测试JSON序列化
	data, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal MCPResponse: %v", err)
	}

	// 测试JSON反序列化
	var unmarshaled MCPResponse
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MCPResponse: %v", err)
	}

	if unmarshaled.ID != response.ID {
		t.Errorf("Expected ID to be %s, got %s", response.ID, unmarshaled.ID)
	}

	// 类型断言Result
	result, ok := unmarshaled.Result.(map[string]interface{})
	if !ok {
		t.Errorf("Expected Result to be map[string]interface{}")
		return
	}

	// 检查结果结构
	tools, ok := result["tools"].([]interface{})
	if !ok {
		t.Errorf("Expected tools to be an array")
	}

	if len(tools) != 1 {
		t.Errorf("Expected 1 tool, got %d", len(tools))
	}
}

// TestMCPError 测试MCP错误结构
func TestMCPError(t *testing.T) {
	mcpErr := &MCPError{
		Code:    -32601,
		Message: "Method not found",
		Data: map[string]interface{}{
			"method": "unknown/method",
		},
	}

	// 测试JSON序列化
	data, err := json.Marshal(mcpErr)
	if err != nil {
		t.Fatalf("Failed to marshal MCPError: %v", err)
	}

	// 测试JSON反序列化
	var unmarshaled MCPError
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MCPError: %v", err)
	}

	if unmarshaled.Code != mcpErr.Code {
		t.Errorf("Expected Code to be %d, got %d", mcpErr.Code, unmarshaled.Code)
	}

	if unmarshaled.Message != mcpErr.Message {
		t.Errorf("Expected Message to be %s, got %s", mcpErr.Message, unmarshaled.Message)
	}

	// 类型断言Data
	dataMap, ok := unmarshaled.Data.(map[string]interface{})
	if !ok {
		t.Errorf("Expected Data to be map[string]interface{}, got %T", unmarshaled.Data)
		return
	}

	if dataMap["method"] != "unknown/method" {
		t.Errorf("Expected method to be 'unknown/method', got %v", dataMap["method"])
	}
}

// TestMCPResourceChange 测试MCP资源变更结构
func TestMCPResourceChange(t *testing.T) {
	change := &MCPResourceChange{
		Type: "resource/updated",
		URI:  "file:///path/to/resource",
		Metadata: map[string]interface{}{
			"content": "updated content",
			"version": 2,
		},
	}

	// 测试JSON序列化
	data, err := json.Marshal(change)
	if err != nil {
		t.Fatalf("Failed to marshal MCPResourceChange: %v", err)
	}

	// 测试JSON反序列化
	var unmarshaled MCPResourceChange
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal MCPResourceChange: %v", err)
	}

	if unmarshaled.Type != change.Type {
		t.Errorf("Expected Type to be %s, got %s", change.Type, unmarshaled.Type)
	}

	if unmarshaled.URI != change.URI {
		t.Errorf("Expected URI to be %s, got %s", change.URI, unmarshaled.URI)
	}

	if unmarshaled.Metadata["content"] != "updated content" {
		t.Errorf("Expected content to be 'updated content', got %v", unmarshaled.Metadata["content"])
	}

	if unmarshaled.Metadata["version"] != float64(2) {
		t.Errorf("Expected version to be 2, got %v", unmarshaled.Metadata["version"])
	}
}

// TestNewDefaultMCPClient 测试创建默认MCP客户端
func TestNewDefaultMCPClient(t *testing.T) {
	client := NewDefaultMCPClient()

	if client == nil {
		t.Fatal("Expected client to be non-nil")
	}

	if client.pendingReqs == nil {
		t.Error("Expected pendingReqs to be initialized")
	}

	if client.subscribers == nil {
		t.Error("Expected subscribers to be initialized")
	}

	if client.logger == nil {
		t.Error("Expected logger to be initialized")
	}
}

// TestMCPClientBasicOperations 测试MCP客户端基本操作
func TestMCPClientBasicOperations(t *testing.T) {
	client := NewDefaultMCPClient()

	// 测试未连接状态
	if client.IsConnected() {
		t.Error("Expected client to be disconnected initially")
	}

	// 测试配置验证
	err := client.Connect(context.Background(), nil)
	if err == nil {
		t.Error("Expected error when connecting with nil config")
	}

	// 测试有效配置
	config := &MCPConfig{
		ServerCommand: []string{"node", "server.js"},
		Timeout:       30 * time.Second,
	}

	// 注意：这里不实际连接，只是测试配置验证
	// 实际连接需要真实的MCP服务器
	if len(config.ServerCommand) == 0 {
		t.Error("ServerCommand should not be empty")
	}

	if config.Timeout <= 0 {
		t.Error("Timeout should be positive")
	}
}

// TestMCPToolAdapter 测试MCP工具适配器基础功能
func TestMCPToolAdapter(t *testing.T) {
	// 创建模拟的MCP工具
	mcpTool := &MCPTool{
		Name:        "test_tool",
		Description: "A test tool",
		InputSchema: &MCPSchema{
			Type: "object",
			Properties: map[string]*MCPProperty{
				"input": {
					Type:        "string",
					Description: "Input parameter",
				},
			},
		},
	}

	// 创建模拟的MCP客户端
	client := NewDefaultMCPClient()

	adapter := NewMCPToolAdapter(mcpTool, client)

	if adapter == nil {
		t.Fatal("Expected adapter to be non-nil")
	}

	// 测试工具名称
	if adapter.Name() != "test_tool" {
		t.Errorf("Expected tool name to be 'test_tool', got %s", adapter.Name())
	}

	// 测试工具描述
	if adapter.Description() != "A test tool" {
		t.Errorf("Expected tool description to be 'A test tool', got %s", adapter.Description())
	}
}
