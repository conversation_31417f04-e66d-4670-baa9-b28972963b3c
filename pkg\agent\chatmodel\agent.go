package chatmodel

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// ChatModelAgent 基于聊天模型的智能体，实现 ReAct 模式
type ChatModelAgent struct {
	*agent.BaseAgent
	model         llm.ChatModel
	systemPrompt  string
	maxIterations int
	temperature   *float64
	maxTokens     *int
}

// Config 聊天模型智能体配置
type Config struct {
	Name          string        // 智能体名称
	Description   string        // 智能体描述
	Model         llm.ChatModel // 聊天模型
	SystemPrompt  string        // 系统提示词
	MaxIterations int           // 最大迭代次数（ReAct 循环）
	Temperature   *float64      // 温度参数
	MaxTokens     *int          // 最大 token 数
}

// NewChatModelAgent 创建新的聊天模型智能体
func NewChatModelAgent(config *Config) (*ChatModelAgent, error) {
	if config == nil {
		return nil, &ValidationError{
			Field:   "config",
			Message: "配置不能为空",
		}
	}

	if config.Model == nil {
		return nil, &ValidationError{
			Field:   "model",
			Message: "聊天模型不能为空",
		}
	}

	if config.Name == "" {
		config.Name = "ChatModelAgent"
	}

	if config.Description == "" {
		config.Description = "基于聊天模型的智能体"
	}

	if config.MaxIterations <= 0 {
		config.MaxIterations = 10 // 默认最大10次迭代
	}

	if config.SystemPrompt == "" {
		config.SystemPrompt = defaultSystemPrompt
	}

	return &ChatModelAgent{
		BaseAgent:     agent.NewBaseAgent(config.Name, config.Description),
		model:         config.Model,
		systemPrompt:  config.SystemPrompt,
		maxIterations: config.MaxIterations,
		temperature:   config.Temperature,
		maxTokens:     config.MaxTokens,
	}, nil
}

// Run 运行智能体，实现 ReAct 模式
func (a *ChatModelAgent) Run(ctx context.Context, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	// 根据模型类型选择合适的缓冲区配置
	// 如果模型支持流式输出，使用流式配置；否则使用批量配置
	pair := runtime.NewAsyncIteratorForLLMBatch[*event.Event](ctx)

	go func() {
		defer pair.Generator.Close()

		// 验证输入
		if err := in.Validate(); err != nil {
			pair.Generator.Send(event.NewErrorEvent(err, "VALIDATION_ERROR", nil))
			return
		}

		// 构建聊天请求
		chatReq, err := a.buildChatRequest(ctx, in)
		if err != nil {
			pair.Generator.Send(event.NewErrorEvent(err, "BUILD_REQUEST_ERROR", nil))
			return
		}

		// 开始 ReAct 循环
		a.runReActLoop(ctx, in, chatReq, func(ev *event.Event) {
			pair.Generator.Send(ev)
		})
	}()

	return pair.Iterator
}

// runReActLoop 运行 ReAct 循环
func (a *ChatModelAgent) runReActLoop(ctx context.Context, in *agent.Input, chatReq *llm.ChatRequest, emit func(*event.Event)) error {
	iteration := 0

	for iteration < a.maxIterations {
		iteration++

		// 发送思考事件
		emit(event.NewThoughtEvent(fmt.Sprintf("开始第 %d 次推理迭代", iteration), "ReAct推理循环"))

		// 调用聊天模型
		resp, err := a.model.Chat(ctx, chatReq)
		if err != nil {
			emit(event.NewErrorEvent(fmt.Errorf("聊天模型调用失败: %w", err), "LLM_CALL_ERROR", nil))
			return err
		}

		// 处理响应
		if len(resp.Choices) == 0 {
			emit(event.NewErrorEvent(fmt.Errorf("聊天模型返回空响应"), "EMPTY_RESPONSE", nil))
			return fmt.Errorf("聊天模型返回空响应")
		}

		choice := resp.Choices[0]
		assistantMsg := choice.Message

		// 发送 token 事件（模拟流式输出）
		content := ""
		if assistantMsg.Content != nil {
			if str, ok := assistantMsg.Content.(string); ok {
				content = str
			}
		}
		if content != "" {
			emit(event.NewTokenEvent(content, false))
		}

		// 添加助手消息到对话历史
		chatReq.AddAssistantMessage(content)

		// 检查是否有工具调用
		if len(assistantMsg.ToolCalls) > 0 {
			// 处理工具调用
			if err := a.handleToolCalls(ctx, in, chatReq, assistantMsg.ToolCalls, emit); err != nil {
				emit(event.NewErrorEvent(err, "TOOL_CALL_ERROR", nil))
				return err
			}
			// 继续下一次迭代
			continue
		}

		// 检查是否完成
		if choice.FinishReason == "stop" || choice.FinishReason == "length" {
			// 发送最终事件
			finalMsg := message.NewAssistantMessage(content)
			emit(event.NewFinalEvent(content, "任务完成", nil))

			// 保存到记忆
			if in.Memory != nil {
				sessionID := a.getSessionID(in)
				if err := in.Memory.Save(ctx, sessionID, finalMsg); err != nil {
					// 记忆保存失败不影响主流程，只记录错误
					emit(event.NewErrorEvent(fmt.Errorf("保存记忆失败: %w", err), "MEMORY_SAVE_ERROR", nil))
				}
			}

			return nil
		}

		// 如果没有工具调用且没有完成，继续下一次迭代
	}

	// 达到最大迭代次数
	emit(event.NewErrorEvent(fmt.Errorf("达到最大迭代次数 %d", a.maxIterations), "MAX_ITERATIONS", nil))
	return fmt.Errorf("达到最大迭代次数 %d", a.maxIterations)
}

// handleToolCalls 处理工具调用
func (a *ChatModelAgent) handleToolCalls(ctx context.Context, in *agent.Input, chatReq *llm.ChatRequest, toolCalls []*llm.ToolCall, emit func(*event.Event)) error {
	for _, toolCall := range toolCalls {
		// 发送工具调用事件
		args := make(map[string]any)
		args["arguments"] = toolCall.Function.Arguments
		emit(event.NewToolCallEvent(toolCall.ID, toolCall.Function.Name, args))

		// 查找工具
		tool := a.findTool(in.Tools, toolCall.Function.Name)
		if tool == nil {
			errMsg := fmt.Sprintf("未找到工具: %s", toolCall.Function.Name)
			emit(event.NewToolResultEvent(toolCall.ID, toolCall.Function.Name, nil, fmt.Errorf("%s", errMsg)))

			// 添加工具错误消息到对话历史
			chatReq.AddToolMessage(errMsg, toolCall.ID)
			continue
		}

		// 解析工具参数
		var params map[string]any
		if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &params); err != nil {
			errMsg := fmt.Sprintf("解析工具参数失败: %v", err)
			emit(event.NewToolResultEvent(toolCall.ID, toolCall.Function.Name, nil, fmt.Errorf("%s", errMsg)))
			chatReq.AddToolMessage(errMsg, toolCall.ID)
			continue
		}

		// 执行工具
		result, err := tool.Execute(ctx, params)
		if err != nil {
			errMsg := fmt.Sprintf("工具执行失败: %v", err)
			emit(event.NewToolResultEvent(toolCall.ID, toolCall.Function.Name, nil, err))

			// 添加工具错误消息到对话历史
			chatReq.AddToolMessage(errMsg, toolCall.ID)
			continue
		}

		// 发送工具结果事件
		resultMap := map[string]any{"result": result}
		emit(event.NewToolResultEvent(toolCall.ID, toolCall.Function.Name, resultMap, nil))

		// 将结果转换为字符串
		var resultStr string
		if str, ok := result.(string); ok {
			resultStr = str
		} else {
			resultBytes, _ := json.Marshal(result)
			resultStr = string(resultBytes)
		}

		// 添加工具结果消息到对话历史
		chatReq.AddToolMessage(resultStr, toolCall.ID)
	}

	return nil
}

// buildChatRequest 构建聊天请求
func (a *ChatModelAgent) buildChatRequest(ctx context.Context, in *agent.Input) (*llm.ChatRequest, error) {
	// 创建聊天请求
	chatReq := llm.NewChatRequest("", nil)

	// 设置模型参数
	if a.temperature != nil {
		chatReq.WithTemperature(*a.temperature)
	}
	if a.maxTokens != nil {
		chatReq.WithMaxTokens(*a.maxTokens)
	}

	// 添加系统消息
	if a.systemPrompt != "" {
		chatReq.AddSystemMessage(a.systemPrompt)
	}

	// 加载历史消息（如果有记忆）
	if in.Memory != nil {
		sessionID := a.getSessionID(in)
		history, err := in.Memory.Load(ctx, sessionID, 20) // 加载最近20条消息
		if err != nil {
			return nil, fmt.Errorf("加载历史消息失败: %w", err)
		}

		// 转换历史消息为聊天消息
		for _, msg := range history {
			chatMsg := a.convertMessageToChatMessage(msg)
			chatReq.Messages = append(chatReq.Messages, chatMsg)
		}
	}

	// 添加当前输入消息
	for _, msg := range in.Messages {
		chatMsg := a.convertMessageToChatMessage(msg)
		chatReq.Messages = append(chatReq.Messages, chatMsg)
	}

	// 设置工具
	if len(in.Tools) > 0 {
		tools := a.convertToolsToDefinitions(in.Tools)
		chatReq.WithTools(tools)
		chatReq.WithToolChoice("auto")
	}

	return chatReq, nil
}

// convertMessageToChatMessage 转换消息为聊天消息
func (a *ChatModelAgent) convertMessageToChatMessage(msg *message.Message) *llm.ChatMessage {
	chatMsg := &llm.ChatMessage{
		Role:       msg.Role,
		Content:    msg.Content,
		Name:       msg.Name,
		ToolCallID: msg.ToolCallID,
	}

	// 转换工具调用
	if len(msg.ToolCalls) > 0 {
		chatMsg.ToolCalls = make([]*llm.ToolCall, len(msg.ToolCalls))
		for i, tc := range msg.ToolCalls {
			chatMsg.ToolCalls[i] = &llm.ToolCall{
				ID:   tc.ID,
				Type: tc.Type,
				Function: &llm.ToolCallFunction{
					Name:      tc.Function.Name,
					Arguments: tc.Function.Arguments,
				},
			}
		}
	}

	return chatMsg
}

// convertToolsToDefinitions 转换工具为定义
func (a *ChatModelAgent) convertToolsToDefinitions(tools []tool.Tool) []llm.ToolDefinition {
	definitions := make([]llm.ToolDefinition, len(tools))
	for i, tool := range tools {
		schema := tool.Schema()
		definitions[i] = llm.ToolDefinition{
			Type: "function",
			Function: &llm.ToolFunctionDefinition{
				Name:        tool.Name(),
				Description: tool.Description(),
				Parameters:  a.convertJSONSchemaToMap(schema),
			},
		}
	}
	return definitions
}

// convertJSONSchemaToMap 转换 JSON Schema 为 map
func (a *ChatModelAgent) convertJSONSchemaToMap(schema *tool.JSONSchema) map[string]interface{} {
	if schema == nil {
		return map[string]interface{}{"type": "object"}
	}

	result := map[string]interface{}{
		"type": schema.Type,
	}

	if schema.Description != "" {
		result["description"] = schema.Description
	}

	if len(schema.Properties) > 0 {
		result["properties"] = schema.Properties
	}

	if len(schema.Required) > 0 {
		result["required"] = schema.Required
	}

	return result
}

// findTool 查找工具
func (a *ChatModelAgent) findTool(tools []tool.Tool, name string) tool.Tool {
	for _, tool := range tools {
		if tool.Name() == name {
			return tool
		}
	}
	return nil
}

// getSessionID 获取会话ID
func (a *ChatModelAgent) getSessionID(in *agent.Input) string {
	if in.Session != nil {
		if sessionID, exists := in.Session.Get("session_id"); exists {
			if id, ok := sessionID.(string); ok {
				return id
			}
		}
	}
	return "default"
}

// ValidationError 表示验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return "验证错误 [" + e.Field + "]: " + e.Message
}

// defaultSystemPrompt 默认系统提示词
const defaultSystemPrompt = `你是一个有用的AI助手。你可以使用提供的工具来帮助用户完成任务。

当你需要使用工具时，请按照以下格式调用：
1. 分析用户的需求
2. 选择合适的工具
3. 调用工具并等待结果
4. 根据工具结果给出最终回答

请始终保持友好和专业的态度。`
