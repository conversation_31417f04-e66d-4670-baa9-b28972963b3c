package audit

import (
	"context"
	"sync"
	"time"
)

// WriteRequest 写入请求
type WriteRequest struct {
	Record   *Record
	Response chan error
}

// AuditWriter 异步批量写入器
type AuditWriter struct {
	store       Store
	sanitizer   *Sanitizer
	config      *BatchConfig
	
	// 通道和控制
	requestCh   chan *WriteRequest
	stopCh      chan struct{}
	doneCh      chan struct{}
	
	// 状态管理
	mu       sync.RWMutex
	closed   bool
	started  bool
	
	// 批量处理
	batch       []*Record
	batchSize   int
	flushTimer  *time.Timer
}

// NewAuditWriter 创建异步批量写入器
func NewAuditWriter(store Store, sanitizer *Sanitizer, config *BatchConfig) *AuditWriter {
	if config == nil {
		config = &BatchConfig{
			Async:         true,
			ChanBuffer:    1024,
			FlushInterval: time.Second,
		}
	}
	
	return &AuditWriter{
		store:     store,
		sanitizer: sanitizer,
		config:    config,
		requestCh: make(chan *WriteRequest, config.<PERSON><PERSON>uffer),
		stopCh:    make(chan struct{}),
		doneCh:    make(chan struct{}),
		batchSize: 100, // 默认批量大小
	}
}

// Start 启动写入器
func (w *AuditWriter) Start(ctx context.Context) error {
	w.mu.Lock()
	defer w.mu.Unlock()
	
	if w.started {
		return NewAuditError("WRITER_ALREADY_STARTED", "写入器已经启动", nil)
	}
	
	if w.closed {
		return NewWriterClosedError()
	}
	
	w.started = true
	
	if w.config.Async {
		go w.runAsync(ctx)
	}
	
	return nil
}

// WriteRecord 写入记录
func (w *AuditWriter) WriteRecord(ctx context.Context, record *Record) error {
	w.mu.RLock()
	defer w.mu.RUnlock()
	
	if w.closed {
		return NewWriterClosedError()
	}
	
	if !w.config.Async {
		// 同步写入
		return w.writeRecordSync(ctx, record)
	}
	
	// 异步写入
	req := &WriteRequest{
		Record:   record,
		Response: make(chan error, 1),
	}
	
	select {
	case w.requestCh <- req:
		// 等待响应
		select {
		case err := <-req.Response:
			return err
		case <-ctx.Done():
			return ctx.Err()
		}
	case <-ctx.Done():
		return ctx.Err()
	default:
		return NewWriterFullError()
	}
}

// writeRecordSync 同步写入记录
func (w *AuditWriter) writeRecordSync(ctx context.Context, record *Record) error {
	if record == nil {
		return NewInvalidRecordError("记录不能为空", nil)
	}
	
	// 脱敏处理
	if w.sanitizer != nil {
		if err := w.sanitizer.SanitizeRecord(record); err != nil {
			return err
		}
	}
	
	// 写入存储
	return w.store.SaveMessage(ctx, record)
}

// runAsync 异步运行循环
func (w *AuditWriter) runAsync(ctx context.Context) {
	defer close(w.doneCh)
	
	w.flushTimer = time.NewTimer(w.config.FlushInterval)
	defer w.flushTimer.Stop()
	
	for {
		select {
		case req := <-w.requestCh:
			w.handleRequest(ctx, req)
			
		case <-w.flushTimer.C:
			w.flushBatch(ctx)
			w.resetFlushTimer()
			
		case <-w.stopCh:
			// 处理剩余请求
			w.drainRequests(ctx)
			// 刷新剩余批次
			w.flushBatch(ctx)
			return
			
		case <-ctx.Done():
			// 上下文取消，优雅关闭
			w.drainRequests(ctx)
			w.flushBatch(ctx)
			return
		}
	}
}

// handleRequest 处理写入请求
func (w *AuditWriter) handleRequest(ctx context.Context, req *WriteRequest) {
	if req == nil || req.Record == nil {
		if req != nil && req.Response != nil {
			req.Response <- NewInvalidRecordError("请求或记录不能为空", nil)
		}
		return
	}
	
	// 脱敏处理
	var err error
	if w.sanitizer != nil {
		err = w.sanitizer.SanitizeRecord(req.Record)
	}
	
	if err != nil {
		if req.Response != nil {
			req.Response <- err
		}
		return
	}
	
	// 添加到批次
	w.batch = append(w.batch, req.Record)
	
	// 发送响应
	if req.Response != nil {
		req.Response <- nil
	}
	
	// 检查是否需要刷新批次
	if len(w.batch) >= w.batchSize {
		w.flushBatch(ctx)
		w.resetFlushTimer()
	}
}

// flushBatch 刷新批次
func (w *AuditWriter) flushBatch(ctx context.Context) {
	if len(w.batch) == 0 {
		return
	}
	
	// 批量写入
	for _, record := range w.batch {
		if err := w.store.SaveMessage(ctx, record); err != nil {
			// 记录错误，但继续处理其他记录
			// 在实际实现中可能需要更复杂的错误处理策略
			_ = err
		}
	}
	
	// 清空批次
	w.batch = w.batch[:0]
}

// resetFlushTimer 重置刷新定时器
func (w *AuditWriter) resetFlushTimer() {
	if !w.flushTimer.Stop() {
		select {
		case <-w.flushTimer.C:
		default:
		}
	}
	w.flushTimer.Reset(w.config.FlushInterval)
}

// drainRequests 处理剩余请求
func (w *AuditWriter) drainRequests(ctx context.Context) {
	for {
		select {
		case req := <-w.requestCh:
			w.handleRequest(ctx, req)
		default:
			return
		}
	}
}

// Close 关闭写入器
func (w *AuditWriter) Close() error {
	w.mu.Lock()
	defer w.mu.Unlock()
	
	if w.closed {
		return nil
	}
	
	w.closed = true
	
	if w.started && w.config.Async {
		// 发送停止信号
		close(w.stopCh)
		
		// 等待异步协程结束
		<-w.doneCh
	}
	
	return nil
}

// Stats 写入器统计信息
type Stats struct {
	// PendingRequests 待处理请求数
	PendingRequests int `json:"pending_requests"`
	// BatchSize 当前批次大小
	BatchSize int `json:"batch_size"`
	// IsRunning 是否正在运行
	IsRunning bool `json:"is_running"`
	// IsClosed 是否已关闭
	IsClosed bool `json:"is_closed"`
}

// GetStats 获取统计信息
func (w *AuditWriter) GetStats() *Stats {
	w.mu.RLock()
	defer w.mu.RUnlock()
	
	return &Stats{
		PendingRequests: len(w.requestCh),
		BatchSize:       len(w.batch),
		IsRunning:       w.started && !w.closed,
		IsClosed:        w.closed,
	}
}

// SetBatchSize 设置批量大小
func (w *AuditWriter) SetBatchSize(size int) {
	w.mu.Lock()
	defer w.mu.Unlock()
	
	if size > 0 && size <= 1000 {
		w.batchSize = size
	}
}

// GetBatchSize 获取批量大小
func (w *AuditWriter) GetBatchSize() int {
	w.mu.RLock()
	defer w.mu.RUnlock()
	
	return w.batchSize
}
