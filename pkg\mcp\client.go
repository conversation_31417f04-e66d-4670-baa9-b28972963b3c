package mcp

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"sync"
	"sync/atomic"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// DefaultMCPClient 默认MCP客户端实现
type DefaultMCPClient struct {
	config      *MCPConfig
	cmd         *exec.Cmd
	stdin       io.WriteCloser
	stdout      io.ReadCloser
	stderr      io.ReadCloser
	connected   int32
	nextID      int64
	pendingReqs map[interface{}]chan *MCPResponse
	subscribers map[string]chan *MCPResourceChange
	mu          sync.RWMutex
	logger      logger.Logger
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewDefaultMCPClient 创建默认MCP客户端
func NewDefaultMCPClient() *DefaultMCPClient {
	return &DefaultMCPClient{
		pendingReqs: make(map[interface{}]chan *MCPResponse),
		subscribers: make(map[string]chan *MCPResourceChange),
		logger:      logger.GetGlobalLogger(),
	}
}

// Connect 连接到MCP服务器
func (c *DefaultMCPClient) Connect(ctx context.Context, config *MCPConfig) error {
	if config == nil {
		return errors.NewValidationError("invalid_config", "MCP config cannot be nil")
	}

	if err := config.Validate(); err != nil {
		return err
	}

	c.config = config
	c.ctx, c.cancel = context.WithCancel(ctx)

	// 启动MCP服务器进程
	if err := c.startServer(); err != nil {
		return err
	}

	// 启动消息处理
	go c.handleMessages()
	go c.handleErrors()

	// 执行初始化握手
	if err := c.initialize(); err != nil {
		c.Disconnect()
		return err
	}

	atomic.StoreInt32(&c.connected, 1)
	c.logger.Info("MCP client connected successfully")
	return nil
}

// Disconnect 断开连接
func (c *DefaultMCPClient) Disconnect() error {
	if !c.IsConnected() {
		return nil
	}

	atomic.StoreInt32(&c.connected, 0)

	// 取消上下文
	if c.cancel != nil {
		c.cancel()
	}

	// 关闭管道
	if c.stdin != nil {
		c.stdin.Close()
	}
	if c.stdout != nil {
		c.stdout.Close()
	}
	if c.stderr != nil {
		c.stderr.Close()
	}

	// 终止进程
	if c.cmd != nil && c.cmd.Process != nil {
		c.cmd.Process.Kill()
		c.cmd.Wait()
	}

	// 清理待处理的请求
	c.mu.Lock()
	for id, ch := range c.pendingReqs {
		close(ch)
		delete(c.pendingReqs, id)
	}

	for uri, ch := range c.subscribers {
		close(ch)
		delete(c.subscribers, uri)
	}
	c.mu.Unlock()

	c.logger.Info("MCP client disconnected")
	return nil
}

// IsConnected 检查连接状态
func (c *DefaultMCPClient) IsConnected() bool {
	return atomic.LoadInt32(&c.connected) == 1
}

// ListTools 列出可用工具
func (c *DefaultMCPClient) ListTools(ctx context.Context) ([]*MCPTool, error) {
	if !c.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodListTools, nil)
	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("MCP error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	var result MCPListToolsResult
	if err := c.unmarshalResult(resp.Result, &result); err != nil {
		return nil, err
	}

	tools := make([]*MCPTool, len(result.Tools))
	for i := range result.Tools {
		tools[i] = &result.Tools[i]
	}

	return tools, nil
}

// CallTool 调用工具
func (c *DefaultMCPClient) CallTool(ctx context.Context, name string, arguments map[string]interface{}) (*MCPToolResult, error) {
	if !c.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	params := &MCPCallToolParams{
		Name:      name,
		Arguments: arguments,
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodCallTool, params)
	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("MCP error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	var result MCPToolResult
	if err := c.unmarshalResult(resp.Result, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// ListResources 列出可用资源
func (c *DefaultMCPClient) ListResources(ctx context.Context) ([]*MCPResource, error) {
	if !c.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodListResources, nil)
	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("MCP error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	var result MCPListResourcesResult
	if err := c.unmarshalResult(resp.Result, &result); err != nil {
		return nil, err
	}

	resources := make([]*MCPResource, len(result.Resources))
	for i := range result.Resources {
		resources[i] = &result.Resources[i]
	}

	return resources, nil
}

// ReadResource 读取资源
func (c *DefaultMCPClient) ReadResource(ctx context.Context, uri string) (*MCPResourceContent, error) {
	if !c.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	params := &MCPReadResourceParams{
		URI: uri,
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodReadResource, params)
	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("MCP error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	var result MCPResourceContent
	if err := c.unmarshalResult(resp.Result, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// Subscribe 订阅资源变更
func (c *DefaultMCPClient) Subscribe(ctx context.Context, uri string) (<-chan *MCPResourceChange, error) {
	if !c.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	params := &MCPSubscribeParams{
		URI: uri,
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodSubscribe, params)
	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("MCP error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	// 创建订阅通道
	ch := make(chan *MCPResourceChange, 100)
	c.mu.Lock()
	c.subscribers[uri] = ch
	c.mu.Unlock()

	return ch, nil
}

// Unsubscribe 取消订阅
func (c *DefaultMCPClient) Unsubscribe(ctx context.Context, uri string) error {
	if !c.IsConnected() {
		return errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	params := &MCPSubscribeParams{
		URI: uri,
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodUnsubscribe, params)
	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return err
	}

	if resp.Error != nil {
		return fmt.Errorf("MCP error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	// 移除订阅
	c.mu.Lock()
	if ch, exists := c.subscribers[uri]; exists {
		close(ch)
		delete(c.subscribers, uri)
	}
	c.mu.Unlock()

	return nil
}

// startServer 启动MCP服务器
func (c *DefaultMCPClient) startServer() error {
	cmd := exec.CommandContext(c.ctx, c.config.ServerCommand[0], c.config.ServerCommand[1:]...)

	if len(c.config.ServerArgs) > 0 {
		cmd.Args = append(cmd.Args, c.config.ServerArgs...)
	}

	if c.config.WorkingDir != "" {
		cmd.Dir = c.config.WorkingDir
	}

	// 设置环境变量
	cmd.Env = os.Environ()
	for k, v := range c.config.Environment {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
	}

	var err error
	c.stdin, err = cmd.StdinPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdin pipe: %w", err)
	}

	c.stdout, err = cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	c.stderr, err = cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start MCP server: %w", err)
	}

	c.cmd = cmd
	c.logger.Infof("MCP server started: %v", c.config.ServerCommand)
	return nil
}

// initialize 执行初始化握手
func (c *DefaultMCPClient) initialize() error {
	params := &MCPInitializeParams{
		ProtocolVersion: "2024-11-05",
		Capabilities: &MCPCapabilities{
			Tools: &MCPToolsCapability{
				ListChanged: true,
			},
			Resources: &MCPResourcesCapability{
				Subscribe:   true,
				ListChanged: true,
			},
		},
		ClientInfo: &MCPClientInfo{
			Name:    "agentscope-golang",
			Version: "0.1.0",
		},
	}

	req := NewMCPRequest(c.nextRequestID(), MCPMethodInitialize, params)

	ctx, cancel := context.WithTimeout(c.ctx, c.config.Timeout)
	defer cancel()

	resp, err := c.sendRequest(ctx, req)
	if err != nil {
		return fmt.Errorf("initialization failed: %w", err)
	}

	if resp.Error != nil {
		return fmt.Errorf("initialization error %d: %s", resp.Error.Code, resp.Error.Message)
	}

	var result MCPInitializeResult
	if err := c.unmarshalResult(resp.Result, &result); err != nil {
		return fmt.Errorf("failed to parse initialization result: %w", err)
	}

	c.logger.Infof("MCP initialization completed, server: %s %s",
		result.ServerInfo.Name, result.ServerInfo.Version)

	// 发送initialized通知
	notification := NewMCPNotification(MCPMethodInitialized, nil)
	return c.sendNotification(notification)
}

// sendRequest 发送请求
func (c *DefaultMCPClient) sendRequest(ctx context.Context, req *MCPRequest) (*MCPResponse, error) {
	// 创建响应通道
	respCh := make(chan *MCPResponse, 1)
	c.mu.Lock()
	c.pendingReqs[req.ID] = respCh
	c.mu.Unlock()

	// 发送请求
	if err := c.sendMessage(req); err != nil {
		c.mu.Lock()
		delete(c.pendingReqs, req.ID)
		c.mu.Unlock()
		return nil, err
	}

	// 等待响应
	select {
	case resp := <-respCh:
		c.mu.Lock()
		delete(c.pendingReqs, req.ID)
		c.mu.Unlock()
		return resp, nil
	case <-ctx.Done():
		c.mu.Lock()
		delete(c.pendingReqs, req.ID)
		c.mu.Unlock()
		return nil, ctx.Err()
	}
}

// sendNotification 发送通知
func (c *DefaultMCPClient) sendNotification(notification *MCPNotification) error {
	return c.sendMessage(notification)
}

// sendMessage 发送消息
func (c *DefaultMCPClient) sendMessage(msg interface{}) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	data = append(data, '\n')

	if _, err := c.stdin.Write(data); err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	return nil
}

// handleMessages 处理消息
func (c *DefaultMCPClient) handleMessages() {
	scanner := bufio.NewScanner(c.stdout)

	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		var msg MCPMessage
		if err := json.Unmarshal([]byte(line), &msg); err != nil {
			c.logger.Warnf("Failed to parse MCP message: %v", err)
			continue
		}

		c.handleMessage(&msg)
	}

	if err := scanner.Err(); err != nil {
		c.logger.Error("Error reading MCP messages: %v", err)
	}
}

// handleMessage 处理单个消息
func (c *DefaultMCPClient) handleMessage(msg *MCPMessage) {
	if msg.IsResponse() {
		c.handleResponse(msg)
	} else if msg.IsNotification() {
		c.handleNotification(msg)
	}
}

// handleResponse 处理响应
func (c *DefaultMCPClient) handleResponse(msg *MCPMessage) {
	c.mu.RLock()
	respCh, exists := c.pendingReqs[msg.ID]
	c.mu.RUnlock()

	if !exists {
		c.logger.Warnf("Received response for unknown request ID: %v", msg.ID)
		return
	}

	resp := &MCPResponse{
		JSONRPC: msg.JSONRPC,
		ID:      msg.ID,
		Result:  msg.Result,
		Error:   msg.Error,
	}

	select {
	case respCh <- resp:
	default:
		c.logger.Warnf("Response channel full for request ID: %v", msg.ID)
	}
}

// handleNotification 处理通知
func (c *DefaultMCPClient) handleNotification(msg *MCPMessage) {
	switch msg.Method {
	case MCPMethodResourceUpdated:
		c.handleResourceUpdated(msg)
	default:
		c.logger.Debug("Received unknown notification: %s", msg.Method)
	}
}

// handleResourceUpdated 处理资源更新通知
func (c *DefaultMCPClient) handleResourceUpdated(msg *MCPMessage) {
	var change MCPResourceChange
	if err := c.unmarshalResult(msg.Params, &change); err != nil {
		c.logger.Warnf("Failed to parse resource change notification: %v", err)
		return
	}

	change.Timestamp = time.Now()

	c.mu.RLock()
	ch, exists := c.subscribers[change.URI]
	c.mu.RUnlock()

	if exists {
		select {
		case ch <- &change:
		default:
			c.logger.Warnf("Resource change channel full for URI: %s", change.URI)
		}
	}
}

// handleErrors 处理错误输出
func (c *DefaultMCPClient) handleErrors() {
	scanner := bufio.NewScanner(c.stderr)

	for scanner.Scan() {
		line := scanner.Text()
		if line != "" {
			c.logger.Warnf("MCP server stderr: %s", line)
		}
	}
}

// nextRequestID 生成下一个请求ID
func (c *DefaultMCPClient) nextRequestID() int64 {
	return atomic.AddInt64(&c.nextID, 1)
}

// unmarshalResult 解析结果
func (c *DefaultMCPClient) unmarshalResult(result interface{}, target interface{}) error {
	data, err := json.Marshal(result)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, target)
}
