package qwen

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/llm"
)

func TestNewQwenChatModel(t *testing.T) {
	// 保存原始环境变量
	originalDashScopeKey := os.Getenv("DASHSCOPE_API_KEY")
	originalQwenKey := os.Getenv("QWEN_API_KEY")
	defer func() {
		if originalDashScopeKey != "" {
			os.Setenv("DASHSCOPE_API_KEY", originalDashScopeKey)
		} else {
			os.Unsetenv("DASHSCOPE_API_KEY")
		}
		if originalQwenKey != "" {
			os.Setenv("QWEN_API_KEY", originalQwenKey)
		} else {
			os.Unsetenv("QWEN_API_KEY")
		}
	}()

	// 测试缺少 API Key
	os.Unsetenv("DASHSCOPE_API_KEY")
	os.Unsetenv("QWEN_API_KEY")
	_, err := NewQwenChatModel()
	if err == nil {
		t.Error("缺少 API Key 应该返回错误")
	}

	// 测试有效的 DASHSCOPE_API_KEY
	os.Setenv("DASHSCOPE_API_KEY", "test-dashscope-key")
	model, err := NewQwenChatModel()
	if err != nil {
		t.Fatalf("创建 Qwen ChatModel 失败: %v", err)
	}

	if model == nil {
		t.Fatal("创建的模型不应该为空")
	}

	if model.client == nil {
		t.Error("客户端不应该为空")
	}

	// 测试有效的 QWEN_API_KEY（当 DASHSCOPE_API_KEY 不存在时）
	os.Unsetenv("DASHSCOPE_API_KEY")
	os.Setenv("QWEN_API_KEY", "test-qwen-key")
	model2, err := NewQwenChatModel()
	if err != nil {
		t.Fatalf("使用 QWEN_API_KEY 创建模型失败: %v", err)
	}

	if model2 == nil {
		t.Fatal("创建的模型不应该为空")
	}
}

func TestNewQwenChatModelWithConfig(t *testing.T) {
	apiKey := "test-api-key"
	baseURL := "https://test.example.com"
	model := "qwen-test"
	timeout := 60 * time.Second
	retries := 5

	chatModel, err := NewQwenChatModelWithConfig(apiKey, baseURL, model, timeout, retries)
	if err != nil {
		t.Fatalf("创建配置化 Qwen ChatModel 失败: %v", err)
	}

	if chatModel == nil {
		t.Fatal("创建的模型不应该为空")
	}

	// 测试默认值
	chatModel2, err := NewQwenChatModelWithConfig(apiKey, "", "", timeout, retries)
	if err != nil {
		t.Fatalf("创建默认配置 Qwen ChatModel 失败: %v", err)
	}

	if chatModel2 == nil {
		t.Fatal("创建的模型不应该为空")
	}
}

func TestQwenChatModelWithMockServer(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			t.Errorf("期望 POST 请求，得到 %s", r.Method)
		}

		if r.URL.Path != "/chat/completions" {
			t.Errorf("期望路径 /chat/completions，得到 %s", r.URL.Path)
		}

		// 检查请求头
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望 Content-Type application/json，得到 %s", r.Header.Get("Content-Type"))
		}

		authHeader := r.Header.Get("Authorization")
		if !strings.HasPrefix(authHeader, "Bearer ") {
			t.Errorf("期望 Bearer 认证，得到 %s", authHeader)
		}

		// 读取请求体
		var reqBody map[string]interface{}
		json.NewDecoder(r.Body).Decode(&reqBody)

		// 检查是否为流式请求
		isStream, _ := reqBody["stream"].(bool)

		if isStream {
			// 流式响应
			w.Header().Set("Content-Type", "text/event-stream")
			w.Header().Set("Cache-Control", "no-cache")
			w.Header().Set("Connection", "keep-alive")

			flusher, ok := w.(http.Flusher)
			if !ok {
				t.Fatal("无法获取 flusher")
			}

			// 发送流式数据
			delta1 := map[string]interface{}{
				"id":      "chatcmpl-stream-test",
				"object":  "chat.completion.chunk",
				"created": time.Now().Unix(),
				"model":   "qwen-plus",
				"choices": []map[string]interface{}{
					{
						"index": 0,
						"delta": map[string]interface{}{
							"role":    "assistant",
							"content": "你好",
						},
					},
				},
			}

			data1, _ := json.Marshal(delta1)
			w.Write([]byte("data: " + string(data1) + "\n\n"))
			flusher.Flush()

			// 第二个数据块
			delta2 := map[string]interface{}{
				"id":      "chatcmpl-stream-test",
				"object":  "chat.completion.chunk",
				"created": time.Now().Unix(),
				"model":   "qwen-plus",
				"choices": []map[string]interface{}{
					{
						"index": 0,
						"delta": map[string]interface{}{
							"content": "，世界！",
						},
						"finish_reason": "stop",
					},
				},
			}

			data2, _ := json.Marshal(delta2)
			w.Write([]byte("data: " + string(data2) + "\n\n"))
			flusher.Flush()

			// 结束标记
			w.Write([]byte("data: [DONE]\n\n"))
			flusher.Flush()
		} else {
			// 非流式响应
			response := map[string]interface{}{
				"id":      "chatcmpl-test",
				"object":  "chat.completion",
				"created": time.Now().Unix(),
				"model":   "qwen-plus",
				"choices": []map[string]interface{}{
					{
						"index": 0,
						"message": map[string]interface{}{
							"role":    "assistant",
							"content": "这是一个测试响应",
						},
						"finish_reason": "stop",
					},
				},
				"usage": map[string]interface{}{
					"prompt_tokens":     10,
					"completion_tokens": 5,
					"total_tokens":      15,
				},
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
		}
	}))
	defer server.Close()

	// 创建客户端
	chatModel, err := NewQwenChatModelWithConfig("test-api-key", server.URL, "qwen-plus", 30*time.Second, 0)
	if err != nil {
		t.Fatalf("创建 Qwen ChatModel 失败: %v", err)
	}

	// 测试非流式聊天
	t.Run("Chat", func(t *testing.T) {
		req := &llm.ChatRequest{
			Model: "qwen-plus",
			Messages: []*llm.ChatMessage{
				{Role: "user", Content: "你好"},
			},
		}

		resp, err := chatModel.Chat(context.Background(), req)
		if err != nil {
			t.Fatalf("聊天请求失败: %v", err)
		}

		if resp.ID != "chatcmpl-test" {
			t.Errorf("响应 ID 不匹配: got %s", resp.ID)
		}

		if len(resp.Choices) != 1 {
			t.Errorf("期望 1 个选择，得到 %d", len(resp.Choices))
		}

		if resp.Choices[0].Message.Content != "这是一个测试响应" {
			t.Errorf("响应内容不匹配: got %v", resp.Choices[0].Message.Content)
		}
	})

	// 测试流式聊天
	t.Run("ChatStream", func(t *testing.T) {
		req := &llm.ChatRequest{
			Model: "qwen-plus",
			Messages: []*llm.ChatMessage{
				{Role: "user", Content: "你好"},
			},
		}

		deltaChan, err := chatModel.ChatStream(context.Background(), req)
		if err != nil {
			t.Fatalf("流式聊天请求失败: %v", err)
		}

		var deltas []*llm.ChatDelta
		for delta := range deltaChan {
			deltas = append(deltas, delta)
		}

		if len(deltas) < 1 {
			t.Errorf("期望至少 1 个增量，得到 %d", len(deltas))
		}

		// 验证第一个增量
		if len(deltas) > 0 && deltas[0].GetContent() != "你好" {
			t.Errorf("第一个增量内容不匹配: got %s", deltas[0].GetContent())
		}
	})
}

func TestQwenChatModelConversion(t *testing.T) {
	chatModel, err := NewQwenChatModelWithConfig("test-key", "", "", 30*time.Second, 0)
	if err != nil {
		t.Fatalf("创建 Qwen ChatModel 失败: %v", err)
	}

	// 测试请求转换
	chatReq := &llm.ChatRequest{
		Model: "qwen-plus",
		Messages: []*llm.ChatMessage{
			{Role: "user", Content: "Hello"},
		},
		Temperature: func() *float64 { v := 0.7; return &v }(),
		MaxTokens:   func() *int { v := 100; return &v }(),
		Tools: []llm.ToolDefinition{
			{
				Type: "function",
				Function: &llm.ToolFunctionDefinition{
					Name:        "test_function",
					Description: "测试函数",
					Parameters:  map[string]interface{}{"type": "object"},
				},
			},
		},
		ToolChoice: "auto",
	}

	generateReq := chatModel.convertChatRequestToGenerateRequest(chatReq)

	if generateReq.Model != "qwen-plus" {
		t.Errorf("模型转换错误: got %s, want qwen-plus", generateReq.Model)
	}

	if len(generateReq.Messages) != 1 {
		t.Errorf("消息数量转换错误: got %d, want 1", len(generateReq.Messages))
	}

	if generateReq.Temperature == nil || *generateReq.Temperature != 0.7 {
		t.Errorf("温度转换错误: got %v, want 0.7", generateReq.Temperature)
	}

	if len(generateReq.Tools) != 1 {
		t.Errorf("工具数量转换错误: got %d, want 1", len(generateReq.Tools))
	}

	if generateReq.ToolChoice != "auto" {
		t.Errorf("工具选择转换错误: got %v, want auto", generateReq.ToolChoice)
	}
}

func TestExtractContentString(t *testing.T) {
	chatModel, err := NewQwenChatModelWithConfig("test-key", "", "", 30*time.Second, 0)
	if err != nil {
		t.Fatalf("创建 Qwen ChatModel 失败: %v", err)
	}

	// 测试字符串内容
	content1 := chatModel.extractContentString("hello world")
	if content1 != "hello world" {
		t.Errorf("字符串内容提取错误: got %s, want hello world", content1)
	}

	// 测试空内容
	content2 := chatModel.extractContentString(nil)
	if content2 != "" {
		t.Errorf("空内容提取错误: got %s, want empty string", content2)
	}

	// 测试非字符串内容
	content3 := chatModel.extractContentString(123)
	if content3 != "" {
		t.Errorf("非字符串内容提取错误: got %s, want empty string", content3)
	}
}

// 集成测试（需要真实 API Key）
func TestQwenChatModelIntegration(t *testing.T) {
	// 检查是否启用集成测试
	if os.Getenv("ENABLE_INTEGRATION_TESTS") != "true" {
		t.Skip("跳过集成测试，设置 ENABLE_INTEGRATION_TESTS=true 启用")
	}

	// 检查 API Key
	apiKey := os.Getenv("DASHSCOPE_API_KEY")
	if apiKey == "" {
		apiKey = os.Getenv("QWEN_API_KEY")
	}
	if apiKey == "" {
		t.Skip("跳过集成测试，需要设置 DASHSCOPE_API_KEY 或 QWEN_API_KEY 环境变量")
	}

	chatModel, err := NewQwenChatModel()
	if err != nil {
		t.Fatalf("创建 Qwen ChatModel 失败: %v", err)
	}

	req := &llm.ChatRequest{
		Messages: []*llm.ChatMessage{
			{Role: "user", Content: "你好，请简单回复"},
		},
		MaxTokens: func() *int { v := 50; return &v }(),
	}

	resp, err := chatModel.Chat(context.Background(), req)
	if err != nil {
		t.Fatalf("集成测试失败: %v", err)
	}

	if len(resp.Choices) == 0 {
		t.Error("响应应该包含至少一个选择")
	}

	content := resp.GetContent()
	if content == "" {
		t.Error("响应内容不应该为空")
	}

	t.Logf("集成测试响应: %s", content)
}
