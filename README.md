# AgentScope-Golang

A high-performance, type-safe multi-agent development framework implemented in Go, inspired by AgentScope.

## Features

- **Type-Safe Agent Framework**: Unified Agent interface with strong typing
- **Real LLM Integration**: Support for Deepseek and other mainstream LLM APIs
- **Flexible Pipeline System**: Sequential, parallel, and conditional execution
- **Tool System**: Extensible tool calling capabilities
- **Memory & Knowledge Base**: Context preservation and knowledge management with full-text search
- **Multi-modal Support**: Text, image, audio, and file processing
- **Entity & Relation Management**: Knowledge graph capabilities with entity extraction and relationship mapping
- **Distributed Deployment**: Scalable multi-node architecture
- **Web API**: RESTful API and WebSocket support

## Quick Start

### Prerequisites

- Go 1.21 or higher
- Deepseek API Key

### Environment Setup

```bash
export DEEPSEEK_API_KEY="your_deepseek_api_key_here"
```

### Installation

```bash
git clone https://github.com/agentscope/agentscope-golang.git
cd agentscope-golang
make deps
```

### Run Example

```bash
make run-example
```

### Basic Usage

```go
package main

import (
    "context"
    "fmt"
    "log"

    "github.com/agentscope/agentscope-golang/pkg/knowledge"
    "github.com/agentscope/agentscope-golang/pkg/agent"
    "github.com/agentscope/agentscope-golang/pkg/message"
)

func main() {
    ctx := context.Background()
    
    // Create knowledge base
    kb := knowledge.NewSimpleKnowledgeBase()
    
    // Add a document
    doc := &knowledge.Document{
        Title:   "AI Fundamentals",
        Content: "Artificial Intelligence is the simulation of human intelligence...",
        Type:    knowledge.DocumentTypeText,
        Tags:    []string{"ai", "fundamentals"},
    }
    
    err := kb.AddDocument(ctx, doc)
    if err != nil {
        log.Fatal(err)
    }
    
    // Search documents
    query := &knowledge.SearchQuery{
        Query: "artificial intelligence",
        Limit: 5,
    }
    
    results, err := kb.SearchDocuments(ctx, query)
    if err != nil {
        log.Fatal(err)
    }
    
    for _, result := range results {
        fmt.Printf("Found: %s (Score: %.2f)\n", 
            result.Document.Title, result.Score)
    }
}
```

## Documentation

- [Knowledge Base System](docs/knowledge-base.md) - Comprehensive guide to document storage, entity management, and knowledge retrieval

## Development

### Project Structure

```
agentscope-golang/
├── cmd/                 # Main applications
├── pkg/                 # Public libraries
├── internal/            # Private code
├── examples/            # Example programs
├── configs/             # Configuration files
├── docs/                # Documentation
├── tests/               # Test files
└── scripts/             # Build scripts
```

### Development Commands

```bash
make fmt        # Format code
make vet        # Static analysis
make test       # Run tests
make test-cover # Run tests with coverage
```

## License

MIT License - see LICENSE file for details.