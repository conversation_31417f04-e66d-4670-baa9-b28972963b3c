package logger

import (
	"testing"
)

func TestNewLogger(t *testing.T) {
	config := &Config{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	if logger == nil {
		t.Fatal("Expected logger to be created")
	}
}

func TestLoggerWithFields(t *testing.T) {
	config := &Config{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test logging with fields
	fieldLogger := logger.WithFields(map[string]interface{}{
		"user_id": 123,
		"action":  "login",
	})

	// This should not panic
	fieldLogger.Info("User logged in")
}

func TestLoggerWithField(t *testing.T) {
	config := &Config{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test logging with single field
	fieldLogger := logger.WithField("component", "agent")

	// This should not panic
	fieldLogger.Info("Agent started")
}

func TestLoggerTextFormat(t *testing.T) {
	config := &Config{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// This should not panic
	logger.Info("test message")
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config.Level != "info" {
		t.Errorf("Expected default level 'info', got '%s'", config.Level)
	}
	if config.Format != "json" {
		t.Errorf("Expected default format 'json', got '%s'", config.Format)
	}
	if config.Output != "stdout" {
		t.Errorf("Expected default output 'stdout', got '%s'", config.Output)
	}
}

func TestLoggerLevels(t *testing.T) {
	config := &Config{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test different log levels - these should not panic
	logger.Debug("debug message")
	logger.Info("info message")
	logger.Warn("warn message")
	logger.Error("error message")
}

func TestLoggerWithError(t *testing.T) {
	config := &Config{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test logging with error
	testErr := &AgentScopeError{
		Type:    ErrorTypeValidation,
		Code:    "TEST_001",
		Message: "test error",
	}

	errorLogger := logger.WithError(testErr)

	// This should not panic
	errorLogger.Error("An error occurred")
}

// AgentScopeError for testing
type AgentScopeError struct {
	Type    string
	Code    string
	Message string
}

func (e *AgentScopeError) Error() string {
	return e.Message
}

const (
	ErrorTypeValidation = "validation"
)
