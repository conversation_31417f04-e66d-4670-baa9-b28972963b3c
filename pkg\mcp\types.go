package mcp

import (
	"context"
	"encoding/json"
	"time"
)

// MCPClient MCP协议客户端接口
type MCPClient interface {
	// Connect 连接到MCP服务器
	Connect(ctx context.Context, config *MCPConfig) error

	// Disconnect 断开连接
	Disconnect() error

	// IsConnected 检查连接状态
	IsConnected() bool

	// ListTools 列出可用工具
	ListTools(ctx context.Context) ([]*MCPTool, error)

	// CallTool 调用工具
	CallTool(ctx context.Context, name string, arguments map[string]interface{}) (*MCPToolResult, error)

	// ListResources 列出可用资源
	ListResources(ctx context.Context) ([]*MCPResource, error)

	// ReadResource 读取资源
	ReadResource(ctx context.Context, uri string) (*MCPResourceContent, error)

	// Subscribe 订阅资源变更
	Subscribe(ctx context.Context, uri string) (<-chan *MCPResourceChange, error)

	// Unsubscribe 取消订阅
	Unsubscribe(ctx context.Context, uri string) error
}

// MCPConfig MCP配置
type MCPConfig struct {
	ServerCommand []string               `json:"server_command"`
	ServerArgs    []string               `json:"server_args,omitempty"`
	Environment   map[string]string      `json:"environment,omitempty"`
	WorkingDir    string                 `json:"working_dir,omitempty"`
	Timeout       time.Duration          `json:"timeout,omitempty"`
	MaxRetries    int                    `json:"max_retries,omitempty"`
	Parameters    map[string]interface{} `json:"parameters,omitempty"`
}

// MCPTool MCP工具定义
type MCPTool struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	InputSchema *MCPSchema `json:"inputSchema"`
}

// MCPSchema MCP模式定义
type MCPSchema struct {
	Type       string                  `json:"type"`
	Properties map[string]*MCPProperty `json:"properties,omitempty"`
	Required   []string                `json:"required,omitempty"`
	Items      *MCPSchema              `json:"items,omitempty"`
}

// MCPProperty MCP属性定义
type MCPProperty struct {
	Type        string      `json:"type"`
	Description string      `json:"description,omitempty"`
	Default     interface{} `json:"default,omitempty"`
	Enum        []string    `json:"enum,omitempty"`
	Minimum     *float64    `json:"minimum,omitempty"`
	Maximum     *float64    `json:"maximum,omitempty"`
}

// MCPToolResult MCP工具执行结果
type MCPToolResult struct {
	Content  []MCPContent           `json:"content"`
	IsError  bool                   `json:"isError,omitempty"`
	Metadata map[string]interface{} `json:"_meta,omitempty"`
}

// MCPContent MCP内容
type MCPContent struct {
	Type     string      `json:"type"`
	Text     string      `json:"text,omitempty"`
	Data     interface{} `json:"data,omitempty"`
	MimeType string      `json:"mimeType,omitempty"`
}

// MCPResource MCP资源定义
type MCPResource struct {
	URI         string                 `json:"uri"`
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	MimeType    string                 `json:"mimeType,omitempty"`
	Metadata    map[string]interface{} `json:"_meta,omitempty"`
}

// MCPResourceContent MCP资源内容
type MCPResourceContent struct {
	URI      string       `json:"uri"`
	MimeType string       `json:"mimeType,omitempty"`
	Contents []MCPContent `json:"contents"`
}

// MCPResourceChange MCP资源变更
type MCPResourceChange struct {
	URI       string                 `json:"uri"`
	Type      string                 `json:"type"` // "created", "updated", "deleted"
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// MCPMessage MCP消息
type MCPMessage struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id,omitempty"`
	Method  string      `json:"method,omitempty"`
	Params  interface{} `json:"params,omitempty"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError MCP错误
type MCPError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MCPRequest MCP请求
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse MCP响应
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPNotification MCP通知
type MCPNotification struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCP方法常量
const (
	MCPMethodInitialize      = "initialize"
	MCPMethodInitialized     = "initialized"
	MCPMethodListTools       = "tools/list"
	MCPMethodCallTool        = "tools/call"
	MCPMethodListResources   = "resources/list"
	MCPMethodReadResource    = "resources/read"
	MCPMethodSubscribe       = "resources/subscribe"
	MCPMethodUnsubscribe     = "resources/unsubscribe"
	MCPMethodResourceUpdated = "notifications/resources/updated"
)

// MCP错误码常量
const (
	MCPErrorCodeParseError     = -32700
	MCPErrorCodeInvalidRequest = -32600
	MCPErrorCodeMethodNotFound = -32601
	MCPErrorCodeInvalidParams  = -32602
	MCPErrorCodeInternalError  = -32603
	MCPErrorCodeServerError    = -32000
)

// MCPCapabilities MCP能力
type MCPCapabilities struct {
	Tools     *MCPToolsCapability     `json:"tools,omitempty"`
	Resources *MCPResourcesCapability `json:"resources,omitempty"`
	Logging   *MCPLoggingCapability   `json:"logging,omitempty"`
}

// MCPToolsCapability 工具能力
type MCPToolsCapability struct {
	ListChanged bool `json:"listChanged,omitempty"`
}

// MCPResourcesCapability 资源能力
type MCPResourcesCapability struct {
	Subscribe   bool `json:"subscribe,omitempty"`
	ListChanged bool `json:"listChanged,omitempty"`
}

// MCPLoggingCapability 日志能力
type MCPLoggingCapability struct {
	Level string `json:"level,omitempty"`
}

// MCPInitializeParams 初始化参数
type MCPInitializeParams struct {
	ProtocolVersion string           `json:"protocolVersion"`
	Capabilities    *MCPCapabilities `json:"capabilities"`
	ClientInfo      *MCPClientInfo   `json:"clientInfo"`
}

// MCPClientInfo 客户端信息
type MCPClientInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// MCPInitializeResult 初始化结果
type MCPInitializeResult struct {
	ProtocolVersion string           `json:"protocolVersion"`
	Capabilities    *MCPCapabilities `json:"capabilities"`
	ServerInfo      *MCPServerInfo   `json:"serverInfo"`
}

// MCPServerInfo 服务器信息
type MCPServerInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// MCPListToolsResult 列出工具结果
type MCPListToolsResult struct {
	Tools []MCPTool `json:"tools"`
}

// MCPCallToolParams 调用工具参数
type MCPCallToolParams struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments,omitempty"`
}

// MCPListResourcesResult 列出资源结果
type MCPListResourcesResult struct {
	Resources []MCPResource `json:"resources"`
}

// MCPReadResourceParams 读取资源参数
type MCPReadResourceParams struct {
	URI string `json:"uri"`
}

// MCPSubscribeParams 订阅参数
type MCPSubscribeParams struct {
	URI string `json:"uri"`
}

// Validate 验证MCP配置
func (c *MCPConfig) Validate() error {
	if len(c.ServerCommand) == 0 {
		return &ValidationError{Field: "server_command", Message: "server command cannot be empty"}
	}

	if c.Timeout <= 0 {
		c.Timeout = 30 * time.Second
	}

	if c.MaxRetries < 0 {
		c.MaxRetries = 3
	}

	return nil
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}

// ToJSON 转换为JSON
func (m *MCPMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON 从JSON解析
func (m *MCPMessage) FromJSON(data []byte) error {
	return json.Unmarshal(data, m)
}

// IsRequest 检查是否为请求
func (m *MCPMessage) IsRequest() bool {
	return m.Method != "" && m.ID != nil
}

// IsResponse 检查是否为响应
func (m *MCPMessage) IsResponse() bool {
	return m.Method == "" && m.ID != nil
}

// IsNotification 检查是否为通知
func (m *MCPMessage) IsNotification() bool {
	return m.Method != "" && m.ID == nil
}

// NewMCPRequest 创建MCP请求
func NewMCPRequest(id interface{}, method string, params interface{}) *MCPRequest {
	return &MCPRequest{
		JSONRPC: "2.0",
		ID:      id,
		Method:  method,
		Params:  params,
	}
}

// NewMCPResponse 创建MCP响应
func NewMCPResponse(id interface{}, result interface{}) *MCPResponse {
	return &MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result:  result,
	}
}

// NewMCPErrorResponse 创建MCP错误响应
func NewMCPErrorResponse(id interface{}, code int, message string, data interface{}) *MCPResponse {
	return &MCPResponse{
		JSONRPC: "2.0",
		ID:      id,
		Error: &MCPError{
			Code:    code,
			Message: message,
			Data:    data,
		},
	}
}

// NewMCPNotification 创建MCP通知
func NewMCPNotification(method string, params interface{}) *MCPNotification {
	return &MCPNotification{
		JSONRPC: "2.0",
		Method:  method,
		Params:  params,
	}
}
