package logger

import (
	"os"
	"strings"
	"sync"
)

var (
	globalLogger Logger
	once         sync.Once
)

// InitGlobalLogger initializes the global logger
func InitGlobalLogger(config *Config) error {
	var err error
	once.Do(func() {
		globalLogger, err = NewLogger(config)
	})
	return err
}

// GetGlobalLogger returns the global logger instance
func GetGlobalLogger() Logger {
	if globalLogger == nil {
		// Initialize with default config if not already initialized
		config := DefaultConfig()

		// Override with environment variables if present
		if level := os.Getenv("LOG_LEVEL"); level != "" {
			config.Level = strings.ToLower(level)
		}
		if format := os.Getenv("LOG_FORMAT"); format != "" {
			config.Format = strings.ToLower(format)
		}

		logger, err := NewLogger(config)
		if err != nil {
			// Fallback to a basic logger if initialization fails
			logger, _ = NewLogger(DefaultConfig())
		}
		globalLogger = logger
	}
	return globalLogger
}

// Convenience functions that use the global logger

// Debug logs a debug message using the global logger
func Debug(args ...interface{}) {
	GetGlobalLogger().Debug(args...)
}

// Debugf logs a formatted debug message using the global logger
func Debugf(format string, args ...interface{}) {
	GetGlobalLogger().Debugf(format, args...)
}

// Info logs an info message using the global logger
func Info(args ...interface{}) {
	GetGlobalLogger().Info(args...)
}

// Infof logs a formatted info message using the global logger
func Infof(format string, args ...interface{}) {
	GetGlobalLogger().Infof(format, args...)
}

// Warn logs a warning message using the global logger
func Warn(args ...interface{}) {
	GetGlobalLogger().Warn(args...)
}

// Warnf logs a formatted warning message using the global logger
func Warnf(format string, args ...interface{}) {
	GetGlobalLogger().Warnf(format, args...)
}

// Error logs an error message using the global logger
func Error(args ...interface{}) {
	GetGlobalLogger().Error(args...)
}

// Errorf logs a formatted error message using the global logger
func Errorf(format string, args ...interface{}) {
	GetGlobalLogger().Errorf(format, args...)
}

// Fatal logs a fatal message using the global logger and exits
func Fatal(args ...interface{}) {
	GetGlobalLogger().Fatal(args...)
}

// Fatalf logs a formatted fatal message using the global logger and exits
func Fatalf(format string, args ...interface{}) {
	GetGlobalLogger().Fatalf(format, args...)
}

// WithField adds a field to the global logger
func WithField(key string, value interface{}) Logger {
	return GetGlobalLogger().WithField(key, value)
}

// WithFields adds multiple fields to the global logger
func WithFields(fields map[string]interface{}) Logger {
	return GetGlobalLogger().WithFields(fields)
}

// WithError adds an error to the global logger
func WithError(err error) Logger {
	return GetGlobalLogger().WithError(err)
}
