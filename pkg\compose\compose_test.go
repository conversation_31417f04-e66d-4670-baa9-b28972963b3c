package compose

import (
"testing"

"github.com/stretchr/testify/assert"
)

// TestNewSequentialAgent 测试创建顺序组合智能体
func TestNewSequentialAgent(t *testing.T) {
// 测试空配置
seqAgent, err := NewSequentialAgent(nil)
assert.<PERSON>rror(t, err)
assert.Nil(t, seqAgent)
assert.Contains(t, err.Error(), "配置不能为空")
}

// TestFailureMode 测试失败模式常量
func TestFailureMode(t *testing.T) {
assert.Equal(t, FailureMode("stop"), FailureModeStop)
assert.Equal(t, FailureMode("continue"), FailureModeContinue)
assert.Equal(t, FailureMode("retry"), FailureModeRetry)
}
