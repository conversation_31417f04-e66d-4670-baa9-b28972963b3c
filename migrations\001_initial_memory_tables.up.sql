-- 初始内存系统表（memory_entries）
-- 适配 PostgreSQL
CREATE TABLE IF NOT EXISTS memory_entries (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL,
    content JSONB,
    metadata JSONB,
    timestamp TIMESTAMPTZ NOT NULL,
    ttl_seconds BIGINT NULL,
    tags TEXT[] NULL,
    score DOUBLE PRECISION NOT NULL DEFAULT 0
);
CREATE INDEX IF NOT EXISTS idx_memory_entries_type ON memory_entries(type);
CREATE INDEX IF NOT EXISTS idx_memory_entries_timestamp ON memory_entries(timestamp);
CREATE INDEX IF NOT EXISTS idx_memory_entries_score ON memory_entries(score);

