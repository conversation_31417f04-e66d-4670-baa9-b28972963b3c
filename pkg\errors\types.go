// Package errors provides structured error handling for AgentScope-Golang
package errors

import (
	"fmt"
	"time"
)

// ErrorType represents the category of error
type ErrorType string

const (
	// ErrorTypeValidation represents validation errors
	ErrorTypeValidation ErrorType = "validation"
	// ErrorTypeNetwork represents network-related errors
	ErrorTypeNetwork ErrorType = "network"
	// ErrorTypeTimeout represents timeout errors
	ErrorTypeTimeout ErrorType = "timeout"
	// ErrorTypeAuth represents authentication/authorization errors
	ErrorTypeAuth ErrorType = "auth"
	// ErrorTypeInternal represents internal system errors
	ErrorTypeInternal ErrorType = "internal"
	// ErrorTypeLLM represents LLM service errors
	ErrorTypeLLM ErrorType = "llm"
	// ErrorTypeTool represents tool execution errors
	ErrorTypeTool ErrorType = "tool"
	// ErrorTypeAgent represents agent-related errors
	ErrorTypeAgent ErrorType = "agent"
	// ErrorTypePipeline represents pipeline execution errors
	ErrorTypePipeline ErrorType = "pipeline"
)

// AgentScopeError represents a structured error in the AgentScope system
type AgentScopeError struct {
	Type      ErrorType              `json:"type"`
	Code      string                 `json:"code"`
	Message   string                 `json:"message"`
	Details   string                 `json:"details,omitempty"`
	Context   map[string]interface{} `json:"context,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Cause     error                  `json:"-"`
}

// Error implements the error interface
func (e *AgentScopeError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s:%s] %s: %s", e.Type, e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

// Unwrap returns the underlying cause error
func (e *AgentScopeError) Unwrap() error {
	return e.Cause
}

// WithContext adds context information to the error
func (e *AgentScopeError) WithContext(key string, value interface{}) *AgentScopeError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithCause sets the underlying cause error
func (e *AgentScopeError) WithCause(cause error) *AgentScopeError {
	e.Cause = cause
	return e
}

// IsType checks if the error is of a specific type
func (e *AgentScopeError) IsType(errorType ErrorType) bool {
	return e.Type == errorType
}

// IsCode checks if the error has a specific code
func (e *AgentScopeError) IsCode(code string) bool {
	return e.Code == code
}
