package runtime

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"
)

func createTempDB(t *testing.T) (string, func()) {
	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_checkpoint.db")

	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return dbPath, cleanup
}

func TestNewMemoryCheckpointStore(t *testing.T) {
	store := NewMemoryCheckpointStore()
	if store == nil {
		t.Fatal("创建内存检查点存储失败")
	}
	defer store.Close()
}

func TestNewSQLiteCheckpointStore(t *testing.T) {
	t.Skip("跳过 SQLite 测试，在某些环境下可能有问题")
}

func TestCheckpointStoreSaveAndLoad(t *testing.T) {
	store := NewMemoryCheckpointStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session-123"

	// 创建测试数据
	data := &CheckpointData{
		SessionID: sessionID,
		AgentName: "test-agent",
		State: map[string]any{
			"step":     1,
			"progress": 0.5,
			"data":     []string{"item1", "item2"},
		},
		Metadata: map[string]any{
			"version": "1.0",
			"user_id": "user123",
		},
	}

	// 保存检查点
	err := store.Save(ctx, sessionID, data)
	if err != nil {
		t.Fatalf("保存检查点失败: %v", err)
	}

	// 加载检查点
	loaded, err := store.Load(ctx, sessionID)
	if err != nil {
		t.Fatalf("加载检查点失败: %v", err)
	}

	// 验证数据
	if loaded.SessionID != sessionID {
		t.Errorf("会话ID不匹配: got %s, want %s", loaded.SessionID, sessionID)
	}

	if loaded.AgentName != data.AgentName {
		t.Errorf("智能体名称不匹配: got %s, want %s", loaded.AgentName, data.AgentName)
	}

	// 验证状态数据
	if loaded.State["step"] != 1 { // 内存存储保持原始类型
		t.Errorf("状态数据不匹配: step = %v", loaded.State["step"])
	}

	if loaded.State["progress"] != 0.5 {
		t.Errorf("状态数据不匹配: progress = %v", loaded.State["progress"])
	}

	// 验证元数据
	if loaded.Metadata["version"] != "1.0" {
		t.Errorf("元数据不匹配: version = %v", loaded.Metadata["version"])
	}

	if loaded.Metadata["user_id"] != "user123" {
		t.Errorf("元数据不匹配: user_id = %v", loaded.Metadata["user_id"])
	}

	// 验证时间戳
	if loaded.CreatedAt.IsZero() {
		t.Error("创建时间不应该为零值")
	}

	if loaded.UpdatedAt.IsZero() {
		t.Error("更新时间不应该为零值")
	}
}

func TestCheckpointStoreUpdate(t *testing.T) {
	store := NewMemoryCheckpointStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session-update"

	// 创建初始数据
	data1 := &CheckpointData{
		SessionID: sessionID,
		AgentName: "agent-v1",
		State: map[string]any{
			"step": 1,
		},
		Metadata: map[string]any{
			"version": "1.0",
		},
	}

	// 保存初始检查点
	err := store.Save(ctx, sessionID, data1)
	if err != nil {
		t.Fatalf("保存初始检查点失败: %v", err)
	}

	// 等待一小段时间确保时间戳不同
	time.Sleep(10 * time.Millisecond)

	// 更新数据
	data2 := &CheckpointData{
		SessionID: sessionID,
		AgentName: "agent-v2",
		State: map[string]any{
			"step": 2,
		},
		Metadata: map[string]any{
			"version": "2.0",
		},
	}

	// 更新检查点
	err = store.Save(ctx, sessionID, data2)
	if err != nil {
		t.Fatalf("更新检查点失败: %v", err)
	}

	// 加载更新后的检查点
	loaded, err := store.Load(ctx, sessionID)
	if err != nil {
		t.Fatalf("加载更新后的检查点失败: %v", err)
	}

	// 验证更新
	if loaded.AgentName != "agent-v2" {
		t.Errorf("智能体名称未更新: got %s, want %s", loaded.AgentName, "agent-v2")
	}

	if loaded.State["step"] != 2 {
		t.Errorf("状态未更新: step = %v", loaded.State["step"])
	}

	if loaded.Metadata["version"] != "2.0" {
		t.Errorf("元数据未更新: version = %v", loaded.Metadata["version"])
	}

	// 验证时间戳
	if loaded.UpdatedAt.Before(loaded.CreatedAt) || loaded.UpdatedAt.Equal(loaded.CreatedAt) {
		t.Error("更新时间应该晚于创建时间")
	}
}

func TestCheckpointStoreExists(t *testing.T) {
	store := NewMemoryCheckpointStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session-exists"

	// 检查不存在的检查点
	exists, err := store.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if exists {
		t.Error("不存在的检查点不应该返回 true")
	}

	// 创建检查点
	data := &CheckpointData{
		SessionID: sessionID,
		AgentName: "test-agent",
		State:     map[string]any{"test": true},
	}

	err = store.Save(ctx, sessionID, data)
	if err != nil {
		t.Fatalf("保存检查点失败: %v", err)
	}

	// 检查存在的检查点
	exists, err = store.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if !exists {
		t.Error("存在的检查点应该返回 true")
	}
}

func TestCheckpointStoreDelete(t *testing.T) {
	store := NewMemoryCheckpointStore()
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-session-delete"

	// 创建检查点
	data := &CheckpointData{
		SessionID: sessionID,
		AgentName: "test-agent",
		State:     map[string]any{"test": true},
	}

	err := store.Save(ctx, sessionID, data)
	if err != nil {
		t.Fatalf("保存检查点失败: %v", err)
	}

	// 验证检查点存在
	exists, err := store.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if !exists {
		t.Error("检查点应该存在")
	}

	// 删除检查点
	err = store.Delete(ctx, sessionID)
	if err != nil {
		t.Fatalf("删除检查点失败: %v", err)
	}

	// 验证检查点不存在
	exists, err = store.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if exists {
		t.Error("删除后检查点不应该存在")
	}

	// 尝试删除不存在的检查点
	err = store.Delete(ctx, sessionID)
	if err == nil {
		t.Error("删除不存在的检查点应该返回错误")
	}
}

func TestCheckpointStoreList(t *testing.T) {
	store := NewMemoryCheckpointStore()
	defer store.Close()

	ctx := context.Background()

	// 检查空列表
	list, err := store.List(ctx)
	if err != nil {
		t.Fatalf("列出检查点失败: %v", err)
	}
	if len(list) != 0 {
		t.Errorf("空存储应该返回空列表: got %d items", len(list))
	}

	// 创建多个检查点
	sessionIDs := []string{"session-1", "session-2", "session-3"}

	for i, sessionID := range sessionIDs {
		data := &CheckpointData{
			SessionID: sessionID,
			AgentName: "test-agent",
			State:     map[string]any{"index": i},
		}

		err := store.Save(ctx, sessionID, data)
		if err != nil {
			t.Fatalf("保存检查点 %s 失败: %v", sessionID, err)
		}

		// 添加小延迟确保创建时间不同
		time.Sleep(1 * time.Millisecond)
	}

	// 列出所有检查点
	list, err = store.List(ctx)
	if err != nil {
		t.Fatalf("列出检查点失败: %v", err)
	}

	if len(list) != len(sessionIDs) {
		t.Errorf("检查点数量不匹配: got %d, want %d", len(list), len(sessionIDs))
	}

	// 验证所有会话ID都在列表中
	sessionMap := make(map[string]bool)
	for _, sessionID := range list {
		sessionMap[sessionID] = true
	}

	for _, expectedID := range sessionIDs {
		if !sessionMap[expectedID] {
			t.Errorf("缺少预期的会话ID: %s", expectedID)
		}
	}
}

func TestCheckpointStoreLoadNonExistent(t *testing.T) {
	store := NewMemoryCheckpointStore()
	defer store.Close()

	ctx := context.Background()

	// 尝试加载不存在的检查点
	_, err := store.Load(ctx, "nonexistent-session")
	if err == nil {
		t.Error("加载不存在的检查点应该返回错误")
	}
}

func TestCheckpointStoreStats(t *testing.T) {
	t.Skip("跳过统计测试，内存存储不支持统计功能")
}

func TestCheckpointStoreClose(t *testing.T) {
	store := NewMemoryCheckpointStore()

	// 关闭存储
	err := store.Close()
	if err != nil {
		t.Fatalf("关闭存储失败: %v", err)
	}

	// 再次关闭应该不会出错
	err = store.Close()
	if err != nil {
		t.Fatalf("重复关闭存储失败: %v", err)
	}
}

// TestPostgresCheckpointStore 测试PostgreSQL检查点存储
func TestPostgresCheckpointStore(t *testing.T) {
	// 检查是否有PostgreSQL环境变量
	pgHost := os.Getenv("POSTGRES_HOST")
	pgUser := os.Getenv("POSTGRES_USER")
	pgPassword := os.Getenv("POSTGRES_PASSWORD")
	pgDB := os.Getenv("POSTGRES_DB")

	if pgHost == "" || pgUser == "" || pgDB == "" {
		t.Skip("跳过PostgreSQL测试：缺少环境变量 POSTGRES_HOST, POSTGRES_USER, POSTGRES_DB")
	}

	config := &PostgresCheckpointConfig{
		Host:     pgHost,
		Port:     5432,
		Database: pgDB,
		Username: pgUser,
		Password: pgPassword,
		SSLMode:  "disable",
	}

	store, err := NewPostgresCheckpointStore(config)
	if err != nil {
		t.Skipf("跳过PostgreSQL测试：无法连接数据库: %v", err)
	}
	defer store.Close()

	ctx := context.Background()
	sessionID := "test-postgres-session-123"

	// 清理可能存在的测试数据
	_ = store.Delete(ctx, sessionID)

	// 创建测试数据
	data := &CheckpointData{
		SessionID: sessionID,
		AgentName: "test-postgres-agent",
		State: map[string]any{
			"step":     1,
			"progress": 0.5,
			"data":     []string{"item1", "item2"},
		},
		Metadata: map[string]any{
			"version": "1.0",
			"user_id": "user123",
		},
	}

	// 保存检查点
	err = store.Save(ctx, sessionID, data)
	if err != nil {
		t.Fatalf("保存检查点失败: %v", err)
	}

	// 加载检查点
	loaded, err := store.Load(ctx, sessionID)
	if err != nil {
		t.Fatalf("加载检查点失败: %v", err)
	}

	// 验证数据
	if loaded.SessionID != sessionID {
		t.Errorf("会话ID不匹配: got %s, want %s", loaded.SessionID, sessionID)
	}

	if loaded.AgentName != data.AgentName {
		t.Errorf("智能体名称不匹配: got %s, want %s", loaded.AgentName, data.AgentName)
	}

	// 验证状态数据（JSON反序列化后数字变为float64）
	if loaded.State["step"].(float64) != 1 {
		t.Errorf("状态数据不匹配: step = %v", loaded.State["step"])
	}

	if loaded.State["progress"].(float64) != 0.5 {
		t.Errorf("状态数据不匹配: progress = %v", loaded.State["progress"])
	}

	// 验证元数据
	if loaded.Metadata["version"] != "1.0" {
		t.Errorf("元数据不匹配: version = %v", loaded.Metadata["version"])
	}

	// 测试检查点是否存在
	exists, err := store.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if !exists {
		t.Error("检查点应该存在")
	}

	// 测试列出检查点
	list, err := store.List(ctx)
	if err != nil {
		t.Fatalf("列出检查点失败: %v", err)
	}

	found := false
	for _, id := range list {
		if id == sessionID {
			found = true
			break
		}
	}
	if !found {
		t.Error("检查点应该在列表中")
	}

	// 清理测试数据
	err = store.Delete(ctx, sessionID)
	if err != nil {
		t.Fatalf("删除检查点失败: %v", err)
	}

	// 验证删除后不存在
	exists, err = store.Exists(ctx, sessionID)
	if err != nil {
		t.Fatalf("检查检查点是否存在失败: %v", err)
	}
	if exists {
		t.Error("删除后检查点不应该存在")
	}
}
