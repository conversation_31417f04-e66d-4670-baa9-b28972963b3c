package ticket

import (
	"context"
	"fmt"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// TicketStatus 工单状态
type TicketStatus string

const (
	StatusOpen       TicketStatus = "open"        // 开放
	StatusInProgress TicketStatus = "in_progress" // 处理中
	StatusResolved   TicketStatus = "resolved"    // 已解决
	StatusClosed     TicketStatus = "closed"      // 已关闭
)

// TicketPriority 工单优先级
type TicketPriority string

const (
	PriorityLow    TicketPriority = "low"    // 低
	PriorityMedium TicketPriority = "medium" // 中
	PriorityHigh   TicketPriority = "high"   // 高
	PriorityUrgent TicketPriority = "urgent" // 紧急
)

// Ticket 工单结构
type Ticket struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"user_id"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Priority    TicketPriority         `json:"priority"`
	Status      TicketStatus           `json:"status"`
	AssignedTo  string                 `json:"assigned_to,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// TicketTool 工单管理工具
type TicketTool struct {
	tickets map[string]*Ticket // 简单的内存存储，实际应用中应使用数据库
}

// NewTicketTool 创建工单管理工具
func NewTicketTool() *TicketTool {
	return &TicketTool{
		tickets: make(map[string]*Ticket),
	}
}

// Name 返回工具名称
func (t *TicketTool) Name() string {
	return "ticket_manager"
}

// Description 返回工具描述
func (t *TicketTool) Description() string {
	return "工单管理工具，支持创建、查询、更新和关闭工单"
}

// Parameters 返回工具参数定义
func (t *TicketTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type":        "string",
				"description": "操作类型：create, get, update, close, list",
				"enum":        []string{"create", "get", "update", "close", "list"},
			},
			"ticket_id": map[string]interface{}{
				"type":        "string",
				"description": "工单ID（get、update、close操作必需）",
			},
			"user_id": map[string]interface{}{
				"type":        "string",
				"description": "用户ID",
			},
			"title": map[string]interface{}{
				"type":        "string",
				"description": "工单标题（create操作必需）",
			},
			"description": map[string]interface{}{
				"type":        "string",
				"description": "工单描述（create操作必需）",
			},
			"category": map[string]interface{}{
				"type":        "string",
				"description": "工单类别",
			},
			"priority": map[string]interface{}{
				"type":        "string",
				"description": "优先级：low, medium, high, urgent",
				"enum":        []string{"low", "medium", "high", "urgent"},
			},
			"status": map[string]interface{}{
				"type":        "string",
				"description": "工单状态：open, in_progress, resolved, closed",
				"enum":        []string{"open", "in_progress", "resolved", "closed"},
			},
			"assigned_to": map[string]interface{}{
				"type":        "string",
				"description": "分配给谁",
			},
		},
		"required": []string{"action"},
	}
}

// Execute 执行工具
func (t *TicketTool) Execute(ctx context.Context, params map[string]interface{}) (interface{}, error) {
	action, ok := params["action"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少必需参数: action")
	}

	switch action {
	case "create":
		return t.createTicket(params)
	case "get":
		return t.getTicket(params)
	case "update":
		return t.updateTicket(params)
	case "close":
		return t.closeTicket(params)
	case "list":
		return t.listTickets(params)
	default:
		return nil, fmt.Errorf("不支持的操作: %s", action)
	}
}

// createTicket 创建工单
func (t *TicketTool) createTicket(params map[string]interface{}) (interface{}, error) {
	userID, _ := params["user_id"].(string)
	title, ok := params["title"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少必需参数: title")
	}

	description, ok := params["description"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少必需参数: description")
	}

	category, _ := params["category"].(string)
	priorityStr, _ := params["priority"].(string)
	if priorityStr == "" {
		priorityStr = "medium"
	}

	// 生成工单ID
	ticketID := fmt.Sprintf("TK-%d", time.Now().Unix())

	ticket := &Ticket{
		ID:          ticketID,
		UserID:      userID,
		Title:       title,
		Description: description,
		Category:    category,
		Priority:    TicketPriority(priorityStr),
		Status:      StatusOpen,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	t.tickets[ticketID] = ticket

	return map[string]interface{}{
		"success":   true,
		"ticket_id": ticketID,
		"message":   "工单创建成功",
		"ticket":    ticket,
	}, nil
}

// getTicket 获取工单
func (t *TicketTool) getTicket(params map[string]interface{}) (interface{}, error) {
	ticketID, ok := params["ticket_id"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少必需参数: ticket_id")
	}

	ticket, exists := t.tickets[ticketID]
	if !exists {
		return map[string]interface{}{
			"success": false,
			"message": "工单不存在",
		}, nil
	}

	return map[string]interface{}{
		"success": true,
		"ticket":  ticket,
	}, nil
}

// updateTicket 更新工单
func (t *TicketTool) updateTicket(params map[string]interface{}) (interface{}, error) {
	ticketID, ok := params["ticket_id"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少必需参数: ticket_id")
	}

	ticket, exists := t.tickets[ticketID]
	if !exists {
		return map[string]interface{}{
			"success": false,
			"message": "工单不存在",
		}, nil
	}

	// 更新字段
	if status, ok := params["status"].(string); ok {
		ticket.Status = TicketStatus(status)
		if status == "resolved" {
			now := time.Now()
			ticket.ResolvedAt = &now
		}
	}

	if assignedTo, ok := params["assigned_to"].(string); ok {
		ticket.AssignedTo = assignedTo
	}

	if priority, ok := params["priority"].(string); ok {
		ticket.Priority = TicketPriority(priority)
	}

	ticket.UpdatedAt = time.Now()

	return map[string]interface{}{
		"success": true,
		"message": "工单更新成功",
		"ticket":  ticket,
	}, nil
}

// closeTicket 关闭工单
func (t *TicketTool) closeTicket(params map[string]interface{}) (interface{}, error) {
	ticketID, ok := params["ticket_id"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少必需参数: ticket_id")
	}

	ticket, exists := t.tickets[ticketID]
	if !exists {
		return map[string]interface{}{
			"success": false,
			"message": "工单不存在",
		}, nil
	}

	ticket.Status = StatusClosed
	ticket.UpdatedAt = time.Now()

	return map[string]interface{}{
		"success": true,
		"message": "工单已关闭",
		"ticket":  ticket,
	}, nil
}

// listTickets 列出工单
func (t *TicketTool) listTickets(params map[string]interface{}) (interface{}, error) {
	userID, _ := params["user_id"].(string)
	status, _ := params["status"].(string)

	var filteredTickets []*Ticket
	for _, ticket := range t.tickets {
		// 按用户ID过滤
		if userID != "" && ticket.UserID != userID {
			continue
		}

		// 按状态过滤
		if status != "" && string(ticket.Status) != status {
			continue
		}

		filteredTickets = append(filteredTickets, ticket)
	}

	return map[string]interface{}{
		"success": true,
		"tickets": filteredTickets,
		"count":   len(filteredTickets),
	}, nil
}

// Validate 验证工具参数
func (t *TicketTool) Validate(params map[string]interface{}) error {
	action, ok := params["action"].(string)
	if !ok {
		return fmt.Errorf("缺少必需参数: action")
	}

	switch action {
	case "create":
		if _, ok := params["title"].(string); !ok {
			return fmt.Errorf("create操作缺少必需参数: title")
		}
		if _, ok := params["description"].(string); !ok {
			return fmt.Errorf("create操作缺少必需参数: description")
		}
	case "get", "update", "close":
		if _, ok := params["ticket_id"].(string); !ok {
			return fmt.Errorf("%s操作缺少必需参数: ticket_id", action)
		}
	case "list":
		// list操作不需要额外参数
	default:
		return fmt.Errorf("不支持的操作: %s", action)
	}

	return nil
}

// Schema 返回工具的JSON Schema
func (t *TicketTool) Schema() *tool.JSONSchema {
	return &tool.JSONSchema{
		Type: "object",
		Properties: map[string]*tool.JSONSchema{
			"action": {
				Type:        "string",
				Description: "操作类型",
				Enum:        []interface{}{"create", "get", "update", "close", "list"},
			},
			"ticket_id": {
				Type:        "string",
				Description: "工单ID（get、update、close操作必需）",
			},
			"title": {
				Type:        "string",
				Description: "工单标题（create操作必需）",
			},
			"description": {
				Type:        "string",
				Description: "工单描述（create操作必需）",
			},
			"category": {
				Type:        "string",
				Description: "工单分类",
			},
			"priority": {
				Type:        "string",
				Description: "优先级",
				Enum:        []interface{}{"low", "medium", "high", "urgent"},
			},
			"status": {
				Type:        "string",
				Description: "工单状态（update操作使用）",
				Enum:        []interface{}{"open", "in_progress", "resolved", "closed"},
			},
			"assignee": {
				Type:        "string",
				Description: "分配给（update操作使用）",
			},
			"resolution": {
				Type:        "string",
				Description: "解决方案（update操作使用）",
			},
		},
		Required: []string{"action"},
	}
}
