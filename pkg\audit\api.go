package audit

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
)

// APIHandler 审计API处理器
type APIHandler struct {
	reader  *AuditReader
	manager *RetentionManager
	config  *WebConfig
}

// NewAPIHandler 创建审计API处理器
func NewAPIHandler(reader *AuditReader, manager *RetentionManager, config *WebConfig) *APIHandler {
	return &APIHandler{
		reader:  reader,
		manager: manager,
		config:  config,
	}
}

// RegisterRoutes 注册路由
func (h *APIHandler) RegisterRoutes(router *mux.Router) {
	if !h.config.AllowReadAPI {
		return // 未启用读取API
	}

	// 审计查询API
	auditRouter := router.PathPrefix("/api/v1/audit").Subrouter()

	// 会话相关API
	auditRouter.HandleFunc("/sessions", h.handleGetSessions).Methods("GET")
	auditRouter.HandleFunc("/sessions/{session_id}/messages", h.handleGetSessionMessages).Methods("GET")
	auditRouter.HandleFunc("/sessions/{session_id}/summary", h.handleGetSessionSummary).Methods("GET")

	// 用户相关API
	auditRouter.HandleFunc("/users/{user_id}/messages", h.handleGetUserMessages).Methods("GET")
	auditRouter.HandleFunc("/users/{user_id}/activity", h.handleGetUserActivity).Methods("GET")

	// 搜索API
	auditRouter.HandleFunc("/search", h.handleSearchMessages).Methods("GET")

	// 保留策略API（需要管理员权限）
	if h.config.AllowDeleteAPI {
		auditRouter.HandleFunc("/retention/run", h.handleRunRetention).Methods("POST")
		auditRouter.HandleFunc("/retention/stats", h.handleGetRetentionStats).Methods("GET")
	}
}

// handleGetSessions 获取会话列表
func (h *APIHandler) handleGetSessions(w http.ResponseWriter, r *http.Request) {
	userID := r.URL.Query().Get("user_id")
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	sessions, total, err := h.reader.QuerySessions(r.Context(), userID, pageSize, offset)
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "查询会话失败", err)
		return
	}

	response := map[string]interface{}{
		"sessions":  sessions,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"has_more":  offset+len(sessions) < total,
	}

	h.writeJSON(w, response)
}

// handleGetSessionMessages 获取会话消息
func (h *APIHandler) handleGetSessionMessages(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	sessionID := vars["session_id"]

	if sessionID == "" {
		h.writeError(w, http.StatusBadRequest, "会话ID不能为空", nil)
		return
	}

	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))
	msgType := r.URL.Query().Get("type")
	agentID := r.URL.Query().Get("agent_id")
	keyword := r.URL.Query().Get("q")

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 50
	}

	query := Query{
		SessionID: &sessionID,
		Limit:     pageSize,
		Offset:    (page - 1) * pageSize,
		Keyword:   keyword,
	}

	if agentID != "" {
		query.AgentID = &agentID
	}

	if msgType != "" {
		query.Types = []string{msgType}
	}

	result, err := h.reader.QueryMessages(r.Context(), query)
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "查询消息失败", err)
		return
	}

	response := map[string]interface{}{
		"messages":  result.Records,
		"total":     result.Total,
		"page":      page,
		"page_size": pageSize,
		"has_more":  result.HasMore,
	}

	h.writeJSON(w, response)
}

// handleGetSessionSummary 获取会话摘要
func (h *APIHandler) handleGetSessionSummary(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	sessionID := vars["session_id"]

	if sessionID == "" {
		h.writeError(w, http.StatusBadRequest, "会话ID不能为空", nil)
		return
	}

	summary, err := h.reader.GetSessionSummary(r.Context(), sessionID)
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "获取会话摘要失败", err)
		return
	}

	h.writeJSON(w, summary)
}

// handleGetUserMessages 获取用户消息
func (h *APIHandler) handleGetUserMessages(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["user_id"]

	if userID == "" {
		h.writeError(w, http.StatusBadRequest, "用户ID不能为空", nil)
		return
	}

	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 50
	}

	result, err := h.reader.QueryMessagesByUser(r.Context(), userID, pageSize, (page-1)*pageSize)
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "查询用户消息失败", err)
		return
	}

	response := map[string]interface{}{
		"messages":  result.Records,
		"total":     result.Total,
		"page":      page,
		"page_size": pageSize,
		"has_more":  result.HasMore,
	}

	h.writeJSON(w, response)
}

// handleGetUserActivity 获取用户活动统计
func (h *APIHandler) handleGetUserActivity(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["user_id"]

	if userID == "" {
		h.writeError(w, http.StatusBadRequest, "用户ID不能为空", nil)
		return
	}

	// 解析时间范围
	var since *time.Time
	if sinceStr := r.URL.Query().Get("since"); sinceStr != "" {
		if t, err := time.Parse(time.RFC3339, sinceStr); err == nil {
			since = &t
		}
	}

	activity, err := h.reader.GetUserActivity(r.Context(), userID, since)
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "获取用户活动失败", err)
		return
	}

	h.writeJSON(w, activity)
}

// handleSearchMessages 搜索消息
func (h *APIHandler) handleSearchMessages(w http.ResponseWriter, r *http.Request) {
	keyword := r.URL.Query().Get("q")
	if keyword == "" {
		h.writeError(w, http.StatusBadRequest, "搜索关键词不能为空", nil)
		return
	}

	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))
	userID := r.URL.Query().Get("user_id")
	agentID := r.URL.Query().Get("agent_id")
	msgType := r.URL.Query().Get("type")

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	query := Query{
		Keyword: keyword,
		Limit:   pageSize,
		Offset:  (page - 1) * pageSize,
	}

	if userID != "" {
		query.UserID = &userID
	}

	if agentID != "" {
		query.AgentID = &agentID
	}

	if msgType != "" {
		query.Types = []string{msgType}
	}

	// 解析时间范围
	if sinceStr := r.URL.Query().Get("since"); sinceStr != "" {
		if t, err := time.Parse(time.RFC3339, sinceStr); err == nil {
			query.Since = &t
		}
	}

	if untilStr := r.URL.Query().Get("until"); untilStr != "" {
		if t, err := time.Parse(time.RFC3339, untilStr); err == nil {
			query.Until = &t
		}
	}

	result, err := h.reader.QueryMessages(r.Context(), query)
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "搜索消息失败", err)
		return
	}

	response := map[string]interface{}{
		"messages":  result.Records,
		"total":     result.Total,
		"page":      page,
		"page_size": pageSize,
		"has_more":  result.HasMore,
		"keyword":   keyword,
	}

	h.writeJSON(w, response)
}

// handleRunRetention 执行保留策略
func (h *APIHandler) handleRunRetention(w http.ResponseWriter, r *http.Request) {
	if h.manager == nil {
		h.writeError(w, http.StatusServiceUnavailable, "保留策略管理器未配置", nil)
		return
	}

	// 这里应该检查管理员权限
	// if !h.isAdmin(r) {
	//     h.writeError(w, http.StatusForbidden, "需要管理员权限", nil)
	//     return
	// }

	jobName := r.URL.Query().Get("job")
	if jobName == "" {
		jobName = "default"
	}

	job, exists := h.manager.GetJob(jobName)
	if !exists {
		h.writeError(w, http.StatusNotFound, "保留策略任务不存在", nil)
		return
	}

	result, err := job.RunOnce(r.Context())
	if err != nil {
		h.writeError(w, http.StatusInternalServerError, "执行保留策略失败", err)
		return
	}

	h.writeJSON(w, result)
}

// handleGetRetentionStats 获取保留策略统计
func (h *APIHandler) handleGetRetentionStats(w http.ResponseWriter, r *http.Request) {
	if h.manager == nil {
		h.writeError(w, http.StatusServiceUnavailable, "保留策略管理器未配置", nil)
		return
	}

	stats := h.manager.GetAllStats()
	h.writeJSON(w, stats)
}

// writeJSON 写入JSON响应
func (h *APIHandler) writeJSON(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(data)
}

// writeError 写入错误响应
func (h *APIHandler) writeError(w http.ResponseWriter, status int, message string, err error) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	response := map[string]interface{}{
		"error":   true,
		"message": message,
	}

	if err != nil {
		response["details"] = err.Error()
	}

	json.NewEncoder(w).Encode(response)
}
