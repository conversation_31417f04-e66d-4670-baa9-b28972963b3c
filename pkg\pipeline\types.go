package pipeline

import (
	"context"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/message"
)

// Pipeline represents a sequence of agents that process messages
type Pipeline interface {
	// AddAgent adds an agent to the pipeline
	AddAgent(agent agent.Agent) error

	// RemoveAgent removes an agent from the pipeline
	RemoveAgent(agentID string) error

	// Execute executes the pipeline with the given message
	Execute(ctx context.Context, msg *message.Message) (*PipelineResult, error)

	// GetAgents returns all agents in the pipeline
	GetAgents() []agent.Agent

	// GetAgentCount returns the number of agents in the pipeline
	GetAgentCount() int

	// Clear removes all agents from the pipeline
	Clear() error

	// GetConfig returns the pipeline configuration
	GetConfig() *Config

	// UpdateConfig updates the pipeline configuration
	UpdateConfig(config *Config) error
}

// PipelineType represents the type of pipeline
type PipelineType string

const (
	// PipelineTypeSequential executes agents sequentially
	PipelineTypeSequential PipelineType = "sequential"
	// PipelineTypeParallel executes agents in parallel
	PipelineTypeParallel PipelineType = "parallel"
	// PipelineTypeConditional executes agents based on conditions
	PipelineTypeConditional PipelineType = "conditional"
)

// Config holds pipeline configuration
type Config struct {
	Name        string                 `yaml:"name" json:"name"`
	Type        PipelineType           `yaml:"type" json:"type"`
	Description string                 `yaml:"description,omitempty" json:"description,omitempty"`
	Timeout     time.Duration          `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	MaxRetries  int                    `yaml:"max_retries,omitempty" json:"max_retries,omitempty"`
	Parameters  map[string]interface{} `yaml:"parameters,omitempty" json:"parameters,omitempty"`
}

// DefaultConfig returns a default pipeline configuration
func DefaultConfig() *Config {
	return &Config{
		Type:       PipelineTypeSequential,
		Timeout:    60 * time.Second,
		MaxRetries: 3,
		Parameters: make(map[string]interface{}),
	}
}

// Validate validates the pipeline configuration
func (c *Config) Validate() error {
	if c.Name == "" {
		return &ValidationError{Field: "name", Message: "pipeline name cannot be empty"}
	}
	if c.Type == "" {
		c.Type = PipelineTypeSequential
	}
	if c.Parameters == nil {
		c.Parameters = make(map[string]interface{})
	}
	if c.Timeout <= 0 {
		c.Timeout = 60 * time.Second
	}
	if c.MaxRetries < 0 {
		c.MaxRetries = 3
	}
	return nil
}

// Clone creates a deep copy of the configuration
func (c *Config) Clone() *Config {
	clone := *c

	// Deep copy Parameters map
	if c.Parameters != nil {
		clone.Parameters = make(map[string]interface{})
		for k, v := range c.Parameters {
			clone.Parameters[k] = v
		}
	}

	return &clone
}

// PipelineResult represents the result of pipeline execution
type PipelineResult struct {
	Success      bool                   `json:"success"`
	Message      *message.Message       `json:"message,omitempty"`
	Error        error                  `json:"error,omitempty"`
	ExecutionLog []ExecutionStep        `json:"execution_log"`
	StartTime    time.Time              `json:"start_time"`
	EndTime      time.Time              `json:"end_time"`
	Duration     time.Duration          `json:"duration"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// ExecutionStep represents a single step in pipeline execution
type ExecutionStep struct {
	AgentID   string                 `json:"agent_id"`
	AgentType string                 `json:"agent_type"`
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
	Duration  time.Duration          `json:"duration"`
	Success   bool                   `json:"success"`
	InputMsg  *message.Message       `json:"input_message,omitempty"`
	OutputMsg *message.Message       `json:"output_message,omitempty"`
	Error     error                  `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// NewPipelineResult creates a new pipeline result
func NewPipelineResult() *PipelineResult {
	return &PipelineResult{
		ExecutionLog: make([]ExecutionStep, 0),
		StartTime:    time.Now(),
		Metadata:     make(map[string]interface{}),
	}
}

// AddStep adds an execution step to the result
func (pr *PipelineResult) AddStep(step ExecutionStep) {
	pr.ExecutionLog = append(pr.ExecutionLog, step)
}

// SetSuccess marks the result as successful
func (pr *PipelineResult) SetSuccess(msg *message.Message) {
	pr.Success = true
	pr.Message = msg
	pr.EndTime = time.Now()
	pr.Duration = pr.EndTime.Sub(pr.StartTime)
}

// SetError marks the result as failed
func (pr *PipelineResult) SetError(err error) {
	pr.Success = false
	pr.Error = err
	pr.EndTime = time.Now()
	pr.Duration = pr.EndTime.Sub(pr.StartTime)
}

// GetSuccessfulSteps returns all successful execution steps
func (pr *PipelineResult) GetSuccessfulSteps() []ExecutionStep {
	var steps []ExecutionStep
	for _, step := range pr.ExecutionLog {
		if step.Success {
			steps = append(steps, step)
		}
	}
	return steps
}

// GetFailedSteps returns all failed execution steps
func (pr *PipelineResult) GetFailedSteps() []ExecutionStep {
	var steps []ExecutionStep
	for _, step := range pr.ExecutionLog {
		if !step.Success {
			steps = append(steps, step)
		}
	}
	return steps
}

// GetStepCount returns the total number of execution steps
func (pr *PipelineResult) GetStepCount() int {
	return len(pr.ExecutionLog)
}

// GetSuccessRate returns the success rate of execution steps
func (pr *PipelineResult) GetSuccessRate() float64 {
	if len(pr.ExecutionLog) == 0 {
		return 0.0
	}

	successCount := len(pr.GetSuccessfulSteps())
	return float64(successCount) / float64(len(pr.ExecutionLog))
}

// SetMetadata sets a metadata field
func (pr *PipelineResult) SetMetadata(key string, value interface{}) {
	if pr.Metadata == nil {
		pr.Metadata = make(map[string]interface{})
	}
	pr.Metadata[key] = value
}

// GetMetadata gets a metadata field
func (pr *PipelineResult) GetMetadata(key string) (interface{}, bool) {
	if pr.Metadata == nil {
		return nil, false
	}
	value, exists := pr.Metadata[key]
	return value, exists
}

// ExecutionContext holds context information for pipeline execution
type ExecutionContext struct {
	PipelineName string
	RequestID    string
	StartTime    time.Time
	Timeout      time.Duration
	MaxRetries   int
	Parameters   map[string]interface{}
	Metadata     map[string]interface{}
}

// NewExecutionContext creates a new execution context
func NewExecutionContext(pipelineName string) *ExecutionContext {
	return &ExecutionContext{
		PipelineName: pipelineName,
		RequestID:    generateRequestID(),
		StartTime:    time.Now(),
		Parameters:   make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
	}
}

// SetParameter sets a parameter in the execution context
func (ec *ExecutionContext) SetParameter(key string, value interface{}) {
	if ec.Parameters == nil {
		ec.Parameters = make(map[string]interface{})
	}
	ec.Parameters[key] = value
}

// GetParameter gets a parameter from the execution context
func (ec *ExecutionContext) GetParameter(key string) (interface{}, bool) {
	if ec.Parameters == nil {
		return nil, false
	}
	value, exists := ec.Parameters[key]
	return value, exists
}

// SetMetadata sets a metadata field in the execution context
func (ec *ExecutionContext) SetMetadata(key string, value interface{}) {
	if ec.Metadata == nil {
		ec.Metadata = make(map[string]interface{})
	}
	ec.Metadata[key] = value
}

// GetMetadata gets a metadata field from the execution context
func (ec *ExecutionContext) GetMetadata(key string) (interface{}, bool) {
	if ec.Metadata == nil {
		return nil, false
	}
	value, exists := ec.Metadata[key]
	return value, exists
}

// IsExpired checks if the execution context has expired
func (ec *ExecutionContext) IsExpired() bool {
	if ec.Timeout <= 0 {
		return false
	}
	return time.Since(ec.StartTime) > ec.Timeout
}

// RemainingTime returns the remaining time before timeout
func (ec *ExecutionContext) RemainingTime() time.Duration {
	if ec.Timeout <= 0 {
		return time.Duration(0)
	}
	elapsed := time.Since(ec.StartTime)
	if elapsed >= ec.Timeout {
		return time.Duration(0)
	}
	return ec.Timeout - elapsed
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return "req_" + time.Now().Format("20060102150405") + "_" + time.Now().Format("000000")
}
