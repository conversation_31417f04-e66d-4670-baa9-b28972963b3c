//go:build mcptests

package mcp

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewMCPClient(t *testing.T) {
	config := &MCPConfig{
		ServerURL: "ws://localhost:8080/mcp",
		Timeout:   30 * time.Second,
	}

	client := NewMCPClient(config)
	assert.NotNil(t, client)
	assert.Equal(t, config, client.config)
	assert.False(t, client.connected)
}

func TestMCPMessage(t *testing.T) {
	msg := &MCPMessage{
		ID:     "test-123",
		Method: "tools/list",
		Params: map[string]interface{}{
			"limit": 10,
		},
	}

	// Test JSON marshaling
	data, err := json.Marshal(msg)
	require.NoError(t, err)

	// Test JSON unmarshaling
	var unmarshaled MCPMessage
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, msg.ID, unmarshaled.ID)
	assert.Equal(t, msg.Method, unmarshaled.Method)
	assert.Equal(t, float64(10), unmarshaled.Params["limit"]) // JSON numbers become float64
}

func TestMCPResponse(t *testing.T) {
	response := &MCPResponse{
		ID: "test-123",
		Result: map[string]interface{}{
			"tools": []interface{}{
				map[string]interface{}{
					"name":        "calculator",
					"description": "A simple calculator",
				},
			},
		},
	}

	// Test JSON marshaling
	data, err := json.Marshal(response)
	require.NoError(t, err)

	// Test JSON unmarshaling
	var unmarshaled MCPResponse
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, response.ID, unmarshaled.ID)
	assert.NotNil(t, unmarshaled.Result)
}

func TestMCPError(t *testing.T) {
	mcpErr := &MCPError{
		Code:    -32601,
		Message: "Method not found",
		Data: map[string]interface{}{
			"method": "unknown/method",
		},
	}

	// Test JSON marshaling
	data, err := json.Marshal(mcpErr)
	require.NoError(t, err)

	// Test JSON unmarshaling
	var unmarshaled MCPError
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, mcpErr.Code, unmarshaled.Code)
	assert.Equal(t, mcpErr.Message, unmarshaled.Message)
	assert.Equal(t, "unknown/method", unmarshaled.Data["method"])
}

func TestMCPTool(t *testing.T) {
	tool := &MCPTool{
		Name:        "calculator",
		Description: "A simple calculator tool",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"operation": map[string]interface{}{
					"type": "string",
					"enum": []string{"add", "subtract", "multiply", "divide"},
				},
				"a": map[string]interface{}{
					"type": "number",
				},
				"b": map[string]interface{}{
					"type": "number",
				},
			},
			"required": []string{"operation", "a", "b"},
		},
	}

	// Test JSON marshaling
	data, err := json.Marshal(tool)
	require.NoError(t, err)

	// Test JSON unmarshaling
	var unmarshaled MCPTool
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, tool.Name, unmarshaled.Name)
	assert.Equal(t, tool.Description, unmarshaled.Description)
	assert.NotNil(t, unmarshaled.InputSchema)
}

func TestMCPResource(t *testing.T) {
	resource := &MCPResource{
		URI:         "file:///path/to/document.txt",
		Name:        "document.txt",
		Description: "A text document",
		MimeType:    "text/plain",
	}

	// Test JSON marshaling
	data, err := json.Marshal(resource)
	require.NoError(t, err)

	// Test JSON unmarshaling
	var unmarshaled MCPResource
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, resource.URI, unmarshaled.URI)
	assert.Equal(t, resource.Name, unmarshaled.Name)
	assert.Equal(t, resource.Description, unmarshaled.Description)
	assert.Equal(t, resource.MimeType, unmarshaled.MimeType)
}

func TestMCPClientRequestResponse(t *testing.T) {
	// This test would require a mock MCP server
	// For now, we'll test the request/response structure

	client := &MCPClient{
		config:      &MCPConfig{Timeout: 30 * time.Second},
		connected:   false,
		pendingReqs: make(map[string]chan *MCPResponse),
	}

	// Test request creation
	ctx := context.Background()
	req := &MCPMessage{
		ID:     "test-123",
		Method: "tools/list",
		Params: map[string]interface{}{},
	}

	// Test that request would timeout if no connection
	_, err := client.sendRequest(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not connected")
}

func TestMCPClientConnectionState(t *testing.T) {
	client := &MCPClient{
		config:    &MCPConfig{},
		connected: false,
	}

	// Test initial state
	assert.False(t, client.IsConnected())

	// Test state change
	client.connected = true
	assert.True(t, client.IsConnected())
}

func TestMCPMessageValidation(t *testing.T) {
	// Test valid message
	validMsg := &MCPMessage{
		ID:     "test-123",
		Method: "tools/list",
		Params: map[string]interface{}{},
	}

	assert.NotEmpty(t, validMsg.ID)
	assert.NotEmpty(t, validMsg.Method)

	// Test message without ID
	invalidMsg := &MCPMessage{
		Method: "tools/list",
		Params: map[string]interface{}{},
	}

	assert.Empty(t, invalidMsg.ID)
	assert.NotEmpty(t, invalidMsg.Method)
}

func TestMCPErrorCodes(t *testing.T) {
	testCases := []struct {
		code    int
		message string
	}{
		{-32700, "Parse error"},
		{-32600, "Invalid Request"},
		{-32601, "Method not found"},
		{-32602, "Invalid params"},
		{-32603, "Internal error"},
	}

	for _, tc := range testCases {
		err := &MCPError{
			Code:    tc.code,
			Message: tc.message,
		}

		assert.Equal(t, tc.code, err.Code)
		assert.Equal(t, tc.message, err.Message)
	}
}

func TestMCPToolInputSchemaValidation(t *testing.T) {
	tool := &MCPTool{
		Name:        "test-tool",
		Description: "A test tool",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"param1": map[string]interface{}{
					"type": "string",
				},
			},
			"required": []string{"param1"},
		},
	}

	// Test that schema is properly structured
	schema := tool.InputSchema
	assert.Equal(t, "object", schema["type"])

	properties, ok := schema["properties"].(map[string]interface{})
	assert.True(t, ok)
	assert.Contains(t, properties, "param1")

	required, ok := schema["required"].([]string)
	assert.True(t, ok)
	assert.Contains(t, required, "param1")
}

func TestMCPResourceURIValidation(t *testing.T) {
	testCases := []struct {
		uri   string
		valid bool
	}{
		{"file:///path/to/file.txt", true},
		{"http://example.com/resource", true},
		{"https://example.com/resource", true},
		{"ftp://example.com/file", true},
		{"invalid-uri", false},
		{"", false},
	}

	for _, tc := range testCases {
		resource := &MCPResource{
			URI: tc.uri,
		}

		if tc.valid {
			assert.NotEmpty(t, resource.URI)
		} else {
			// In a real implementation, you might want to validate URIs
			// For now, we just check if it's empty
			if tc.uri == "" {
				assert.Empty(t, resource.URI)
			}
		}
	}
}

func BenchmarkMCPMessageMarshal(b *testing.B) {
	msg := &MCPMessage{
		ID:     "test-123",
		Method: "tools/list",
		Params: map[string]interface{}{
			"limit":  10,
			"offset": 0,
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(msg)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkMCPMessageUnmarshal(b *testing.B) {
	data := []byte(`{"id":"test-123","method":"tools/list","params":{"limit":10,"offset":0}}`)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var msg MCPMessage
		err := json.Unmarshal(data, &msg)
		if err != nil {
			b.Fatal(err)
		}
	}
}
