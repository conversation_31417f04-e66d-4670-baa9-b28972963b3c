package agent

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// mockAgent 模拟Agent实现，用于测试
type mockAgent struct {
	name        string
	description string
}

func (m *mockAgent) Name(ctx context.Context) string {
	return m.name
}

func (m *mockAgent) Description(ctx context.Context) string {
	return m.description
}

func (m *mockAgent) Run(ctx context.Context, in *Input) *runtime.AsyncIterator[*event.Event] {
	// 简单的模拟实现，返回空的迭代器
	return nil
}

// newMockAgent 创建模拟Agent
func newMockAgent(name, description string) Agent {
	return &mockAgent{
		name:        name,
		description: description,
	}
}

// TestManagerConcurrency 测试Manager并发安全
func TestManagerConcurrency(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	const numGoroutines = 10
	const agentsPerGoroutine = 10

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// 并发注册Agent
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < agentsPerGoroutine; j++ {
				agentName := fmt.Sprintf("agent-%d-%d", goroutineID, j)
				agent := newMockAgent(agentName, "test agent")
				err := manager.RegisterAgent(ctx, agent)
				assert.NoError(t, err)
			}
		}(i)
	}

	wg.Wait()

	// 验证所有Agent都被注册
	agents := manager.GetAllAgents()
	assert.Len(t, agents, numGoroutines*agentsPerGoroutine)
}

// TestManagerEdgeCases 测试Manager边界情况
func TestManagerEdgeCases(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 测试nil Agent
	err := manager.RegisterAgent(ctx, nil)
	assert.Error(t, err)

	// 测试获取不存在的Agent
	agent, err := manager.GetAgent("non-existent")
	assert.Error(t, err)
	assert.Nil(t, agent)

	// 测试注销不存在的Agent
	err = manager.UnregisterAgent("non-existent")
	assert.Error(t, err)

	// 测试空Manager的GetAllAgents
	agents := manager.GetAllAgents()
	assert.Empty(t, agents)

	// 测试HasAgent
	exists := manager.HasAgent("non-existent")
	assert.False(t, exists)

	// 测试GetAgentCount
	count := manager.GetAgentCount()
	assert.Equal(t, 0, count)
}

// TestManagerAgentLifecycle 测试Agent生命周期管理
func TestManagerAgentLifecycle(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册Agent
	agent1 := newMockAgent("agent1", "test agent 1")
	err := manager.RegisterAgent(ctx, agent1)
	assert.NoError(t, err)

	agent2 := newMockAgent("agent2", "test agent 2")
	err = manager.RegisterAgent(ctx, agent2)
	assert.NoError(t, err)

	// 验证Agent存在
	retrievedAgent, err := manager.GetAgent("agent1")
	assert.NoError(t, err)
	assert.Equal(t, agent1, retrievedAgent)

	// 获取所有Agent
	agents := manager.GetAllAgents()
	assert.Len(t, agents, 2)

	// 验证HasAgent
	assert.True(t, manager.HasAgent("agent1"))
	assert.True(t, manager.HasAgent("agent2"))

	// 注销一个Agent
	err = manager.UnregisterAgent("agent1")
	assert.NoError(t, err)

	// 验证Agent已被移除
	_, err = manager.GetAgent("agent1")
	assert.Error(t, err)

	// 验证另一个Agent仍然存在
	_, err = manager.GetAgent("agent2")
	assert.NoError(t, err)

	// 验证Agent数量
	agents = manager.GetAllAgents()
	assert.Len(t, agents, 1)
	assert.Equal(t, 1, manager.GetAgentCount())
}

// TestManagerDuplicateRegistration 测试重复注册
func TestManagerDuplicateRegistration(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	agent1 := newMockAgent("test-agent", "test agent 1")
	agent2 := newMockAgent("test-agent", "test agent 2")

	// 首次注册
	err := manager.RegisterAgent(ctx, agent1)
	assert.NoError(t, err)

	// 重复注册相同名称
	err = manager.RegisterAgent(ctx, agent2)
	assert.Error(t, err)

	// 验证原Agent仍然存在
	retrievedAgent, err := manager.GetAgent("test-agent")
	assert.NoError(t, err)
	assert.Equal(t, agent1, retrievedAgent)
}

// TestManagerValidation 测试Manager输入验证
func TestManagerValidation(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	tests := []struct {
		name      string
		agent     Agent
		expectErr bool
	}{
		{
			name:      "valid agent",
			agent:     newMockAgent("valid-agent", "valid agent"),
			expectErr: false,
		},
		{
			name:      "nil agent",
			agent:     nil,
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.RegisterAgent(ctx, tt.agent)
			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestManagerClearAndShutdown 测试Manager清理和关闭功能
func TestManagerClearAndShutdown(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册一些Agent
	agent1 := newMockAgent("agent1", "test agent 1")
	agent2 := newMockAgent("agent2", "test agent 2")

	err := manager.RegisterAgent(ctx, agent1)
	assert.NoError(t, err)
	err = manager.RegisterAgent(ctx, agent2)
	assert.NoError(t, err)

	// 验证Agent已注册
	assert.Equal(t, 2, manager.GetAgentCount())

	// 测试Clear
	manager.Clear()
	assert.Equal(t, 0, manager.GetAgentCount())
	assert.Empty(t, manager.GetAllAgents())

	// 重新注册Agent
	err = manager.RegisterAgent(ctx, agent1)
	assert.NoError(t, err)
	assert.Equal(t, 1, manager.GetAgentCount())

	// 测试Shutdown
	err = manager.Shutdown()
	assert.NoError(t, err)
	assert.Equal(t, 0, manager.GetAgentCount())
}

// TestManagerListAgents 测试ListAgents功能
func TestManagerListAgents(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 空Manager
	names := manager.ListAgents()
	assert.Empty(t, names)

	// 注册一些Agent
	agentNames := []string{"agent1", "agent2", "agent3"}
	for _, name := range agentNames {
		agent := newMockAgent(name, "test agent")
		err := manager.RegisterAgent(ctx, agent)
		assert.NoError(t, err)
	}

	// 获取Agent名称列表
	names = manager.ListAgents()
	assert.Len(t, names, 3)

	// 验证所有名称都存在
	nameSet := make(map[string]bool)
	for _, name := range names {
		nameSet[name] = true
	}
	for _, expectedName := range agentNames {
		assert.True(t, nameSet[expectedName], "Expected agent name %s not found", expectedName)
	}
}

// TestInput 测试Input类型的所有功能
func TestInput(t *testing.T) {
	// 测试NewInput
	input := runtime.NewInput()
	assert.NotNil(t, input)
	assert.Empty(t, input.Messages)
	assert.Empty(t, input.Tools)
	assert.Empty(t, input.Options)
	assert.Nil(t, input.Memory)
	assert.Nil(t, input.Session)

	// 测试AddMessage
	msg1 := &message.Message{Role: "user", Content: "Hello"}
	msg2 := &message.Message{Role: "assistant", Content: "Hi"}

	input.AddMessage(msg1).AddMessage(msg2)
	assert.Len(t, input.Messages, 2)
	assert.Equal(t, msg1, input.Messages[0])
	assert.Equal(t, msg2, input.Messages[1])

	// 测试SetOption和GetOption
	input.SetOption("temperature", 0.7)
	input.SetOption("max_tokens", 100)
	input.SetOption("stream", true)
	input.SetOption("model", "gpt-4")

	// 测试GetOption
	value, exists := input.GetOption("temperature")
	assert.True(t, exists)
	assert.Equal(t, 0.7, value)

	_, exists = input.GetOption("non_existent")
	assert.False(t, exists)

	// 测试GetOptionFloat64
	temp, exists := input.GetOptionFloat64("temperature")
	assert.True(t, exists)
	assert.Equal(t, 0.7, temp)

	_, exists = input.GetOptionFloat64("max_tokens")
	assert.False(t, exists) // 因为max_tokens是int类型

	// 测试GetOptionInt
	maxTokens, exists := input.GetOptionInt("max_tokens")
	assert.True(t, exists)
	assert.Equal(t, 100, maxTokens)

	_, exists = input.GetOptionInt("temperature")
	assert.False(t, exists) // 因为temperature是float64类型

	// 测试GetOptionBool
	stream, exists := input.GetOptionBool("stream")
	assert.True(t, exists)
	assert.True(t, stream)

	_, exists = input.GetOptionBool("model")
	assert.False(t, exists) // 因为model是string类型

	// 测试GetOptionString
	model, exists := input.GetOptionString("model")
	assert.True(t, exists)
	assert.Equal(t, "gpt-4", model)

	_, exists = input.GetOptionString("stream")
	assert.False(t, exists) // 因为stream是bool类型
}

// TestInputClone 测试Input克隆功能
func TestInputClone(t *testing.T) {
	// 创建原始Input
	original := runtime.NewInput()
	msg1 := &message.Message{Role: "user", Content: "Hello"}
	msg2 := &message.Message{Role: "assistant", Content: "Hi"}

	original.AddMessage(msg1).AddMessage(msg2)
	original.SetOption("temperature", 0.7)
	original.SetOption("max_tokens", 100)

	// 克隆
	clone := original.Clone()

	// 验证克隆结果
	assert.NotSame(t, original, clone)
	assert.NotSame(t, &original.Messages, &clone.Messages)
	assert.NotSame(t, &original.Tools, &clone.Tools)
	assert.NotSame(t, &original.Options, &clone.Options)

	// 验证内容相同
	assert.Len(t, clone.Messages, 2)
	assert.Equal(t, original.Messages[0], clone.Messages[0])
	assert.Equal(t, original.Messages[1], clone.Messages[1])

	temp, exists := clone.GetOptionFloat64("temperature")
	assert.True(t, exists)
	assert.Equal(t, 0.7, temp)

	maxTokens, exists := clone.GetOptionInt("max_tokens")
	assert.True(t, exists)
	assert.Equal(t, 100, maxTokens)

	// 验证修改克隆不影响原始对象
	clone.SetOption("temperature", 0.9)
	originalTemp, _ := original.GetOptionFloat64("temperature")
	cloneTemp, _ := clone.GetOptionFloat64("temperature")
	assert.Equal(t, 0.7, originalTemp)
	assert.Equal(t, 0.9, cloneTemp)
}

// TestInputValidation 测试Input验证功能
func TestInputValidation(t *testing.T) {
	tests := []struct {
		name      string
		input     *Input
		expectErr bool
		errField  string
	}{
		{
			name:      "nil input",
			input:     nil,
			expectErr: true,
			errField:  "input",
		},
		{
			name:      "empty messages",
			input:     runtime.NewInput(),
			expectErr: true,
			errField:  "messages",
		},
		{
			name: "valid input",
			input: runtime.NewInput().AddMessage(&message.Message{
				Role:    "user",
				Content: "Hello",
			}),
			expectErr: false,
		},
		{
			name: "nil message",
			input: &Input{
				Messages: []*message.Message{nil},
			},
			expectErr: true,
			errField:  "messages",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.input.Validate()
			if tt.expectErr {
				assert.Error(t, err)
				if validationErr, ok := err.(*runtime.ValidationError); ok {
					assert.Equal(t, tt.errField, validationErr.Field)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestBaseAgent 测试BaseAgent功能
func TestBaseAgent(t *testing.T) {
	ctx := context.Background()

	// 测试NewBaseAgent
	agent := NewBaseAgent("test-agent", "A test agent")
	assert.NotNil(t, agent)
	assert.Equal(t, "test-agent", agent.Name(ctx))
	assert.Equal(t, "A test agent", agent.Description(ctx))

	// 测试SetName
	agent.SetName("updated-agent")
	assert.Equal(t, "updated-agent", agent.Name(ctx))

	// 测试SetDescription
	agent.SetDescription("An updated test agent")
	assert.Equal(t, "An updated test agent", agent.Description(ctx))
}

// TestValidationError 测试ValidationError类型
func TestValidationError(t *testing.T) {
	// 测试不带索引的错误
	err := &runtime.ValidationError{
		Field:   "test_field",
		Message: "test message",
	}
	assert.Contains(t, err.Error(), "test_field")
	assert.Contains(t, err.Error(), "test message")

	// 测试带索引的错误
	index := 5
	errWithIndex := &runtime.ValidationError{
		Field:   "test_field",
		Message: "test message",
		Index:   &index,
	}
	assert.Contains(t, errWithIndex.Error(), "test_field")
	assert.Contains(t, errWithIndex.Error(), "test message")
}

func TestNewManager(t *testing.T) {
	manager := NewManager()

	assert.NotNil(t, manager, "Manager should not be nil")
	assert.Equal(t, 0, manager.GetAgentCount(), "Initial agent count should be 0")
	assert.Empty(t, manager.ListAgents(), "Initial agent list should be empty")
}

func TestRegisterAgent(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 测试注册有效Agent
	agent1 := newMockAgent("test-agent-1", "Test Agent 1")
	err := manager.RegisterAgent(ctx, agent1)
	assert.NoError(t, err, "Should register valid agent successfully")
	assert.Equal(t, 1, manager.GetAgentCount(), "Agent count should be 1")

	// 测试注册nil Agent
	err = manager.RegisterAgent(ctx, nil)
	assert.Error(t, err, "Should fail to register nil agent")
	assert.Contains(t, err.Error(), "agent cannot be nil")

	// 测试注册重复名称的Agent
	agent2 := newMockAgent("test-agent-1", "Another Test Agent")
	err = manager.RegisterAgent(ctx, agent2)
	assert.Error(t, err, "Should fail to register duplicate agent")
	assert.Contains(t, err.Error(), "already exists")

	// 测试注册空名称的Agent
	agentEmpty := newMockAgent("", "Empty Name Agent")
	err = manager.RegisterAgent(ctx, agentEmpty)
	assert.Error(t, err, "Should fail to register agent with empty name")
	assert.Contains(t, err.Error(), "agent name cannot be empty")
}

func TestGetAgent(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册测试Agent
	agent1 := newMockAgent("test-agent-1", "Test Agent 1")
	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)

	// 测试获取存在的Agent
	retrievedAgent, err := manager.GetAgent("test-agent-1")
	assert.NoError(t, err, "Should get existing agent successfully")
	assert.Equal(t, agent1, retrievedAgent, "Retrieved agent should match registered agent")

	// 测试获取不存在的Agent
	_, err = manager.GetAgent("non-existent-agent")
	assert.Error(t, err, "Should fail to get non-existent agent")
	assert.Contains(t, err.Error(), "not found")

	// 测试获取空名称Agent
	_, err = manager.GetAgent("")
	assert.Error(t, err, "Should fail to get agent with empty name")
	assert.Contains(t, err.Error(), "agent name cannot be empty")
}

func TestUnregisterAgent(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册测试Agent
	agent1 := newMockAgent("test-agent-1", "Test Agent 1")
	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)

	// 测试注销存在的Agent
	err = manager.UnregisterAgent("test-agent-1")
	assert.NoError(t, err, "Should unregister existing agent successfully")
	assert.Equal(t, 0, manager.GetAgentCount(), "Agent count should be 0 after unregistration")

	// 测试注销不存在的Agent
	err = manager.UnregisterAgent("non-existent-agent")
	assert.Error(t, err, "Should fail to unregister non-existent agent")
	assert.Contains(t, err.Error(), "not found")

	// 测试注销空名称Agent
	err = manager.UnregisterAgent("")
	assert.Error(t, err, "Should fail to unregister agent with empty name")
	assert.Contains(t, err.Error(), "agent name cannot be empty")
}

func TestListAgents(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 初始状态应该为空
	agents := manager.ListAgents()
	assert.Empty(t, agents, "Initial agent list should be empty")

	// 注册多个Agent
	agent1 := newMockAgent("agent-1", "Agent 1")
	agent2 := newMockAgent("agent-2", "Agent 2")
	agent3 := newMockAgent("agent-3", "Agent 3")

	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)
	err = manager.RegisterAgent(ctx, agent2)
	require.NoError(t, err)
	err = manager.RegisterAgent(ctx, agent3)
	require.NoError(t, err)

	// 检查Agent列表
	agents = manager.ListAgents()
	assert.Len(t, agents, 3, "Should have 3 agents")
	assert.Contains(t, agents, "agent-1")
	assert.Contains(t, agents, "agent-2")
	assert.Contains(t, agents, "agent-3")
}

func TestHasAgent(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 初始状态应该没有任何Agent
	assert.False(t, manager.HasAgent("test-agent"), "Should not have any agent initially")

	// 注册Agent
	agent1 := newMockAgent("test-agent", "Test Agent")
	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)

	// 检查Agent存在性
	assert.True(t, manager.HasAgent("test-agent"), "Should have registered agent")
	assert.False(t, manager.HasAgent("non-existent-agent"), "Should not have non-existent agent")
}

func TestClear(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册多个Agent
	agent1 := newMockAgent("agent-1", "Agent 1")
	agent2 := newMockAgent("agent-2", "Agent 2")

	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)
	err = manager.RegisterAgent(ctx, agent2)
	require.NoError(t, err)

	assert.Equal(t, 2, manager.GetAgentCount(), "Should have 2 agents before clear")

	// 清空所有Agent
	manager.Clear()

	assert.Equal(t, 0, manager.GetAgentCount(), "Should have 0 agents after clear")
	assert.Empty(t, manager.ListAgents(), "Agent list should be empty after clear")
}

func TestUpdateAgent(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册初始Agent
	agent1 := newMockAgent("test-agent", "Original Agent")
	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)

	// 更新Agent
	newAgent := newMockAgent("test-agent", "Updated Agent")
	err = manager.UpdateAgent(ctx, "test-agent", newAgent)
	assert.NoError(t, err, "Should update existing agent successfully")

	// 验证Agent已更新
	retrievedAgent, err := manager.GetAgent("test-agent")
	require.NoError(t, err)
	assert.Equal(t, "Updated Agent", retrievedAgent.Description(ctx))

	// 测试更新不存在的Agent
	err = manager.UpdateAgent(ctx, "non-existent-agent", newAgent)
	assert.Error(t, err, "Should fail to update non-existent agent")

	// 测试名称不匹配的更新
	mismatchAgent := newMockAgent("different-name", "Mismatch Agent")
	err = manager.UpdateAgent(ctx, "test-agent", mismatchAgent)
	assert.Error(t, err, "Should fail to update with mismatched name")
}

func TestShutdown(t *testing.T) {
	manager := NewManager()
	ctx := context.Background()

	// 注册一些Agent
	agent1 := newMockAgent("agent-1", "Agent 1")
	err := manager.RegisterAgent(ctx, agent1)
	require.NoError(t, err)

	assert.Equal(t, 1, manager.GetAgentCount(), "Should have 1 agent before shutdown")

	// 关闭管理器
	err = manager.Shutdown()
	assert.NoError(t, err, "Should shutdown successfully")
	assert.Equal(t, 0, manager.GetAgentCount(), "Should have 0 agents after shutdown")
}
