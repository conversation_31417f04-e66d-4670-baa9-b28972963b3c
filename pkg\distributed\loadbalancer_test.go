//go:build disttests

package distributed

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRoundRobinLoadBalancer(t *testing.T) {
	lb := NewRoundRobinLoadBalancer()
	ctx := context.Background()

	// Test with no services
	_, err := lb.SelectService(ctx, "test-service")
	assert.Error(t, err)

	// Add services
	services := []*ServiceInfo{
		{ID: "service-1", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-2", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-3", Name: "test-service", Status: ServiceStatusHealthy},
	}
	lb.UpdateServices("test-service", services)

	// Test round-robin selection
	selectedIDs := make(map[string]int)
	for i := 0; i < 9; i++ {
		selected, err := lb.SelectService(ctx, "test-service")
		require.NoError(t, err)
		selectedIDs[selected.ID]++
	}

	// Each service should be selected 3 times
	assert.Equal(t, 3, selectedIDs["service-1"])
	assert.Equal(t, 3, selectedIDs["service-2"])
	assert.Equal(t, 3, selectedIDs["service-3"])

	// Test stats
	stats := lb.GetStats()
	assert.Equal(t, "round_robin", stats.Algorithm)
	assert.Equal(t, int64(9), stats.TotalRequests)
}

func TestWeightedRoundRobinLoadBalancer(t *testing.T) {
	lb := NewWeightedRoundRobinLoadBalancer()
	ctx := context.Background()

	// Add services with different weights
	services := []*ServiceInfo{
		{
			ID:     "service-1",
			Name:   "test-service",
			Status: ServiceStatusHealthy,
			Metadata: map[string]interface{}{
				"weight": 1,
			},
		},
		{
			ID:     "service-2",
			Name:   "test-service",
			Status: ServiceStatusHealthy,
			Metadata: map[string]interface{}{
				"weight": 2,
			},
		},
		{
			ID:     "service-3",
			Name:   "test-service",
			Status: ServiceStatusHealthy,
			Metadata: map[string]interface{}{
				"weight": 3,
			},
		},
	}
	lb.UpdateServices("test-service", services)

	// Test weighted selection
	selectedIDs := make(map[string]int)
	for i := 0; i < 60; i++ {
		selected, err := lb.SelectService(ctx, "test-service")
		require.NoError(t, err)
		selectedIDs[selected.ID]++
	}

	// Service-3 should be selected most often (weight 3)
	// Service-2 should be selected more than service-1 (weight 2 vs 1)
	assert.True(t, selectedIDs["service-3"] > selectedIDs["service-2"])
	assert.True(t, selectedIDs["service-2"] > selectedIDs["service-1"])

	// Test stats
	stats := lb.GetStats()
	assert.Equal(t, "weighted_round_robin", stats.Algorithm)
	assert.Equal(t, int64(60), stats.TotalRequests)
}

func TestRandomLoadBalancer(t *testing.T) {
	lb := NewRandomLoadBalancer()
	ctx := context.Background()

	// Test with no services
	_, err := lb.SelectService(ctx, "test-service")
	assert.Error(t, err)

	// Add services
	services := []*ServiceInfo{
		{ID: "service-1", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-2", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-3", Name: "test-service", Status: ServiceStatusHealthy},
	}
	lb.UpdateServices("test-service", services)

	// Test random selection
	selectedIDs := make(map[string]int)
	for i := 0; i < 300; i++ {
		selected, err := lb.SelectService(ctx, "test-service")
		require.NoError(t, err)
		selectedIDs[selected.ID]++
	}

	// All services should be selected (with random distribution)
	assert.True(t, selectedIDs["service-1"] > 0)
	assert.True(t, selectedIDs["service-2"] > 0)
	assert.True(t, selectedIDs["service-3"] > 0)

	// Test stats
	stats := lb.GetStats()
	assert.Equal(t, "random", stats.Algorithm)
	assert.Equal(t, int64(300), stats.TotalRequests)
}

func TestLoadBalancerWithUnhealthyServices(t *testing.T) {
	lb := NewRoundRobinLoadBalancer()
	ctx := context.Background()

	// Add services with mixed health status
	services := []*ServiceInfo{
		{ID: "service-1", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-2", Name: "test-service", Status: ServiceStatusUnhealthy},
		{ID: "service-3", Name: "test-service", Status: ServiceStatusHealthy},
	}
	lb.UpdateServices("test-service", services)

	// Test that only healthy services are selected
	selectedIDs := make(map[string]int)
	for i := 0; i < 10; i++ {
		selected, err := lb.SelectService(ctx, "test-service")
		require.NoError(t, err)
		selectedIDs[selected.ID]++
	}

	// Only healthy services should be selected
	assert.True(t, selectedIDs["service-1"] > 0)
	assert.Equal(t, 0, selectedIDs["service-2"]) // Unhealthy service
	assert.True(t, selectedIDs["service-3"] > 0)
}

func TestLoadBalancerManager(t *testing.T) {
	// Create mock discovery
	discovery := NewInMemoryServiceDiscovery()
	manager := NewLoadBalancerManager(discovery)

	// Register a load balancer
	lb := NewRoundRobinLoadBalancer()
	manager.RegisterLoadBalancer("test-service", lb)

	// Add services to discovery
	ctx := context.Background()
	services := []*ServiceInfo{
		{ID: "service-1", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-2", Name: "test-service", Status: ServiceStatusHealthy},
	}

	for _, service := range services {
		err := discovery.RegisterService(ctx, service)
		require.NoError(t, err)
	}

	// Start manager
	manager.Start()
	defer manager.Stop()

	// Wait for services to be updated
	time.Sleep(100 * time.Millisecond)

	// Test service selection
	selected, err := manager.SelectService(ctx, "test-service")
	require.NoError(t, err)
	assert.Contains(t, []string{"service-1", "service-2"}, selected.ID)

	// Test stats
	stats := manager.GetStats()
	assert.Contains(t, stats, "test-service")
}

func TestLoadBalancerStats(t *testing.T) {
	lb := NewRoundRobinLoadBalancer()
	ctx := context.Background()

	// Add services
	services := []*ServiceInfo{
		{ID: "service-1", Name: "test-service", Status: ServiceStatusHealthy},
		{ID: "service-2", Name: "test-service", Status: ServiceStatusHealthy},
	}
	lb.UpdateServices("test-service", services)

	// Make some requests
	for i := 0; i < 5; i++ {
		lb.SelectService(ctx, "test-service")
	}

	// Check stats
	stats := lb.GetStats()
	assert.Equal(t, int64(5), stats.TotalRequests)
	assert.Equal(t, "round_robin", stats.Algorithm)
	assert.Len(t, stats.ServiceStats, 2)

	// Check individual service stats
	for _, serviceStats := range stats.ServiceStats {
		assert.True(t, serviceStats.RequestCount > 0)
		assert.False(t, serviceStats.LastSelected.IsZero())
	}
}

func TestWeightedService(t *testing.T) {
	service := &ServiceInfo{
		ID:     "test-service",
		Name:   "test",
		Status: ServiceStatusHealthy,
	}

	weighted := &WeightedService{
		ServiceInfo:   service,
		Weight:        5,
		CurrentWeight: 0,
	}

	assert.Equal(t, 5, weighted.Weight)
	assert.Equal(t, 0, weighted.CurrentWeight)
	assert.Equal(t, service, weighted.ServiceInfo)
}

func BenchmarkRoundRobinLoadBalancer(b *testing.B) {
	lb := NewRoundRobinLoadBalancer()
	ctx := context.Background()

	// Setup services
	services := make([]*ServiceInfo, 10)
	for i := 0; i < 10; i++ {
		services[i] = &ServiceInfo{
			ID:     fmt.Sprintf("service-%d", i),
			Name:   "bench-service",
			Status: ServiceStatusHealthy,
		}
	}
	lb.UpdateServices("bench-service", services)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		lb.SelectService(ctx, "bench-service")
	}
}

func BenchmarkWeightedRoundRobinLoadBalancer(b *testing.B) {
	lb := NewWeightedRoundRobinLoadBalancer()
	ctx := context.Background()

	// Setup services with weights
	services := make([]*ServiceInfo, 10)
	for i := 0; i < 10; i++ {
		services[i] = &ServiceInfo{
			ID:     fmt.Sprintf("service-%d", i),
			Name:   "bench-service",
			Status: ServiceStatusHealthy,
			Metadata: map[string]interface{}{
				"weight": i + 1,
			},
		}
	}
	lb.UpdateServices("bench-service", services)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		lb.SelectService(ctx, "bench-service")
	}
}

func BenchmarkRandomLoadBalancer(b *testing.B) {
	lb := NewRandomLoadBalancer()
	ctx := context.Background()

	// Setup services
	services := make([]*ServiceInfo, 10)
	for i := 0; i < 10; i++ {
		services[i] = &ServiceInfo{
			ID:     fmt.Sprintf("service-%d", i),
			Name:   "bench-service",
			Status: ServiceStatusHealthy,
		}
	}
	lb.UpdateServices("bench-service", services)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		lb.SelectService(ctx, "bench-service")
	}
}
