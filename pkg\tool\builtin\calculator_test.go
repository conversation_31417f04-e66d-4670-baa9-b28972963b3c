package builtin

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCalculatorTool_Name(t *testing.T) {
	calc := NewCalculatorTool()
	assert.Equal(t, "calculator", calc.Name())
}

func TestCalculatorTool_Description(t *testing.T) {
	calc := NewCalculatorTool()
	desc := calc.Description()
	assert.NotEmpty(t, desc)
	assert.Contains(t, desc, "计算器")
}

func TestCalculatorTool_Schema(t *testing.T) {
	calc := NewCalculatorTool()
	schema := calc.Schema()

	require.NotNil(t, schema)
	assert.Equal(t, "object", schema.Type)

	// 检查必需的属性
	require.NotNil(t, schema.Properties)

	// 检查 expression 属性
	exprProp, exists := schema.Properties["expression"]
	require.True(t, exists)
	assert.Equal(t, "string", exprProp.Type)
	assert.NotEmpty(t, exprProp.Description)

	// 检查必需字段
	require.Contains(t, schema.Required, "expression")
}

func TestCalculatorTool_Execute_BasicOperations(t *testing.T) {
	calc := NewCalculatorTool()
	ctx := context.Background()

	tests := []struct {
		name       string
		expression string
		expected   float64
		shouldErr  bool
	}{
		{
			name:       "加法",
			expression: "2 + 3",
			expected:   5,
			shouldErr:  false,
		},
		{
			name:       "减法",
			expression: "10 - 4",
			expected:   6,
			shouldErr:  false,
		},
		{
			name:       "乘法",
			expression: "6 * 7",
			expected:   42,
			shouldErr:  false,
		},
		{
			name:       "除法",
			expression: "15 / 3",
			expected:   5,
			shouldErr:  false,
		},
		{
			name:       "复合运算",
			expression: "2 + 3 * 4",
			expected:   14,
			shouldErr:  false,
		},
		{
			name:       "括号运算",
			expression: "(2 + 3) * 4",
			expected:   20,
			shouldErr:  true, // 当前实现不支持括号
		},
		{
			name:       "小数运算",
			expression: "3.14 + 2.86",
			expected:   6.0,
			shouldErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := map[string]any{
				"expression": tt.expression,
			}

			result, err := calc.Execute(ctx, params)

			if tt.shouldErr {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)

				// 结果应该是 map[string]interface{}
				resultMap, ok := result.(map[string]interface{})
				require.True(t, ok, "结果应该是 map[string]interface{}")

				// 检查结果字段
				value, exists := resultMap["result"]
				require.True(t, exists, "结果应该包含 result 字段")

				floatValue, ok := value.(float64)
				require.True(t, ok, "result 应该是 float64 类型")

				assert.InDelta(t, tt.expected, floatValue, 0.0001)

				// 检查表达式字段
				expr, exists := resultMap["expression"]
				require.True(t, exists, "结果应该包含 expression 字段")
				assert.Equal(t, tt.expression, expr)
			}
		})
	}
}

func TestCalculatorTool_Execute_ErrorCases(t *testing.T) {
	calc := NewCalculatorTool()
	ctx := context.Background()

	tests := []struct {
		name   string
		params map[string]any
	}{
		{
			name:   "缺少表达式参数",
			params: map[string]any{},
		},
		{
			name: "表达式参数类型错误",
			params: map[string]any{
				"expression": 123,
			},
		},
		{
			name: "空表达式",
			params: map[string]any{
				"expression": "",
			},
		},
		{
			name: "无效表达式",
			params: map[string]any{
				"expression": "2 + + 3",
			},
		},
		{
			name: "除零错误",
			params: map[string]any{
				"expression": "5 / 0",
			},
		},
		{
			name: "未知操作符",
			params: map[string]any{
				"expression": "2 ^ 3",
			},
		},
		{
			name: "不匹配的括号",
			params: map[string]any{
				"expression": "(2 + 3",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := calc.Execute(ctx, tt.params)
			assert.Error(t, err)
			assert.Nil(t, result)
		})
	}
}

func TestCalculatorTool_Execute_AdvancedOperations(t *testing.T) {
	calc := NewCalculatorTool()
	ctx := context.Background()

	tests := []struct {
		name       string
		expression string
		expected   float64
	}{
		{
			name:       "负数运算",
			expression: "-5 + 3",
			expected:   -2,
		},
		{
			name:       "简单运算",
			expression: "2 + 3 * 4 - 1",
			expected:   13,
		},
		{
			name:       "大数运算",
			expression: "1000000 + 2000000",
			expected:   3000000,
		},
		{
			name:       "精度测试",
			expression: "0.1 + 0.2",
			expected:   0.3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := map[string]any{
				"expression": tt.expression,
			}

			result, err := calc.Execute(ctx, params)
			require.NoError(t, err)

			resultMap, ok := result.(map[string]interface{})
			require.True(t, ok)

			value, exists := resultMap["result"]
			require.True(t, exists)

			floatValue, ok := value.(float64)
			require.True(t, ok)

			assert.InDelta(t, tt.expected, floatValue, 0.0001)
		})
	}
}

func TestCalculatorTool_Execute_NilContext(t *testing.T) {
	calc := NewCalculatorTool()

	params := map[string]any{
		"expression": "2 + 3",
	}

	// 即使 context 为 nil，也应该能正常工作
	result, err := calc.Execute(nil, params)
	require.NoError(t, err)

	resultMap, ok := result.(map[string]interface{})
	require.True(t, ok)

	value, exists := resultMap["result"]
	require.True(t, exists)

	floatValue, ok := value.(float64)
	require.True(t, ok)

	assert.Equal(t, 5.0, floatValue)
}

func TestCalculatorTool_Execute_ConcurrentAccess(t *testing.T) {
	calc := NewCalculatorTool()
	ctx := context.Background()

	// 测试并发访问
	const numGoroutines = 10
	const numOperations = 100

	results := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			for j := 0; j < numOperations; j++ {
				params := map[string]any{
					"expression": "2 + 3",
				}

				_, err := calc.Execute(ctx, params)
				if err != nil {
					results <- err
					return
				}
			}
			results <- nil
		}(i)
	}

	// 等待所有 goroutine 完成
	for i := 0; i < numGoroutines; i++ {
		err := <-results
		assert.NoError(t, err)
	}
}
