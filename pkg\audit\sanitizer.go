package audit

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"regexp"
	"strings"
)

// PIIPattern PII模式定义
type PIIPattern struct {
	Name    string
	Pattern *regexp.Regexp
	Replace string
}

// 预定义的PII模式
var (
	EmailPattern = PIIPattern{
		Name:    "email",
		Pattern: regexp.MustCompile(`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`),
		Replace: "[EMAIL]",
	}

	PhonePattern = PIIPattern{
		Name:    "phone",
		Pattern: regexp.MustCompile(`(?:\+86[-\s]?)?(?:1[3-9]\d{9}|(?:0\d{2,3}[-\s]?)?\d{7,8})`),
		Replace: "[PHONE]",
	}

	IDCardPattern = PIIPattern{
		Name:    "idcard",
		Pattern: regexp.MustCompile(`\b\d{17}[\dXx]\b`),
		Replace: "[IDCARD]",
	}

	CreditCardPattern = PIIPattern{
		Name:    "creditcard",
		Pattern: regexp.MustCompile(`\b(?:\d{4}[-\s]?){3}\d{4}\b`),
		Replace: "[CREDITCARD]",
	}

	IPAddressPattern = PIIPattern{
		Name:    "ip",
		Pattern: regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`),
		Replace: "[IP]",
	}
)

// GetDefaultPIIPatterns 获取默认PII模式
func GetDefaultPIIPatterns() map[string]PIIPattern {
	return map[string]PIIPattern{
		"email":      EmailPattern,
		"phone":      PhonePattern,
		"idcard":     IDCardPattern,
		"creditcard": CreditCardPattern,
		"ip":         IPAddressPattern,
	}
}

// Sanitizer 数据脱敏器
type Sanitizer struct {
	patterns  map[string]PIIPattern
	encryptor *Encryptor
	hashOnly  bool
	redactPII bool
}

// NewSanitizer 创建数据脱敏器
func NewSanitizer(config *PrivacyConfig) (*Sanitizer, error) {
	s := &Sanitizer{
		patterns:  make(map[string]PIIPattern),
		hashOnly:  config.HashContent,
		redactPII: config.RedactPII,
	}

	// 加载PII模式
	if config.RedactPII {
		defaultPatterns := GetDefaultPIIPatterns()
		for _, patternName := range config.PIIPatterns {
			if pattern, exists := defaultPatterns[patternName]; exists {
				s.patterns[patternName] = pattern
			}
		}
	}

	// 初始化加密器
	if config.EncryptAtRest && config.EncryptKeyEnv != "" {
		encryptor, err := NewEncryptor(config.EncryptKeyEnv)
		if err != nil {
			return nil, NewSanitizationError("初始化加密器失败", err)
		}
		s.encryptor = encryptor
	}

	return s, nil
}

// SanitizeRecord 脱敏记录
func (s *Sanitizer) SanitizeRecord(r *Record) error {
	if r == nil {
		return NewInvalidRecordError("记录不能为空", nil)
	}

	// 处理内容
	if r.Content != "" {
		sanitized, err := s.SanitizeContent(r.Content)
		if err != nil {
			return err
		}
		r.Content = sanitized
	}

	// 如果只存储哈希，清空原始内容
	if s.hashOnly {
		r.ContentHash = HashContent(r.Content)
		r.Content = ""
	} else if r.ContentHash == "" {
		r.ContentHash = HashContent(r.Content)
	}

	return nil
}

// SanitizeContent 脱敏内容
func (s *Sanitizer) SanitizeContent(content string) (string, error) {
	if content == "" {
		return content, nil
	}

	result := content

	// PII脱敏
	if s.redactPII {
		for _, pattern := range s.patterns {
			result = pattern.Pattern.ReplaceAllString(result, pattern.Replace)
		}
	}

	// 加密
	if s.encryptor != nil {
		encrypted, err := s.encryptor.Encrypt(result)
		if err != nil {
			return "", NewEncryptionError("加密内容失败", err)
		}
		result = encrypted
	}

	return result, nil
}

// UnsanitizeContent 反脱敏内容（仅用于解密）
func (s *Sanitizer) UnsanitizeContent(content string) (string, error) {
	if content == "" || s.encryptor == nil {
		return content, nil
	}

	decrypted, err := s.encryptor.Decrypt(content)
	if err != nil {
		return "", NewDecryptionError("解密内容失败", err)
	}

	return decrypted, nil
}

// Encryptor 加密器
type Encryptor struct {
	key []byte
	gcm cipher.AEAD
}

// NewEncryptor 创建加密器
func NewEncryptor(keyEnv string) (*Encryptor, error) {
	// 从环境变量获取密钥
	keyStr := os.Getenv(keyEnv)
	if keyStr == "" {
		return nil, NewEncryptionError("未找到加密密钥环境变量: "+keyEnv, nil)
	}

	if len(keyStr) != 32 {
		return nil, NewEncryptionError("密钥长度必须为32字节", nil)
	}

	key := []byte(keyStr)

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, NewEncryptionError("创建AES密码器失败", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, NewEncryptionError("创建GCM模式失败", err)
	}

	return &Encryptor{
		key: key,
		gcm: gcm,
	}, nil
}

// Encrypt 加密数据
func (e *Encryptor) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	// 生成随机nonce
	nonce := make([]byte, e.gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", NewEncryptionError("生成nonce失败", err)
	}

	// 加密
	ciphertext := e.gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 解密数据
func (e *Encryptor) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// Base64解码
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", NewDecryptionError("Base64解码失败", err)
	}

	// 检查数据长度
	nonceSize := e.gcm.NonceSize()
	if len(data) < nonceSize {
		return "", NewDecryptionError("密文数据太短", nil)
	}

	// 提取nonce和密文
	nonce, cipherData := data[:nonceSize], data[nonceSize:]

	// 解密
	plaintext, err := e.gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return "", NewDecryptionError("解密失败", err)
	}

	return string(plaintext), nil
}

// RedactorFunc 脱敏函数类型
type RedactorFunc func(content string) string

// CreateCustomRedactor 创建自定义脱敏器
func CreateCustomRedactor(patterns map[string]string) RedactorFunc {
	compiledPatterns := make(map[string]*regexp.Regexp)

	for name, pattern := range patterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			compiledPatterns[name] = compiled
		}
	}

	return func(content string) string {
		result := content
		for name, pattern := range compiledPatterns {
			result = pattern.ReplaceAllString(result, fmt.Sprintf("[%s]", strings.ToUpper(name)))
		}
		return result
	}
}

// HashSensitiveFields 对敏感字段进行哈希处理
func HashSensitiveFields(fields map[string]string) map[string]string {
	result := make(map[string]string)
	for key, value := range fields {
		if value != "" {
			hash := sha256.Sum256([]byte(value))
			result[key] = fmt.Sprintf("%x", hash)
		} else {
			result[key] = value
		}
	}
	return result
}

// ValidateEncryptionKey 验证加密密钥
func ValidateEncryptionKey(key string) error {
	if len(key) != 32 {
		return NewEncryptionError("加密密钥长度必须为32字节", nil)
	}
	return nil
}
