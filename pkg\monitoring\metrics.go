package monitoring

import (
	"encoding/json"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	metrics map[string]*Metric
	mu      sync.RWMutex
	logger  logger.Logger
	stopCh  chan struct{}
	running bool
}

// Metric 指标
type Metric struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Value       float64           `json:"value"`
	Labels      map[string]string `json:"labels"`
	Timestamp   time.Time         `json:"timestamp"`
	Description string            `json:"description"`
	Unit        string            `json:"unit"`
}

// MetricType 指标类型
type MetricType string

const (
	CounterType   MetricType = "counter"
	GaugeType     MetricType = "gauge"
	HistogramType MetricType = "histogram"
	SummaryType   MetricType = "summary"
)

// Counter 计数器
type Counter struct {
	value  int64
	name   string
	labels map[string]string
}

// Gauge 仪表盘
type Gauge struct {
	value  int64
	name   string
	labels map[string]string
}

// Histogram 直方图
type Histogram struct {
	buckets map[float64]int64
	sum     int64
	count   int64
	name    string
	labels  map[string]string
	mu      sync.Mutex
}

// Summary 摘要
type Summary struct {
	sum    float64
	count  int64
	name   string
	labels map[string]string
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(logger logger.Logger) *MetricsCollector {
	return &MetricsCollector{
		metrics: make(map[string]*Metric),
		logger:  logger,
		stopCh:  make(chan struct{}),
	}
}

// Start 启动指标收集
func (mc *MetricsCollector) Start() {
	mc.mu.Lock()
	if mc.running {
		mc.mu.Unlock()
		return
	}
	mc.running = true
	mc.mu.Unlock()

	go mc.collectSystemMetrics()
	mc.logger.Info("Metrics collector started")
}

// Stop 停止指标收集
func (mc *MetricsCollector) Stop() {
	mc.mu.Lock()
	if !mc.running {
		mc.mu.Unlock()
		return
	}
	mc.running = false
	mc.mu.Unlock()

	close(mc.stopCh)
	mc.logger.Info("Metrics collector stopped")
}

// RegisterCounter 注册计数器
func (mc *MetricsCollector) RegisterCounter(name, description string, labels map[string]string) *Counter {
	mc.mu.Lock()
	defer mc.mu.Unlock()

	counter := &Counter{
		name:   name,
		labels: labels,
	}

	mc.metrics[name] = &Metric{
		Name:        name,
		Type:        CounterType,
		Labels:      labels,
		Description: description,
		Timestamp:   time.Now(),
	}

	return counter
}

// RegisterGauge 注册仪表盘
func (mc *MetricsCollector) RegisterGauge(name, description string, labels map[string]string) *Gauge {
	mc.mu.Lock()
	defer mc.mu.Unlock()

	gauge := &Gauge{
		name:   name,
		labels: labels,
	}

	mc.metrics[name] = &Metric{
		Name:        name,
		Type:        GaugeType,
		Labels:      labels,
		Description: description,
		Timestamp:   time.Now(),
	}

	return gauge
}

// RegisterHistogram 注册直方图
func (mc *MetricsCollector) RegisterHistogram(name, description string, labels map[string]string, buckets []float64) *Histogram {
	mc.mu.Lock()
	defer mc.mu.Unlock()

	bucketMap := make(map[float64]int64)
	for _, bucket := range buckets {
		bucketMap[bucket] = 0
	}

	histogram := &Histogram{
		buckets: bucketMap,
		name:    name,
		labels:  labels,
	}

	mc.metrics[name] = &Metric{
		Name:        name,
		Type:        HistogramType,
		Labels:      labels,
		Description: description,
		Timestamp:   time.Now(),
	}

	return histogram
}

// Inc 增加计数器
func (c *Counter) Inc() {
	atomic.AddInt64(&c.value, 1)
}

// Add 增加计数器指定值
func (c *Counter) Add(delta float64) {
	atomic.AddInt64(&c.value, int64(delta))
}

// Value 获取计数器值
func (c *Counter) Value() float64 {
	return float64(atomic.LoadInt64(&c.value))
}

// Set 设置仪表盘值
func (g *Gauge) Set(value float64) {
	atomic.StoreInt64(&g.value, int64(value))
}

// Inc 增加仪表盘值
func (g *Gauge) Inc() {
	atomic.AddInt64(&g.value, 1)
}

// Dec 减少仪表盘值
func (g *Gauge) Dec() {
	atomic.AddInt64(&g.value, -1)
}

// Add 增加仪表盘指定值
func (g *Gauge) Add(delta float64) {
	atomic.AddInt64(&g.value, int64(delta))
}

// Sub 减少仪表盘指定值
func (g *Gauge) Sub(delta float64) {
	atomic.AddInt64(&g.value, -int64(delta))
}

// Value 获取仪表盘值
func (g *Gauge) Value() float64 {
	return float64(atomic.LoadInt64(&g.value))
}

// Observe 观察直方图值
func (h *Histogram) Observe(value float64) {
	// 更新计数与总和
	atomic.AddInt64(&h.count, 1)
	atomic.AddInt64(&h.sum, int64(value))

	// 使用互斥锁更新桶，避免对map元素进行原子操作
	h.mu.Lock()
	for bucket := range h.buckets {
		if value <= bucket {
			h.buckets[bucket] = h.buckets[bucket] + 1
		}
	}
	h.mu.Unlock()
}

// Count 获取直方图计数
func (h *Histogram) Count() int64 {
	return atomic.LoadInt64(&h.count)
}

// Sum 获取直方图总和
func (h *Histogram) Sum() int64 {
	return atomic.LoadInt64(&h.sum)
}

// Buckets 获取直方图桶
func (h *Histogram) Buckets() map[float64]int64 {
	buckets := make(map[float64]int64)
	h.mu.Lock()
	for bucket, count := range h.buckets {
		buckets[bucket] = count
	}
	h.mu.Unlock()
	return buckets
}

// Observe 观察摘要值
func (s *Summary) Observe(value float64) {
	atomic.AddInt64(&s.count, 1)
	// 这里简化处理，实际应该使用更复杂的算法
	s.sum += value
}

// Count 获取摘要计数
func (s *Summary) Count() int64 {
	return atomic.LoadInt64(&s.count)
}

// Sum 获取摘要总和
func (s *Summary) Sum() float64 {
	return s.sum
}

// GetMetrics 获取所有指标
func (mc *MetricsCollector) GetMetrics() map[string]*Metric {
	mc.mu.RLock()
	defer mc.mu.RUnlock()

	metrics := make(map[string]*Metric)
	for name, metric := range mc.metrics {
		metricCopy := *metric
		metricCopy.Timestamp = time.Now()
		metrics[name] = &metricCopy
	}

	return metrics
}

// GetMetric 获取指定指标
func (mc *MetricsCollector) GetMetric(name string) (*Metric, bool) {
	mc.mu.RLock()
	defer mc.mu.RUnlock()

	metric, exists := mc.metrics[name]
	if !exists {
		return nil, false
	}

	metricCopy := *metric
	metricCopy.Timestamp = time.Now()
	return &metricCopy, true
}

// collectSystemMetrics 收集系统指标
func (mc *MetricsCollector) collectSystemMetrics() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	// 注册系统指标
	memoryGauge := mc.RegisterGauge("system_memory_bytes", "System memory usage in bytes", nil)
	goroutinesGauge := mc.RegisterGauge("system_goroutines_total", "Number of goroutines", nil)
	gcDurationGauge := mc.RegisterGauge("system_gc_duration_seconds", "GC duration in seconds", nil)

	for {
		select {
		case <-ticker.C:
			mc.updateSystemMetrics(memoryGauge, goroutinesGauge, gcDurationGauge)
		case <-mc.stopCh:
			return
		}
	}
}

// updateSystemMetrics 更新系统指标
func (mc *MetricsCollector) updateSystemMetrics(memoryGauge, goroutinesGauge, gcDurationGauge *Gauge) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 更新内存使用
	memoryGauge.Set(float64(m.Alloc))

	// 更新goroutine数量
	goroutinesGauge.Set(float64(runtime.NumGoroutine()))

	// 更新GC持续时间
	gcDurationGauge.Set(float64(m.PauseTotalNs) / 1e9)

	mc.logger.Debugf("Updated system metrics: memory=%d, goroutines=%d, gc_duration=%.3fs",
		m.Alloc, runtime.NumGoroutine(), float64(m.PauseTotalNs)/1e9)
}

// AgentMetrics Agent指标
type AgentMetrics struct {
	collector         *MetricsCollector
	requestsCounter   *Counter
	errorsCounter     *Counter
	durationHistogram *Histogram
	activeGauge       *Gauge
}

// NewAgentMetrics 创建Agent指标
func NewAgentMetrics(collector *MetricsCollector, agentID string) *AgentMetrics {
	labels := map[string]string{"agent_id": agentID}

	return &AgentMetrics{
		collector: collector,
		requestsCounter: collector.RegisterCounter(
			"agent_requests_total",
			"Total number of agent requests",
			labels,
		),
		errorsCounter: collector.RegisterCounter(
			"agent_errors_total",
			"Total number of agent errors",
			labels,
		),
		durationHistogram: collector.RegisterHistogram(
			"agent_request_duration_seconds",
			"Agent request duration in seconds",
			labels,
			[]float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0},
		),
		activeGauge: collector.RegisterGauge(
			"agent_active_requests",
			"Number of active agent requests",
			labels,
		),
	}
}

// RecordRequest 记录请求
func (am *AgentMetrics) RecordRequest() {
	am.requestsCounter.Inc()
	am.activeGauge.Inc()
}

// RecordError 记录错误
func (am *AgentMetrics) RecordError() {
	am.errorsCounter.Inc()
}

// RecordDuration 记录持续时间
func (am *AgentMetrics) RecordDuration(duration time.Duration) {
	am.durationHistogram.Observe(duration.Seconds())
	am.activeGauge.Dec()
}

// LLMMetrics LLM指标
type LLMMetrics struct {
	collector         *MetricsCollector
	requestsCounter   *Counter
	errorsCounter     *Counter
	tokensCounter     *Counter
	durationHistogram *Histogram
}

// NewLLMMetrics 创建LLM指标
func NewLLMMetrics(collector *MetricsCollector, provider string) *LLMMetrics {
	labels := map[string]string{"provider": provider}

	return &LLMMetrics{
		collector: collector,
		requestsCounter: collector.RegisterCounter(
			"llm_requests_total",
			"Total number of LLM requests",
			labels,
		),
		errorsCounter: collector.RegisterCounter(
			"llm_errors_total",
			"Total number of LLM errors",
			labels,
		),
		tokensCounter: collector.RegisterCounter(
			"llm_tokens_total",
			"Total number of LLM tokens processed",
			labels,
		),
		durationHistogram: collector.RegisterHistogram(
			"llm_request_duration_seconds",
			"LLM request duration in seconds",
			labels,
			[]float64{0.5, 1.0, 2.0, 5.0, 10.0, 30.0},
		),
	}
}

// RecordRequest 记录LLM请求
func (lm *LLMMetrics) RecordRequest() {
	lm.requestsCounter.Inc()
}

// RecordError 记录LLM错误
func (lm *LLMMetrics) RecordError() {
	lm.errorsCounter.Inc()
}

// RecordTokens 记录token数量
func (lm *LLMMetrics) RecordTokens(tokens int) {
	lm.tokensCounter.Add(float64(tokens))
}

// RecordDuration 记录LLM请求持续时间
func (lm *LLMMetrics) RecordDuration(duration time.Duration) {
	lm.durationHistogram.Observe(duration.Seconds())
}

// MetricsExporter 指标导出器
type MetricsExporter struct {
	collector *MetricsCollector
	logger    logger.Logger
}

// NewMetricsExporter 创建指标导出器
func NewMetricsExporter(collector *MetricsCollector, logger logger.Logger) *MetricsExporter {
	return &MetricsExporter{
		collector: collector,
		logger:    logger,
	}
}

// ExportPrometheus 导出Prometheus格式指标
func (me *MetricsExporter) ExportPrometheus() string {
	metrics := me.collector.GetMetrics()
	var output string

	for _, metric := range metrics {
		// 添加帮助信息
		output += fmt.Sprintf("# HELP %s %s\n", metric.Name, metric.Description)
		output += fmt.Sprintf("# TYPE %s %s\n", metric.Name, metric.Type)

		// 添加指标值
		labelsStr := ""
		if len(metric.Labels) > 0 {
			var labelPairs []string
			for k, v := range metric.Labels {
				labelPairs = append(labelPairs, fmt.Sprintf(`%s="%s"`, k, v))
			}
			labelsStr = "{" + strings.Join(labelPairs, ",") + "}"
		}

		output += fmt.Sprintf("%s%s %f %d\n", metric.Name, labelsStr, metric.Value, metric.Timestamp.Unix())
	}

	return output
}

// ExportJSON 导出JSON格式指标
func (me *MetricsExporter) ExportJSON() ([]byte, error) {
	metrics := me.collector.GetMetrics()
	return json.Marshal(metrics)
}

// Global metrics collector instance
var globalCollector *MetricsCollector
var once sync.Once

// GetGlobalMetricsCollector 获取全局指标收集器
func GetGlobalMetricsCollector() *MetricsCollector {
	once.Do(func() {
		globalCollector = NewMetricsCollector(logger.GetGlobalLogger())
	})
	return globalCollector
}
