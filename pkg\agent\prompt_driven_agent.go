package agent

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/config"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/knowledge"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/llm/prompts"
	"github.com/agentscope/agentscope-golang/pkg/logger"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/tool"
	"github.com/agentscope/agentscope-golang/pkg/tool/builtin/ticket"
)

// PromptDrivenAgent 提示词驱动的Agent实现
type PromptDrivenAgent struct {
	*BaseAgent
	config       *config.PromptDrivenAgentConfig
	llmClient    llm.LLMClient
	tools        map[string]tool.Tool
	faqManager   *knowledge.FAQManager
	systemPrompt string
}

// NewPromptDrivenAgent 创建提示词驱动的Agent
func NewPromptDrivenAgent(name string, agentConfig *config.PromptDrivenAgentConfig, llmClient llm.LLMClient) (*PromptDrivenAgent, error) {
	baseAgent := NewBaseAgent(name, agentConfig.Description)

	agent := &PromptDrivenAgent{
		BaseAgent:  baseAgent,
		config:     agentConfig,
		llmClient:  llmClient,
		tools:      make(map[string]tool.Tool),
		faqManager: knowledge.NewFAQManager(),
	}

	// 构建系统提示词
	if err := agent.buildSystemPrompt(); err != nil {
		return nil, fmt.Errorf("构建系统提示词失败: %w", err)
	}

	// 注册工具
	if err := agent.registerTools(); err != nil {
		return nil, fmt.Errorf("注册工具失败: %w", err)
	}

	// 初始化知识库
	if err := agent.initializeKnowledgeBases(); err != nil {
		return nil, fmt.Errorf("初始化知识库失败: %w", err)
	}

	return agent, nil
}

// buildSystemPrompt 构建系统提示词
func (a *PromptDrivenAgent) buildSystemPrompt() error {
	// 使用能力模板组合构建系统提示词
	systemPrompt := prompts.CombineCapabilities(
		a.config.AiCapabilities,
		a.config.BaseRole,
		a.config.PersonalityTraits,
		a.config.BehavioralConstraints,
	)

	// 添加业务配置信息
	if a.config.BusinessConfig != nil {
		systemPrompt += a.buildBusinessContext()
	}

	a.systemPrompt = systemPrompt
	logger.Info("系统提示词构建完成", "length", len(systemPrompt))

	return nil
}

// buildBusinessContext 构建业务上下文信息
func (a *PromptDrivenAgent) buildBusinessContext() string {
	context := "\n【业务知识库】\n"

	// 添加问题分类信息
	if classification := a.config.BusinessConfig.Classification; classification != nil {
		context += "问题分类标准：\n"
		for _, category := range classification.Categories {
			context += fmt.Sprintf("• %s: %s (优先级: %s)\n",
				category.Name, category.Description, category.Priority)
			if len(category.Keywords) > 0 {
				context += fmt.Sprintf("  关键词: %s\n", strings.Join(category.Keywords, ", "))
			}
		}
		context += "\n"
	}

	// 添加FAQ信息
	if faqs := a.config.BusinessConfig.FAQs; len(faqs) > 0 {
		context += "常见问题库：\n"
		for _, faq := range faqs {
			context += fmt.Sprintf("Q: %s\nA: %s\n", faq.Question, faq.Answer)
			if len(faq.Keywords) > 0 {
				context += fmt.Sprintf("关键词: %s\n", strings.Join(faq.Keywords, ", "))
			}
			context += "---\n"
		}
		context += "\n"
	}

	// 添加情绪处理指导
	if emotion := a.config.BusinessConfig.EmotionHandling; emotion != nil {
		context += "情绪处理指导：\n"
		context += fmt.Sprintf("• 负面情绪升级阈值: %.1f\n", emotion.EscalationThreshold)
		if len(emotion.NegativeKeywords) > 0 {
			context += fmt.Sprintf("• 负面情绪关键词: %s\n", strings.Join(emotion.NegativeKeywords, ", "))
		}
		if len(emotion.CalmingPhrases) > 0 {
			context += fmt.Sprintf("• 安抚用语: %s\n", strings.Join(emotion.CalmingPhrases, ", "))
		}
		context += "\n"
	}

	// 添加质量标准
	if quality := a.config.BusinessConfig.QualityStandards; quality != nil {
		context += "服务质量标准：\n"
		context += fmt.Sprintf("• 响应时间: %s\n", quality.ResponseTime)
		context += fmt.Sprintf("• 解决率: %s\n", quality.ResolutionRate)
		context += fmt.Sprintf("• 满意度: %s\n", quality.SatisfactionScore)
		if len(quality.KeyMetrics) > 0 {
			context += fmt.Sprintf("• 关键指标: %s\n", strings.Join(quality.KeyMetrics, ", "))
		}
		context += "\n"
	}

	return context
}

// registerTools 注册工具
func (a *PromptDrivenAgent) registerTools() error {
	for _, toolName := range a.config.Tools {
		switch toolName {
		case "ticket_manager":
			// 注册工单管理工具
			ticketTool := ticket.NewTicketTool()
			a.tools[toolName] = ticketTool
			logger.Info("注册工具", "tool", toolName)
		default:
			logger.Warn("未知工具", "tool", toolName)
		}
	}
	return nil
}

// initializeKnowledgeBases 初始化知识库
func (a *PromptDrivenAgent) initializeKnowledgeBases() error {
	if a.config.BusinessConfig == nil || a.config.BusinessConfig.KnowledgeBases == nil {
		logger.Info("未配置知识库，跳过初始化")
		return nil
	}

	kbConfig := a.config.BusinessConfig.KnowledgeBases

	// 创建文件存储库
	if kbConfig.Repository.Type == "file" {
		repository := knowledge.NewFileRepository(kbConfig.Repository.BasePath)

		// 加载所有启用的知识库
		for _, baseRef := range kbConfig.Bases {
			if !baseRef.Enabled {
				continue
			}

			kb, err := repository.LoadKnowledgeBase(context.Background(), baseRef.ID)
			if err != nil {
				logger.Warn("加载知识库失败", "kb_id", baseRef.ID, "error", err)
				continue
			}

			if err := a.faqManager.LoadKnowledgeBase(context.Background(), kb); err != nil {
				logger.Warn("加载知识库到管理器失败", "kb_id", baseRef.ID, "error", err)
				continue
			}

			logger.Info("知识库加载成功", "kb_id", baseRef.ID, "name", kb.Name)
		}
	}

	return nil
}

// searchKnowledge 搜索相关知识
func (a *PromptDrivenAgent) searchKnowledge(ctx context.Context, query string) string {
	if a.faqManager == nil {
		return ""
	}

	// 构建搜索查询
	searchQuery := &knowledge.FAQSearchQuery{
		Query:    query,
		Limit:    3,
		MinScore: 0.3,
	}

	// 如果配置了角色，添加角色过滤
	if a.config.BaseRole != "" {
		searchQuery.Role = a.config.BaseRole
	}

	// 执行搜索
	results, err := a.faqManager.Search(ctx, searchQuery)
	if err != nil {
		logger.Warn("知识库搜索失败", "error", err)
		return ""
	}

	if len(results) == 0 {
		return ""
	}

	// 构建知识上下文
	var context strings.Builder
	context.WriteString("以下是相关的FAQ信息，请参考这些信息回答用户问题：\n\n")

	for i, result := range results {
		context.WriteString(fmt.Sprintf("%d. 问题：%s\n", i+1, result.Item.Question))
		context.WriteString(fmt.Sprintf("   答案：%s\n", result.Item.Answer))
		context.WriteString(fmt.Sprintf("   匹配度：%.2f\n", result.Score))
		if len(result.MatchedKeywords) > 0 {
			context.WriteString(fmt.Sprintf("   匹配关键词：%s\n", strings.Join(result.MatchedKeywords, ", ")))
		}
		context.WriteString("\n")
	}

	return context.String()
}

// Run 运行Agent
func (a *PromptDrivenAgent) Run(ctx context.Context, input string) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)

	go func() {
		defer pair.Generator.Close()

		// 发送思考事件
		pair.Generator.Send(&event.Event{
			Type: event.EventThought,
			Data: "正在分析用户问题...",
			At:   time.Now(),
		})

		// 检索相关知识
		knowledgeContext := a.searchKnowledge(ctx, input)

		// 构建对话消息
		systemPrompt := a.systemPrompt
		if knowledgeContext != "" {
			systemPrompt += "\n\n相关知识库信息：\n" + knowledgeContext
		}

		messages := []*llm.ChatMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: input,
			},
		}

		// 调用LLM生成回复
		request := &llm.GenerateRequest{
			Messages: messages,
		}

		response, err := a.llmClient.Generate(ctx, request)
		if err != nil {
			pair.Generator.Send(event.NewErrorEvent(err, "llm_error", nil))
			return
		}

		if len(response.Choices) == 0 {
			pair.Generator.Send(event.NewErrorEvent(fmt.Errorf("LLM返回空响应"), "empty_response", nil))
			return
		}

		// 发送分析事件
		pair.Generator.Send(&event.Event{
			Type: "analysis",
			Data: "已完成问题分析和情绪识别",
			At:   time.Now(),
		})

		// 检查是否需要调用工具
		content := response.Choices[0].Message.Content
		contentStr, ok := content.(string)
		if !ok {
			contentStr = fmt.Sprintf("%v", content)
		}
		if a.needToolCall(contentStr, input) {
			pair.Generator.Send(&event.Event{
				Type: event.EventToolCall,
				Data: "正在调用相关工具处理问题...",
				At:   time.Now(),
			})

			// 这里可以添加工具调用逻辑
			// 暂时简化处理
		}

		// 发送最终回复
		pair.Generator.Send(&event.Event{
			Type: "response",
			Data: contentStr,
			At:   time.Now(),
		})

		// 发送质量评估事件
		pair.Generator.Send(&event.Event{
			Type: "quality_assessment",
			Data: "回复质量评估: 准确性90%, 友好性95%, 专业性88%",
			At:   time.Now(),
		})

		// 发送最终事件
		pair.Generator.Send(event.NewFinalEvent(contentStr, "对话处理完成", nil))
	}()

	return pair.Iterator
}

// needToolCall 判断是否需要调用工具
func (a *PromptDrivenAgent) needToolCall(response, input string) bool {
	// 简化的工具调用判断逻辑
	toolKeywords := []string{"工单", "ticket", "创建", "查询", "更新"}

	for _, keyword := range toolKeywords {
		if strings.Contains(input, keyword) || strings.Contains(response, keyword) {
			return true
		}
	}

	return false
}

// GetSystemPrompt 获取系统提示词（用于调试）
func (a *PromptDrivenAgent) GetSystemPrompt() string {
	return a.systemPrompt
}
