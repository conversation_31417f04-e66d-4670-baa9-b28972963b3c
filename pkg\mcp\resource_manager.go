package mcp

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/agentscope/agentscope-golang/pkg/errors"
	"github.com/agentscope/agentscope-golang/pkg/logger"
)

// MCPResourceManager MCP资源管理器
type MCPResourceManager struct {
	client      MCPClient
	cache       map[string]*CachedResource
	subscribers map[string][]chan *MCPResourceChange
	mu          sync.RWMutex
	logger      logger.Logger
	cacheConfig *ResourceCacheConfig
}

// CachedResource 缓存的资源
type CachedResource struct {
	Content     *MCPResourceContent
	CachedAt    time.Time
	ExpiresAt   time.Time
	AccessCount int64
}

// ResourceCacheConfig 资源缓存配置
type ResourceCacheConfig struct {
	MaxSize         int           `json:"max_size"`
	DefaultTTL      time.Duration `json:"default_ttl"`
	MaxMemoryUsage  int64         `json:"max_memory_usage"`
	CleanupInterval time.Duration `json:"cleanup_interval"`
}

// NewMCPResourceManager 创建MCP资源管理器
func NewMCPResourceManager(client MCPClient, config *ResourceCacheConfig) *MCPResourceManager {
	if config == nil {
		config = &ResourceCacheConfig{
			MaxSize:         1000,
			DefaultTTL:      30 * time.Minute,
			MaxMemoryUsage:  100 * 1024 * 1024, // 100MB
			CleanupInterval: 5 * time.Minute,
		}
	}

	manager := &MCPResourceManager{
		client:      client,
		cache:       make(map[string]*CachedResource),
		subscribers: make(map[string][]chan *MCPResourceChange),
		logger:      logger.GetGlobalLogger(),
		cacheConfig: config,
	}

	// 启动缓存清理
	go manager.startCacheCleanup()

	return manager
}

// ListResources 列出可用资源
func (rm *MCPResourceManager) ListResources(ctx context.Context) ([]*MCPResource, error) {
	if !rm.client.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	resources, err := rm.client.ListResources(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list MCP resources: %w", err)
	}

	rm.logger.Debugf("Listed %d MCP resources", len(resources))
	return resources, nil
}

// ReadResource 读取资源（带缓存）
func (rm *MCPResourceManager) ReadResource(ctx context.Context, uri string) (*MCPResourceContent, error) {
	// 检查缓存
	if cached := rm.getCachedResource(uri); cached != nil {
		rm.logger.Debugf("Resource cache hit: %s", uri)
		return cached.Content, nil
	}

	// 从MCP服务器读取
	content, err := rm.client.ReadResource(ctx, uri)
	if err != nil {
		return nil, fmt.Errorf("failed to read MCP resource %s: %w", uri, err)
	}

	// 缓存资源
	rm.cacheResource(uri, content)
	rm.logger.Debugf("Resource cached: %s", uri)

	return content, nil
}

// ReadResourceWithoutCache 读取资源（不使用缓存）
func (rm *MCPResourceManager) ReadResourceWithoutCache(ctx context.Context, uri string) (*MCPResourceContent, error) {
	if !rm.client.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	content, err := rm.client.ReadResource(ctx, uri)
	if err != nil {
		return nil, fmt.Errorf("failed to read MCP resource %s: %w", uri, err)
	}

	return content, nil
}

// SubscribeToResource 订阅资源变更
func (rm *MCPResourceManager) SubscribeToResource(ctx context.Context, uri string) (<-chan *MCPResourceChange, error) {
	if !rm.client.IsConnected() {
		return nil, errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	// 订阅MCP资源变更
	mcpCh, err := rm.client.Subscribe(ctx, uri)
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to MCP resource %s: %w", uri, err)
	}

	// 创建本地通道
	localCh := make(chan *MCPResourceChange, 100)

	// 添加到订阅者列表
	rm.mu.Lock()
	rm.subscribers[uri] = append(rm.subscribers[uri], localCh)
	rm.mu.Unlock()

	// 启动转发协程
	go rm.forwardResourceChanges(uri, mcpCh, localCh)

	rm.logger.Debugf("Subscribed to resource: %s", uri)
	return localCh, nil
}

// UnsubscribeFromResource 取消订阅资源变更
func (rm *MCPResourceManager) UnsubscribeFromResource(ctx context.Context, uri string, ch <-chan *MCPResourceChange) error {
	if !rm.client.IsConnected() {
		return errors.NewConnectionError("not_connected", "MCP client is not connected")
	}

	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 从订阅者列表中移除
	subscribers := rm.subscribers[uri]
	for i, subscriber := range subscribers {
		if subscriber == ch {
			rm.subscribers[uri] = append(subscribers[:i], subscribers[i+1:]...)
			close(subscriber)
			break
		}
	}

	// 如果没有更多订阅者，取消MCP订阅
	if len(rm.subscribers[uri]) == 0 {
		delete(rm.subscribers, uri)
		if err := rm.client.Unsubscribe(ctx, uri); err != nil {
			return fmt.Errorf("failed to unsubscribe from MCP resource %s: %w", uri, err)
		}
	}

	rm.logger.Debugf("Unsubscribed from resource: %s", uri)
	return nil
}

// InvalidateCache 使缓存失效
func (rm *MCPResourceManager) InvalidateCache(uri string) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	delete(rm.cache, uri)
	rm.logger.Debugf("Cache invalidated for resource: %s", uri)
}

// ClearCache 清空所有缓存
func (rm *MCPResourceManager) ClearCache() {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	rm.cache = make(map[string]*CachedResource)
	rm.logger.Info("All resource cache cleared")
}

// GetCacheStats 获取缓存统计
func (rm *MCPResourceManager) GetCacheStats() *CacheStats {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	stats := &CacheStats{
		TotalEntries: len(rm.cache),
		TotalSize:    0,
	}

	for uri, cached := range rm.cache {
		stats.TotalSize += rm.estimateResourceSize(cached.Content)

		if time.Now().After(cached.ExpiresAt) {
			stats.ExpiredEntries++
		}

		stats.Entries = append(stats.Entries, &CacheEntry{
			URI:         uri,
			CachedAt:    cached.CachedAt,
			ExpiresAt:   cached.ExpiresAt,
			AccessCount: cached.AccessCount,
			Size:        rm.estimateResourceSize(cached.Content),
		})
	}

	return stats
}

// getCachedResource 获取缓存的资源
func (rm *MCPResourceManager) getCachedResource(uri string) *CachedResource {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	cached, exists := rm.cache[uri]
	if !exists {
		return nil
	}

	// 检查是否过期
	if time.Now().After(cached.ExpiresAt) {
		delete(rm.cache, uri)
		return nil
	}

	// 更新访问计数
	cached.AccessCount++
	return cached
}

// cacheResource 缓存资源
func (rm *MCPResourceManager) cacheResource(uri string, content *MCPResourceContent) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 检查缓存大小限制
	if len(rm.cache) >= rm.cacheConfig.MaxSize {
		rm.evictOldestResource()
	}

	// 检查内存使用限制
	resourceSize := rm.estimateResourceSize(content)
	if rm.getCurrentMemoryUsage()+resourceSize > rm.cacheConfig.MaxMemoryUsage {
		rm.evictLargestResource()
	}

	now := time.Now()
	rm.cache[uri] = &CachedResource{
		Content:     content,
		CachedAt:    now,
		ExpiresAt:   now.Add(rm.cacheConfig.DefaultTTL),
		AccessCount: 1,
	}
}

// evictOldestResource 驱逐最旧的资源
func (rm *MCPResourceManager) evictOldestResource() {
	var oldestURI string
	var oldestTime time.Time

	for uri, cached := range rm.cache {
		if oldestURI == "" || cached.CachedAt.Before(oldestTime) {
			oldestURI = uri
			oldestTime = cached.CachedAt
		}
	}

	if oldestURI != "" {
		delete(rm.cache, oldestURI)
		rm.logger.Debugf("Evicted oldest resource from cache: %s", oldestURI)
	}
}

// evictLargestResource 驱逐最大的资源
func (rm *MCPResourceManager) evictLargestResource() {
	var largestURI string
	var largestSize int64

	for uri, cached := range rm.cache {
		size := rm.estimateResourceSize(cached.Content)
		if size > largestSize {
			largestURI = uri
			largestSize = size
		}
	}

	if largestURI != "" {
		delete(rm.cache, largestURI)
		rm.logger.Debugf("Evicted largest resource from cache: %s (%d bytes)", largestURI, largestSize)
	}
}

// getCurrentMemoryUsage 获取当前内存使用量
func (rm *MCPResourceManager) getCurrentMemoryUsage() int64 {
	var total int64
	for _, cached := range rm.cache {
		total += rm.estimateResourceSize(cached.Content)
	}
	return total
}

// estimateResourceSize 估算资源大小
func (rm *MCPResourceManager) estimateResourceSize(content *MCPResourceContent) int64 {
	var size int64

	// URI大小
	size += int64(len(content.URI))

	// MimeType大小
	size += int64(len(content.MimeType))

	// 内容大小
	for _, mcpContent := range content.Contents {
		size += int64(len(mcpContent.Type))
		size += int64(len(mcpContent.Text))
		size += int64(len(mcpContent.MimeType))

		// 估算Data字段大小（简化）
		if mcpContent.Data != nil {
			size += 1024 // 假设平均1KB
		}
	}

	return size
}

// forwardResourceChanges 转发资源变更
func (rm *MCPResourceManager) forwardResourceChanges(uri string, mcpCh <-chan *MCPResourceChange, localCh chan *MCPResourceChange) {
	defer close(localCh)

	for change := range mcpCh {
		// 使缓存失效
		rm.InvalidateCache(uri)

		// 转发变更通知
		select {
		case localCh <- change:
		default:
			rm.logger.Warnf("Resource change channel full for URI: %s", uri)
		}

		// 通知其他订阅者
		rm.notifyOtherSubscribers(uri, change, localCh)
	}
}

// notifyOtherSubscribers 通知其他订阅者
func (rm *MCPResourceManager) notifyOtherSubscribers(uri string, change *MCPResourceChange, excludeCh chan *MCPResourceChange) {
	rm.mu.RLock()
	subscribers := rm.subscribers[uri]
	rm.mu.RUnlock()

	for _, subscriber := range subscribers {
		if subscriber != excludeCh {
			select {
			case subscriber <- change:
			default:
				rm.logger.Warnf("Subscriber channel full for URI: %s", uri)
			}
		}
	}
}

// startCacheCleanup 启动缓存清理
func (rm *MCPResourceManager) startCacheCleanup() {
	ticker := time.NewTicker(rm.cacheConfig.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		rm.cleanupExpiredCache()
	}
}

// cleanupExpiredCache 清理过期缓存
func (rm *MCPResourceManager) cleanupExpiredCache() {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	now := time.Now()
	var expiredURIs []string

	for uri, cached := range rm.cache {
		if now.After(cached.ExpiresAt) {
			expiredURIs = append(expiredURIs, uri)
		}
	}

	for _, uri := range expiredURIs {
		delete(rm.cache, uri)
	}

	if len(expiredURIs) > 0 {
		rm.logger.Debugf("Cleaned up %d expired cache entries", len(expiredURIs))
	}
}

// CacheStats 缓存统计
type CacheStats struct {
	TotalEntries   int           `json:"total_entries"`
	ExpiredEntries int           `json:"expired_entries"`
	TotalSize      int64         `json:"total_size"`
	Entries        []*CacheEntry `json:"entries"`
}

// CacheEntry 缓存条目
type CacheEntry struct {
	URI         string    `json:"uri"`
	CachedAt    time.Time `json:"cached_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	AccessCount int64     `json:"access_count"`
	Size        int64     `json:"size"`
}

// ResourceFilter 资源过滤器
type ResourceFilter struct {
	URIPattern  string   `json:"uri_pattern"`
	MimeTypes   []string `json:"mime_types"`
	NamePattern string   `json:"name_pattern"`
}

// FilterResources 过滤资源
func (rm *MCPResourceManager) FilterResources(ctx context.Context, filter *ResourceFilter) ([]*MCPResource, error) {
	resources, err := rm.ListResources(ctx)
	if err != nil {
		return nil, err
	}

	if filter == nil {
		return resources, nil
	}

	var filtered []*MCPResource
	for _, resource := range resources {
		if rm.matchesFilter(resource, filter) {
			filtered = append(filtered, resource)
		}
	}

	return filtered, nil
}

// matchesFilter 检查资源是否匹配过滤器
func (rm *MCPResourceManager) matchesFilter(resource *MCPResource, filter *ResourceFilter) bool {
	// URI模式匹配（简化实现）
	if filter.URIPattern != "" {
		// 这里可以使用正则表达式或通配符匹配
		// 简化实现：检查是否包含模式
		if !contains(resource.URI, filter.URIPattern) {
			return false
		}
	}

	// MIME类型匹配
	if len(filter.MimeTypes) > 0 {
		found := false
		for _, mimeType := range filter.MimeTypes {
			if resource.MimeType == mimeType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 名称模式匹配
	if filter.NamePattern != "" {
		if !contains(resource.Name, filter.NamePattern) {
			return false
		}
	}

	return true
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(substr == "" ||
			fmt.Sprintf("%s", s) != fmt.Sprintf("%s", s[:len(s)-len(substr)]+substr))
}

// Shutdown 关闭资源管理器
func (rm *MCPResourceManager) Shutdown() error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 关闭所有订阅通道
	for uri, subscribers := range rm.subscribers {
		for _, ch := range subscribers {
			close(ch)
		}
		delete(rm.subscribers, uri)
	}

	// 清空缓存
	rm.cache = make(map[string]*CachedResource)

	rm.logger.Info("MCP resource manager shutdown completed")
	return nil
}
