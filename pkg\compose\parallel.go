package compose

import (
	"context"
	"fmt"
	"sync"

	"github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
)

// ParallelConfig 并行组合配置
type ParallelConfig struct {
	Name            string          // 组合名称
	Description     string          // 组合描述
	Agents          []agent.Agent   // 智能体列表
	FailureStrategy FailureStrategy // 失败策略
	MaxConcurrency  int             // 最大并发数，0表示无限制
}

// FailureStrategy 失败策略
type FailureStrategy string

const (
	// FailureStrategyFailFast 快速失败：任何一个智能体失败就立即停止所有其他智能体
	FailureStrategyFailFast FailureStrategy = "fail_fast"
	// FailureStrategyCollectAll 收集所有结果：等待所有智能体完成，无论成功还是失败
	FailureStrategyCollectAll FailureStrategy = "collect_all"
	// FailureStrategyMajority 多数决策：等待大多数智能体完成，如果大多数失败则认为整体失败
	FailureStrategyMajority FailureStrategy = "majority"
)

// ParallelAgent 并行组合智能体
type ParallelAgent struct {
	*agent.BaseAgent
	agents          []agent.Agent
	failureStrategy FailureStrategy
	maxConcurrency  int
}

// NewParallelAgent 创建并行组合智能体
func NewParallelAgent(config *ParallelConfig) (*ParallelAgent, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if len(config.Agents) == 0 {
		return nil, fmt.Errorf("智能体列表不能为空")
	}

	if config.Name == "" {
		config.Name = "ParallelAgent"
	}

	if config.Description == "" {
		config.Description = "并行执行多个智能体的组合智能体"
	}

	if config.FailureStrategy == "" {
		config.FailureStrategy = FailureStrategyFailFast
	}

	if config.MaxConcurrency <= 0 {
		config.MaxConcurrency = len(config.Agents) // 默认允许所有智能体并发执行
	}

	return &ParallelAgent{
		BaseAgent:       agent.NewBaseAgent(config.Name, config.Description),
		agents:          config.Agents,
		failureStrategy: config.FailureStrategy,
		maxConcurrency:  config.MaxConcurrency,
	}, nil
}

// Run 运行并行组合智能体
func (p *ParallelAgent) Run(ctx context.Context, in *agent.Input) *runtime.AsyncIterator[*event.Event] {
	pair := runtime.NewAsyncIterator[*event.Event](ctx, 50) // 更大的缓冲区以处理并发事件

	go func() {
		defer pair.Generator.Close()
		emit := func(ev *event.Event) {
			if err := pair.Generator.Send(ev); err != nil {
				fmt.Printf("发送事件失败: %v\n", err)
			}
		}

		defer func() {
			if r := recover(); r != nil {
				emit(event.NewErrorEvent(fmt.Errorf("并行组合执行发生panic: %v", r), "PANIC", nil))
			}
		}()

		if err := p.validateInput(in); err != nil {
			emit(event.NewErrorEvent(err, "VALIDATION_ERROR", nil))
			return
		}

		// 发送开始事件
		emit(event.NewTransferEvent(p.Name(ctx), "Parallel", "开始并行执行"))

		// 执行并行智能体
		results := p.executeParallel(ctx, in, emit)

		// 处理结果
		p.processResults(ctx, results, emit)
	}()

	return pair.Iterator
}

// AgentResult 智能体执行结果
type AgentResult struct {
	AgentIndex int
	AgentName  string
	Success    bool
	Output     any
	Error      error
	Events     []*event.Event
}

// executeParallel 并行执行智能体
func (p *ParallelAgent) executeParallel(ctx context.Context, in *agent.Input, emit func(*event.Event)) []*AgentResult {
	agentCount := len(p.agents)
	results := make([]*AgentResult, agentCount)
	resultChan := make(chan *AgentResult, agentCount)

	// 创建可取消的上下文
	cancelCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 使用信号量控制并发数
	semaphore := make(chan struct{}, p.maxConcurrency)

	var wg sync.WaitGroup

	// 启动所有智能体
	for i, ag := range p.agents {
		wg.Add(1)
		go func(index int, agent agent.Agent) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			agentName := agent.Name(ctx)

			// 发送智能体开始事件
			emit(event.NewTransferEvent(p.Name(ctx), agentName, fmt.Sprintf("开始执行智能体 %d/%d", index+1, agentCount)))

			result := &AgentResult{
				AgentIndex: index,
				AgentName:  agentName,
			}

			// 执行智能体
			iterator := agent.Run(cancelCtx, in)
			var events []*event.Event
			var finalEvent *event.Event
			var errorEvent *event.Event

			// 收集所有事件
			for {
				ev, ok := iterator.Next()
				if !ok {
					break
				}

				events = append(events, ev)

				// 转发事件，添加来源信息
				forwardedEvent := p.wrapEvent(ev, agentName, index)
				emit(forwardedEvent)

				// 记录关键事件
				if ev.Type == event.EventFinal {
					finalEvent = ev
				} else if ev.Type == event.EventError {
					errorEvent = ev
				}
			}

			result.Events = events

			// 处理执行结果
			if errorEvent != nil {
				result.Success = false
				result.Error = fmt.Errorf("%v", errorEvent.Data)
			} else if finalEvent != nil {
				result.Success = true
				result.Output = finalEvent.Data
			} else {
				result.Success = false
				result.Error = fmt.Errorf("智能体 %s 没有产生最终事件或错误事件", agentName)
			}

			// 发送智能体完成事件
			emit(event.NewTransferEvent(agentName, p.Name(ctx), fmt.Sprintf("智能体 %d/%d 执行完成，成功: %t", index+1, agentCount, result.Success)))

			// 发送结果
			select {
			case resultChan <- result:
			case <-cancelCtx.Done():
				return
			}

			// 根据失败策略决定是否取消其他智能体
			if !result.Success && p.failureStrategy == FailureStrategyFailFast {
				cancel() // 取消所有其他智能体
			}
		}(i, ag)
	}

	// 等待所有智能体完成或被取消
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		results[result.AgentIndex] = result
	}

	return results
}

// processResults 处理并行执行结果
func (p *ParallelAgent) processResults(ctx context.Context, results []*AgentResult, emit func(*event.Event)) {
	successCount := 0
	failureCount := 0
	var outputs []any
	var errors []error

	// 统计结果
	for _, result := range results {
		if result == nil {
			continue // 可能因为取消而没有结果
		}

		if result.Success {
			successCount++
			outputs = append(outputs, result.Output)
		} else {
			failureCount++
			errors = append(errors, result.Error)
		}
	}

	totalCount := successCount + failureCount

	// 发送统计事件
	emit(event.NewTransferEvent(p.Name(ctx), "Summary", fmt.Sprintf("并行执行完成：成功 %d，失败 %d，总计 %d", successCount, failureCount, totalCount)))

	// 根据失败策略决定整体结果
	var overallSuccess bool
	var finalOutput any
	var finalError error

	switch p.failureStrategy {
	case FailureStrategyFailFast:
		overallSuccess = failureCount == 0
		if overallSuccess {
			finalOutput = outputs
		} else {
			finalError = fmt.Errorf("并行执行失败，失败数量: %d", failureCount)
		}

	case FailureStrategyCollectAll:
		overallSuccess = true // 总是成功，收集所有结果
		finalOutput = map[string]any{
			"successes": outputs,
			"failures":  errors,
			"summary": map[string]int{
				"success_count": successCount,
				"failure_count": failureCount,
				"total_count":   totalCount,
			},
		}

	case FailureStrategyMajority:
		majority := (totalCount + 1) / 2 // 超过一半
		overallSuccess = successCount >= majority
		if overallSuccess {
			finalOutput = outputs
		} else {
			finalError = fmt.Errorf("并行执行失败，成功数量 %d 未达到多数要求 %d", successCount, majority)
		}
	}

	// 发送最终结果
	if overallSuccess {
		var content string
		if finalOutput != nil {
			content = fmt.Sprintf("并行执行成功，结果数量: %d", len(outputs))
		} else {
			content = "并行执行成功"
		}
		emit(event.NewFinalEvent(content, "并行组合执行完成", map[string]any{
			"strategy":      string(p.failureStrategy),
			"success_count": successCount,
			"failure_count": failureCount,
			"total_count":   totalCount,
		}))
	} else {
		emit(event.NewErrorEvent(finalError, "PARALLEL_EXECUTION_FAILED", map[string]any{
			"strategy":      string(p.failureStrategy),
			"success_count": successCount,
			"failure_count": failureCount,
			"total_count":   totalCount,
		}))
	}
}

// validateInput 验证输入
func (p *ParallelAgent) validateInput(in *agent.Input) error {
	if in == nil {
		return fmt.Errorf("输入不能为空")
	}
	return nil
}

// wrapEvent 包装事件，添加来源信息
func (p *ParallelAgent) wrapEvent(originalEvent *event.Event, agentName string, agentIndex int) *event.Event {
	// 简化实现：直接转发原始事件，在元数据中添加来源信息
	wrappedEvent := &event.Event{
		Type: originalEvent.Type,
		Data: originalEvent.Data,
		Err:  originalEvent.Err,
		At:   originalEvent.At,
		Metadata: map[string]any{
			"source_agent":       agentName,
			"source_agent_index": agentIndex,
		},
	}

	// 如果原始事件有元数据，合并它们
	if originalEvent.Metadata != nil {
		for k, v := range originalEvent.Metadata {
			wrappedEvent.Metadata[k] = v
		}
	}

	return wrappedEvent
}

// GetAgents 获取智能体列表
func (p *ParallelAgent) GetAgents() []agent.Agent {
	return p.agents
}

// GetFailureStrategy 获取失败策略
func (p *ParallelAgent) GetFailureStrategy() FailureStrategy {
	return p.failureStrategy
}

// GetMaxConcurrency 获取最大并发数
func (p *ParallelAgent) GetMaxConcurrency() int {
	return p.maxConcurrency
}
