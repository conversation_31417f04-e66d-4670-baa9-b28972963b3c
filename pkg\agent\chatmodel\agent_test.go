package chatmodel

import (
	"context"
	"fmt"
	"strings"
	"testing"

	agentpkg "github.com/agentscope/agentscope-golang/pkg/agent"
	"github.com/agentscope/agentscope-golang/pkg/event"
	"github.com/agentscope/agentscope-golang/pkg/llm"
	"github.com/agentscope/agentscope-golang/pkg/memory"
	"github.com/agentscope/agentscope-golang/pkg/message"
	"github.com/agentscope/agentscope-golang/pkg/runtime"
	"github.com/agentscope/agentscope-golang/pkg/session"
	"github.com/agentscope/agentscope-golang/pkg/tool"
)

// mockChatModel 模拟聊天模型
type mockChatModel struct {
	responses []*llm.ChatResponse
	errors    []error
	callCount int
}

func (m *mockChatModel) Chat(ctx context.Context, req *llm.ChatRequest) (*llm.ChatResponse, error) {
	if m.callCount >= len(m.responses) {
		if m.callCount < len(m.errors) {
			return nil, m.errors[m.callCount]
		}
		return &llm.ChatResponse{
			Choices: []llm.ChatChoice{
				{
					Message: llm.ChatMessage{
						Role:    "assistant",
						Content: "默认响应",
					},
					FinishReason: "stop",
				},
			},
		}, nil
	}

	resp := m.responses[m.callCount]
	m.callCount++
	return resp, nil
}

func (m *mockChatModel) ChatStream(ctx context.Context, req *llm.ChatRequest) (<-chan *llm.ChatDelta, error) {
	// 简单实现，实际测试中可能不需�?
	deltaChan := make(chan *llm.ChatDelta)
	close(deltaChan)
	return deltaChan, nil
}

// mockTool 模拟工具
type mockTool struct {
	name        string
	description string
	schema      *tool.JSONSchema
	result      string
	err         error
}

func (t *mockTool) Name() string {
	return t.name
}

func (t *mockTool) Description() string {
	return t.description
}

func (t *mockTool) Schema() *tool.JSONSchema {
	return t.schema
}

func (t *mockTool) Execute(ctx context.Context, params map[string]any) (any, error) {
	if t.err != nil {
		return nil, t.err
	}
	return t.result, nil
}

// collectEvents 收集迭代器中的所有事�?
func collectEvents(iterator *runtime.AsyncIterator[*event.Event]) []*event.Event {
	var events []*event.Event
	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}
		events = append(events, ev)
	}
	return events
}

func TestNewChatModelAgent(t *testing.T) {
	mockModel := &mockChatModel{}

	// 测试有效配置
	config := &Config{
		Name:        "TestAgent",
		Description: "测试智能助手",
		Model:       mockModel,
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失败 %v", err)
	}

	if agent.Name(context.Background()) != "TestAgent" {
		t.Errorf("智能体名称不匹配: got %s, want TestAgent", agent.Name(context.Background()))
	}

	// 测试空配�?
	_, err = NewChatModelAgent(nil)
	if err == nil {
		t.Error("空配置应该返回错误")
	}

	// 测试缺少模型
	config2 := &Config{
		Name: "TestAgent",
	}
	_, err = NewChatModelAgent(config2)
	if err == nil {
		t.Error("缺少模型应该返回错误")
	}

	// 测试默认�?
	config3 := &Config{
		Model: mockModel,
	}
	agent3, err := NewChatModelAgent(config3)
	if err != nil {
		t.Fatalf("创建智能体失败 %v", err)
	}

	if agent3.Name(context.Background()) != "ChatModelAgent" {
		t.Errorf("默认名称不匹配： got %s, want ChatModelAgent", agent3.Name(context.Background()))
	}
}

func TestChatModelAgentRun(t *testing.T) {
	// 创建模拟响应
	mockModel := &mockChatModel{
		responses: []*llm.ChatResponse{
			{
				Choices: []llm.ChatChoice{
					{
						Message: llm.ChatMessage{
							Role:    "assistant",
							Content: "你好！我是AI助手",
						},
						FinishReason: "stop",
					},
				},
			},
		},
	}

	config := &Config{
		Name:          "TestAgent",
		Model:         mockModel,
		MaxIterations: 3,
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失败 %v", err)
	}

	// 创建输入
	input := &agentpkg.Input{
		Messages: []*message.Message{message.NewUserMessage("你好")},
		Tools:    []tool.Tool{},
		Memory:   memory.NewMemoryStore(),
		Session:  session.New(),
		Options:  make(map[string]any),
	}

	// 运行智能�?
	ctx := context.Background()
	iterator := agent.Run(ctx, input)

	// 收集事件
	var events []*event.Event
	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}
		events = append(events, ev)
	}

	// 验证事件
	if len(events) == 0 {
		t.Error("应该产生至少一个事件")
	}

	// 检查是否有思考事�?
	hasThought := false
	hasFinal := false
	for _, ev := range events {
		if ev.Type == event.EventThought {
			hasThought = true
		}
		if ev.Type == event.EventFinal {
			hasFinal = true
		}
	}

	if !hasThought {
		t.Error("应该包含思考事件")
	}

	if !hasFinal {
		t.Error("应该包含最终事件")
	}
}

func TestChatModelAgentWithTools(t *testing.T) {
	// 创建带工具调用的模拟响应
	mockModel := &mockChatModel{
		responses: []*llm.ChatResponse{
			{
				Choices: []llm.ChatChoice{
					{
						Message: llm.ChatMessage{
							Role:    "assistant",
							Content: "我需要使用工具来帮助你",
							ToolCalls: []*llm.ToolCall{
								{
									ID:   "call-123",
									Type: "function",
									Function: &llm.ToolCallFunction{
										Name:      "test_tool",
										Arguments: `{"query": "test"}`,
									},
								},
							},
						},
						FinishReason: "tool_calls",
					},
				},
			},
			{
				Choices: []llm.ChatChoice{
					{
						Message: llm.ChatMessage{
							Role:    "assistant",
							Content: "根据工具结果，我可以告诉你答案是：测试结果",
						},
						FinishReason: "stop",
					},
				},
			},
		},
	}

	config := &Config{
		Name:          "TestAgent",
		Model:         mockModel,
		MaxIterations: 5,
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失败 %v", err)
	}

	// 创建模拟工具
	mockTool := &mockTool{
		name:        "test_tool",
		description: "测试工具",
		schema: &tool.JSONSchema{
			Type: "object",
			Properties: map[string]*tool.JSONSchema{
				"query": {
					Type:        "string",
					Description: "查询参数",
				},
			},
			Required: []string{"query"},
		},
		result: "测试结果",
	}

	// 创建输入
	input := &agentpkg.Input{
		Messages: []*message.Message{message.NewUserMessage("请使用工具帮我查询")},
		Tools:    []tool.Tool{mockTool},
		Memory:   memory.NewMemoryStore(),
		Session:  session.New(),
		Options:  make(map[string]any),
	}

	// 运行智能�?
	ctx := context.Background()
	iterator := agent.Run(ctx, input)

	// 收集事件
	var events []*event.Event
	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}
		events = append(events, ev)
	}

	// 验证事件
	hasToolCall := false
	hasToolResult := false
	for _, ev := range events {
		if ev.Type == event.EventToolCall {
			hasToolCall = true
		}
		if ev.Type == event.EventToolResult {
			hasToolResult = true
		}
	}

	if !hasToolCall {
		t.Error("应该包含工具调用事件")
	}

	if !hasToolResult {
		t.Error("应该包含工具结果事件")
	}
}

func TestChatModelAgentValidation(t *testing.T) {
	mockModel := &mockChatModel{}

	config := &Config{
		Name:  "TestAgent",
		Model: mockModel,
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失�? %v", err)
	}

	// 测试空输�?
	ctx := context.Background()
	iterator := agent.Run(ctx, nil)

	// 应该产生错误事件
	ev, ok := iterator.Next()
	if !ok {
		t.Fatal("应该产生错误事件")
	}

	if ev.Type != event.EventError {
		t.Errorf("应该是错误事件，得到: %s", ev.Type)
	}

	// 测试空消息列
	input := &agentpkg.Input{
		Messages: []*message.Message{}, // 空消息列
		Tools:    []tool.Tool{},
		Options:  make(map[string]any),
	}

	iterator2 := agent.Run(ctx, input)
	ev2, ok := iterator2.Next()
	if !ok {
		t.Fatal("应该产生错误事件")
	}

	if ev2.Type != event.EventError {
		t.Errorf("应该是错误事件，得到: %s", ev2.Type)
	}
}

func TestChatModelAgentMaxIterations(t *testing.T) {
	// 创建永远不完成的模拟响应
	mockModel := &mockChatModel{
		responses: []*llm.ChatResponse{
			{
				Choices: []llm.ChatChoice{
					{
						Message: llm.ChatMessage{
							Role:    "assistant",
							Content: "继续思考中...",
						},
						FinishReason: "max_tokens", // 不是 stop �?length，会继续迭代
					},
				},
			},
			{
				Choices: []llm.ChatChoice{
					{
						Message: llm.ChatMessage{
							Role:    "assistant",
							Content: "还在思考中...",
						},
						FinishReason: "max_tokens", // 不是 stop �?length，会继续迭代
					},
				},
			},
			{
				Choices: []llm.ChatChoice{
					{
						Message: llm.ChatMessage{
							Role:    "assistant",
							Content: "继续思考中...",
						},
						FinishReason: "max_tokens", // 不是 stop �?length，会继续迭代
					},
				},
			},
		},
	}

	config := &Config{
		Name:          "TestAgent",
		Model:         mockModel,
		MaxIterations: 2, // 设置较小的最大迭代次数
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失败 %v", err)
	}

	// 创建输入
	input := &agentpkg.Input{
		Messages: []*message.Message{message.NewUserMessage("请持续思考")},
		Tools:    []tool.Tool{},
		Session:  session.New(),
		Options:  make(map[string]any),
	}

	// 运行智能�?
	ctx := context.Background()
	iterator := agent.Run(ctx, input)

	// 收集事件
	var events []*event.Event
	for {
		ev, ok := iterator.Next()
		if !ok {
			break
		}
		events = append(events, ev)
	}

	// 应该以错误事件结束（达到最大迭代次数）
	if len(events) == 0 {
		t.Fatal("应该产生事件")
	}

	lastEvent := events[len(events)-1]
	if lastEvent.Type != event.EventError {
		t.Errorf("最后一个事件应该是错误事件，得�? %s", lastEvent.Type)
	}
}

func TestChatModelAgentErrorHandling(t *testing.T) {
	// 测试聊天模型返回错误的情�?
	mockModel := &mockChatModel{
		errors: []error{fmt.Errorf("模拟LLM错误")},
	}

	config := &Config{
		Name:          "TestAgent",
		Model:         mockModel,
		MaxIterations: 1,
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失�? %v", err)
	}

	ctx := context.Background()
	input := &agentpkg.Input{
		Messages: []*message.Message{
			message.NewUserMessage("测试消息"),
		},
		Tools:   []tool.Tool{},
		Memory:  memory.NewMemoryStore(),
		Session: session.New(),
	}

	iterator := agent.Run(ctx, input)
	events := collectEvents(iterator)

	// 应该包含错误事件
	hasError := false
	for _, ev := range events {
		if ev.Type == event.EventError {
			hasError = true
			break
		}
	}

	if !hasError {
		t.Error("应该包含错误事件")
	}
}

func TestChatModelAgentConfigValidation(t *testing.T) {
	// 测试无效配置
	t.Run("nil config", func(t *testing.T) {
		_, err := NewChatModelAgent(nil)
		if err == nil {
			t.Error("期望错误，但没有返回错误")
		}
		if !strings.Contains(err.Error(), "配置不能为空") {
			t.Errorf("错误消息不匹配 got %s", err.Error())
		}
	})

	t.Run("nil model", func(t *testing.T) {
		config := &Config{
			Name: "TestAgent",
		}
		_, err := NewChatModelAgent(config)
		if err == nil {
			t.Error("期望错误，但没有返回错误")
		}
		if !strings.Contains(err.Error(), "聊天模型不能为空") {
			t.Errorf("错误消息不匹配 got %s", err.Error())
		}
	})

	// 测试默认值设�?
	t.Run("empty name gets default", func(t *testing.T) {
		config := &Config{
			Model: &mockChatModel{},
		}
		agent, err := NewChatModelAgent(config)
		if err != nil {
			t.Fatalf("创建智能体失败 %v", err)
		}
		if agent.Name(context.Background()) != "ChatModelAgent" {
			t.Errorf("期望默认名称 ChatModelAgent，得�? %s", agent.Name(context.Background()))
		}
	})

	t.Run("invalid max iterations gets default", func(t *testing.T) {
		config := &Config{
			Name:          "TestAgent",
			Model:         &mockChatModel{},
			MaxIterations: -1,
		}
		agent, err := NewChatModelAgent(config)
		if err != nil {
			t.Fatalf("创建智能体失败： %v", err)
		}
		if agent.maxIterations != 10 {
			t.Errorf("期望默认最大迭代次�?10，得�? %d", agent.maxIterations)
		}
	})
}

func TestChatModelAgentUtilityMethods(t *testing.T) {
	mockModel := &mockChatModel{}
	config := &Config{
		Name:        "TestAgent",
		Model:       mockModel,
		Temperature: floatPtr(0.7),
		MaxTokens:   intPtr(1000),
	}

	agent, err := NewChatModelAgent(config)
	if err != nil {
		t.Fatalf("创建智能体失�? %v", err)
	}

	// 测试 getSessionID
	input := &agentpkg.Input{
		Session: session.New(),
	}
	input.Session.Set("session_id", "test-session")
	sessionID := agent.getSessionID(input)
	if sessionID != "test-session" {
		t.Errorf("会话ID不匹�? got %s, want test-session", sessionID)
	}

	// 测试没有会话ID的情�?
	input2 := &agentpkg.Input{
		Session: session.New(),
	}
	sessionID2 := agent.getSessionID(input2)
	if sessionID2 == "" {
		t.Error("应该生成默认会话ID")
	}

	// 测试 findTool
	mockTool := &mockTool{name: "test_tool"}
	tools := []tool.Tool{mockTool}
	foundTool := agent.findTool(tools, "test_tool")
	if foundTool == nil {
		t.Error("应该找到工具")
	}

	notFoundTool := agent.findTool(tools, "nonexistent_tool")
	if notFoundTool != nil {
		t.Error("不应该找到不存在的工具")
	}

	// 测试 convertMessageToChatMessage
	msg := message.NewUserMessage("测试消息")
	msg.Name = "TestUser"
	chatMsg := agent.convertMessageToChatMessage(msg)
	if chatMsg.Role != "user" {
		t.Errorf("角色不匹�? got %s, want user", chatMsg.Role)
	}
	if chatMsg.Name != "TestUser" {
		t.Errorf("名称不匹�? got %s, want TestUser", chatMsg.Name)
	}

	// 测试 convertToolsToDefinitions
	definitions := agent.convertToolsToDefinitions(tools)
	if len(definitions) != 1 {
		t.Errorf("工具定义数量不匹�? got %d, want 1", len(definitions))
	}
	if definitions[0].Function.Name != "test_tool" {
		t.Errorf("工具名称不匹�? got %s, want test_tool", definitions[0].Function.Name)
	}
}

// 辅助函数
func floatPtr(f float64) *float64 {
	return &f
}

func intPtr(i int) *int {
	return &i
}
