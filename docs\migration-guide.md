# AgentScope-Golang 迁移指南

本文档提供从旧版本 AgentScope-Golang 迁移到新架构的详细指南。新架构基于事件驱动设计，提供了更好的可扩展性、可观测性和组合能力。

## 目录

- [架构变化概述](#架构变化概述)
- [核心接口变化](#核心接口变化)
- [迁移步骤](#迁移步骤)
- [API 对比](#api-对比)
- [示例代码](#示例代码)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

## 架构变化概述

### 新架构特性

1. **事件驱动架构**: 所有 Agent 执行过程通过事件流进行通信
2. **组合式设计**: 支持 Sequential、Parallel、Loop 等组合模式
3. **统一错误处理**: 结构化错误类型和错误代码
4. **可观测性**: 内置监控、指标收集和链路追踪
5. **工具系统重构**: 基于 JSON Schema 的工具定义和验证
6. **记忆系统**: 支持多种后端的会话记忆存储

### 主要变化

| 组件 | 旧版本 | 新版本 | 变化说明 |
|------|--------|--------|----------|
| Agent 接口 | 同步执行 | 异步事件流 | 支持流式输出和中断恢复 |
| 工具系统 | map 参数 | JSON Schema | 类型安全和验证 |
| 错误处理 | 普通 error | 结构化错误 | 错误分类和上下文信息 |
| 组合模式 | 无 | Sequential/Parallel/Loop | 多智能体协作 |
| 监控系统 | 无 | 内置指标收集 | 性能监控和调试 |

## 核心接口变化

### Agent 接口

**旧版本:**
```go
type Agent interface {
    Name() string
    Description() string
    Execute(ctx context.Context, input string) (string, error)
}
```

**新版本:**
```go
type Agent interface {
    Name(ctx context.Context) string
    Description(ctx context.Context) string
    Run(ctx context.Context, input *Input) *runtime.AsyncIterator[*event.Event]
}
```

### 工具接口

**旧版本:**
```go
type Tool interface {
    Name() string
    Description() string
    Execute(ctx context.Context, args map[string]any) (*ToolResult, error)
}
```

**新版本:**
```go
type Tool interface {
    Name() string
    Description() string
    Schema() *JSONSchema
    Execute(ctx context.Context, input string) (string, error)
}
```

### LLM 接口

**旧版本:**
```go
type LLM interface {
    Generate(ctx context.Context, prompt string) (string, error)
}
```

**新版本:**
```go
type ChatModel interface {
    Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
    ChatStream(ctx context.Context, req *ChatRequest) (<-chan *ChatDelta, error)
}
```

## 迁移步骤

### 步骤 1: 更新依赖

```bash
# 更新到新版本
go get github.com/agentscope/agentscope-golang@latest
go mod tidy
```

### 步骤 2: 迁移 Agent 实现

**旧版本 Agent:**
```go
type MyAgent struct {
    name string
    llm  LLM
}

func (a *MyAgent) Execute(ctx context.Context, input string) (string, error) {
    return a.llm.Generate(ctx, input)
}
```

**新版本 Agent:**
```go
type MyAgent struct {
    name      string
    chatModel llm.ChatModel
}

func (a *MyAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
    pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)
    
    go func() {
        defer pair.Generator.Close()
        
        // 构建聊天请求
        req := &llm.ChatRequest{
            Messages: input.Messages,
            Tools:    input.Tools,
        }
        
        // 调用 LLM
        resp, err := a.chatModel.Chat(ctx, req)
        if err != nil {
            pair.Generator.Send(&event.Event{
                Type: event.EventError,
                Data: &event.ErrorData{Message: err.Error()},
            })
            return
        }
        
        // 发送最终结果
        pair.Generator.Send(&event.Event{
            Type: event.EventFinal,
            Data: &event.FinalData{Content: resp.Content},
        })
    }()
    
    return pair.Iterator
}
```

### 步骤 3: 迁移工具实现

**旧版本工具:**
```go
type HTTPTool struct{}

func (t *HTTPTool) Execute(ctx context.Context, args map[string]any) (*ToolResult, error) {
    url := args["url"].(string)
    // HTTP 请求逻辑
    return &ToolResult{Content: response}, nil
}
```

**新版本工具:**
```go
type HTTPTool struct{}

func (t *HTTPTool) Schema() *newtool.JSONSchema {
    return &newtool.JSONSchema{
        Type: "object",
        Properties: map[string]*newtool.JSONSchema{
            "url": {Type: "string", Description: "请求URL"},
        },
        Required: []string{"url"},
    }
}

func (t *HTTPTool) Execute(ctx context.Context, input string) (string, error) {
    var args map[string]any
    if err := json.Unmarshal([]byte(input), &args); err != nil {
        return "", err
    }
    
    url := args["url"].(string)
    // HTTP 请求逻辑
    return response, nil
}
```

### 步骤 4: 更新错误处理

**旧版本:**
```go
if err != nil {
    return "", fmt.Errorf("执行失败: %w", err)
}
```

**新版本:**
```go
if err != nil {
    return "", errors.Wrap(err, errors.ErrorTypeAgent, "AGT_001", "智能体执行失败")
}
```

### 步骤 5: 添加监控和可观测性

```go
// 创建监控拦截器
monitoringInterceptor := &MonitoringInterceptor{}

// 创建拦截器链
chain := runtime.NewInterceptorChain()
chain.Add(monitoringInterceptor)

// 在 Runner 中使用
runner := runtime.NewRunner(chain)
```

## API 对比

### 创建和运行 Agent

**旧版本:**
```go
agent := &MyAgent{name: "assistant"}
result, err := agent.Execute(ctx, "Hello")
if err != nil {
    log.Fatal(err)
}
fmt.Println(result)
```

**新版本:**
```go
agent := &MyAgent{name: "assistant"}
input := &agent.Input{
    Messages: []*message.Message{
        message.NewUserMessage("Hello"),
    },
}

iterator := agent.Run(ctx, input)
for {
    event, ok := iterator.Next()
    if !ok {
        break
    }
    
    switch event.Type {
    case "final":
        if data, ok := event.Data.(*event.FinalData); ok {
            fmt.Println(data.Content)
        }
    case "error":
        if data, ok := event.Data.(*event.ErrorData); ok {
            log.Printf("错误: %s", data.Message)
        }
    }
}
```

### 组合多个 Agent

**新版本特性 (旧版本不支持):**
```go
// 顺序执行
sequential, err := compose.NewSequentialAgent(&compose.SequentialConfig{
    Name:        "工作流",
    Agents:      []agent.Agent{agent1, agent2, agent3},
    FailureMode: compose.FailureModeStop,
})

// 并行执行
parallel, err := compose.NewParallelAgent(&compose.ParallelConfig{
    Name:     "并行任务",
    Agents:   []agent.Agent{agent1, agent2},
    Strategy: compose.CollectAll,
})

// 循环执行
loop, err := compose.NewLoopAgent(&compose.LoopConfig{
    Name:         "迭代优化",
    Agent:        agent1,
    MaxIteration: 5,
    StopCondition: func(ctx context.Context, iteration int, lastOutput string) bool {
        return strings.Contains(lastOutput, "完成")
    },
})
```

## 示例代码

### 完整的迁移示例

**旧版本完整示例:**
```go
package main

import (
    "context"
    "fmt"
    "log"
)

type SimpleAgent struct {
    name string
}

func (a *SimpleAgent) Name() string {
    return a.name
}

func (a *SimpleAgent) Execute(ctx context.Context, input string) (string, error) {
    return fmt.Sprintf("处理: %s", input), nil
}

func main() {
    agent := &SimpleAgent{name: "助手"}
    result, err := agent.Execute(context.Background(), "你好")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(result)
}
```

**新版本完整示例:**
```go
package main

import (
    "context"
    "fmt"
    
    "github.com/agentscope/agentscope-golang/pkg/agent"
    "github.com/agentscope/agentscope-golang/pkg/event"
    "github.com/agentscope/agentscope-golang/pkg/message"
    "github.com/agentscope/agentscope-golang/pkg/runtime"
)

type SimpleAgent struct {
    name string
}

func (a *SimpleAgent) Name(ctx context.Context) string {
    return a.name
}

func (a *SimpleAgent) Description(ctx context.Context) string {
    return "简单的示例智能体"
}

func (a *SimpleAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
    pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)
    
    go func() {
        defer pair.Generator.Close()
        
        // 获取用户消息
        userMsg := input.Messages[len(input.Messages)-1].Content
        
        // 发送最终结果
        pair.Generator.Send(&event.Event{
            Type: "final",
            Data: &event.FinalData{
                Content: fmt.Sprintf("处理: %s", userMsg),
            },
        })
    }()
    
    return pair.Iterator
}

func main() {
    agent := &SimpleAgent{name: "助手"}
    
    input := &agent.Input{
        Messages: []*message.Message{
            message.NewUserMessage("你好"),
        },
    }
    
    iterator := agent.Run(context.Background(), input)
    for {
        event, ok := iterator.Next()
        if !ok {
            break
        }
        
        if event.Type == "final" {
            if data, ok := event.Data.(*event.FinalData); ok {
                fmt.Println(data.Content)
            }
        }
    }
}
```

## 常见问题

### Q: 为什么要从同步改为异步事件流？

A: 异步事件流提供了以下优势：
- 支持流式输出，提升用户体验
- 支持中断和恢复，提高可靠性
- 更好的可观测性，便于调试和监控
- 支持复杂的多智能体协作模式

### Q: 如何处理现有的同步代码？

A: 可以在 Agent 的 Run 方法中包装同步代码：
```go
func (a *MyAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
    pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)
    
    go func() {
        defer pair.Generator.Close()
        
        // 调用现有的同步方法
        result, err := a.oldSyncMethod(ctx, input)
        if err != nil {
            pair.Generator.Send(&event.Event{
                Type: "error",
                Data: &event.ErrorData{Message: err.Error()},
            })
            return
        }
        
        pair.Generator.Send(&event.Event{
            Type: "final",
            Data: &event.FinalData{Content: result},
        })
    }()
    
    return pair.Iterator
}
```

### Q: 工具系统的 JSON Schema 如何定义？

A: 参考以下示例：
```go
func (t *MyTool) Schema() *newtool.JSONSchema {
    return &newtool.JSONSchema{
        Type:        "object",
        Description: "我的工具描述",
        Properties: map[string]*newtool.JSONSchema{
            "param1": {
                Type:        "string",
                Description: "参数1描述",
            },
            "param2": {
                Type:        "integer",
                Description: "参数2描述",
                Minimum:     &[]float64{0}[0],
                Maximum:     &[]float64{100}[0],
            },
        },
        Required: []string{"param1"},
    }
}
```

## 最佳实践

### 1. 错误处理

使用结构化错误处理：
```go
import "github.com/agentscope/agentscope-golang/pkg/errors"

// 创建特定类型的错误
err := errors.New(errors.ErrorTypeAgent, "AGT_001", "智能体执行失败")

// 包装现有错误
wrappedErr := errors.Wrap(originalErr, errors.ErrorTypeNetwork, "NET_001", "网络请求失败")

// 添加上下文信息
err.WithContext("agent_id", "agent_123")
```

### 2. 监控和可观测性

添加监控拦截器：
```go
// 创建监控拦截器
monitoringInterceptor := &MonitoringInterceptor{}

// 创建拦截器链
chain := runtime.NewInterceptorChain()
chain.Add(monitoringInterceptor)

// 在执行前后收集指标
chain.BeforeRun(ctx, agentName, input)
// ... 执行 Agent
chain.AfterRun(ctx, agentName, input, err)
```

### 3. 资源管理

正确管理 AsyncIterator：
```go
func (a *MyAgent) Run(ctx context.Context, input *agent.Input) *runtime.AsyncIterator[*event.Event] {
    pair := runtime.NewAsyncIterator[*event.Event](ctx, 10)
    
    go func() {
        // 确保关闭 Generator
        defer pair.Generator.Close()
        
        // 监听上下文取消
        select {
        case <-ctx.Done():
            return
        default:
            // 执行逻辑
        }
    }()
    
    return pair.Iterator
}
```

### 4. 组合模式选择

根据场景选择合适的组合模式：
- **Sequential**: 有依赖关系的任务序列
- **Parallel**: 独立的并行任务
- **Loop**: 需要迭代优化的任务
- **Transfer**: 需要动态路由的复杂工作流

---

更多详细信息请参考：
- [架构设计文档](./architecture-design.md)
- [API 参考文档](./api-reference.md)
- [示例代码](../examples/)
